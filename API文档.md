# BOS 加油站管理系统 API 文档

## 概述

本文档描述了BOS（Back Office System）后台办公系统的RESTful API接口。系统提供了燃油交易、订单管理、报表分析、员工管理、班次管理、促销管理、会员管理、油品管理、设备管理、客户管理、库存管理和支付管理等功能。

**Base URL**: `/api/v1`

## 认证

所有API请求都需要适当的认证。具体认证方式请参考员工登录接口。

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 错误响应
```json
{
  "code": "ERROR_CODE",
  "message": "错误描述",
  "detail": "详细错误信息"
}
```

## API 接口

### 1. 燃油交易管理 (Fuel Transactions)

#### 1.1 获取燃油交易列表
- **接口**: `GET /fuel-transactions`
- **描述**: 获取燃油交易列表，支持分页和过滤
- **查询参数**:
  - `station_id` (int): 加油站ID
  - `status` (string): 交易状态 (pending, processed, cancelled)
  - `pump_id` (string): 油枪ID
  - `member_id` (int): 会员ID
  - `date_from` (string): 开始日期 (格式: 2006-01-02)
  - `date_to` (string): 结束日期 (格式: 2006-01-02)
  - `transaction_number` (string): 交易编号
  - `fuel_type` (string): 燃油类型
  - `fuel_grade` (string): 燃油等级
  - `tank` (int): 油罐编号
  - `page` (int): 页码，默认为1
  - `limit` (int): 每页数量，默认为10
  - `sort_by` (string): 排序字段，默认为created_at
  - `sort_dir` (string): 排序方向 (asc, desc)，默认为desc

#### 1.2 创建燃油交易
- **接口**: `POST /fuel-transactions`
- **描述**: 创建新的燃油交易记录
- **请求体**:
```json
{
  "transaction_number": "string",
  "station_id": 0,
  "pump_id": "string",
  "nozzle_id": "string",
  "fuel_type": "string",
  "fuel_grade": "string",
  "tank": 0,
  "unit_price": 0,
  "volume": 0,
  "amount": 0,
  "total_volume": 0,
  "total_amount": 0,
  "member_card_id": "string",
  "member_id": 0,
  "employee_id": 0,
  "fcc_transaction_id": "string",
  "pos_terminal_id": "string",
  "start_totalizer": 0,
  "end_totalizer": 0,
  "nozzle_start_time": "2023-01-01T00:00:00Z",
  "nozzle_end_time": "2023-01-01T00:00:00Z",
  "metadata": {}
}
```

#### 1.3 获取燃油交易详情
- **接口**: `GET /fuel-transactions/{id}`
- **描述**: 根据ID获取燃油交易详情

#### 1.4 关联燃油交易到订单
- **接口**: `POST /fuel-transactions/{id}/link`
- **描述**: 创建燃油交易与订单的关联关系
- **请求体**:
```json
{
  "order_id": 0,
  "allocated_amount": 0
}
```

#### 1.5 解除燃油交易与订单关联
- **接口**: `DELETE /fuel-transactions/{id}/unlink/{order_id}`
- **描述**: 解除燃油交易与订单的关联关系

#### 1.6 获取燃油交易关联的订单
- **接口**: `GET /fuel-transactions/{id}/orders`
- **描述**: 获取指定燃油交易关联的所有订单

#### 1.7 更新关联分配金额
- **接口**: `PUT /fuel-transactions/{id}/link/{order_id}`
- **描述**: 更新燃油交易与订单关联的分配金额

### 2. 订单管理 (Orders)

#### 2.1 获取订单列表
- **接口**: `GET /orders`
- **描述**: 获取订单列表，支持分页和过滤
- **查询参数**:
  - `customer_id` (int): 客户ID
  - `station_id` (int): 加油站ID
  - `status` (string): 订单状态 (new, processing, completed, cancelled)
  - `date_from` (string): 开始日期
  - `date_to` (string): 结束日期
  - `order_number` (string): 订单编号
  - `product_type` (string): 产品类型
  - `payment_method` (string): 支付方式
  - `employee_no` (string): 员工编号
  - `page` (int): 页码
  - `limit` (int): 每页数量
  - `sort_by` (string): 排序字段
  - `sort_dir` (string): 排序方向

#### 2.2 创建订单
- **接口**: `POST /orders`
- **描述**: 创建新的订单记录
- **请求体**:
```json
{
  "fuel_transaction_id": 0,
  "station_id": 0,
  "customer_id": 0,
  "customer_name": "string",
  "employee_no": "string",
  "terminal_id": "string",
  "metadata": {},
  "payment_type": "string",
  "payment_method": 0,
  "allocated_amount": 0
}
```

#### 2.3 获取订单详情
- **接口**: `GET /orders/{id}`
- **描述**: 根据ID获取订单详情

#### 2.4 添加订单项
- **接口**: `POST /orders/{id}/items`
- **描述**: 向订单添加商品项

#### 2.5 移除订单项
- **接口**: `DELETE /orders/{id}/items/{item_id}`
- **描述**: 从订单中移除指定商品项

#### 2.6 应用促销
- **接口**: `POST /orders/{id}/promotions`
- **描述**: 向订单应用促销活动

#### 2.7 完成订单
- **接口**: `POST /orders/{id}/complete`
- **描述**: 将订单状态更新为已完成

#### 2.8 取消订单
- **接口**: `POST /orders/{id}/cancel`
- **描述**: 取消订单

#### 2.9 获取订单支付状态
- **接口**: `GET /orders/{id}/payment-status`
- **描述**: 获取订单的支付状态

#### 2.10 获取订单关联的燃油交易
- **接口**: `GET /orders/{order_id}/fuel-transactions`
- **描述**: 获取指定订单关联的所有燃油交易

### 3. 报表管理 (Reports)

#### 3.1 获取支付方式汇总
- **接口**: `GET /reports/payment-methods`
- **描述**: 获取按支付方式的销售汇总
- **查询参数**:
  - `station_id` (int): 站点ID
  - `date_from` (string): 开始日期
  - `date_to` (string): 结束日期

#### 3.2 获取聚合收入报表
- **接口**: `GET /reports/revenue`
- **描述**: 获取聚合收入报表
- **查询参数**:
  - `start_date` (string): 开始日期 (必填)
  - `end_date` (string): 结束日期 (必填)
  - `site_ids` (string): 站点ID列表 (逗号分隔)
  - `granularity` (string): 聚合粒度 (day, week, month)

#### 3.3 获取油品应收汇总
- **接口**: `GET /reports/receivable`
- **描述**: 获取油品应收汇总

#### 3.4 获取油枪销售汇总
- **接口**: `GET /reports/nozzle-sales`
- **描述**: 获取油枪销售汇总

#### 3.5 获取交易明细
- **接口**: `GET /reports/transactions`
- **描述**: 获取交易明细数据(用于报表)

#### 3.6 获取按商品分类销售汇总
- **接口**: `GET /reports/sales-by-category`
- **描述**: 按商品分类聚合销售额

#### 3.7 获取班次日终报表
- **接口**: `GET /reports/shift-eod`
- **描述**: 获取指定站点、日期的班次日终报表数据
- **查询参数**:
  - `station_id` (int): 站点ID (必填)
  - `date` (string): 查询日期 (必填，格式: 2024-01-04)

### 4. 增强报表 (Enhanced Reports)

#### 4.1 获取总收入
- **接口**: `GET /enhanced-reports/total-revenue`
- **描述**: 获取总收入统计

#### 4.2 获取燃油应收汇总
- **接口**: `GET /enhanced-reports/fuel-receivable`
- **描述**: 获取燃油应收汇总

#### 4.3 获取按支付方式销售明细
- **接口**: `GET /enhanced-reports/sales-by-payment-method`
- **描述**: 获取按支付方式的销售明细

#### 4.4 获取按产品销售明细
- **接口**: `GET /enhanced-reports/sales-by-product`
- **描述**: 获取按产品的销售明细

#### 4.5 获取油枪详细销售
- **接口**: `GET /enhanced-reports/nozzle-sales`
- **描述**: 获取油枪详细销售数据

#### 4.6 获取查询性能
- **接口**: `GET /enhanced-reports/query-performance`
- **描述**: 获取查询性能统计

### 5. 员工管理 (Employee)

#### 5.1 员工登录
- **接口**: `POST /employee/login`
- **描述**: 员工登录认证
- **请求体**:
```json
{
  "employee_no": "string",
  "password": "string"
}
```

#### 5.2 创建员工
- **接口**: `POST /employee`
- **描述**: 创建新员工

#### 5.3 获取员工信息
- **接口**: `GET /employee/{id}`
- **描述**: 根据ID获取员工信息

#### 5.4 更新员工信息
- **接口**: `PUT /employee/{id}`
- **描述**: 更新员工信息

#### 5.5 删除员工
- **接口**: `DELETE /employee/{id}`
- **描述**: 删除员工

#### 5.6 获取员工列表
- **接口**: `GET /employees`
- **描述**: 获取员工列表

### 6. 班次管理 (Shifts)

#### 6.1 获取班次列表
- **接口**: `GET /shifts`
- **描述**: 获取班次列表

#### 6.2 开始新班次
- **接口**: `POST /shifts/start`
- **描述**: 开始新的工作班次

#### 6.3 结束当前班次
- **接口**: `POST /shifts/{station_id}/end`
- **描述**: 结束指定站点的当前班次

#### 6.4 获取当前活跃班次
- **接口**: `GET /shifts/current/{station_id}`
- **描述**: 获取指定站点的当前活跃班次

#### 6.5 确保班次已开始
- **接口**: `POST /shifts/{station_id}/ensure`
- **描述**: 确保指定站点的班次已开始

#### 6.6 获取班次详情
- **接口**: `GET /shifts/{id}`
- **描述**: 根据ID获取班次详情

#### 6.7 根据编号获取班次
- **接口**: `GET /shifts/number/{number}`
- **描述**: 根据班次编号获取班次信息

#### 6.8 获取班次报表
- **接口**: `GET /shifts/report/{id}`
- **描述**: 获取指定班次的报表数据

#### 6.9 软删除班次
- **接口**: `DELETE /shifts/{id}`
- **描述**: 软删除指定班次

#### 6.10 恢复已删除班次
- **接口**: `POST /shifts/{id}/restore`
- **描述**: 恢复已删除的班次

### 7. 促销管理 (Promotions)

#### 7.1 获取促销活动列表
- **接口**: `GET /promotions`
- **描述**: 获取所有促销活动，支持分页、搜索和筛选
- **查询参数**:
  - `status` (string): 促销活动状态筛选
  - `type` (string): 促销活动类型筛选
  - `search` (string): 搜索关键词
  - `page` (int): 页码，默认为1
  - `pageSize` (int): 每页记录数，默认为10

#### 7.2 查看促销活动详情
- **接口**: `GET /promotions/{id}`
- **描述**: 获取指定促销活动的详细信息

#### 7.3 显示促销活动表单
- **接口**: `GET /promotions/new`
- **描述**: 显示创建新促销活动的表单

#### 7.4 显示编辑促销活动表单
- **接口**: `GET /promotions/edit/{id}`
- **描述**: 显示编辑指定促销活动的表单

#### 7.5 保存促销活动
- **接口**: `POST /promotions/save`
- **描述**: 创建新的促销活动

#### 7.6 更新促销活动
- **接口**: `POST /promotions/save/{id}`
- **描述**: 更新指定的促销活动

#### 7.7 删除促销活动
- **接口**: `DELETE /promotions/{id}`
- **描述**: 删除指定的促销活动

#### 7.8 更新促销活动状态
- **接口**: `POST /promotions/{id}/status`
- **描述**: 更新促销活动的状态

### 8. 促销计算 (Calculator)

#### 8.1 促销计算主页
- **接口**: `GET /calculator`
- **描述**: 获取促销计算器主页信息

#### 8.2 获取计算器状态
- **接口**: `GET /calculator/status`
- **描述**: 获取促销计算器的当前状态

#### 8.3 处理订单
- **接口**: `POST /calculator/process`
- **描述**: 处理订单并应用促销规则

#### 8.4 计算促销
- **接口**: `POST /calculator/calculate`
- **描述**: 计算订单的促销优惠
- **请求体**:
```json
{
  "userId": "string",
  "orderAmount": 0,
  "orderTime": "2023-01-01T12:00:00Z",
  "items": [
    {
      "itemId": "string",
      "name": "string",
      "category": "string",
      "price": 0,
      "quantity": 0
    }
  ]
}
```

### 9. 会员管理 (Members)

#### 9.1 创建会员
- **接口**: `POST /members`
- **描述**: 创建新的会员账户
- **请求体**:
```json
{
  "phone": "string",
  "name": "string",
  "password": "string"
}
```

#### 9.2 获取会员列表
- **接口**: `GET /members`
- **描述**: 获取会员列表

#### 9.3 获取会员信息
- **接口**: `GET /members/{id}`
- **描述**: 根据ID获取会员信息

#### 9.4 根据手机号获取会员信息
- **接口**: `GET /members/phone/{phone}`
- **描述**: 根据手机号获取会员信息

#### 9.5 验证会员
- **接口**: `POST /members/verify`
- **描述**: 验证会员身份

#### 9.6 更新会员信息
- **接口**: `PUT /members/{id}`
- **描述**: 更新会员信息

#### 9.7 更新会员状态
- **接口**: `PATCH /members/{id}/status`
- **描述**: 更新会员状态

#### 9.8 获取会员统计
- **接口**: `GET /members/statistics`
- **描述**: 获取会员统计数据

### 10. 油品管理 (Oil)

#### 10.1 获取油品销售价格
- **接口**: `GET /oil/sales-price`
- **描述**: 根据产品ID、客户ID、客户组ID和数量获取油品销售价格
- **查询参数**:
  - `product_id` (uint): 产品ID (必填)
  - `customer_id` (uint): 客户ID
  - `customer_group_id` (uint): 客户组ID
  - `quantity` (float64): 购买数量，默认1.0

#### 10.2 获取油品列表
- **接口**: `GET /oil/products`
- **描述**: 获取所有油品产品列表

#### 10.3 获取单个油品详情
- **接口**: `GET /oil/products/{id}`
- **描述**: 根据ID获取油品详细信息

#### 10.4 创建新油品
- **接口**: `POST /oil/products`
- **描述**: 创建新的油品产品

#### 10.5 更新油品信息
- **接口**: `PUT /oil/products/{id}`
- **描述**: 更新指定油品的信息

#### 10.6 设置油品基础价格
- **接口**: `POST /oil/products/{id}/price`
- **描述**: 设置指定油品的基础价格
- **请求体**:
```json
{
  "price": 0,
  "cost_price": 0,
  "suggest_price": 0,
  "wholesale_price": 0,
  "effective_at": "2023-01-01T00:00:00Z",
  "operator": "string"
}
```

#### 10.7 获取定价策略列表
- **接口**: `GET /oil/strategies`
- **描述**: 获取所有定价策略

#### 10.8 创建定价策略
- **接口**: `POST /oil/strategies`
- **描述**: 创建新的定价策略

#### 10.9 激活定价策略
- **接口**: `POST /oil/strategies/{id}/activate`
- **描述**: 激活指定的定价策略

#### 10.10 停用定价策略
- **接口**: `POST /oil/strategies/{id}/deactivate`
- **描述**: 停用指定的定价策略

#### 10.11 获取价格调整流程列表
- **接口**: `GET /oil/adjustments`
- **描述**: 获取所有价格调整流程

#### 10.12 发起价格调整
- **接口**: `POST /oil/adjustments`
- **描述**: 发起新的价格调整流程

#### 10.13 获取价格调整详情
- **接口**: `GET /oil/adjustments/{id}`
- **描述**: 获取指定价格调整的详细信息

#### 10.14 审批价格调整
- **接口**: `POST /oil/adjustments/{id}/approve`
- **描述**: 审批价格调整申请
- **请求体**:
```json
{
  "approver": "string"
}
```

#### 10.15 提交价格调整流程审批
- **接口**: `POST /oil/adjustments/{id}/submit`
- **描述**: 提交价格调整流程进行审批

### 11. PTS2设备管理 (PTS2 Devices)

#### 11.1 获取设备列表
- **接口**: `GET /pts2/devices`
- **描述**: 获取所有PTS2设备列表

#### 11.2 获取设备详情
- **接口**: `GET /pts2/devices/{deviceId}`
- **描述**: 获取指定设备的详细信息

#### 11.3 获取设备状态
- **接口**: `GET /pts2/devices/{deviceId}/status`
- **描述**: 获取指定设备的当前状态

#### 11.4 连接设备
- **接口**: `POST /pts2/devices/{deviceId}/connect`
- **描述**: 连接到指定的PTS2设备

#### 11.5 断开设备连接
- **接口**: `POST /pts2/devices/{deviceId}/disconnect`
- **描述**: 断开与指定设备的连接

#### 11.6 获取设备信息
- **接口**: `GET /pts2/devices/{deviceId}/info`
- **描述**: 获取设备的基本信息

### 12. PTS2油泵管理 (PTS2 Pumps)

#### 12.1 获取油泵状态
- **接口**: `GET /pts2/devices/{deviceId}/pumps/{pumpId}`
- **描述**: 获取指定PTS2设备上指定油泵的当前状态信息

#### 12.2 授权油泵
- **接口**: `POST /pts2/devices/{deviceId}/pumps/{pumpId}/authorize`
- **描述**: 授权指定油泵进行加油操作
- **请求体**:
```json
{
  "nozzle_id": 1,
  "tag": "TAG123456"
}
```

#### 12.3 预设油泵
- **接口**: `POST /pts2/devices/{deviceId}/pumps/{pumpId}/preset`
- **描述**: 对指定油泵进行预设操作

#### 12.4 设置油泵价格
- **接口**: `POST /pts2/devices/{deviceId}/pumps/{pumpId}/prices`
- **描述**: 设置指定油泵的价格信息

#### 12.5 设置油枪价格
- **接口**: `POST /pts2/devices/{deviceId}/pumps/{pumpId}/nozzles/{nozzleId}/price`
- **描述**: 设置指定油枪的价格

### 13. 客户管理 (Customers)

#### 13.1 创建新客户
- **接口**: `POST /customers`
- **描述**: 创建新的客户档案

#### 13.2 获取客户列表
- **接口**: `GET /customers`
- **描述**: 分页获取客户列表，支持多种过滤条件
- **查询参数**:
  - `offset` (int): 偏移量，默认0
  - `limit` (int): 每页数量，默认10
  - `hos_id` (int): HOS系统ID
  - `name` (string): 客户名称
  - `phone` (string): 手机号
  - `email` (string): 邮箱
  - `order_by` (string): 排序字段
  - `order_desc` (bool): 是否降序，默认false

#### 13.3 统计客户数量
- **接口**: `GET /customers/count`
- **描述**: 根据过滤条件统计客户总数

#### 13.4 获取客户信息
- **接口**: `GET /customers/{id}`
- **描述**: 根据客户ID获取客户详细信息

#### 13.5 更新客户信息
- **接口**: `PUT /customers/{id}`
- **描述**: 更新指定客户的信息

#### 13.6 根据手机号获取客户
- **接口**: `GET /customers/phone/{phone}`
- **描述**: 根据手机号获取客户信息

#### 13.7 获取客户车辆列表
- **接口**: `GET /customers/{customer_id}/vehicles`
- **描述**: 根据客户ID获取其名下的车辆列表

#### 13.8 获取客户标签
- **接口**: `GET /customers/{customer_id}/labels`
- **描述**: 获取指定客户的所有标签

### 14. 车辆管理 (Vehicles)

#### 14.1 创建新车辆
- **接口**: `POST /vehicles`
- **描述**: 创建新的车辆记录

#### 14.2 获取车辆信息
- **接口**: `GET /vehicles/{id}`
- **描述**: 根据车辆ID获取车辆详细信息

#### 14.3 更新车辆信息
- **接口**: `PUT /vehicles/{id}`
- **描述**: 更新指定车辆的信息

#### 14.4 根据车牌号获取车辆
- **接口**: `GET /vehicles/number/{number}`
- **描述**: 根据车牌号获取车辆信息

### 15. 标签管理 (Labels)

#### 15.1 创建新标签
- **接口**: `POST /labels`
- **描述**: 创建新的客户标签

#### 15.2 获取标签列表
- **接口**: `GET /labels`
- **描述**: 获取所有可用的标签列表

#### 15.3 给客户分配标签
- **接口**: `POST /customer-labels`
- **描述**: 将指定标签分配给客户

### 16. 库存管理 (Inventory)

#### 16.1 商品管理

##### 16.1.1 创建商品
- **接口**: `POST /inventory/products`
- **描述**: 创建新的商品

##### 16.1.2 获取商品信息
- **接口**: `GET /inventory/products/{id}`
- **描述**: 根据ID获取商品详细信息

##### 16.1.3 获取商品列表
- **接口**: `GET /inventory/products`
- **描述**: 获取商品列表

##### 16.1.4 删除商品
- **接口**: `DELETE /inventory/products/{id}`
- **描述**: 删除指定商品

#### 16.2 库存查询

##### 16.2.1 获取库存列表
- **接口**: `GET /inventory/stocks`
- **描述**: 获取库存列表

##### 16.2.2 获取库存详情
- **接口**: `GET /inventory/stocks/{id}`
- **描述**: 获取指定库存的详细信息

#### 16.3 库存操作

##### 16.3.1 入库操作
- **接口**: `POST /inventory/inbound`
- **描述**: 执行商品入库操作

##### 16.3.2 出库操作
- **接口**: `POST /inventory/outbound`
- **描述**: 执行商品出库操作

#### 16.4 库存预留

##### 16.4.1 创建预留
- **接口**: `POST /inventory/reservations`
- **描述**: 创建库存预留

##### 16.4.2 释放预留
- **接口**: `POST /inventory/reservations/{id}/release`
- **描述**: 释放指定的库存预留

#### 16.5 盘点管理

##### 16.5.1 获取盘点列表
- **接口**: `GET /inventory/stocktakes`
- **描述**: 获取盘点记录列表

##### 16.5.2 获取盘点详情
- **接口**: `GET /inventory/stocktakes/{id}`
- **描述**: 获取指定盘点的详细信息

##### 16.5.3 开始盘点
- **接口**: `POST /inventory/stocktakes`
- **描述**: 开始新的盘点流程

##### 16.5.4 记录盘点项目
- **接口**: `POST /inventory/stocktakes/items/record`
- **描述**: 记录盘点项目数据

##### 16.5.5 完成盘点
- **接口**: `POST /inventory/stocktakes/{id}/complete`
- **描述**: 完成指定的盘点流程

#### 16.6 预警管理

##### 16.6.1 获取预警信息
- **接口**: `GET /inventory/warnings`
- **描述**: 获取库存预警信息

#### 16.7 分类管理

##### 16.7.1 创建分类
- **接口**: `POST /inventory/categories`
- **描述**: 创建新的商品分类

##### 16.7.2 获取分类列表
- **接口**: `GET /inventory/categories`
- **描述**: 获取商品分类列表

##### 16.7.3 获取分类详情
- **接口**: `GET /inventory/categories/{id}`
- **描述**: 获取指定分类的详细信息

### 17. 支付管理 (Payments)

#### 17.1 支付处理

##### 17.1.1 处理支付
- **接口**: `POST /payments`
- **描述**: 处理支付请求

##### 17.1.2 获取支付信息
- **接口**: `GET /payments/{id}`
- **描述**: 根据ID获取支付详细信息

##### 17.1.3 获取支付状态
- **接口**: `GET /payments/{id}/status`
- **描述**: 获取指定支付的状态

##### 17.1.4 取消支付
- **接口**: `POST /payments/{id}/cancel`
- **描述**: 取消指定的支付

##### 17.1.5 获取支付列表
- **接口**: `GET /payments`
- **描述**: 获取支付记录列表

#### 17.2 退款处理

##### 17.2.1 处理退款
- **接口**: `POST /payments/refunds`
- **描述**: 处理退款请求

##### 17.2.2 获取退款状态
- **接口**: `GET /payments/refunds/{id}/status`
- **描述**: 获取退款状态

#### 17.3 支付方式管理

##### 17.3.1 获取支付方式列表
- **接口**: `GET /payment-methods`
- **描述**: 获取所有可用的支付方式

##### 17.3.2 创建支付方式
- **接口**: `POST /payment-methods`
- **描述**: 创建新的支付方式

##### 17.3.3 更新支付方式
- **接口**: `PUT /payment-methods/{id}`
- **描述**: 更新指定的支付方式

##### 17.3.4 启用支付方式
- **接口**: `POST /payment-methods/{id}/enable`
- **描述**: 启用指定的支付方式

##### 17.3.5 禁用支付方式
- **接口**: `POST /payment-methods/{id}/disable`
- **描述**: 禁用指定的支付方式

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| `BAD_REQUEST` | 请求参数错误 |
| `VALIDATION_ERROR` | 参数验证失败 |
| `NOT_FOUND` | 资源不存在 |
| `CONFLICT` | 资源冲突 |
| `INTERNAL_SERVER_ERROR` | 服务器内部错误 |
| `UNAUTHORIZED` | 未授权访问 |
| `FORBIDDEN` | 禁止访问 |

## 数据类型说明

### 日期时间格式
- 日期格式: `2006-01-02`
- 日期时间格式: `2006-01-02T15:04:05Z`

### 分页参数
- `page`: 页码，从1开始
- `limit`: 每页数量，默认为10
- `sort_by`: 排序字段
- `sort_dir`: 排序方向，`asc`或`desc`

### 常用状态值

#### 燃油交易状态
- `pending`: 待处理
- `processed`: 已处理
- `cancelled`: 已取消

#### 订单状态
- `new`: 新建
- `processing`: 处理中
- `completed`: 已完成
- `cancelled`: 已取消

#### 支付状态
- `pending`: 待支付
- `completed`: 已完成
- `failed`: 支付失败
- `cancelled`: 已取消

## 注意事项

1. 所有时间参数都使用UTC时区
2. 金额字段使用浮点数，精确到小数点后2位
3. 分页查询默认按创建时间倒序排列
4. 删除操作通常为软删除，不会物理删除数据
5. 部分接口需要特定权限才能访问
6. 建议在生产环境中启用HTTPS
7. API响应可能包含额外的元数据字段用于调试和监控

## 版本信息

- API版本: v1
- 文档版本: 1.0
- 最后更新: 2024年 