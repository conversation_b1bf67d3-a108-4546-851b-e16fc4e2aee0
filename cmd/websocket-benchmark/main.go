package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"gitlab4.weicheche.cn/indo-bp/bos/internal/websocket"
)

func main() {
	var (
		configFile      = flag.String("config", "", "配置文件路径")
		serverURL       = flag.String("url", "ws://localhost:8080/ws", "WebSocket服务器URL")
		maxConnections  = flag.Int("connections", 100, "最大连接数")
		connectionRate  = flag.Int("conn-rate", 10, "每秒建立连接数")
		messageRate     = flag.Int("msg-rate", 100, "每秒发送消息数")
		testDuration    = flag.Duration("duration", 60*time.Second, "测试持续时间")
		messageSize     = flag.Int("msg-size", 1024, "消息大小（字节）")
		authToken       = flag.String("token", "test-token", "认证Token")
		outputFile      = flag.String("output", "", "结果输出文件")
		verbose         = flag.Bool("verbose", false, "详细输出")
	)
	
	flag.Parse()
	
	var config *websocket.BenchmarkConfig
	
	// 从配置文件加载配置
	if *configFile != "" {
		var err error
		config, err = loadConfigFromFile(*configFile)
		if err != nil {
			log.Fatalf("加载配置文件失败: %v", err)
		}
	} else {
		// 使用命令行参数创建配置
		config = &websocket.BenchmarkConfig{
			MaxConnections:  *maxConnections,
			ConnectionRate:  *connectionRate,
			MessageRate:     *messageRate,
			TestDuration:    *testDuration,
			MessageSize:     *messageSize,
			MessageTypes: []websocket.MessageType{
				websocket.MessageTypeNozzleStatus,
				websocket.MessageTypeTransactionUpdate,
				websocket.MessageTypeDashboardUpdate,
			},
			ServerURL:       *serverURL,
			AuthToken:       *authToken,
			EnableHeartbeat: true,
			EnableReconnect: false,
			CollectMetrics:  true,
		}
	}
	
	if *verbose {
		fmt.Println("=== 性能测试配置 ===")
		printConfig(config)
		fmt.Println()
	}
	
	// 创建性能测试器
	benchmark := websocket.NewWebSocketBenchmark(config)
	
	// 运行测试
	fmt.Println("开始WebSocket性能测试...")
	result, err := benchmark.Run()
	if err != nil {
		log.Fatalf("性能测试失败: %v", err)
	}
	
	// 打印结果
	result.PrintResults()
	
	// 保存结果到文件
	if *outputFile != "" {
		if err := saveResultToFile(result, *outputFile); err != nil {
			log.Printf("保存结果文件失败: %v", err)
		} else {
			fmt.Printf("结果已保存到: %s\n", *outputFile)
		}
	}
	
	// 生成性能报告
	generatePerformanceReport(result)
}

// loadConfigFromFile 从文件加载配置
func loadConfigFromFile(filename string) (*websocket.BenchmarkConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	
	var config websocket.BenchmarkConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, err
	}
	
	return &config, nil
}

// saveResultToFile 保存结果到文件
func saveResultToFile(result *websocket.BenchmarkResult, filename string) error {
	data, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return err
	}
	
	return os.WriteFile(filename, data, 0644)
}

// printConfig 打印配置信息
func printConfig(config *websocket.BenchmarkConfig) {
	fmt.Printf("服务器URL: %s\n", config.ServerURL)
	fmt.Printf("最大连接数: %d\n", config.MaxConnections)
	fmt.Printf("连接建立速率: %d/秒\n", config.ConnectionRate)
	fmt.Printf("消息发送速率: %d/秒\n", config.MessageRate)
	fmt.Printf("测试持续时间: %v\n", config.TestDuration)
	fmt.Printf("消息大小: %d 字节\n", config.MessageSize)
	fmt.Printf("认证Token: %s\n", config.AuthToken)
	fmt.Printf("启用心跳: %v\n", config.EnableHeartbeat)
	fmt.Printf("启用重连: %v\n", config.EnableReconnect)
	fmt.Printf("收集指标: %v\n", config.CollectMetrics)
}

// generatePerformanceReport 生成性能报告
func generatePerformanceReport(result *websocket.BenchmarkResult) {
	fmt.Println("\n=== 性能分析报告 ===")
	
	// 连接性能分析
	connectionSuccessRate := float64(result.SuccessfulConnections) / float64(result.TotalConnections) * 100
	fmt.Printf("连接性能: ")
	if connectionSuccessRate >= 95 {
		fmt.Printf("优秀 (%.1f%%)\n", connectionSuccessRate)
	} else if connectionSuccessRate >= 90 {
		fmt.Printf("良好 (%.1f%%)\n", connectionSuccessRate)
	} else if connectionSuccessRate >= 80 {
		fmt.Printf("一般 (%.1f%%)\n", connectionSuccessRate)
	} else {
		fmt.Printf("较差 (%.1f%%)\n", connectionSuccessRate)
	}
	
	// 消息传输性能分析
	messageLossRate := float64(result.MessagesLost) / float64(result.MessagesSent) * 100
	fmt.Printf("消息传输: ")
	if messageLossRate <= 1 {
		fmt.Printf("优秀 (丢失率: %.2f%%)\n", messageLossRate)
	} else if messageLossRate <= 5 {
		fmt.Printf("良好 (丢失率: %.2f%%)\n", messageLossRate)
	} else if messageLossRate <= 10 {
		fmt.Printf("一般 (丢失率: %.2f%%)\n", messageLossRate)
	} else {
		fmt.Printf("较差 (丢失率: %.2f%%)\n", messageLossRate)
	}
	
	// 延迟性能分析
	fmt.Printf("延迟性能: ")
	if result.P95Latency <= 10*time.Millisecond {
		fmt.Printf("优秀 (P95: %v)\n", result.P95Latency)
	} else if result.P95Latency <= 50*time.Millisecond {
		fmt.Printf("良好 (P95: %v)\n", result.P95Latency)
	} else if result.P95Latency <= 100*time.Millisecond {
		fmt.Printf("一般 (P95: %v)\n", result.P95Latency)
	} else {
		fmt.Printf("较差 (P95: %v)\n", result.P95Latency)
	}
	
	// 吞吐量性能分析
	fmt.Printf("吞吐量性能: ")
	if result.MessagesPerSecond >= 1000 {
		fmt.Printf("优秀 (%.0f msg/s)\n", result.MessagesPerSecond)
	} else if result.MessagesPerSecond >= 500 {
		fmt.Printf("良好 (%.0f msg/s)\n", result.MessagesPerSecond)
	} else if result.MessagesPerSecond >= 100 {
		fmt.Printf("一般 (%.0f msg/s)\n", result.MessagesPerSecond)
	} else {
		fmt.Printf("较差 (%.0f msg/s)\n", result.MessagesPerSecond)
	}
	
	// 总体评分
	score := calculateOverallScore(result)
	fmt.Printf("\n总体评分: %.1f/100\n", score)
	
	// 性能建议
	generatePerformanceRecommendations(result)
}

// calculateOverallScore 计算总体评分
func calculateOverallScore(result *websocket.BenchmarkResult) float64 {
	score := 0.0
	
	// 连接成功率 (25分)
	connectionSuccessRate := float64(result.SuccessfulConnections) / float64(result.TotalConnections)
	score += connectionSuccessRate * 25
	
	// 消息传输成功率 (25分)
	messageSuccessRate := float64(result.MessagesReceived) / float64(result.MessagesSent)
	score += messageSuccessRate * 25
	
	// 延迟性能 (25分)
	if result.P95Latency <= 10*time.Millisecond {
		score += 25
	} else if result.P95Latency <= 50*time.Millisecond {
		score += 20
	} else if result.P95Latency <= 100*time.Millisecond {
		score += 15
	} else if result.P95Latency <= 200*time.Millisecond {
		score += 10
	} else {
		score += 5
	}
	
	// 吞吐量性能 (25分)
	if result.MessagesPerSecond >= 1000 {
		score += 25
	} else if result.MessagesPerSecond >= 500 {
		score += 20
	} else if result.MessagesPerSecond >= 100 {
		score += 15
	} else if result.MessagesPerSecond >= 50 {
		score += 10
	} else {
		score += 5
	}
	
	return score
}

// generatePerformanceRecommendations 生成性能建议
func generatePerformanceRecommendations(result *websocket.BenchmarkResult) {
	fmt.Println("\n=== 性能优化建议 ===")
	
	recommendations := []string{}
	
	// 连接相关建议
	connectionSuccessRate := float64(result.SuccessfulConnections) / float64(result.TotalConnections) * 100
	if connectionSuccessRate < 95 {
		recommendations = append(recommendations, "• 连接成功率较低，建议检查服务器连接池配置和网络状况")
	}
	
	// 消息传输相关建议
	messageLossRate := float64(result.MessagesLost) / float64(result.MessagesSent) * 100
	if messageLossRate > 5 {
		recommendations = append(recommendations, "• 消息丢失率较高，建议增加消息缓冲区大小或优化消息处理逻辑")
	}
	
	// 延迟相关建议
	if result.P95Latency > 100*time.Millisecond {
		recommendations = append(recommendations, "• 延迟较高，建议优化消息处理逻辑或增加服务器资源")
	}
	
	// 吞吐量相关建议
	if result.MessagesPerSecond < 100 {
		recommendations = append(recommendations, "• 吞吐量较低，建议优化消息序列化/反序列化或使用消息批处理")
	}
	
	// 错误相关建议
	if result.ConnectionErrors > 0 {
		recommendations = append(recommendations, "• 存在连接错误，建议检查网络配置和服务器负载")
	}
	
	if result.MessageErrors > 0 {
		recommendations = append(recommendations, "• 存在消息错误，建议检查消息格式和处理逻辑")
	}
	
	if len(recommendations) == 0 {
		fmt.Println("性能表现良好，无需特别优化。")
	} else {
		for _, rec := range recommendations {
			fmt.Println(rec)
		}
	}
	
	fmt.Println("\n=== 与轮询方式对比 ===")
	
	// 假设原来的轮询频率
	pollingInterval := 2 * time.Second // FCC喷嘴状态轮询间隔
	pollingRequestsPerSecond := 1.0 / pollingInterval.Seconds()
	
	// 计算性能提升
	wsRequestsPerSecond := result.MessagesPerSecond
	performanceImprovement := (wsRequestsPerSecond - pollingRequestsPerSecond) / pollingRequestsPerSecond * 100
	
	fmt.Printf("轮询方式请求频率: %.2f 请求/秒\n", pollingRequestsPerSecond)
	fmt.Printf("WebSocket消息频率: %.2f 消息/秒\n", wsRequestsPerSecond)
	fmt.Printf("性能提升: %.1f%%\n", performanceImprovement)
	
	// 计算资源节省
	fmt.Printf("HTTP请求开销节省: ~90%% (无需重复建立连接)\n")
	fmt.Printf("实时性提升: 从 %v 延迟到 %v 延迟\n", pollingInterval, result.AvgLatency)
}
