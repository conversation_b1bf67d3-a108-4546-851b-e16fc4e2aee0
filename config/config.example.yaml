# BOS主服务配置模板
# 复制此文件为 config.yaml 并根据实际环境修改配置值

# 调试模式配置
debug: true
log_level: debug

# 服务器配置
server:
  # 服务器监听端口 (环境变量: BOS_SERVER_PORT)
  port: 8080
  # 运行环境: development, testing, production (环境变量: BOS_ENVIRONMENT)
  environment: development
  # BOS模式下的默认站点ID (环境变量: BOS_DEFAULT_STATION_ID)
  default_station_id: 1

# 认证服务配置
auth:
  # JWT密钥，生产环境必须使用强密钥 (环境变量: BOS_JWT_SECRET)
  jwt_secret: "your-jwt-secret-key-here-change-in-production"
  # JWT过期时间（小时） (环境变量: BOS_JWT_EXPIRATION_HOURS)
  jwt_expiration_hours: 24
  # 刷新令牌过期时间（天） (环境变量: BOS_REFRESH_EXPIRATION_DAYS)
  refresh_expiration_days: 7

# 促销服务配置
promotion:
  # 促销服务主机地址 (环境变量: BOS_PROMOTION_HOST)
  host: localhost
  # 促销服务端口 (环境变量: BOS_PROMOTION_PORT)
  port: 8080

# 统一数据库配置
bos_db:
  # 数据库主机地址 (环境变量: BOS_DB_HOST)
  host: localhost
  # 数据库端口 (环境变量: BOS_DB_PORT)
  port: 5432
  # 数据库用户名 (环境变量: BOS_DB_USER)
  user: postgres
  # 数据库密码 (环境变量: BOS_DB_PASSWORD)
  password: your-database-password
  # 数据库名称 (环境变量: BOS_DB_NAME)
  dbname: bos_db
  # SSL模式: disable, require, verify-ca, verify-full (环境变量: BOS_DB_SSLMODE)
  sslmode: disable

# 各服务的schema配置
schemas:
  # 认证服务schema (环境变量: BOS_AUTH_SCHEMA)
  auth_schema: core_schema
  # 订单服务schema (环境变量: BOS_ORDER_SCHEMA)
  order_schema: order_schema
  # 促销服务schema (环境变量: BOS_PROMOTION_SCHEMA)
  promotion_schema: promotion_schema
  # 油品服务schema (环境变量: BOS_OIL_SCHEMA)
  oil_schema: oil_schema
  # 客户服务schema (环境变量: BOS_CUSTOMER_SCHEMA)
  customer_schema: customer_schema
  # 库存服务schema (环境变量: BOS_INVENTORY_SCHEMA)
  inventory_schema: inventory_schema
  # 支付服务schema (环境变量: BOS_PAYMENT_SCHEMA)
  payment_schema: payment_schema
  # FCC服务schema (环境变量: BOS_FCC_SCHEMA)
  fcc_schema: fcc_schema
  # 采购管理schema (环境变量: BOS_PROCUREMENT_SCHEMA)
  procurement_schema: procurement_schema
  # 设备管理schema (环境变量: BOS_EQUIPMENT_SCHEMA)
  equipment_schema: equipment_schema

# 日志配置
log:
  # 日志级别: debug, info, warn, error (环境变量: BOS_LOG_LEVEL)
  level: info
  # 日志格式: json, text (环境变量: BOS_LOG_FORMAT)
  format: json
  # 日志输出: stdout, stderr, 或文件路径 (环境变量: BOS_LOG_OUTPUT)
  output: stdout

# Redis配置（如果使用）
redis:
  # Redis服务器地址 (环境变量: BOS_REDIS_ADDR)
  addr: localhost:6379
  # Redis密码 (环境变量: BOS_REDIS_PASSWORD)
  password: ""
  # Redis数据库编号 (环境变量: BOS_REDIS_DB)
  db: 0
  # 连接池大小 (环境变量: BOS_REDIS_POOL_SIZE)
  pool_size: 10
  # 最小空闲连接数 (环境变量: BOS_REDIS_MIN_IDLE_CONNS)
  min_idle_conns: 2
  # 拨号超时时间 (环境变量: BOS_REDIS_DIAL_TIMEOUT)
  dial_timeout: 5s
  # 读取超时时间 (环境变量: BOS_REDIS_READ_TIMEOUT)
  read_timeout: 3s
  # 写入超时时间 (环境变量: BOS_REDIS_WRITE_TIMEOUT)
  write_timeout: 3s
