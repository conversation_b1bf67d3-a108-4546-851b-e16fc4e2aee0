package config

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// Config 应用程序配置
type Config struct {
	Server    ServerConfig    `yaml:"server"`
	BosDB     DatabaseConfig  `yaml:"bos_db"`  // 统一数据库配置
	Schemas   SchemasConfig   `yaml:"schemas"` // 各服务的schema配置
	Log       LogConfig       `yaml:"log"`
	Auth      AuthConfig      `yaml:"auth"`      // 认证服务配置
	Promotion PromotionConfig `yaml:"promotion"` // 促销服务配置
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port             int    `yaml:"port"`
	Environment      string `yaml:"environment"`
	DefaultStationID int64  `yaml:"default_station_id"` // BOS模式下的默认站点ID
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	DBName   string `yaml:"dbname"`
	SSLMode  string `yaml:"sslmode"`
}

// SchemasConfig 各服务的schema配置
type SchemasConfig struct {
	AuthSchema        string `yaml:"auth_schema"`
	OrderSchema       string `yaml:"order_schema"`
	PromotionSchema   string `yaml:"promotion_schema"`
	OilSchema         string `yaml:"oil_schema"`
	CustomerSchema    string `yaml:"customer_schema"`
	InventorySchema   string `yaml:"inventory_schema"`
	PaymentSchema     string `yaml:"payment_schema"`
	FCCSchema         string `yaml:"fcc_schema"`
	ProcurementSchema string `yaml:"procurement_schema"`
	EquipmentSchema   string `yaml:"equipment_schema"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
	Output string `yaml:"output"`
}

// AuthConfig 认证服务配置
type AuthConfig struct {
	JWTSecret             string `yaml:"jwt_secret"`
	JWTExpirationHours    int    `yaml:"jwt_expiration_hours"`
	RefreshExpirationDays int    `yaml:"refresh_expiration_days"`
}

// PromotionConfig 促销服务配置
type PromotionConfig struct {
	Host string `yaml:"host"` // 促销服务主机地址
	Port int    `yaml:"port"` // 促销服务端口
}

// LoadConfig 加载配置
func LoadConfig(configPath string) (*Config, error) {
	// 检查配置文件是否存在
	if configPath == "" {
		return nil, fmt.Errorf("配置文件路径不能为空，请提供配置文件路径")
	}

	absPath, err := filepath.Abs(configPath)
	if err != nil {
		return nil, fmt.Errorf("无法获取配置文件的绝对路径: %w", err)
	}

	if _, err := os.Stat(absPath); err != nil {
		return nil, fmt.Errorf("配置文件不存在: %s，请复制 config.example.yaml 为 config.yaml 并配置相应参数", absPath)
	}

	// 初始化空配置
	config := &Config{}

	// 从配置文件加载
	data, err := os.ReadFile(absPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件错误: %w", err)
	}

	if err := yaml.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("解析YAML配置文件错误: %w", err)
	}

	// 如果指定了配置文件路径，从文件加载
	if configPath != "" {
		absPath, err := filepath.Abs(configPath)
		if err != nil {
			return nil, fmt.Errorf("无法获取配置文件的绝对路径: %w", err)
		}

		if _, err := os.Stat(absPath); err == nil {
			data, err := os.ReadFile(absPath)
			if err != nil {
				return nil, fmt.Errorf("读取配置文件错误: %w", err)
			}

			if err := yaml.Unmarshal(data, config); err != nil {
				return nil, fmt.Errorf("解析YAML配置文件错误: %w", err)
			}
		} else {
			return nil, fmt.Errorf("配置文件不存在: %s", absPath)
		}
	}

	// 从环境变量覆盖配置（使用统一的命名规范）
	overrideFromEnv(config)

	// 验证配置
	if err := validateConfig(config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return config, nil
}

// GetDSN 获取数据库连接字符串（默认使用public schema）
func (c *Config) GetDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s search_path=public",
		c.BosDB.Host, c.BosDB.Port, c.BosDB.User, c.BosDB.Password, c.BosDB.DBName, c.BosDB.SSLMode)
}

// GetAuthDSN 获取Auth数据库连接字符串
func (c *Config) GetAuthDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s search_path=%s",
		c.BosDB.Host, c.BosDB.Port, c.BosDB.User, c.BosDB.Password, c.BosDB.DBName, c.BosDB.SSLMode, c.Schemas.AuthSchema)
}

// GetOrderDSN 获取订单服务数据库连接字符串
func (c *Config) GetOrderDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s search_path=%s",
		c.BosDB.Host, c.BosDB.Port, c.BosDB.User, c.BosDB.Password, c.BosDB.DBName, c.BosDB.SSLMode, c.Schemas.OrderSchema)
}

// GetPromotionDSN 获取促销服务数据库连接字符串
func (c *Config) GetPromotionDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s search_path=%s",
		c.BosDB.Host, c.BosDB.Port, c.BosDB.User, c.BosDB.Password, c.BosDB.DBName, c.BosDB.SSLMode, c.Schemas.PromotionSchema)
}

// GetOilDSN 获取油品服务数据库连接字符串
func (c *Config) GetOilDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s search_path=%s",
		c.BosDB.Host, c.BosDB.Port, c.BosDB.User, c.BosDB.Password, c.BosDB.DBName, c.BosDB.SSLMode, c.Schemas.OilSchema)
}

// GetCustomerDSN 获取客户服务数据库连接字符串
func (c *Config) GetCustomerDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s search_path=%s",
		c.BosDB.Host, c.BosDB.Port, c.BosDB.User, c.BosDB.Password, c.BosDB.DBName, c.BosDB.SSLMode, c.Schemas.CustomerSchema)
}

// GetInventoryDSN 获取库存服务数据库连接字符串
func (c *Config) GetInventoryDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s search_path=%s",
		c.BosDB.Host, c.BosDB.Port, c.BosDB.User, c.BosDB.Password, c.BosDB.DBName, c.BosDB.SSLMode, c.Schemas.InventorySchema)
}

// GetPaymentDSN 获取支付服务数据库连接字符串
func (c *Config) GetPaymentDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s search_path=%s",
		c.BosDB.Host, c.BosDB.Port, c.BosDB.User, c.BosDB.Password, c.BosDB.DBName, c.BosDB.SSLMode, c.Schemas.PaymentSchema)
}

// GetFCCDSN 获取FCC数据库连接字符串
func (c *Config) GetFCCDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s search_path=%s",
		c.BosDB.Host, c.BosDB.Port, c.BosDB.User, c.BosDB.Password, c.BosDB.DBName, c.BosDB.SSLMode, c.Schemas.FCCSchema)
}

// GetPromotionCalculatorURL 获取促销计算服务的完整URL
func (c *Config) GetPromotionCalculatorURL() string {
	return fmt.Sprintf("http://%s:%d/api/v1/calculator/process", c.Promotion.Host, c.Promotion.Port)
}

// overrideFromEnv 从环境变量覆盖配置
func overrideFromEnv(config *Config) {
	// 服务器配置
	if port := os.Getenv("BOS_SERVER_PORT"); port != "" {
		fmt.Sscanf(port, "%d", &config.Server.Port)
	}
	if env := os.Getenv("BOS_ENVIRONMENT"); env != "" {
		config.Server.Environment = env
	}
	if stationID := os.Getenv("BOS_DEFAULT_STATION_ID"); stationID != "" {
		fmt.Sscanf(stationID, "%d", &config.Server.DefaultStationID)
	}

	// 数据库配置
	if host := os.Getenv("BOS_DB_HOST"); host != "" {
		config.BosDB.Host = host
	}
	if port := os.Getenv("BOS_DB_PORT"); port != "" {
		fmt.Sscanf(port, "%d", &config.BosDB.Port)
	}
	if user := os.Getenv("BOS_DB_USER"); user != "" {
		config.BosDB.User = user
	}
	if password := os.Getenv("BOS_DB_PASSWORD"); password != "" {
		config.BosDB.Password = password
	}
	if dbname := os.Getenv("BOS_DB_NAME"); dbname != "" {
		config.BosDB.DBName = dbname
	}
	if sslmode := os.Getenv("BOS_DB_SSLMODE"); sslmode != "" {
		config.BosDB.SSLMode = sslmode
	}

	// JWT配置
	if secret := os.Getenv("BOS_JWT_SECRET"); secret != "" {
		config.Auth.JWTSecret = secret
	}
	if expiration := os.Getenv("BOS_JWT_EXPIRATION_HOURS"); expiration != "" {
		fmt.Sscanf(expiration, "%d", &config.Auth.JWTExpirationHours)
	}
	if refreshExpiration := os.Getenv("BOS_REFRESH_EXPIRATION_DAYS"); refreshExpiration != "" {
		fmt.Sscanf(refreshExpiration, "%d", &config.Auth.RefreshExpirationDays)
	}

	// 促销服务配置
	if host := os.Getenv("BOS_PROMOTION_HOST"); host != "" {
		config.Promotion.Host = host
	}
	if port := os.Getenv("BOS_PROMOTION_PORT"); port != "" {
		fmt.Sscanf(port, "%d", &config.Promotion.Port)
	}

	// Schema配置
	if schema := os.Getenv("BOS_AUTH_SCHEMA"); schema != "" {
		config.Schemas.AuthSchema = schema
	}
	if schema := os.Getenv("BOS_ORDER_SCHEMA"); schema != "" {
		config.Schemas.OrderSchema = schema
	}
	if schema := os.Getenv("BOS_PROMOTION_SCHEMA"); schema != "" {
		config.Schemas.PromotionSchema = schema
	}
	if schema := os.Getenv("BOS_OIL_SCHEMA"); schema != "" {
		config.Schemas.OilSchema = schema
	}
	if schema := os.Getenv("BOS_CUSTOMER_SCHEMA"); schema != "" {
		config.Schemas.CustomerSchema = schema
	}
	if schema := os.Getenv("BOS_INVENTORY_SCHEMA"); schema != "" {
		config.Schemas.InventorySchema = schema
	}
	if schema := os.Getenv("BOS_PAYMENT_SCHEMA"); schema != "" {
		config.Schemas.PaymentSchema = schema
	}
	if schema := os.Getenv("BOS_FCC_SCHEMA"); schema != "" {
		config.Schemas.FCCSchema = schema
	}
	if schema := os.Getenv("BOS_PROCUREMENT_SCHEMA"); schema != "" {
		config.Schemas.ProcurementSchema = schema
	}
	if schema := os.Getenv("BOS_EQUIPMENT_SCHEMA"); schema != "" {
		config.Schemas.EquipmentSchema = schema
	}

	// 日志配置
	if level := os.Getenv("BOS_LOG_LEVEL"); level != "" {
		config.Log.Level = level
	}
	if format := os.Getenv("BOS_LOG_FORMAT"); format != "" {
		config.Log.Format = format
	}
	if output := os.Getenv("BOS_LOG_OUTPUT"); output != "" {
		config.Log.Output = output
	}
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	// 验证服务器配置
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("服务器端口必须在1-65535范围内，当前值: %d", config.Server.Port)
	}

	validEnvs := []string{"development", "testing", "production"}
	if !contains(validEnvs, config.Server.Environment) {
		return fmt.Errorf("环境配置必须是 %v 之一，当前值: %s", validEnvs, config.Server.Environment)
	}

	// 验证数据库配置
	if config.BosDB.Host == "" {
		return fmt.Errorf("数据库主机地址不能为空")
	}
	if config.BosDB.Port <= 0 || config.BosDB.Port > 65535 {
		return fmt.Errorf("数据库端口必须在1-65535范围内，当前值: %d", config.BosDB.Port)
	}
	if config.BosDB.User == "" {
		return fmt.Errorf("数据库用户名不能为空")
	}
	if config.BosDB.Password == "" {
		return fmt.Errorf("数据库密码不能为空")
	}
	if config.BosDB.DBName == "" {
		return fmt.Errorf("数据库名称不能为空")
	}

	validSSLModes := []string{"disable", "require", "verify-ca", "verify-full"}
	if !contains(validSSLModes, config.BosDB.SSLMode) {
		return fmt.Errorf("SSL模式必须是 %v 之一，当前值: %s", validSSLModes, config.BosDB.SSLMode)
	}

	// 验证JWT配置
	if config.Auth.JWTSecret == "" || config.Auth.JWTSecret == "your-jwt-secret-key-here" || config.Auth.JWTSecret == "your-jwt-secret-key-here-change-in-production" {
		return fmt.Errorf("JWT密钥不能为空或使用默认值，请设置安全的密钥")
	}
	if len(config.Auth.JWTSecret) < 32 {
		return fmt.Errorf("JWT密钥长度至少32个字符，当前长度: %d", len(config.Auth.JWTSecret))
	}
	if config.Auth.JWTExpirationHours <= 0 {
		return fmt.Errorf("JWT过期时间必须大于0小时，当前值: %d", config.Auth.JWTExpirationHours)
	}
	if config.Auth.RefreshExpirationDays <= 0 {
		return fmt.Errorf("刷新令牌过期时间必须大于0天，当前值: %d", config.Auth.RefreshExpirationDays)
	}

	// 验证Schema配置
	if config.Schemas.AuthSchema == "" {
		return fmt.Errorf("认证服务schema不能为空")
	}
	if config.Schemas.OrderSchema == "" {
		return fmt.Errorf("订单服务schema不能为空")
	}

	// 验证促销服务配置
	if config.Promotion.Host == "" {
		return fmt.Errorf("促销服务主机地址不能为空")
	}
	if config.Promotion.Port <= 0 || config.Promotion.Port > 65535 {
		return fmt.Errorf("促销服务端口必须在1-65535范围内，当前值: %d", config.Promotion.Port)
	}

	// 验证日志配置
	validLogLevels := []string{"debug", "info", "warn", "error"}
	if !contains(validLogLevels, config.Log.Level) {
		return fmt.Errorf("日志级别必须是 %v 之一，当前值: %s", validLogLevels, config.Log.Level)
	}

	validLogFormats := []string{"json", "text"}
	if !contains(validLogFormats, config.Log.Format) {
		return fmt.Errorf("日志格式必须是 %v 之一，当前值: %s", validLogFormats, config.Log.Format)
	}

	return nil
}

// contains 检查字符串切片是否包含指定值
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// GetSchemaForService 获取指定服务的schema名称
func (c *Config) GetSchemaForService(service string) string {
	switch service {
	case "auth":
		return c.Schemas.AuthSchema
	case "order":
		return c.Schemas.OrderSchema
	case "promotion":
		return c.Schemas.PromotionSchema
	case "oil":
		return c.Schemas.OilSchema
	case "customer":
		return c.Schemas.CustomerSchema
	case "inventory":
		return c.Schemas.InventorySchema
	case "payment":
		return c.Schemas.PaymentSchema
	case "fcc":
		return c.Schemas.FCCSchema
	default:
		return "public"
	}
}
