debug: true
log_level: debug
server:
  port: 8080
  environment: development

# 认证服务配置
auth:
  jwt_secret: "your-jwt-secret-key-here"
  jwt_expiration_hours: 24
  refresh_expiration_days: 7

auth_db:
  host: localhost
  port: 5432
  user: postgres
  password: zB3!lL9+wJ
  dbname: core_service
  sslmode: disable

fcc_db:
  host: localhost
  port: 5432
  username: postgres
  password: zB3!lL9+wJ
  database: pts_devices
  sslmode: disable

order_db:
  host: localhost
  port: 5432
  user: postgres
  password: zB3!lL9+wJ
  dbname: order_service
  sslmode: disable

log:
  level: debug
  format: json
  output: stdout

promotion_db:
  host: localhost
  port: 5432
  user: postgres
  password: zB3!lL9+wJ
  dbname: promotion_service
  sslmode: disable

oil_db:
  host: localhost
  port: 5432
  user: postgres
  password: zB3!lL9+wJ
  dbname: oil_service
  sslmode: disable

customer_db:
  host: localhost
  port: 5432
  user: postgres
  password: zB3!lL9+wJ
  dbname: customer_service
  sslmode: disable

inventory_db:
  host: localhost
  port: 5432
  user: postgres
  password: zB3!lL9+wJ
  dbname: inventory_service
  sslmode: disable

payment_db:
  host: localhost
  port: 5432
  user: postgres
  password: zB3!lL9+wJ
  dbname: payment_service
  sslmode: disable