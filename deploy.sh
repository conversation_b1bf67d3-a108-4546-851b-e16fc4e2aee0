#!/bin/bash

APP_NAME="hos-release"
SERVER_USER="root"
SERVER_IP="*************"
REMOTE_PATH="/home/<USER>/$APP_NAME"

echo "正在编译应用..."
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o $APP_NAME .

if [ $? -ne 0 ]; then
    echo "编译失败!"
    exit 1
fi

echo "正在上传到服务器..."
#scp $APP_NAME $SERVER_USER@$SERVER_IP:~/

#echo "正在部署..."
#ssh $SERVER_USER@$SERVER_IP << EOF
#    sudo systemctl stop $APP_NAME || true
#    sudo mv ~/$APP_NAME $REMOTE_PATH/
#    sudo chmod +x $REMOTE_PATH/$APP_NAME
#    sudo systemctl start $APP_NAME
#    sudo systemctl status $APP_NAME
#EOF

echo "部署完成!"