# 完整加油交易流程和关键接口文档

## 文档概述

本文档详细描述了BP加油站管理系统的完整线下加油交易流程，包含每个步骤的业务逻辑和对应的关键API接口。该流程确保交易的安全性、准确性和用户体验。

## 系统架构

该系统采用微服务架构，主要包含以下服务：
- **BOS (Back Office System)**: 后台办公系统
- **Member Service**: 会员管理服务  
- **Oil Service**: 油品管理服务（库存、定价、调价）
- **Order Service**: 订单管理服务
- **Promotion Service**: 促销管理服务
- **PTS2-GO**: 油机控制系统接口
- **Shift Service**: 班次管理服务

---

## 完整加油交易流程

### 流程概述

加油交易流程可以分为两个主要部分：

1. **第一部分：燃油交易记录创建流程**
   - 从员工登录到完成加油，创建燃油交易记录
   - 包含油机操作、加油过程、基础数据记录
   - 主要涉及：BOS、PTS2-GO、Oil Service

2. **第二部分：收银结算流程**  
   - 从EDC选择燃油交易记录开始的收银结算处理
   - 包含交易选择、会员处理、促销计算、订单创建、支付处理
   - 主要涉及：BOS、Member Service、Promotion Service、Order Service

---

## 第一部分：燃油交易记录创建流程

### 阶段一：预处理与加油准备

#### 1. 员工登录验证

**业务流程：**
- 员工使用工号和密码登录POS终端
- 系统验证员工身份和权限
- 记录员工登录状态和操作记录

**关键接口：**

```http
POST /api/v1/employee/login
Content-Type: application/json

{
  "employee_no": "EMP001",
  "password": "password123"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "employee": {
      "id": 1,
      "employee_no": "EMP001",
      "name": "张三",
      "created_at": "2025-04-16T10:30:00Z",
      "updated_at": "2025-04-16T10:30:00Z"
    }
  }
}
```

#### 2. 油机选择与状态检查

**业务流程：**
```
POS终端操作：
- 选择油泵号和油枪
- 系统检查油枪状态（调用 pts2-go 或本地状态管理）

油枪状态判断：
- 检查是否存在未完成的燃油交易
- 如果有未完成交易：
  * 显示"该油枪正在使用中，请选择其他油枪"
  * 返回油枪选择界面
  * 或提示处理未完成交易
- 如果油枪空闲：
  * 继续后续流程
```

**关键接口：**

```http
GET /api/v1/fuel-transactions?pump_id=P01&status=pending
```

**检查未完成交易响应：**
```json
{
  "items": [
    {
      "id": 123,
      "pump_id": "P01",
      "status": "pending",
      "created_at": "2025-04-10T10:25:00Z"
    }
  ],
  "total": 1
}
```

#### 3. 加油员EDC刷卡认证

**业务流程：**
```
安全认证步骤：
- 加油员在EDC设备上刷自己的员工卡
- 系统验证加油员身份和加油权限
- 记录操作员信息（用于后续交易追溯）
- 验证通过后才能进行油机授权
```

**关键接口：**
```http
POST /api/v1/employee/verify-card
Content-Type: application/json

{
  "card_id": "CARD001",
  "pump_id": "P01"
}
```

#### 4. 油品选择与油机授权

**业务流程：**
```
油品配置：
- 选择油品类型（92#、95#、柴油等）
- 获取当前油价（从 oil-service）
- 向油机发送启动授权（通过 pts2-go）

灵活加油方式：
- 可选择预设金额/油量（如客户有明确需求）
- 或者不预设，允许客户自由控制加油量
- 油机启动，等待客户加油
```

**关键接口：**

获取油品价格：
```http
GET /api/v1/oil/products/pricing?fuel_type=gasoline&fuel_grade=92
```

油机授权：
```http
POST /api/v1/pts2/pump/authorize
Content-Type: application/json

{
  "pump_id": "P01",
  "nozzle_id": "N01",
  "fuel_type": "gasoline",
  "fuel_grade": "92",
  "preset_amount": 200.00,  // 可选
  "preset_volume": 30.0,    // 可选
  "employee_id": 2001
}
```

### 阶段二：加油过程

#### 5. 实际加油操作

**业务流程：**
```
加油过程监控：
- 油机开始出油，实时监控加油数据
- 系统记录：油品类型、单价、升数、金额
- 客户可随时停止加油（提枪或达到预设值）
- 油机自动停止，返回最终加油数据
```

**关键接口：**

实时监控加油数据：
```http
GET /api/v1/pts2/pump/P01/status
```

**响应示例：**
```json
{
  "pump_id": "P01",
  "nozzle_id": "N01",
  "status": "dispensing",
  "current_volume": 25.5,
  "current_amount": 184.36,
  "unit_price": 7.23,
  "fuel_type": "gasoline",
  "fuel_grade": "92"
}
```

#### 6. 加油完成确认

**业务流程：**
```
数据确认：
- 获取最终加油数据：升数、单价、总金额
- 验证数据完整性和准确性
- 客户挂回油枪，自动释放油枪锁定状态
```

**关键接口：**

获取最终加油数据：
```http
GET /api/v1/pts2/pump/P01/transaction/complete
```

创建燃油交易记录：
```http
POST /api/v1/fuel-transactions
Content-Type: application/json

{
  "transaction_number": "FT-20250410-002",
  "station_id": 101,
  "pump_id": "P01",
  "nozzle_id": "N01",
  "fuel_type": "汽油",
  "fuel_grade": "92#",
  "unit_price": 7.23,
  "volume": 50.5,
  "amount": 365.12,
  "total_volume": 50.5,
  "total_amount": 365.12,
  "employee_id": 2001,
  "status": "processed"
}
```

---

## 第二部分：收银结算流程

### 阶段三：交易选择与收银处理

#### 7. EDC显示与选择燃油交易记录

**业务流程：**
```
EDC交易选择流程：
- EDC设备显示当前站点所有未结算的燃油交易记录
- 显示信息包括：交易编号、油枪号、油品类型、升数、金额、交易时间
- 加油员根据客户提供的信息（油枪号、金额等）选择对应的交易记录
- 确认选择后，进入后续的会员和支付处理流程
```

**关键接口：**

获取未结算的燃油交易列表：
```http
GET /api/v1/fuel-transactions?station_id=101&status=processed&settlement_status=pending
```

**响应示例：**
```json
{
  "items": [
    {
      "id": 123456,
      "transaction_number": "FT-20250410-002",
      "pump_id": "P01",
      "nozzle_id": "N01",
      "fuel_type": "汽油",
      "fuel_grade": "92#",
      "volume": 50.5,
      "amount": 365.12,
      "created_at": "2025-04-10T14:25:00Z",
      "employee_id": 2001
    },
    {
      "id": 123457,
      "transaction_number": "FT-20250410-003",
      "pump_id": "P02",
      "nozzle_id": "N02",
      "fuel_type": "柴油",
      "fuel_grade": "0#",
      "volume": 80.0,
      "amount": 560.00,
      "created_at": "2025-04-10T14:30:00Z",
      "employee_id": 2001
    }
  ],
  "total": 2
}
```

选择特定燃油交易记录：
```http
PUT /api/v1/fuel-transactions/123456/select
Content-Type: application/json

{
  "selected_by": 2001,
  "selected_at": "2025-04-10T14:35:00Z"
}
```

#### 8. 会员识别/注册（选择交易后）

**业务流程：**
```
会员处理流程：
如果客户提供手机号：
- 输入手机号进行会员查询
- 调用 customer-service 根据手机号查找会员信息
- 如果存在：获取会员等级、积分、优惠资格

如果手机号不存在但需要注册：
- 输入基本信息（姓名等）
- 调用 customer-service 创建新会员
- 自动分配会员等级(REGULAR)和初始积分(0)
- 绑定手机号作为唯一标识

如果不提供手机号：
- 按匿名客户处理
- 不享受会员优惠和积分
```

**关键接口：**

根据手机号查询会员：
```http
GET /api/v1/members/by-phone/13888888888
```

创建新会员：
```http
POST /api/v1/members
Content-Type: application/json

{
  "phone": "13888888888",
  "name": "李四",
  "level": "REGULAR",
  "points": 0
}
```

**响应示例：**
```json
{
  "id": 1001,
  "phone": "13888888888",
  "name": "李四",
  "level": "REGULAR",
  "points": 100,
  "created_at": "2025-04-10T10:20:00Z"
}
```

#### 9. 支付方式选择

**业务流程：**
```
支付方式处理：
- 展示可用支付方式（现金、刷卡、移动支付等）
- 客户选择支付方式
- 记录支付方式（影响后续促销计算）
- 支付方式会影响可用的促销规则

支付方式类型：
- 现金 (cash)
- 刷卡 (card)  
- 移动支付 (mobile)
- 油卡 (fuel_card)
```

#### 10. 促销计算与应用

**业务流程：**
```
促销计算流程：
- 基于会员信息、支付方式、购买金额计算可用促销
- 调用 promotion-service 获取适用的促销规则
- 自动应用最优促销组合
- 计算折扣金额和赠品
- 更新最终应付金额

促销类型：
- 会员折扣：基于会员等级的价格折扣
- 支付方式优惠：特定支付方式的优惠
- 满额赠品：达到指定金额赠送商品
- 积分抵扣：使用积分抵扣部分金额
```

**关键接口：**

获取可用促销：
```http
POST /api/v1/promotions/calculate
Content-Type: application/json

{
  "customer_id": 1001,
  "payment_method": "cash",
  "items": [
    {
      "product_id": 5001,
      "product_type": "fuel",
      "quantity": 50.5,
      "unit_price": 7.23,
      "total_price": 365.12
    }
  ],
  "total_amount": 365.12
}
```

**响应示例：**
```json
{
  "applicable_promotions": [
    {
      "id": 2001,
      "name": "会员折扣",
      "type": "discount",
      "discount_amount": 20.00,
      "discount_rate": 0.05
    }
  ],
  "gifts": [
    {
      "product_id": 6001,
      "product_name": "矿泉水",
      "quantity": 2
    }
  ],
  "final_amount": 345.12,
  "total_discount": 20.00
}
```

#### 11. 赠品处理与库存扣减

**业务流程：**
```
赠品处理流程：
- 确认赠品库存是否充足
- 如果库存不足，提示调整或取消赠品
- 扣减赠品库存（调用库存管理接口）
- 记录赠品发放记录
- 更新库存状态

赠品库存扣减注意事项：
- 先检查赠品库存可用性
- 原子性扣减操作，避免超卖
- 失败时回滚操作
```

**关键接口：**

检查赠品库存：
```http
GET /api/v1/inventory/products/6001/stock
```

扣减赠品库存：
```http
POST /api/v1/inventory/deduct
Content-Type: application/json

{
  "items": [
    {
      "product_id": 6001,
      "quantity": 2,
      "reason": "promotion_gift",
      "transaction_id": "FT-20250410-002"
    }
  ]
}
```

#### 12. 订单创建

**业务流程：**
```
订单创建流程：
- 基于燃油交易、会员信息、促销信息创建订单
- 自动生成订单编号
- 记录所有交易细节
- 设置订单状态为已完成
- 关联燃油交易和支付记录
```

**关键接口：**

```http
POST /api/v1/orders
Content-Type: application/json

{
  "fuel_transaction_id": 123456,
  "station_id": 101,
  "customer_id": 1001,
  "customer_name": "李四",
  "payment_method": "cash",
  "allocated_amount": 345.12,
  "metadata": {
    "source": "pos",
    "employee_id": 2001,
    "promotions_applied": [2001],
    "gifts_issued": [
      {
        "product_id": 6001,
        "quantity": 2
      }
    ]
  }
}
```

**响应示例：**
```json
{
  "id": 1,
  "order_number": "ORD202504101030001234",
  "customer_id": 1001,
  "customer_name": "李四",
  "station_id": 101,
  "status": "completed",
  "total_amount": 365.12,
  "discount_amount": 20.00,
  "final_amount": 345.12,
  "paid_amount": 345.12,
  "items": [
    {
      "id": 1,
      "product_id": 5001,
      "product_name": "92#汽油",
      "product_type": "fuel",
      "quantity": 50.5,
      "unit_price": 7.23,
      "total_price": 365.12
    }
  ],
  "promotions": [
    {
      "id": 1,
      "promotion_id": 2001,
      "promotion_name": "会员折扣",
      "promotion_type": "discount",
      "discount_amount": 20.00
    }
  ],
  "gifts": [
    {
      "product_id": 6001,
      "product_name": "矿泉水",
      "quantity": 2
    }
  ]
}
```

#### 13. 支付处理

**业务流程：**
```
支付处理流程：
- 基于选择的支付方式进行支付处理
- 现金支付：记录收款金额和找零
- 刷卡支付：调用银联/第三方支付接口
- 移动支付：生成二维码或调用支付接口
- 更新订单支付状态
- 生成支付凭证

支付失败处理：
- 显示支付失败原因（网络问题、余额不足、卡片问题等）
- 提示客户选择其他支付方式
- 返回到步骤9：支付方式选择
- 客户可以选择现金、其他银行卡或移动支付方式
```

**关键接口：**

处理支付：
```http
POST /api/v1/orders/1/payment
Content-Type: application/json

{
  "payment_method": "cash",
  "amount": 345.12,
  "received_amount": 400.00,  // 现金支付时的实际收款
  "change_amount": 54.88,     // 找零金额
  "transaction_id": "PAY-20250410-001"
}
```

#### 14. 交易完成与收尾

**业务流程：**
```
交易完成流程：
- 打印交易小票
- 发送交易确认信息（如需要）
- 释放系统资源
- 记录完整交易日志
- 标记交易为已完成状态
```

**关键接口：**

生成交易小票：
```http
POST /api/v1/receipts/generate
Content-Type: application/json

{
  "order_id": 1,
  "template": "fuel_transaction"
}
```

标记交易完成：
```http
PUT /api/v1/orders/1/complete
Content-Type: application/json

{
  "completed_at": "2025-04-10T14:45:00Z",
  "completed_by": 2001
}
```

---

## 交易流程图

### 整体流程概览
```
第一部分：燃油交易记录创建流程
[员工登录] → [选择油枪] → [检查油枪状态] → [EDC刷卡认证]
     ↓
[选择油品] → [油机授权] → [开始加油] → [监控加油过程]
     ↓
[加油完成] → [挂回油枪] → [创建燃油交易记录] → [第一部分完成]
     ↓
第二部分：收银结算流程
[EDC显示交易列表] → [选择燃油交易] → [会员识别/注册] → [选择支付方式]
     ↓
[计算促销] → [处理赠品] → [扣减库存] → [创建订单]
     ↓
[处理支付] → [打印小票] → [交易完成]
```

### 第一部分：燃油交易记录创建流程图

```mermaid
flowchart TD
    A[开始] --> B[员工登录验证]
    B --> C{登录成功?}
    C -->|否| B
    C -->|是| D[选择油泵和油枪]
    
    D --> E[检查油枪状态]
    E --> F{油枪是否空闲?}
    F -->|否| G[提示：油枪使用中<br/>选择其他油枪]
    G --> D
    F -->|是| H[加油员EDC刷卡认证]
    
    H --> I{EDC认证成功?}
    I -->|否| H
    I -->|是| J[选择油品类型和价格]
    
    J --> K[向油机发送授权]
    K --> L[油机启动，等待加油]
    
    %% 加油过程阶段
    L --> M[客户开始加油]
    M --> N[实时监控加油数据]
    N --> O[客户停止加油/达到预设值]
    O --> P[获取最终加油数据]
    P --> Q[客户挂回油枪]
    Q --> R[创建燃油交易记录]
    R --> S[第一部分完成]
    
    %% 样式定义
    classDef processBox fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef decisionBox fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef errorBox fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef startEndBox fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef completeBox fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    
    class A,S startEndBox
    class B,D,E,H,J,K,L,M,N,O,P,Q,R processBox
    class C,F,I decisionBox
    class G errorBox
```

### 第二部分：收银结算流程图

```mermaid
flowchart TD
    A[开始收银结算] --> B[EDC显示未结算交易列表]
    B --> C[加油员选择燃油交易记录]
    C --> D{客户提供手机号?}
    D -->|否| E[按匿名客户处理]
    D -->|是| F[根据手机号查询会员]
    F --> G{会员存在?}
    G -->|否| H[创建新会员]
    G -->|是| I[获取会员信息]
    H --> I
    E --> J[选择支付方式]
    I --> J
    
    J --> K[计算适用促销]
    K --> L{有促销赠品?}
    L -->|是| M[检查赠品库存]
    M --> N{库存充足?}
    N -->|否| O[调整或取消赠品]
    N -->|是| P[扣减赠品库存]
    O --> Q[创建订单]
    P --> Q
    L -->|否| Q
    
    Q --> R[处理支付]
    R --> S{支付成功?}
    S -->|否| T[支付失败处理]
    T --> J
    S -->|是| U[生成交易小票]
    
    U --> V[交易完成]
    V --> W[结束]
    
    %% 样式定义
    classDef processBox fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef decisionBox fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef errorBox fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef startEndBox fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef memberBox fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    
    class A,W startEndBox
    class B,C,E,F,H,I,J,K,M,P,Q,R,U,V processBox
    class D,G,L,N,S decisionBox
    class O,T errorBox
```

---

## 错误处理和异常情况

### 1. 油枪占用处理
```json
{
  "code": "PUMP_OCCUPIED",
  "message": "油枪正在使用中",
  "detail": "请选择其他可用油枪或等待当前交易完成"
}
```

### 2. 赠品库存不足处理
```json
{
  "code": "INSUFFICIENT_GIFT_STOCK",
  "message": "赠品库存不足",
  "detail": "矿泉水库存不足，无法提供赠品"
}
```

### 3. 支付失败处理
```json
{
  "code": "PAYMENT_FAILED",
  "message": "支付处理失败",
  "detail": "银行卡支付超时，请重试或选择其他支付方式"
}
```

---

## 技术要求和注意事项

### 1. 事务性保障
- 关键操作使用数据库事务确保一致性
- 赠品库存扣减、订单创建、支付处理需要原子性操作
- 失败时自动回滚相关操作

### 2. 并发控制
- 油枪状态检查使用乐观锁或悲观锁
- 赠品库存扣减防止超卖问题
- 会员积分更新避免并发冲突

### 3. 日志记录
- 记录每个关键步骤的操作日志
- 包含操作员、时间戳、操作内容
- 用于审计和问题排查

### 4. 性能优化
- 关键接口响应时间控制在2秒以内
- 使用缓存提高查询性能
- 异步처리非关键业务逻辑

---

## 流程分离点说明

### 为什么要拆分为两个部分？

1. **业务逻辑分离**
   - **第一部分**专注于油机操作和燃油数据记录，是核心的燃油业务
   - **第二部分**专注于商业逻辑处理，包括会员、促销、支付等增值服务

2. **系统解耦**
   - 第一部分主要涉及：BOS、PTS2-GO、Oil Service
   - 第二部分主要涉及：Member Service、Promotion Service、Order Service
   - 两部分可以独立开发、测试和部署

3. **异常处理**
   - 第一部分失败：燃油交易本身失败，影响核心业务
   - 第二部分失败：可以保留燃油交易记录，后续补偿处理

4. **性能优化**
   - 第一部分要求实时性高，确保油机操作流畅
   - 第二部分可以异步处理部分业务逻辑

### 分离点：燃油交易记录创建

**分离标志**：`POST /api/v1/fuel-transactions` 接口成功返回

**第一部分输出**：
```json
{
  "id": 123456,
  "transaction_number": "FT-20250410-002",
  "station_id": 101,
  "pump_id": "P01",
  "fuel_type": "汽油",
  "fuel_grade": "92#",
  "volume": 50.5,
  "amount": 365.12,
  "employee_id": 2001,
  "status": "processed"
}
```

**第二部分输入**：燃油交易记录ID，用于后续所有业务处理的关联。

---

## 总结

本文档详细描述了BP加油站管理系统的完整线下加油交易流程，并将其拆分为两个相对独立的部分：

1. **燃油交易记录创建流程**：涵盖了从员工登录到创建燃油交易记录的核心业务流程
2. **收银结算流程**：涵盖了从EDC选择交易记录开始的会员处理、促销计算、订单创建、支付处理等商业逻辑

通过这种拆分方式，各个服务模块职责更加清晰，便于系统的维护、扩展和故障处理。标准化的API接口确保了两个部分之间的有效衔接，为前端应用和第三方系统集成提供了完整的接口规范。 