# 收银结算流程接口分析

## 概述

本文档基于《20250622完整加油交易流程和接口文档.md》中的第二部分：收银结算流程，分析所需的接口及其实现情况。

## 收银结算流程步骤概览

收银结算流程包含以下关键步骤：
1. **步骤7**: EDC显示与选择燃油交易记录
2. **步骤8**: 会员识别/注册
3. **步骤9**: 支付方式选择
4. **步骤10**: 促销计算与应用
5. **步骤11**: 赠品处理与库存扣减
6. **步骤12**: 订单创建
7. **步骤13**: 支付处理
8. **步骤14**: 交易完成与收尾

## 接口分析（按步骤顺序）

### 步骤7: EDC显示与选择燃油交易记录 ✅

**已有接口：**
```http
GET /api/v1/fuel-transactions?station_id=101&status=processed
```
- **功能**: 获取未结算的燃油交易列表
- **来源**: `fuel-transaction-interface-guide.md`

```http
GET /api/v1/fuel-transactions/{id}
```
- **功能**: 获取选定的燃油交易详情
- **来源**: `fuel-transaction-interface-guide.md`

### 步骤8: 会员识别/注册 ❌

**缺失接口：**
```http
GET /api/v1/members/by-phone/{phone}
```
- **功能**: 根据手机号查询会员信息
- **所属服务**: Member Service

```http
POST /api/v1/members
```
- **功能**: 创建新会员
- **所属服务**: Member Service

### 步骤9: 支付方式选择 ✅

**已有接口：**
```http
GET /payment-methods?enabled=true&station_id={station_id}
```
- **功能**: 获取可用的支付方式列表
- **来源**: `payment-api-documentation.md`

### 步骤10: 促销计算与应用 ❌

**缺失接口：**
```http
POST /api/v1/promotions/calculate
```
- **功能**: 基于会员、支付方式、购买金额计算适用促销
- **所属服务**: Promotion Service

```http
GET /api/v1/promotions/applicable
```
- **功能**: 获取当前可用的促销规则
- **所属服务**: Promotion Service

### 步骤11: 赠品处理与库存扣减 ❌

**缺失接口：**
```http
GET /api/v1/inventory/products/{id}/stock
```
- **功能**: 检查赠品库存可用性
- **所属服务**: Inventory Service

```http
POST /api/v1/inventory/deduct
```
- **功能**: 扣减库存（赠品发放）
- **所属服务**: Inventory Service

```http
POST /api/v1/inventory/check-availability
```
- **功能**: 批量检查多个商品的库存可用性
- **所属服务**: Inventory Service

### 步骤12: 订单创建 ✅

**已有接口：**
```http
POST /api/v1/orders
```
- **功能**: 基于燃油交易创建订单
- **来源**: `订单管理接口文档.md`

### 步骤13: 支付处理 ✅

**已有接口：**
```http
POST /payments
```
- **功能**: 处理支付请求
- **来源**: `payment-api-documentation.md`

```http
GET /payments/{id}/status
```
- **功能**: 查询支付状态
- **来源**: `payment-api-documentation.md`

### 步骤14: 交易完成与收尾 ✅ / ❌

**已有接口：**
```http
POST /api/v1/orders/{id}/complete
```
- **功能**: 完成订单
- **来源**: `订单管理接口文档.md`

**缺失接口：**
```http
POST /api/v1/receipts/generate
```
- **功能**: 生成交易小票
- **所属服务**: 小票服务（可能集成在BOS中）

```http
GET /api/v1/receipts/templates
```
- **功能**: 获取小票模板
- **所属服务**: 小票服务（可能集成在BOS中）