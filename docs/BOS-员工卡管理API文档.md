# BOS 员工卡管理 API 文档

## 概述

员工卡管理模块为BOS加油站管理系统提供员工卡的全生命周期管理功能，包括员工卡的创建、查询、更新、删除、激活、暂停、验证等操作。该模块连接认证数据库（auth_db）中的用户信息与业务系统，实现员工身份认证和权限管理。

**Base URL**: `/api/v1`

## 认证方式

系统使用JWT令牌进行认证：
```
Authorization: Bearer <token>
```

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "detail": "详细错误信息"
}
```

## 数据模型

### StaffCard (员工卡)
```json
{
  "id": "int64",
  "card_number": "string",
  "user_id": "string",
  "station_id": "int64",
  "card_type": "string",
  "status": "string",
  "valid_from": "datetime",
  "valid_until": "datetime",
  "permissions": "object",
  "metadata": "object",
  "created_at": "datetime",
  "updated_at": "datetime",
  "deleted_at": "datetime",
  "user": "AuthUser"
}
```

### AuthUser (关联用户信息)
```json
{
  "id": "string",
  "username": "string",
  "email": "string",
  "phone": "string",
  "full_name": "string",
  "department": "string",
  "status": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### StaffCardValidationResult (员工卡验证结果)
```json
{
  "is_valid": "boolean",
  "card_id": "int64",
  "user_id": "string",
  "status": "string",
  "message": "string",
  "staff_card": "StaffCard"
}
```

## 枚举值说明

### 员工卡类型 (card_type)
- `employee`: 普通员工
- `manager`: 管理员
- `admin`: 系统管理员

### 员工卡状态 (status)
- `active`: 活跃状态
- `inactive`: 未激活
- `suspended`: 已暂停
- `expired`: 已过期

## API 接口列表

### 1. 员工卡基础管理

#### 1.1 创建员工卡
- **接口**: `POST /staff-cards`
- **描述**: 创建新的员工卡
- **认证**: 需要认证
- **请求体**:
```json
{
  "cardNumber": "SC00000001",
  "userId": "550e8400-e29b-41d4-a716-************",
  "stationId": 1,
  "cardType": "employee",
  "status": "active",
  "validFrom": "2024-01-01T00:00:00Z",
  "validUntil": "2024-12-31T23:59:59Z",
  "permissions": {
    "fuel_transaction": true,
    "order_management": true,
    "payment_processing": false
  },
  "metadata": {
    "department": "operations",
    "notes": "新员工入职"
  }
}
```

**请求参数说明**:
- `cardNumber` (string, 必填): 员工卡号，必须唯一
- `userId` (string, 必填): 关联的用户ID（UUID格式）
- `stationId` (int64, 可选): 关联的站点ID，null表示可在所有站点使用
- `cardType` (string, 必填): 员工卡类型，可选值：employee, manager, admin
- `status` (string, 必填): 员工卡状态，可选值：active, inactive, suspended, expired
- `validFrom` (datetime, 可选): 有效期开始时间，默认为当前时间
- `validUntil` (datetime, 可选): 有效期结束时间，null表示永久有效
- `permissions` (object, 可选): 特殊权限配置
- `metadata` (object, 可选): 扩展元数据

**成功响应**:
```json
{
  "id": 1,
  "card_number": "SC00000001",
  "user_id": "550e8400-e29b-41d4-a716-************",
  "station_id": 1,
  "card_type": "employee",
  "status": "active",
  "valid_from": "2024-01-01T00:00:00Z",
  "valid_until": "2024-12-31T23:59:59Z",
  "permissions": {
    "fuel_transaction": true,
    "order_management": true,
    "payment_processing": false
  },
  "metadata": {
    "department": "operations",
    "notes": "新员工入职"
  },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

#### 1.2 获取员工卡列表
- **接口**: `GET /staff-cards`
- **描述**: 获取员工卡列表，支持分页和筛选
- **认证**: 需要认证
- **查询参数**:
  - `page` (int, 可选): 页码，默认为1
  - `pageSize` (int, 可选): 每页数量，默认为20，最大为100
  - `userId` (string, 可选): 按用户ID筛选
  - `cardNumber` (string, 可选): 按卡号筛选
  - `stationId` (int64, 可选): 按站点ID筛选
  - `cardType` (string, 可选): 按卡类型筛选
  - `status` (string, 可选): 按状态筛选
  - `validOnly` (boolean, 可选): 只返回有效期内的卡
  - `dateFrom` (string, 可选): 创建时间起始日期，格式：YYYY-MM-DD
  - `dateTo` (string, 可选): 创建时间结束日期，格式：YYYY-MM-DD
  - `sortBy` (string, 可选): 排序字段，默认为created_at
  - `sortOrder` (string, 可选): 排序方向，asc或desc，默认为desc
  - `includeUser` (boolean, 可选): 是否包含用户信息

**成功响应**:
```json
{
  "data": [
    {
      "id": 1,
      "card_number": "SC00000001",
      "user_id": "550e8400-e29b-41d4-a716-************",
      "station_id": 1,
      "card_type": "employee",
      "status": "active",
      "valid_from": "2024-01-01T00:00:00Z",
      "valid_until": "2024-12-31T23:59:59Z",
      "permissions": {},
      "metadata": {},
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "user": {
        "id": "550e8400-e29b-41d4-a716-************",
        "username": "zhang.san",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "full_name": "张三",
        "department": "operations",
        "status": "active"
      }
    }
  ],
  "page": 1,
  "size": 20,
  "total": 1
}
```

#### 1.3 获取员工卡详情
- **接口**: `GET /staff-cards/{id}`
- **描述**: 根据ID获取员工卡详情
- **认证**: 需要认证
- **路径参数**:
  - `id` (int64): 员工卡ID
- **查询参数**:
  - `includeUser` (boolean, 可选): 是否包含用户信息

**成功响应**:
```json
{
  "id": 1,
  "card_number": "SC00000001",
  "user_id": "550e8400-e29b-41d4-a716-************",
  "station_id": 1,
  "card_type": "employee",
  "status": "active",
  "valid_from": "2024-01-01T00:00:00Z",
  "valid_until": "2024-12-31T23:59:59Z",
  "permissions": {},
  "metadata": {},
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z",
  "user": {
    "id": "550e8400-e29b-41d4-a716-************",
    "username": "zhang.san",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "full_name": "张三",
    "department": "operations",
    "status": "active"
  }
}
```

#### 1.4 更新员工卡
- **接口**: `PUT /staff-cards/{id}`
- **描述**: 更新员工卡信息
- **认证**: 需要认证
- **路径参数**:
  - `id` (int64): 员工卡ID
- **请求体**:
```json
{
  "stationId": 2,
  "cardType": "manager",
  "status": "active",
  "validFrom": "2024-01-01T00:00:00Z",
  "validUntil": "2024-12-31T23:59:59Z",
  "clearValidUntil": false,
  "permissions": {
    "fuel_transaction": true,
    "order_management": true,
    "payment_processing": true
  },
  "metadata": {
    "promotion_date": "2024-01-15",
    "notes": "晋升为管理员"
  }
}
```

**请求参数说明**:
- `stationId` (int64, 可选): 关联的站点ID
- `cardType` (string, 可选): 员工卡类型
- `status` (string, 可选): 员工卡状态
- `validFrom` (datetime, 可选): 有效期开始时间
- `validUntil` (datetime, 可选): 有效期结束时间
- `clearValidUntil` (boolean, 可选): 是否清除有效期结束时间
- `permissions` (object, 可选): 特殊权限配置
- `metadata` (object, 可选): 扩展元数据

**成功响应**:
```json
{
  "id": 1,
  "card_number": "SC00000001",
  "user_id": "550e8400-e29b-41d4-a716-************",
  "station_id": 2,
  "card_type": "manager",
  "status": "active",
  "valid_from": "2024-01-01T00:00:00Z",
  "valid_until": "2024-12-31T23:59:59Z",
  "permissions": {
    "fuel_transaction": true,
    "order_management": true,
    "payment_processing": true
  },
  "metadata": {
    "promotion_date": "2024-01-15",
    "notes": "晋升为管理员"
  },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:00:00Z"
}
```

#### 1.5 删除员工卡
- **接口**: `DELETE /staff-cards/{id}`
- **描述**: 删除员工卡（软删除）
- **认证**: 需要认证
- **路径参数**:
  - `id` (int64): 员工卡ID

**成功响应**: HTTP 204 No Content

### 2. 员工卡操作管理

#### 2.1 根据卡号获取员工卡
- **接口**: `GET /staff-cards/number/{cardNumber}`
- **描述**: 根据卡号获取员工卡信息
- **认证**: 需要认证
- **路径参数**:
  - `cardNumber` (string): 员工卡号

**成功响应**:
```json
{
  "id": 1,
  "card_number": "SC00000001",
  "user_id": "550e8400-e29b-41d4-a716-************",
  "station_id": 1,
  "card_type": "employee",
  "status": "active",
  "valid_from": "2024-01-01T00:00:00Z",
  "valid_until": "2024-12-31T23:59:59Z",
  "permissions": {},
  "metadata": {},
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

#### 2.2 激活员工卡
- **接口**: `POST /staff-cards/number/{cardNumber}/activate`
- **描述**: 激活指定卡号的员工卡
- **认证**: 需要认证
- **路径参数**:
  - `cardNumber` (string): 员工卡号

**成功响应**:
```json
{
  "message": "Card activated successfully"
}
```

#### 2.3 暂停员工卡
- **接口**: `POST /staff-cards/number/{cardNumber}/suspend`
- **描述**: 暂停指定卡号的员工卡
- **认证**: 需要认证
- **路径参数**:
  - `cardNumber` (string): 员工卡号
- **请求体**:
```json
{
  "reason": "违规操作"
}
```

**请求参数说明**:
- `reason` (string, 必填): 暂停原因

**成功响应**:
```json
{
  "message": "Card suspended successfully"
}
```

#### 2.4 验证员工卡
- **接口**: `POST /staff-cards/number/{cardNumber}/validate`
- **描述**: 验证员工卡是否可用于交易
- **认证**: 需要认证
- **路径参数**:
  - `cardNumber` (string): 员工卡号
- **请求体**:
```json
{
  "stationId": 1
}
```

**请求参数说明**:
- `stationId` (int64, 必填): 站点ID

**成功响应**:
```json
{
  "is_valid": true,
  "card_id": 1,
  "user_id": "550e8400-e29b-41d4-a716-************",
  "status": "active",
  "message": "验证成功",
  "staff_card": {
    "id": 1,
    "card_number": "SC00000001",
    "user_id": "550e8400-e29b-41d4-a716-************",
    "station_id": 1,
    "card_type": "employee",
    "status": "active",
    "valid_from": "2024-01-01T00:00:00Z",
    "valid_until": "2024-12-31T23:59:59Z",
    "permissions": {},
    "metadata": {},
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

#### 2.5 延长员工卡有效期
- **接口**: `POST /staff-cards/number/{cardNumber}/extend`
- **描述**: 延长员工卡的有效期
- **认证**: 需要认证
- **路径参数**:
  - `cardNumber` (string): 员工卡号
- **请求体**:
```json
{
  "validUntil": "2025-12-31T23:59:59Z"
}
```

**请求参数说明**:
- `validUntil` (datetime, 可选): 新的有效期结束时间，null表示永久有效

**成功响应**:
```json
{
  "message": "Card validity extended successfully"
}
```

### 3. 用户员工卡管理

#### 3.1 获取用户的员工卡列表
- **接口**: `GET /users/{userId}/staff-cards`
- **描述**: 获取指定用户的所有员工卡
- **认证**: 需要认证
- **路径参数**:
  - `userId` (string): 用户ID（UUID格式）

**成功响应**:
```json
[
  {
    "id": 1,
    "card_number": "SC00000001",
    "user_id": "550e8400-e29b-41d4-a716-************",
    "station_id": 1,
    "card_type": "employee",
    "status": "active",
    "valid_from": "2024-01-01T00:00:00Z",
    "valid_until": "2024-12-31T23:59:59Z",
    "permissions": {},
    "metadata": {},
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
]
```

#### 3.2 为用户创建员工卡
- **接口**: `POST /users/{userId}/staff-cards`
- **描述**: 为指定用户创建员工卡
- **认证**: 需要认证
- **路径参数**:
  - `userId` (string): 用户ID（UUID格式）
- **请求体**:
```json
{
  "cardNumber": "SC00000002",
  "cardType": "employee",
  "stationId": 1
}
```

**请求参数说明**:
- `cardNumber` (string, 必填): 员工卡号，长度3-50字符
- `cardType` (string, 必填): 员工卡类型，可选值：employee, manager, admin
- `stationId` (int64, 可选): 关联的站点ID

**成功响应**:
```json
{
  "id": 2,
  "card_number": "SC00000002",
  "user_id": "550e8400-e29b-41d4-a716-************",
  "station_id": 1,
  "card_type": "employee",
  "status": "active",
  "valid_from": "2024-01-15T11:00:00Z",
  "valid_until": null,
  "permissions": {},
  "metadata": {},
  "created_at": "2024-01-15T11:00:00Z",
  "updated_at": "2024-01-15T11:00:00Z"
}
```

### 4. 站点员工卡管理

#### 4.1 获取站点的活跃员工卡
- **接口**: `GET /stations/{stationId}/staff-cards`
- **描述**: 获取指定站点的所有活跃员工卡
- **认证**: 需要认证
- **路径参数**:
  - `stationId` (int64): 站点ID

**成功响应**:
```json
[
  {
    "id": 1,
    "card_number": "SC00000001",
    "user_id": "550e8400-e29b-41d4-a716-************",
    "station_id": 1,
    "card_type": "employee",
    "status": "active",
    "valid_from": "2024-01-01T00:00:00Z",
    "valid_until": "2024-12-31T23:59:59Z",
    "permissions": {},
    "metadata": {},
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
]
```

### 5. 批量操作

#### 5.1 批量创建员工卡
- **接口**: `POST /staff-cards/bulk-create`
- **描述**: 为多个用户批量创建员工卡
- **认证**: 需要认证
- **请求体**:
```json
{
  "userIds": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-446655440001"
  ],
  "cardType": "employee",
  "stationId": 1
}
```

**请求参数说明**:
- `userIds` (array, 必填): 用户ID列表，至少包含一个用户ID
- `cardType` (string, 必填): 员工卡类型，可选值：employee, manager, admin
- `stationId` (int64, 可选): 关联的站点ID

**成功响应**:
```json
[
  {
    "id": 3,
    "card_number": "SC00000003",
    "user_id": "550e8400-e29b-41d4-a716-************",
    "station_id": 1,
    "card_type": "employee",
    "status": "active",
    "valid_from": "2024-01-15T11:30:00Z",
    "valid_until": null,
    "permissions": {},
    "metadata": {},
    "created_at": "2024-01-15T11:30:00Z",
    "updated_at": "2024-01-15T11:30:00Z"
  },
  {
    "id": 4,
    "card_number": "SC00000004",
    "user_id": "550e8400-e29b-41d4-a716-446655440001",
    "station_id": 1,
    "card_type": "employee",
    "status": "active",
    "valid_from": "2024-01-15T11:30:00Z",
    "valid_until": null,
    "permissions": {},
    "metadata": {},
    "created_at": "2024-01-15T11:30:00Z",
    "updated_at": "2024-01-15T11:30:00Z"
  }
]
```

## 错误码说明

| HTTP状态码 | 错误描述 |
|-----------|---------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如卡号已存在） |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 常见错误示例

### 1. 卡号已存在
```json
{
  "code": 409,
  "message": "员工卡号已存在",
  "detail": "Card number 'SC00000001' already exists"
}
```

### 2. 用户不存在
```json
{
  "code": 404,
  "message": "用户不存在",
  "detail": "User with ID '550e8400-e29b-41d4-a716-************' not found"
}
```

### 3. 员工卡验证失败
```json
{
  "is_valid": false,
  "card_id": null,
  "user_id": null,
  "status": null,
  "message": "员工卡不存在"
}
```

### 4. 权限不足
```json
{
  "code": 403,
  "message": "权限不足",
  "detail": "Insufficient permissions to perform this operation"
}
```

## 业务规则

### 1. 员工卡号规则
- 员工卡号必须唯一
- 系统自动生成的卡号格式：SC + 8位数字（如：SC00000001）
- 手动指定的卡号长度限制：3-50字符

### 2. 有效期规则
- `valid_from` 默认为当前时间
- `valid_until` 为null表示永久有效
- `valid_until` 必须大于 `valid_from`

### 3. 站点权限规则
- `station_id` 为null表示可在所有站点使用
- 指定 `station_id` 则只能在该站点使用
- 管理员和系统管理员通常不限制站点

### 4. 状态转换规则
- 新创建的员工卡默认状态为 `active`
- 只有 `active` 状态的卡可以用于交易
- `suspended` 状态的卡需要管理员手动激活
- `expired` 状态的卡需要延长有效期后才能使用

## 使用场景

### 1. 员工入职流程
1. 在认证系统中创建用户账户
2. 调用员工卡创建接口为用户分配员工卡
3. 设置适当的卡类型和权限
4. 员工开始使用员工卡进行日常操作

### 2. 加油交易验证
1. 收银员扫描或输入员工卡号
2. 系统调用验证接口检查员工卡有效性
3. 验证通过后允许进行交易操作
4. 交易记录中关联员工卡信息

### 3. 权限管理
1. 管理员根据员工职责调整卡类型
2. 通过权限配置控制员工可执行的操作
3. 必要时暂停或激活员工卡
4. 定期检查和更新员工卡有效期

### 4. 批量管理
1. 新站点开业时批量创建员工卡
2. 部门调整时批量更新员工卡站点信息
3. 定期清理过期或无效的员工卡

## 注意事项

### 1. 数据一致性
- 员工卡与认证系统用户信息保持同步
- 删除用户时需要同时处理相关员工卡
- 站点信息变更时需要更新相关员工卡

### 2. 安全考虑
- 员工卡验证应在每次交易前进行
- 敏感操作需要额外的权限验证
- 记录员工卡的使用日志便于审计

### 3. 性能优化
- 频繁查询的员工卡信息可以考虑缓存
- 批量操作时注意数据库性能
- 大量数据查询时使用分页机制

### 4. 兼容性
- 系统支持从旧的员工编号（employee_no）迁移到员工卡系统
- 新旧系统并行运行期间需要保持数据同步
- API版本升级时保持向后兼容性

## 示例代码

### 1. 创建员工卡
```bash
curl -X POST /api/v1/staff-cards \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "cardNumber": "SC00000001",
    "userId": "550e8400-e29b-41d4-a716-************",
    "cardType": "employee",
    "status": "active"
  }'
```

### 2. 验证员工卡
```bash
curl -X POST /api/v1/staff-cards/number/SC00000001/validate \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "stationId": 1
  }'
```

### 3. 获取员工卡列表
```bash
curl -X GET "/api/v1/staff-cards?page=1&pageSize=20&status=active&includeUser=true" \
  -H "Authorization: Bearer <token>"
```

### 4. 批量创建员工卡
```bash
curl -X POST /api/v1/staff-cards/bulk-create \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "userIds": [
      "550e8400-e29b-41d4-a716-************",
      "550e8400-e29b-41d4-a716-446655440001"
    ],
    "cardType": "employee",
    "stationId": 1
  }'
```

---

**文档版本**: v1.0  
**最后更新**: 2024-01-15  
**维护者**: BOS开发团队 