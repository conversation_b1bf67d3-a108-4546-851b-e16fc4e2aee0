# BOS 员工认证 API 文档

## 概述

员工认证模块为BOS加油站管理系统提供员工登录认证功能，支持员工通过员工编号和密码进行身份验证，获取访问系统的权限令牌。

**Base URL**: `/api/v1`

## 认证方式

系统使用JWT令牌进行认证：
```
Authorization: Bearer <token>
```

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 错误响应
```json
{
  "code": "ERROR_CODE",
  "message": "错误描述",
  "detail": "详细错误信息"
}
```

## API 接口列表

### 1. 员工认证管理

#### 1.1 员工登录
- **接口**: `POST /employee/login`
- **描述**: 员工登录认证，验证员工编号和密码
- **认证**: 无需认证
- **请求体**:
```json
{
  "employee_no": "EMP001",
  "password": "password123"
}
```

**请求参数说明**:
- `employee_no` (string, 必填): 员工编号
- `password` (string, 必填): 员工密码

**成功响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "employee": {
      "id": 1,
      "employee_no": "EMP001", 
      "name": "张三",
      "position": "加油员",
      "department": "运营部",
      "status": "active",
      "hire_date": "2024-01-01",
      "last_login_at": "2024-01-15T10:30:00Z",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

**错误响应**:

1. **参数错误** (400):
```json
{
  "code": "MISSING_REQUIRED_FIELDS",
  "message": "缺少必填字段",
  "detail": "员工编号和密码不能为空"
}
```

2. **认证失败** (401):
```json
{
  "code": "AUTHENTICATION_FAILED",
  "message": "认证失败", 
  "detail": "员工编号或密码错误"
}
```

3. **请求格式错误** (400):
```json
{
  "code": "INVALID_REQUEST",
  "message": "无效的请求格式",
  "detail": "JSON格式错误"
}
```

**注意事项**:
- 员工登录使用员工编号（employee_no）作为用户标识
- 返回的token用于后续API请求的认证
- 登录成功后会更新员工的最后登录时间
- 系统会记录员工的登录活动日志

#### 1.2 获取当前员工信息
- **接口**: `GET /employee/profile`
- **描述**: 获取当前登录员工的详细信息
- **认证**: 需要认证
- **请求头**:
```
Authorization: Bearer <token>
```

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "employee_no": "EMP001",
    "name": "张三",
    "position": "加油员",
    "department": "运营部",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "status": "active",
    "permissions": [
      "fuel_transaction:create",
      "fuel_transaction:read",
      "order:create",
      "order:read"
    ],
    "station_access": [
      {
        "station_id": 1,
        "station_name": "Main Station",
        "role": "operator"
      }
    ],
    "hire_date": "2024-01-01",
    "last_login_at": "2024-01-15T10:30:00Z"
  }
}
```

### 2. 员工管理

#### 2.1 创建员工
- **接口**: `POST /employee`
- **描述**: 创建新员工账户
- **认证**: 需要认证 (admin权限)
- **请求体**:
```json
{
  "employee_no": "EMP002",
  "name": "李四",
  "password": "password123",
  "position": "主管",
  "department": "运营部",
  "phone": "13800138001",
  "email": "<EMAIL>"
}
```

**成功响应**:
```json
{
  "code": 200,
  "message": "添加成功",
  "data": {
    "id": 2,
    "employee_no": "EMP002",
    "name": "李四",
    "position": "主管",
    "department": "运营部",
    "status": "active",
    "created_at": "2024-01-15T11:00:00Z"
  }
}
```

#### 2.2 获取员工列表
- **接口**: `GET /employees`
- **描述**: 获取员工列表，支持分页和筛选
- **认证**: 需要认证 (admin权限)
- **查询参数**:
  - `page` (int): 页码，默认1
  - `size` (int): 每页记录数，默认10
  - `name` (string): 按员工姓名筛选
  - `department` (string): 按部门筛选
  - `status` (string): 按状态筛选 (active, inactive, suspended)

**成功响应**:
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "total": 25,
    "page": 1,
    "size": 10,
    "employees": [
      {
        "id": 1,
        "employee_no": "EMP001",
        "name": "张三",
        "position": "加油员",
        "department": "运营部",
        "status": "active",
        "hire_date": "2024-01-01",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 2.3 获取员工详情
- **接口**: `GET /employee/{id}`
- **描述**: 根据ID获取员工详细信息
- **认证**: 需要认证
- **路径参数**:
  - `id` (int): 员工ID

**成功响应**:
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "id": 1,
    "employee_no": "EMP001",
    "name": "张三",
    "position": "加油员",
    "department": "运营部",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "status": "active",
    "hire_date": "2024-01-01",
    "last_login_at": "2024-01-15T10:30:00Z",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

#### 2.4 更新员工信息
- **接口**: `PUT /employee/{id}`
- **描述**: 更新员工信息
- **认证**: 需要认证 (admin权限)
- **路径参数**:
  - `id` (int): 员工ID
- **请求体**:
```json
{
  "name": "张三(更新)",
  "position": "高级加油员",
  "phone": "13800138002",
  "email": "<EMAIL>"
}
```

**成功响应**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "employee_no": "EMP001",
    "name": "张三(更新)",
    "position": "高级加油员",
    "phone": "13800138002",
    "email": "<EMAIL>",
    "updated_at": "2024-01-15T11:30:00Z"
  }
}
```

#### 2.5 删除员工
- **接口**: `DELETE /employee/{id}`
- **描述**: 删除员工账户（软删除）
- **认证**: 需要认证 (admin权限)
- **路径参数**:
  - `id` (int): 员工ID

**成功响应**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": {
    "id": 1,
    "employee_no": "EMP001",
    "name": "张三",
    "deleted_at": "2024-01-15T11:35:00Z"
  }
}
```

## 数据模型

### Employee (员工)
```json
{
  "id": "int64",
  "employee_no": "string",
  "name": "string",
  "password": "string (内部字段，不在响应中返回)",
  "position": "string",
  "department": "string",
  "phone": "string",
  "email": "string",
  "status": "string",
  "hire_date": "date",
  "last_login_at": "datetime",
  "created_at": "datetime",
  "updated_at": "datetime",
  "deleted_at": "datetime"
}
```

## 状态码说明

### 员工状态 (status)
- `active`: 在职
- `inactive`: 离职
- `suspended`: 停职

### 员工职位 (position)
- `station_manager`: 站长
- `assistant_manager`: 副站长
- `supervisor`: 主管
- `senior_operator`: 高级操作员
- `operator`: 操作员
- `cashier`: 收银员
- `maintenance`: 维护员

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| INVALID_REQUEST | 无效的请求格式 |
| MISSING_REQUIRED_FIELDS | 缺少必填字段 |
| AUTHENTICATION_FAILED | 认证失败 |
| EMPLOYEE_NOT_FOUND | 员工不存在 |
| EMPLOYEE_ALREADY_EXISTS | 员工已存在 |
| PERMISSION_DENIED | 权限不足 |
| INVALID_EMPLOYEE_STATUS | 无效的员工状态 |
| EMPLOYEE_SUSPENDED | 员工被停职 |
| WEAK_PASSWORD | 密码强度不足 |

## 使用示例

### 员工登录流程

1. **员工登录**
```bash
curl -X POST /api/v1/employee/login \
  -H "Content-Type: application/json" \
  -d '{
    "employee_no": "EMP001",
    "password": "password123"
  }'
```

2. **使用token访问API**
```bash
curl -X GET /api/v1/employee/profile \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 员工管理操作

```bash
# 创建员工
curl -X POST /api/v1/employee \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "employee_no": "EMP002",
    "name": "李四",
    "password": "password123",
    "position": "操作员"
  }'

# 获取员工列表
curl -G /api/v1/employees \
  -H "Authorization: Bearer <token>" \
  -d page=1 \
  -d size=20 \
  -d department=运营部

# 更新员工信息
curl -X PUT /api/v1/employee/1 \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "张三(更新)",
    "position": "高级操作员"
  }'
```

## 安全注意事项

1. **密码策略**: 员工密码应遵循公司密码策略，建议包含字母、数字和特殊字符
2. **令牌安全**: JWT令牌应妥善保管，避免泄露
3. **权限控制**: 不同级别的员工有不同的操作权限
4. **登录日志**: 系统会记录所有员工的登录活动
5. **账户锁定**: 连续登录失败可能导致账户被临时锁定
6. **定期审核**: 建议定期审核员工账户状态和权限 