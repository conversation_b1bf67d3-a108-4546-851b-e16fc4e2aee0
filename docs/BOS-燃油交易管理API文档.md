# BOS 燃油交易管理 API 文档

## 概述

燃油交易管理模块提供完整的燃油交易生命周期管理功能，包括交易创建、查询、状态管理、订单关联等核心功能。

**Base URL**: `/api/v1`

## 认证

所有API请求都需要适当的认证。

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 错误响应
```json
{
  "code": "ERROR_CODE",
  "message": "错误描述",
  "detail": "详细错误信息"
}
```

## API 接口列表

### 1. 燃油交易基础管理

#### 1.1 获取燃油交易列表
- **接口**: `GET /fuel-transactions`
- **描述**: 获取燃油交易列表，支持分页和多种过滤条件
- **查询参数**:
  - `station_id` (int): 加油站ID
  - `status` (string): 交易状态 (pending, processed, cancelled)
  - `pump_id` (string): 油枪ID
  - `nozzle_id` (string): 喷枪ID
  - `member_id` (int): 会员ID
  - `date_from` (string): 开始日期 (格式: 2006-01-02)
  - `date_to` (string): 结束日期 (格式: 2006-01-02)
  - `transaction_number` (string): 交易编号
  - `fuel_type` (string): 燃油类型
  - `fuel_grade` (string): 燃油等级
  - `tank` (int): 油罐编号
  - `page` (int): 页码，默认为1
  - `limit` (int): 每页数量，默认为10
  - `sort_by` (string): 排序字段，默认为created_at
  - `sort_dir` (string): 排序方向 (asc, desc)，默认为desc

**成功响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "id": 1,
        "transaction_number": "TXN202401150001",
        "station_id": 1,
        "pump_id": "PUMP01",
        "nozzle_id": "NOZZLE01",
        "fuel_type": "GASOLINE",
        "fuel_grade": "95",
        "tank": 1,
        "unit_price": 7.85,
        "volume": 50.00,
        "amount": 392.50,
        "total_volume": 50.00,
        "total_amount": 392.50,
        "member_card_id": "CARD001",
        "member_id": 123,
        "employee_id": 456,
        "status": "processed",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 10,
    "total_page": 10
  }
}
```

#### 1.2 创建燃油交易
- **接口**: `POST /fuel-transactions`
- **描述**: 创建新的燃油交易记录
- **请求体**:
```json
{
  "transaction_number": "TXN202401150001",
  "station_id": 1,
  "pump_id": "PUMP01",
  "nozzle_id": "NOZZLE01",
  "fuel_type": "GASOLINE",
  "fuel_grade": "95",
  "tank": 1,
  "unit_price": 7.85,
  "volume": 50.00,
  "amount": 392.50,
  "total_volume": 50.00,
  "total_amount": 392.50,
  "member_card_id": "CARD001",
  "member_id": 123,
  "employee_id": 456,
  "fcc_transaction_id": "FCC123456",
  "pos_terminal_id": "POS001",
  "start_totalizer": 1000.00,
  "end_totalizer": 1050.00,
  "nozzle_start_time": "2024-01-15T10:25:00Z",
  "nozzle_end_time": "2024-01-15T10:30:00Z",
  "metadata": {
    "driver_license": "12345678",
    "vehicle_number": "京A12345"
  }
}
```

**成功响应**:
```json
{
  "code": 201,
  "message": "燃油交易创建成功",
  "data": {
    "id": 1,
    "transaction_number": "TXN202401150001",
    "status": "pending",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

#### 1.3 获取燃油交易详情
- **接口**: `GET /fuel-transactions/{id}`
- **描述**: 根据ID获取燃油交易详情
- **路径参数**:
  - `id` (int): 燃油交易ID

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "transaction_number": "TXN202401150001",
    "station_id": 1,
    "pump_id": "PUMP01",
    "nozzle_id": "NOZZLE01",
    "fuel_type": "GASOLINE",
    "fuel_grade": "95",
    "tank": 1,
    "unit_price": 7.85,
    "volume": 50.00,
    "amount": 392.50,
    "total_volume": 50.00,
    "total_amount": 392.50,
    "member_card_id": "CARD001",
    "member_id": 123,
    "employee_id": 456,
    "fcc_transaction_id": "FCC123456",
    "pos_terminal_id": "POS001",
    "start_totalizer": 1000.00,
    "end_totalizer": 1050.00,
    "nozzle_start_time": "2024-01-15T10:25:00Z",
    "nozzle_end_time": "2024-01-15T10:30:00Z",
    "status": "processed",
    "metadata": {
      "driver_license": "12345678",
      "vehicle_number": "京A12345"
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:35:00Z"
  }
}
```

### 2. 燃油交易与订单关联管理

#### 2.1 关联燃油交易到订单
- **接口**: `POST /fuel-transactions/{id}/link`
- **描述**: 创建燃油交易与订单的关联关系
- **路径参数**:
  - `id` (int): 燃油交易ID
- **请求体**:
```json
{
  "order_id": 789,
  "allocated_amount": 392.50
}
```

**成功响应**:
```json
{
  "code": 200,
  "message": "关联成功",
  "data": {
    "fuel_transaction_id": 1,
    "order_id": 789,
    "allocated_amount": 392.50,
    "created_at": "2024-01-15T10:40:00Z"
  }
}
```

#### 2.2 解除燃油交易与订单关联
- **接口**: `DELETE /fuel-transactions/{id}/unlink/{order_id}`
- **描述**: 解除燃油交易与订单的关联关系
- **路径参数**:
  - `id` (int): 燃油交易ID
  - `order_id` (int): 订单ID

**成功响应**:
```json
{
  "code": 200,
  "message": "解除关联成功"
}
```

#### 2.3 获取燃油交易关联的订单
- **接口**: `GET /fuel-transactions/{id}/orders`
- **描述**: 获取指定燃油交易关联的所有订单
- **路径参数**:
  - `id` (int): 燃油交易ID

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "order_id": 789,
      "allocated_amount": 392.50,
      "order_status": "completed",
      "created_at": "2024-01-15T10:40:00Z"
    }
  ]
}
```

#### 2.4 获取订单关联的燃油交易
- **接口**: `GET /orders/{order_id}/fuel-transactions`
- **描述**: 获取指定订单关联的所有燃油交易
- **路径参数**:
  - `order_id` (int): 订单ID

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "fuel_transaction_id": 1,
      "transaction_number": "TXN202401150001",
      "allocated_amount": 392.50,
      "fuel_type": "GASOLINE",
      "fuel_grade": "95",
      "volume": 50.00,
      "unit_price": 7.85,
      "pump_id": "PUMP01",
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### 2.5 更新关联分配金额
- **接口**: `PUT /fuel-transactions/{id}/link/{order_id}`
- **描述**: 更新燃油交易与订单关联的分配金额
- **路径参数**:
  - `id` (int): 燃油交易ID
  - `order_id` (int): 订单ID
- **请求体**:
```json
{
  "allocated_amount": 300.00
}
```

**成功响应**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "fuel_transaction_id": 1,
    "order_id": 789,
    "allocated_amount": 300.00,
    "updated_at": "2024-01-15T11:00:00Z"
  }
}
```

### 3. 燃油交易状态管理

#### 3.1 确认燃油交易关联
- **接口**: `POST /fuel-transactions/{id}/confirm/{order_id}`
- **描述**: 确认燃油交易与订单的关联关系
- **路径参数**:
  - `id` (int): 燃油交易ID
  - `order_id` (int): 订单ID

**成功响应**:
```json
{
  "code": 200,
  "message": "确认成功",
  "data": {
    "fuel_transaction_id": 1,
    "order_id": 789,
    "status": "confirmed",
    "confirmed_at": "2024-01-15T11:05:00Z"
  }
}
```

#### 3.2 取消燃油交易关联
- **接口**: `POST /fuel-transactions/{id}/cancel/{order_id}`
- **描述**: 取消燃油交易与订单的关联关系
- **路径参数**:
  - `id` (int): 燃油交易ID
  - `order_id` (int): 订单ID

**成功响应**:
```json
{
  "code": 200,
  "message": "取消成功",
  "data": {
    "fuel_transaction_id": 1,
    "order_id": 789,
    "status": "cancelled",
    "cancelled_at": "2024-01-15T11:10:00Z"
  }
}
```

#### 3.3 清理燃油交易关联
- **接口**: `POST /fuel-transactions/{id}/cleanup`
- **描述**: 清理燃油交易的所有关联关系
- **路径参数**:
  - `id` (int): 燃油交易ID

**成功响应**:
```json
{
  "code": 200,
  "message": "清理成功",
  "data": {
    "fuel_transaction_id": 1,
    "cleaned_links_count": 3,
    "cleaned_at": "2024-01-15T11:15:00Z"
  }
}
```

#### 3.4 处理燃油交易状态
- **接口**: `POST /fuel-transactions/{id}/process`
- **描述**: 处理燃油交易状态变更
- **路径参数**:
  - `id` (int): 燃油交易ID
- **请求体**:
```json
{
  "action": "process",
  "operator_id": 456,
  "remarks": "正常处理"
}
```

**成功响应**:
```json
{
  "code": 200,
  "message": "处理成功",
  "data": {
    "fuel_transaction_id": 1,
    "old_status": "pending",
    "new_status": "processed",
    "processed_at": "2024-01-15T11:20:00Z",
    "operator_id": 456
  }
}
```

## 数据模型

### FuelTransaction (燃油交易)
```json
{
  "id": "int64",
  "transaction_number": "string",
  "station_id": "int64",
  "pump_id": "string",
  "nozzle_id": "string",
  "fuel_type": "string",
  "fuel_grade": "string",
  "tank": "int",
  "unit_price": "float64",
  "volume": "float64",
  "amount": "float64",
  "total_volume": "float64",
  "total_amount": "float64",
  "member_card_id": "string",
  "member_id": "int64",
  "employee_id": "int64",
  "fcc_transaction_id": "string",
  "pos_terminal_id": "string",
  "start_totalizer": "float64",
  "end_totalizer": "float64",
  "nozzle_start_time": "datetime",
  "nozzle_end_time": "datetime",
  "status": "string",
  "metadata": "object",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### FuelTransactionOrderLink (燃油交易订单关联)
```json
{
  "fuel_transaction_id": "int64",
  "order_id": "int64",
  "allocated_amount": "float64",
  "status": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

## 状态码说明

### 燃油交易状态 (status)
- `pending`: 待处理
- `processing`: 处理中
- `processed`: 已处理
- `cancelled`: 已取消
- `failed`: 处理失败

### 关联状态 (link status)
- `linked`: 已关联
- `confirmed`: 已确认
- `cancelled`: 已取消

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| FUEL_TRANSACTION_NOT_FOUND | 燃油交易不存在 |
| INVALID_TRANSACTION_STATUS | 无效的交易状态 |
| LINK_ALREADY_EXISTS | 关联关系已存在 |
| LINK_NOT_FOUND | 关联关系不存在 |
| INSUFFICIENT_AMOUNT | 金额不足 |
| TRANSACTION_ALREADY_PROCESSED | 交易已处理 |
| INVALID_PUMP_ID | 无效的油枪ID |
| INVALID_MEMBER_ID | 无效的会员ID |

## 使用示例

### 创建燃油交易并关联订单的完整流程

1. **创建燃油交易**
```bash
curl -X POST /api/v1/fuel-transactions \
  -H "Content-Type: application/json" \
  -d '{
    "transaction_number": "TXN202401150001",
    "station_id": 1,
    "pump_id": "PUMP01",
    "fuel_type": "GASOLINE",
    "fuel_grade": "95",
    "volume": 50.00,
    "amount": 392.50
  }'
```

2. **关联到订单**
```bash
curl -X POST /api/v1/fuel-transactions/1/link \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": 789,
    "allocated_amount": 392.50
  }'
```

3. **确认关联**
```bash
curl -X POST /api/v1/fuel-transactions/1/confirm/789
```

### 查询特定条件的燃油交易

```bash
curl -G /api/v1/fuel-transactions \
  -d station_id=1 \
  -d fuel_type=GASOLINE \
  -d date_from=2024-01-15 \
  -d date_to=2024-01-15 \
  -d page=1 \
  -d limit=20
```

## 注意事项

1. **并发安全**: 系统在处理燃油交易与订单关联时实现了并发锁机制，防止重复关联
2. **状态一致性**: 燃油交易状态变更会触发相关联订单状态的同步更新
3. **数据完整性**: 删除关联关系前会检查相关订单的状态，确保业务数据完整性
4. **审计日志**: 所有燃油交易的状态变更和关联操作都会记录详细的审计日志
5. **事务处理**: 关键操作使用数据库事务确保数据一致性 