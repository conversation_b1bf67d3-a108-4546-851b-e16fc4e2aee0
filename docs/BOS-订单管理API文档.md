# BOS订单管理API文档

## 文档信息

- **文档版本**: v2.1
- **创建时间**: 2025年1月22日
- **API版本**: v1
- **基础URL**: `/api/v1`
- **内容类型**: `application/json`

## 概述

BOS（Business Operation System）订单管理系统提供完整的订单生命周期管理功能，包括订单创建、查询、修改、取消等核心业务功能。

### 主要特性

- **基于燃油交易的订单创建**: 订单与燃油交易关联
- **自动促销计算**: 集成促销服务，自动应用优惠活动
- **多支付方式支持**: 支持现金、银行卡、移动支付等
- **终端设备追踪**: 支持记录下单终端设备信息
- **员工信息关联**: 自动获取并返回操作员工姓名
- **班次状态检查**: 确保在开班状态下才能下单

## 核心接口

### 1. 创建订单

**接口信息**
- **路径**: `POST /orders`
- **描述**: 基于燃油交易创建新订单

**请求参数**

```json
{
  "fuel_transaction_id": 123456,
  "station_id": 101,
  "customer_id": 1001,
  "customer_name": "张三",
  "employee_no": "EMP001",
  "terminal_id": "POS001",
  "payment_type": "BANK_CARD",
  "payment_method": 1,
  "allocated_amount": 392.50,
  "metadata": {
    "vehicle_number": "京A12345",
    "source": "pos"
  }
}
```

**参数说明**

| 参数名 | 类型 | 必填 | 描述 | 约束 |
|--------|------|------|------|------|
| fuel_transaction_id | int64 | 是 | 燃油交易ID | 必须是有效的燃油交易ID |
| station_id | int64 | 是 | 加油站ID | 必须是有效的加油站ID |
| customer_id | int64 | 否 | 客户ID | 会员客户必填 |
| customer_name | string | 否 | 客户姓名 | 最大长度100字符 |
| employee_no | string | 否 | 员工编号 | 最大长度50字符 |
| terminal_id | string | 否 | 终端设备ID | 最大长度50字符，存储到燃油交易的pos_terminal_id字段 |
| payment_type | string | 是 | 支付类型 | CASH/BANK_CARD/WECHAT/ALIPAY等 |
| payment_method | int64 | 是 | 具体支付方式ID | 必须是有效的支付方式ID |
| allocated_amount | float64 | 否 | 分配金额 | 不得超过燃油交易可用金额 |
| metadata | object | 否 | 订单元数据 | 自定义键值对 |

**成功响应** (HTTP 200)

```json
{
  "id": 789,
  "order_number": "ORD202501220001",
  "fuel_transaction_id": 123456,
  "station_id": 101,
  "customer_id": 1001,
  "customer_name": "张三",
  "employee_no": "EMP001",
  "terminal_id": "POS001",
  "staff_name": "李四",
  "status": "completed",
  "total_amount": 392.50,
  "discount_amount": 0.00,
  "final_amount": 392.50,
  "paid_amount": 392.50,
  "tax_amount": 0.00,
  "payment_type": "BANK_CARD",
  "payment_method": 1,
  "payment_status": "paid",
  "items": [
    {
      "id": 1,
      "product_id": 0,
      "product_name": "92#汽油",
      "product_type": "fuel",
      "quantity": 54.29,
      "unit_price": 7.23,
      "total_price": 392.50,
      "fuel_grade": "92#",
      "pump_id": "P01"
    }
  ],
  "promotions": [],
  "payments": [
    {
      "id": 1,
      "payment_method": 1,
      "payment_type": "BANK_CARD",
      "amount": 392.50,
      "status": "completed",
      "transaction_id": "PAY202501220001",
      "created_at": "2025-01-22T10:30:00Z"
    }
  ],
  "metadata": {
    "vehicle_number": "京A12345",
    "source": "pos",
    "terminal_id_updated": true,
    "shift_check_passed": true
  },
  "created_at": "2025-01-22T10:30:00Z",
  "updated_at": "2025-01-22T10:30:00Z",
  "completed_at": "2025-01-22T10:30:00Z"
}
```

**业务规则**

1. **班次状态检查**: 下单前检查当前站点是否处于开班状态
2. **终端ID记录**: 如果提供terminal_id，会自动更新到对应燃油交易的pos_terminal_id字段
3. **员工姓名获取**: 通过employee_no自动关联获取员工姓名
4. **促销自动计算**: 系统自动调用促销服务计算可用优惠
5. **并发控制**: 使用燃油交易ID作为锁，防止重复创建

### 2. 获取订单详情

**接口信息**
- **路径**: `GET /orders/{id}`
- **描述**: 获取指定订单的详细信息

**路径参数**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | int64 | 是 | 订单ID |

**成功响应** (HTTP 200)

返回完整的订单信息，格式与创建订单响应相同，包含：
- 订单基本信息
- 员工姓名（staff_name字段）
- 终端ID信息（通过关联的燃油交易获取）
- 订单项列表
- 促销信息
- 支付记录

### 3. 获取订单列表

**接口信息**
- **路径**: `GET /orders`
- **描述**: 分页查询订单列表

**查询参数**

| 参数名 | 类型 | 必填 | 描述 | 默认值 |
|--------|------|------|------|--------|
| customer_id | int64 | 否 | 客户ID | - |
| station_id | int64 | 否 | 加油站ID | - |
| status | string | 否 | 订单状态 | - |
| date_from | string | 否 | 开始日期(YYYY-MM-DD) | - |
| date_to | string | 否 | 结束日期(YYYY-MM-DD) | - |
| order_number | string | 否 | 订单编号 | - |
| employee_no | string | 否 | 员工编号 | - |
| page | int | 否 | 页码 | 1 |
| limit | int | 否 | 每页数量 | 10 |
| sort_by | string | 否 | 排序字段 | created_at |
| sort_dir | string | 否 | 排序方向 | desc |

**成功响应** (HTTP 200)

```json
{
  "items": [
    {
      "id": 789,
      "order_number": "ORD202501220001",
      "station_id": 101,
      "customer_id": 1001,
      "customer_name": "张三",
      "employee_no": "EMP001",
      "staff_name": "李四",
      "status": "completed",
      "total_amount": 392.50,
      "final_amount": 392.50,
      "payment_type": "BANK_CARD",
      "payment_status": "paid",
      "created_at": "2025-01-22T10:30:00Z",
      "updated_at": "2025-01-22T10:30:00Z"
    }
  ],
  "total": 150,
  "page": 1,
  "page_size": 10,
  "total_page": 15
}
```

## 数据结构定义

### 订单状态枚举

| 状态值 | 描述 | 说明 |
|--------|------|------|
| new | 新建 | 订单刚创建，未处理 |
| processing | 处理中 | 订单正在处理 |
| completed | 已完成 | 订单已完成所有流程 |
| cancelled | 已取消 | 订单被取消 |

### 支付类型枚举

| 支付类型 | 描述 | 备注 |
|----------|------|------|
| CASH | 现金支付 | 传统现金支付方式 |
| BANK_CARD | 银行卡支付 | 借记卡、信用卡支付 |
| WECHAT | 微信支付 | 微信扫码或NFC支付 |
| ALIPAY | 支付宝支付 | 支付宝扫码或NFC支付 |
| POINTS | 积分支付 | 使用会员积分支付 |
| COUPON | 优惠券支付 | 使用优惠券抵扣 |
| MIXED | 混合支付 | 多种支付方式组合 |

### 订单对象结构

```json
{
  "id": "订单ID (int64)",
  "order_number": "订单编号 (string)",
  "fuel_transaction_id": "燃油交易ID (int64)",
  "station_id": "加油站ID (int64)",
  "customer_id": "客户ID (int64, 可选)",
  "customer_name": "客户姓名 (string, 可选)",
  "employee_no": "员工编号 (string, 可选)",
  "terminal_id": "终端ID (string, 可选) - 从关联的燃油交易获取",
  "staff_name": "员工姓名 (string, 可选) - 通过employee_no关联core_schema.users表获取",
  "status": "订单状态 (string)",
  "total_amount": "订单总金额 (float64)",
  "discount_amount": "折扣金额 (float64)",
  "final_amount": "最终金额 (float64)",
  "tax_amount": "税额 (float64)",
  "paid_amount": "已支付金额 (float64)",
  "payment_type": "支付类型 (string)",
  "payment_method": "支付方式ID (int64)",
  "payment_status": "支付状态 (string)",
  "items": "订单项列表 (array)",
  "promotions": "促销列表 (array)",
  "payments": "支付记录列表 (array)",
  "metadata": "元数据 (object)",
  "created_at": "创建时间 (ISO 8601)",
  "updated_at": "更新时间 (ISO 8601)",
  "completed_at": "完成时间 (ISO 8601, 可选)",
  "cancelled_at": "取消时间 (ISO 8601, 可选)"
}
```

## 错误处理

### 常见错误码

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| VALIDATION_ERROR | 400 | 请求参数验证失败 | 检查请求参数格式和必填项 |
| NOT_FOUND | 404 | 资源不存在 | 确认资源ID是否正确 |
| FORBIDDEN | 403 | 操作被禁止 | 检查班次状态或操作权限 |
| SHIFT_NOT_ACTIVE | 403 | 班次未开启 | 请先开启班次后再进行下单操作 |
| INSUFFICIENT_AMOUNT | 400 | 燃油交易可用金额不足 | 检查分配金额是否超限 |
| DUPLICATE_ORDER | 409 | 重复创建订单 | 检查是否已存在相同燃油交易的订单 |
| INTERNAL_ERROR | 500 | 内部服务器错误 | 联系技术支持 |

### 标准错误响应格式

```json
{
  "code": "错误码",
  "message": "错误描述信息",
  "detail": "详细错误说明",
  "timestamp": "错误发生时间",
  "request_id": "请求追踪ID"
}
```

## 技术说明

### 员工姓名获取逻辑

系统通过以下优先级顺序获取员工姓名：

1. **优先路径**: 燃油交易表的 `staff_card_id` → 员工卡表(`staff_cards`) → 用户表(`core_schema.users.full_name`)
2. **兼容路径**: 燃油交易表的 `employee_id` → 员工卡表(`staff_cards`) → 用户表(`core_schema.users.full_name`)
3. **兜底路径**: 订单表的 `employee_no` → 用户表(`core_schema.users.username`)

### 终端ID存储机制

- **存储位置**: `fuel_transactions` 表的 `pos_terminal_id` 字段
- **更新时机**: 下单时如果请求包含 `terminal_id`，立即更新对应燃油交易记录
- **业务价值**: 便于追踪订单来源终端，支持业务分析和审计

### 班次状态检查

- **检查时机**: 下单请求的参数验证之后，业务逻辑处理之前
- **检查逻辑**: 调用 `ShiftService.GetCurrentShift()` 检查当前站点是否有活跃班次
- **失败处理**: 如果没有活跃班次，返回HTTP 403错误，提示需要先开启班次

---

**文档维护说明**
- 本文档随代码变更同步更新
- 版本: v2.1 (2025年1月22日)
- 主要更新: 添加terminal_id和staff_name字段说明，完善班次检查逻辑
