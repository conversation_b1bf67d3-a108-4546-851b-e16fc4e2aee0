# End of Day Report API 实现说明

## 概述

本文档说明了 End of Day Report API 的实现，该接口用于生成指定站点和日期的日终报表数据，整合当日所有班次的员工加油情况、销售统计和支付数据。

## 实现架构

### 1. 分层架构

```
Controller Layer (Handler)
    ↓
Service Layer (Business Logic)
    ↓
Repository Layer (Data Access)
    ↓
Database Layer (PostgreSQL)
```

### 2. 核心组件

#### 2.1 Handler Layer
- **文件**: `bos/internal/api/handler/end_of_day_report_handler.go`
- **职责**: 处理HTTP请求，参数验证，响应格式化
- **主要方法**: `GetEndOfDayReport`

#### 2.2 Service Layer
- **接口**: `bos/internal/service/end_of_day_report_service.go`
- **实现**: `bos/internal/service/end_of_day_report_service_impl.go`
- **职责**: 业务逻辑处理，数据聚合，班次时间范围计算

#### 2.3 Repository Layer
- **接口**: `bos/internal/repository/end_of_day_report_repository.go`
- **实现**: `bos/internal/repository/postgres/end_of_day_report_repository.go`
- **职责**: 数据访问，SQL查询，跨schema查询处理

#### 2.4 Types Layer
- **文件**: `bos/internal/types/end_of_day_report.go`
- **职责**: 数据类型定义，请求/响应结构体

## 核心功能

### 1. 动态班次时间范围计算

系统会根据以下规则动态调整班次时间范围：

- **规则1**: 如果0点到第一个班次开班时间之间有交易，创建虚拟早班
- **规则2**: 如果最后一个班次结班时间到24点之间有交易，创建虚拟晚班
- **规则3**: 如果没有交易，扩展相邻班次的时间范围

### 2. 多维度数据聚合

- **员工维度**: 按员工聚合燃油销售、支付数据、其他收入
- **班次维度**: 按班次汇总所有员工数据
- **日期维度**: 按日期汇总所有班次数据

### 3. 多条件筛选

支持以下筛选条件：
- 员工姓名（模糊匹配）
- 油品等级
- 支付方式

### 4. 跨Schema查询

系统使用MultiDatabase模式处理跨schema查询：
- `order_schema`: 班次、交易、订单数据
- `core_schema`: 用户、站点信息
- `payment_schema`: 支付方式配置

## 数据库查询设计

### 1. 班次信息查询

```sql
SELECT 
    id, shift_number, station_id, start_time, end_time,
    CASE WHEN end_time IS NULL THEN 'active' ELSE 'closed' END as status
FROM shifts 
WHERE station_id = $1 
  AND DATE(start_time) <= $2 
  AND (end_time IS NULL OR DATE(end_time) >= $2)
  AND deleted_at IS NULL
ORDER BY start_time;
```

### 2. 交易数据查询

```sql
SELECT 
    ft.id, ft.transaction_number, ft.station_id, ft.volume, ft.amount,
    ft.unit_price, ft.fuel_grade, ft.fuel_type, ft.employee_id, ft.staff_card_id,
    op.id as payment_id, op.payment_method, op.amount as payment_amount,
    op.completed_at as payment_time, op.status as payment_status
FROM fuel_transactions ft
INNER JOIN orders o ON ft.id = ANY(
    SELECT fuel_transaction_id 
    FROM fuel_transaction_order_links 
    WHERE order_id = o.id AND status = 'active'
)
INNER JOIN order_payments op ON o.id = op.order_id
WHERE ft.station_id = $1
  AND DATE(op.completed_at) = $2
  AND op.status = 'completed'
  AND ft.deleted_at IS NULL
  AND o.deleted_at IS NULL
ORDER BY op.completed_at, ft.id;
```

### 3. 其他收入查询

```sql
SELECT 
    oi.id, oi.order_id, oi.product_type, oi.product_name,
    oi.quantity, oi.unit_price, oi.total_price as total_amount,
    o.employee_no as employee_id, o.staff_card_id,
    op.completed_at as payment_time, op.payment_method
FROM order_items oi
INNER JOIN orders o ON oi.order_id = o.id
INNER JOIN order_payments op ON o.id = op.order_id
WHERE o.station_id = $1
  AND DATE(op.completed_at) = $2
  AND op.status = 'completed'
  AND oi.product_type IN ('nitrogen', 'other')
  AND oi.deleted_at IS NULL
  AND o.deleted_at IS NULL
ORDER BY op.completed_at, oi.id;
```

## 使用方法

### 1. 初始化组件

```go
// 创建数据库连接
multiDB := database.NewMultiDatabase(orderDBConfig, coreDBConfig, paymentDBConfig)
if err := multiDB.Connect(ctx); err != nil {
    log.Fatal(err)
}

// 创建repository
repo := postgres.NewEndOfDayReportRepository(multiDB)

// 创建service
service := service.NewEndOfDayReportService(repo)

// 创建handler
handler := handlers.NewEndOfDayReportHandler(service)
```

### 2. 注册路由

```go
api := e.Group("/api/v1")
reports := api.Group("/reports")
reports.GET("/end-of-day", handler.GetEndOfDayReport)
```

### 3. API调用示例

```bash
# 基本查询
curl "http://localhost:8080/api/v1/reports/end-of-day?station_id=1&date=2024-01-04"

# 按员工筛选
curl "http://localhost:8080/api/v1/reports/end-of-day?station_id=1&date=2024-01-04&attendant_name=Jayson"

# 按油品等级筛选
curl "http://localhost:8080/api/v1/reports/end-of-day?station_id=1&date=2024-01-04&fuel_grade=BP%20Ultimate"

# 按支付方式筛选
curl "http://localhost:8080/api/v1/reports/end-of-day?station_id=1&date=2024-01-04&payment_method=cash"

# 多条件筛选
curl "http://localhost:8080/api/v1/reports/end-of-day?station_id=1&date=2024-01-04&attendant_name=Jay&fuel_grade=BP%20Ultimate&payment_method=cash"
```

## 响应格式

### 成功响应

```json
{
  "success": true,
  "message": "日终报表获取成功",
  "data": {
    "report_header": {
      "company_name": "PT ANEKA PETROINDO RAYA",
      "report_type": "END OF DAY REPORT",
      "station_id": 1,
      "station_name": "Meruya Ilir Station",
      "site_code": "JK001",
      "report_date": "2024-01-04",
      "generated_at": "2024-01-05T08:30:00+07:00"
    },
    "shifts": [
      {
        "shift_info": {
          "shift_number": 1,
          "shift_name": "Shift 1",
          "start_time": "2024-01-04T00:00:00+07:00",
          "end_time": "2024-01-04T08:00:00+07:00",
          "status": "closed",
          "time_range": "00:00-07:59"
        },
        "attendants": [...],
        "shift_summary": {...}
      }
    ],
    "daily_summary": {...}
  },
  "meta": {
    "generated_at": "2024-01-05T08:30:00+07:00",
    "processing_time_ms": 1250,
    "data_source": "aggregated",
    "version": "1.0",
    "query_params": {
      "station_id": 1,
      "date": "2024-01-04",
      "filters_applied": []
    }
  }
}
```

### 错误响应

```json
{
  "success": false,
  "message": "获取日终报表失败",
  "error": {
    "code": "STATION_NOT_FOUND",
    "message": "站点不存在",
    "detail": "Station with ID 1 not found"
  }
}
```

## 错误处理

### 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| INVALID_STATION_ID | 400 | 站点ID格式无效 |
| STATION_NOT_FOUND | 404 | 站点不存在 |
| INVALID_DATE_FORMAT | 400 | 日期格式无效 |
| DATE_OUT_OF_RANGE | 400 | 日期超出查询范围 |
| NO_DATA_FOUND | 404 | 指定日期无数据 |
| INTERNAL_ERROR | 500 | 内部服务器错误 |

## 性能考虑

### 1. 数据库优化

建议创建以下索引：

```sql
-- 支付时间索引
CREATE INDEX idx_order_payments_payment_time_station 
ON order_payments(completed_at, station_id) 
WHERE status = 'completed';

-- 燃油交易索引
CREATE INDEX idx_fuel_transactions_station_employee 
ON fuel_transactions(station_id, employee_id, created_at);

-- 班次时间范围索引
CREATE INDEX idx_shifts_station_time_range 
ON shifts(station_id, start_time, end_time) 
WHERE end_time IS NOT NULL AND deleted_at IS NULL;
```

### 2. 查询优化

- 使用连表查询减少数据库往返次数
- 按时间范围筛选数据
- 使用适当的索引提高查询性能

### 3. 内存优化

- 使用流式处理处理大量数据
- 及时释放不需要的对象
- 使用合适的数据结构减少内存占用

## 扩展建议

### 1. 缓存策略

- 对历史数据实施缓存策略
- 使用Redis缓存热点数据
- 实现缓存失效机制

### 2. 异步处理

- 对于复杂查询，考虑使用异步处理
- 实现任务队列处理大量报表请求
- 提供查询状态接口

### 3. 数据压缩

- 对响应数据进行压缩
- 使用分页减少单次传输数据量
- 实现数据流式传输

## 测试

### 1. 单元测试

```go
func TestEndOfDayReportService_GetEndOfDayReport(t *testing.T) {
    // 测试正常情况
    // 测试无数据情况
    // 测试筛选条件
    // 测试错误处理
}
```

### 2. 集成测试

```go
func TestEndOfDayReportAPI_Integration(t *testing.T) {
    // 测试完整的API调用流程
    // 测试数据库连接
    // 测试跨schema查询
}
```

## 部署说明

### 1. 环境配置

```bash
export DB_HOST=*************
export DB_PORT=5432
export DB_USER=postgres
export DB_PASSWORD=zB3!lL9+wJ
export DB_NAME=bos_db
export ORDER_SCHEMA=order_schema
export CORE_SCHEMA=core_schema
export PAYMENT_SCHEMA=payment_schema
```

### 2. 启动服务

```bash
go run examples/end_of_day_report_example.go
```

### 3. 健康检查

```bash
curl http://localhost:8080/health
```

## 总结

End of Day Report API 提供了完整的日终报表功能，具有以下特点：

- **灵活的班次划分**: 支持动态班次时间范围调整
- **准确的数据统计**: 以payment_time为准确保数据一致性
- **多维度筛选**: 支持员工、油品、支付方式等多种筛选条件
- **完整的数据汇总**: 提供员工、班次、日期三个层级的数据汇总
- **跨schema查询**: 支持多数据库schema的数据整合
- **良好的扩展性**: 模块化设计，易于维护和扩展

该实现遵循了接口文档的所有要求，并提供了完整的错误处理和性能优化建议。 