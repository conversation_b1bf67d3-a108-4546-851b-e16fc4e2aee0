# Dashboard API 实现说明

## 概述

本文档说明了在BOS项目中实现的Dashboard API接口，包括实现细节、使用方法和测试指南。

## 实现架构

### 分层架构

```
HTTP请求 → Router → Handler → Service → Repository → Database
```

### 核心组件

1. **Repository层** (`internal/repository/dashboard_repository.go`)
   - 数据访问层，负责数据库查询
   - 实现数据聚合和计算
   - 处理日期范围查询

2. **Service层** (`internal/service/dashboard_service.go`)
   - 业务逻辑层，负责参数验证和业务处理
   - 日期解析和验证
   - 变化率计算

3. **Handler层** (`internal/api/handlers/dashboard_handler.go`)
   - HTTP接口层，负责请求处理和响应封装
   - 参数解析和错误处理
   - 统一响应格式

4. **Router层** (`internal/api/router/router.go`)
   - 路由注册和管理
   - 中间件集成

## 实现的接口

### 1. Dashboard汇总数据接口

**路径**: `GET /api/v1/dashboard/summary`

**功能**: 获取指定日期的站点运营汇总数据

**参数**:
- `station_id` (可选): 站点ID
- `date` (可选): 查询日期，格式YYYY-MM-DD

**响应结构**:
```json
{
  "success": true,
  "message": "Dashboard汇总数据获取成功",
  "data": {
    "query_date": "2025-01-27",
    "today_revenue": 618810000,
    "revenue_change": 8.3,
    "today_transactions": 287,
    "transaction_change": 5.2,
    "today_volume": 38800,
    "volume_change": 12.1,
    "fuel_sales_mix": [...]
  },
  "meta": {...}
}
```

### 2. 销售趋势数据接口

**路径**: `GET /api/v1/dashboard/sales-trend`

**功能**: 获取指定日期向前N天的销售趋势数据

**参数**:
- `station_id` (可选): 站点ID
- `date` (可选): 结束日期，格式YYYY-MM-DD
- `days` (可选): 查询天数，默认7天

### 3. 燃油销售组合接口

**路径**: `GET /api/v1/dashboard/fuel-sales-mix`

**功能**: 获取指定日期的燃油销售组合详细数据

**参数**:
- `station_id` (可选): 站点ID
- `date` (可选): 查询日期，格式YYYY-MM-DD

## 数据库查询策略

### 核心查询逻辑

1. **汇总数据查询**:
   ```sql
   SELECT 
       COALESCE(SUM(amount), 0) as today_revenue,
       COUNT(*) as today_transactions,
       COALESCE(SUM(volume), 0) as today_volume
   FROM fuel_transactions 
   WHERE station_id = $1 
     AND DATE(created_at) = $2
     AND status = 'processed'
   ```

2. **趋势数据查询**:
   ```sql
   SELECT 
       DATE(created_at) as date,
       COALESCE(SUM(amount), 0) as revenue,
       COUNT(*) as transactions,
       COALESCE(SUM(volume), 0) as volume
   FROM fuel_transactions 
   WHERE station_id = $1 
     AND DATE(created_at) >= $2
     AND DATE(created_at) <= $3
     AND status = 'processed'
   GROUP BY DATE(created_at)
   ORDER BY DATE(created_at)
   ```

3. **燃油组合查询**:
   ```sql
   SELECT 
       fuel_type,
       COALESCE(SUM(volume), 0) as volume,
       COALESCE(SUM(amount), 0) as revenue,
       COUNT(*) as transactions,
       COALESCE(AVG(unit_price), 0) as avg_price_per_liter
   FROM fuel_transactions 
   WHERE station_id = $1 
     AND DATE(created_at) = $2
     AND status = 'processed'
   GROUP BY fuel_type
   ORDER BY revenue DESC
   ```

## 关键特性

### 1. 日期参数支持
- 支持 `YYYY-MM-DD` 格式的日期查询
- 默认查询当天数据（Asia/Jakarta时区）
- 支持历史数据查询（最多90天）

### 2. 变化率计算
- 自动计算收入、交易数、销量的日环比变化率
- 处理边界情况（前一天无数据时）
- 保留两位小数精度

### 3. 数据补齐
- 自动补充缺失日期的数据（填充0值）
- 确保趋势数据的连续性

### 4. 错误处理
- 统一的错误响应格式
- 详细的错误分类和处理
- 友好的错误信息

### 5. 性能监控
- 记录处理时间
- 标识数据源类型
- 支持性能分析

## 使用示例

### cURL测试

```bash
# 获取今天的汇总数据
curl -X GET "http://localhost:8080/api/v1/dashboard/summary" \
  -H "Authorization: Bearer your-token"

# 获取指定日期的汇总数据
curl -X GET "http://localhost:8080/api/v1/dashboard/summary?date=2025-01-27" \
  -H "Authorization: Bearer your-token"

# 获取最近14天的趋势数据
curl -X GET "http://localhost:8080/api/v1/dashboard/sales-trend?days=14" \
  -H "Authorization: Bearer your-token"

# 获取燃油销售组合
curl -X GET "http://localhost:8080/api/v1/dashboard/fuel-sales-mix" \
  -H "Authorization: Bearer your-token"
```

### JavaScript示例

```javascript
// 基础API调用函数
async function callDashboardAPI(endpoint, params = {}) {
  const url = new URL(`/api/v1/dashboard/${endpoint}`, window.location.origin);
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined) {
      url.searchParams.append(key, params[key]);
    }
  });
  
  const response = await fetch(url, {
    headers: {
      'Authorization': `Bearer ${getAuthToken()}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return response.json();
}

// 获取Dashboard数据
async function getDashboardData() {
  try {
    const summary = await callDashboardAPI('summary');
    const trend = await callDashboardAPI('sales-trend', { days: 7 });
    const fuelMix = await callDashboardAPI('fuel-sales-mix');
    
    return {
      summary: summary.data,
      trend: trend.data,
      fuelMix: fuelMix.data
    };
  } catch (error) {
    console.error('获取Dashboard数据失败:', error);
    throw error;
  }
}
```

## 部署说明

### 1. 数据库索引

为了优化查询性能，建议创建以下索引：

```sql
-- 燃油交易表的复合索引
CREATE INDEX idx_fuel_transactions_station_date 
ON fuel_transactions(station_id, created_at);

-- 状态索引
CREATE INDEX idx_fuel_transactions_status 
ON fuel_transactions(status);

-- 燃油类型索引
CREATE INDEX idx_fuel_transactions_fuel_type 
ON fuel_transactions(fuel_type);
```

### 2. 配置要求

- 数据库连接池配置
- 时区设置（Asia/Jakarta）
- 认证中间件配置

### 3. 监控建议

- 监控API响应时间
- 监控数据库查询性能
- 监控错误率和异常情况

## 测试指南

### 1. 单元测试

每个层都应该有相应的单元测试：

- Repository层：测试数据库查询逻辑
- Service层：测试业务逻辑和参数验证
- Handler层：测试HTTP请求处理

### 2. 集成测试

- 测试完整的API调用流程
- 测试不同参数组合
- 测试错误处理场景

### 3. 性能测试

- 测试大数据量查询性能
- 测试并发访问性能
- 测试缓存效果

## 扩展建议

### 1. 缓存策略

- 实现Redis缓存热点数据
- 设置合理的缓存过期时间
- 支持缓存失效机制

### 2. 分页支持

- 为大数据量查询添加分页
- 优化查询性能

### 3. 实时更新

- 考虑WebSocket实时推送
- 实现数据变化通知

### 4. 数据导出

- 支持数据导出功能
- 多种格式支持（CSV、Excel）

## 总结

Dashboard API的实现遵循了良好的分层架构设计，提供了完整的功能和错误处理机制。通过合理的数据库查询策略和性能优化，能够满足实时Dashboard展示的需求。

该实现具有良好的可扩展性和可维护性，为后续功能扩展奠定了坚实的基础。 