# 设备状态实时监控系统技术方案

## 1. 系统概述

### 1.1 业务需求
- 轮询多个FCC（Fuel Control Computer）接口获取设备实时状态
- 内存缓存设备状态（最大60个设备）
- WebSocket实时推送状态变化给终端
- 终端心跳中的班次信息对比和更新

### 1.2 技术特点
- MVP原则，复用现有架构
- 设计模式：策略模式 + 组合模式
- 高频访问，内存缓存，无需持久化
- 支持主接口/health接口降级轮询

## 2. 架构设计

### 2.1 整体架构
```
BOS Main App
├── FCC Manager (新增)
│   ├── FCC Poller Interface (轮询器接口)
│   ├── Device Cache Interface (缓存接口)
│   └── Shift Comparator Interface (对比器接口)
├── WebSocket Handler (新增)
│   ├── Device Status Push
│   └── Token Validation (预留)
└── 现有架构保持不变
```

### 2.2 核心接口设计

#### 2.2.1 FCC轮询器接口
```go
type FCCPoller interface {
    Poll(ctx context.Context, fcc FCCConfig) ([]DeviceStatus, error)
    GetPriority() int // 1=主接口，2=health接口
}

type FCCManager struct {
    fccs    []FCCConfig
    pollers []FCCPoller
    cache   DeviceCache
    fuelTransactionRepo repository.FuelTransactionRepository
}
```

#### 2.2.2 设备缓存接口
```go
type DeviceCache interface {
    Set(deviceID string, status DeviceStatus)
    Get(deviceID string) (DeviceStatus, bool)
    GetAll() map[string]DeviceStatus
    GetByFCC(fccID string) map[string]DeviceStatus
    HasChanged(deviceID string, newStatus DeviceStatus) bool
    MarkFCCOffline(fccID string, deviceIDs []string)
}
```

#### 2.2.3 班次对比器接口
```go
type ShiftComparator interface {
    NeedsUpdate(current, incoming ShiftInfo) bool
}
```

## 3. 数据结构设计

### 3.1 FCC配置
```go
type FCCConfig struct {
    ID         string   `json:"id"`          // FCC标识
    PrimaryURL string   `json:"primary_url"` // 主接口URL
    HealthURL  string   `json:"health_url"`  // health接口URL
    DeviceIDs  []string `json:"device_ids"`  // 该FCC管理的nozzle_id列表
}
```

### 3.2 设备状态
```go
type DeviceStatus struct {
    DeviceID       string    `json:"device_id"`       // nozzle_id (UUID)
    FCCID          string    `json:"fcc_id"`          // 所属FCC
    Status         string    `json:"status"`          // online/offline/busy等
    HasTransaction bool      `json:"has_transaction"` // 是否有待结算交易
    LastUpdated    time.Time `json:"last_updated"`
    Source         string    `json:"source"`          // "api" 或 "health"
}
```

### 3.3 班次信息
```go
type ShiftInfo struct {
    ShiftID   *int64                 `json:"shift_id"`
    StationID int64                  `json:"station_id"`
    Data      map[string]interface{} `json:"data"` // 灵活扩展字段
}
```

## 4. 核心业务逻辑

### 4.1 多FCC轮询逻辑
```go
func (fm *FCCManager) PollAll(ctx context.Context) {
    for _, fcc := range fm.fccs {
        go fm.pollSingleFCC(ctx, fcc) // 并发轮询每个FCC
    }
}

func (fm *FCCManager) pollSingleFCC(ctx context.Context, fcc FCCConfig) {
    var devices []DeviceStatus
    var success bool
    
    // 按优先级尝试轮询器
    for _, poller := range fm.pollers {
        result, err := poller.Poll(ctx, fcc)
        if err == nil {
            devices = result
            success = true
            break
        }
    }
    
    if !success {
        // 该FCC轮询失败，标记其管理的所有设备为offline
        fm.cache.MarkFCCOffline(fcc.ID, fcc.DeviceIDs)
        return
    }
    
    // 批量查询交易状态并更新缓存
    fm.updateDevicesWithTransactionStatus(devices)
}
```

### 4.2 优化的交易状态判断逻辑

基于项目现有逻辑，优化nozzle交易状态判断：

```go
// 批量查询nozzle的待结算交易状态
func (s *DeviceService) batchCheckPendingTransactions(nozzleIDs []string) map[string]bool {
    results := make(map[string]bool)
    
    // 方案1：检查fuel_transaction状态 + 分配状态
    query := `
        WITH nozzle_transactions AS (
            SELECT DISTINCT ON (ft.nozzle_id) 
                ft.nozzle_id,
                ft.id as transaction_id,
                ft.status as transaction_status,
                ft.amount as total_amount
            FROM order_schema.fuel_transactions ft
            WHERE ft.nozzle_id = ANY($1)
            ORDER BY ft.nozzle_id, ft.created_at DESC
        ),
        allocation_status AS (
            SELECT 
                nt.nozzle_id,
                nt.transaction_status,
                nt.total_amount,
                COALESCE(SUM(ftol.allocated_amount), 0) as allocated_amount
            FROM nozzle_transactions nt
            LEFT JOIN order_schema.fuel_transaction_order_links ftol 
                ON nt.transaction_id = ftol.fuel_transaction_id 
                AND ftol.status = 'active'
            GROUP BY nt.nozzle_id, nt.transaction_status, nt.total_amount
        )
        SELECT 
            nozzle_id,
            CASE 
                WHEN transaction_status IN ('pending', 'processing') THEN true
                WHEN transaction_status = 'processed' AND ABS(total_amount - allocated_amount) > 0.01 THEN true
                ELSE false
            END as has_pending_transaction
        FROM allocation_status
    `
    
    var queryResults []struct {
        NozzleID              string `db:"nozzle_id"`
        HasPendingTransaction bool   `db:"has_pending_transaction"`
    }
    
    if err := s.db.Select(&queryResults, query, pq.Array(nozzleIDs)); err != nil {
        log.Printf("查询nozzle交易状态失败: %v", err)
        return results
    }
    
    for _, result := range queryResults {
        results[result.NozzleID] = result.HasPendingTransaction
    }
    
    return results
}
```

### 4.3 WebSocket推送逻辑
```go
type WebSocketHandler struct {
    clients map[string]*websocket.Conn
    mutex   sync.RWMutex
}

func (ws *WebSocketHandler) HandleConnection(c echo.Context) error {
    conn, err := upgrader.Upgrade(c.Response(), c.Request(), nil)
    if err != nil {
        return err
    }
    
    // 预留token验证
    // token := c.QueryParam("token")
    // if !ws.validateToken(token) { return unauthorized }
    
    clientID := uuid.New().String()
    ws.addClient(clientID, conn)
    
    // 发送当前所有设备状态
    ws.sendCurrentStatus(conn)
    
    // 处理客户端消息（心跳等）
    ws.handleClientMessages(clientID, conn)
    
    return nil
}

func (ws *WebSocketHandler) BroadcastStatusChange(deviceID string, status DeviceStatus) {
    message := map[string]interface{}{
        "type":      "device_status_update",
        "device_id": deviceID,
        "status":    status,
        "timestamp": time.Now(),
    }
    
    ws.broadcast(message)
}
```

## 5. 环境变量配置

### 5.1 配置文件：`bos/release/bos-environment`
```bash
# FCC设备监控配置
BOS_FCC_CONFIGS='[
  {
    "id": "fcc1",
    "primary_url": "http://192.168.1.100:8080/api/devices",
    "health_url": "http://192.168.1.100:8080/health",
    "device_ids": ["uuid1", "uuid2", "uuid3"]
  },
  {
    "id": "fcc2", 
    "primary_url": "http://192.168.1.101:8080/api/devices",
    "health_url": "http://192.168.1.101:8080/health",
    "device_ids": ["uuid4", "uuid5", "uuid6"]
  }
]'

# 轮询配置
BOS_DEVICE_POLL_INTERVAL=10s
BOS_DEVICE_CACHE_SIZE=60
BOS_DEVICE_POLL_TIMEOUT=5s

# WebSocket配置
BOS_WEBSOCKET_PATH=/api/v1/ws/device-status
BOS_WEBSOCKET_MAX_CONNECTIONS=100
```

## 6. API接口设计

### 6.1 WebSocket接口
- **路径**: `/api/v1/ws/device-status`
- **协议**: WebSocket
- **认证**: 预留token参数 `?token=xxx`

#### 6.1.1 推送消息格式
```json
{
  "type": "device_status_update",
  "device_id": "uuid1",
  "status": {
    "device_id": "uuid1",
    "fcc_id": "fcc1",
    "status": "online",
    "has_transaction": true,
    "last_updated": "2025-01-22T10:30:00Z",
    "source": "api"
  },
  "timestamp": "2025-01-22T10:30:00Z"
}
```

#### 6.1.2 心跳消息格式
```json
{
  "type": "heartbeat",
  "shift_info": {
    "shift_id": 123,
    "station_id": 1,
    "data": {
      "employee_id": "emp001",
      "start_time": "2025-01-22T08:00:00Z"
    }
  },
  "timestamp": "2025-01-22T10:30:00Z"
}
```

## 7. 实现计划

### 7.1 MVP Phase 1 (Week 1)
- [ ] 核心接口定义和数据结构
- [ ] FCCConfig环境变量解析
- [ ] 内存缓存实现（简单map + mutex）
- [ ] 双轮询器实现（主接口 + health降级）
- [ ] 优化的fuel_transaction查询逻辑

### 7.2 MVP Phase 2 (Week 2)
- [ ] 多FCC并发轮询框架
- [ ] WebSocket集成到Echo框架
- [ ] 设备状态变化推送
- [ ] 按FCC分组的offline处理

### 7.3 MVP Phase 3 (Week 3)
- [ ] 班次心跳对比逻辑
- [ ] 错误处理和重试机制
- [ ] 性能优化和监控
- [ ] 单元测试

## 8. 技术细节

### 8.1 并发控制
- 每个FCC独立goroutine轮询
- 使用sync.RWMutex保护缓存读写
- WebSocket连接池管理

### 8.2 错误处理
- 轮询失败自动降级到health接口
- 所有轮询器失败时标记FCC设备offline
- WebSocket连接断开自动清理

### 8.3 性能考虑
- 批量查询fuel_transaction减少数据库压力
- 内存缓存避免重复计算
- 状态变化时才推送，减少网络流量

## 9. 监控和日志

### 9.1 关键指标
- FCC轮询成功率
- 设备状态变化频率
- WebSocket连接数
- 数据库查询耗时

### 9.2 日志格式
```
[DEVICE] FCC轮询 - FCCID: fcc1, Status: success, Devices: 3, Duration: 150ms
[DEVICE] 设备状态变化 - DeviceID: uuid1, Status: online->offline, Source: api
[WEBSOCKET] 客户端连接 - ClientID: client123, IP: ************
```

---

**文档版本**: v1.0  
**创建日期**: 2025-01-22  
**维护人员**: BOS开发团队