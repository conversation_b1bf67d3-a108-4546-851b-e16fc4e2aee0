// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/calculator": {
            "get": {
                "description": "返回促销计算服务的主页",
                "consumes": [
                    "text/html"
                ],
                "produces": [
                    "text/html"
                ],
                "tags": [
                    "促销计算"
                ],
                "summary": "促销计算器首页",
                "responses": {
                    "200": {
                        "description": "促销服务演示",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/calculator/calculate": {
            "post": {
                "description": "计算给定订单的所有适用折扣（功能与ProcessOrder相同）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "促销计算"
                ],
                "summary": "计算订单折扣",
                "parameters": [
                    {
                        "description": "订单信息",
                        "name": "order",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.Order"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "折扣计算结果",
                        "schema": {
                            "$ref": "#/definitions/handlers.DiscountResponse"
                        }
                    },
                    "400": {
                        "description": "请求数据无效",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/calculator/process": {
            "post": {
                "description": "接收订单数据，应用适用的促销规则，返回折扣计算结果",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "促销计算"
                ],
                "summary": "处理订单并计算折扣",
                "parameters": [
                    {
                        "description": "订单信息",
                        "name": "order",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.Order"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "折扣计算结果",
                        "schema": {
                            "$ref": "#/definitions/handlers.DiscountResponse"
                        }
                    },
                    "400": {
                        "description": "请求数据无效",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/calculator/status": {
            "get": {
                "description": "返回促销计算服务的当前运行状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "促销计算"
                ],
                "summary": "获取服务状态",
                "responses": {
                    "200": {
                        "description": "包含服务状态和当前时间",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/employee": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "添加新员工到系统中",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "employees"
                ],
                "summary": "添加员工",
                "parameters": [
                    {
                        "description": "员工信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CreateEmployeeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "添加成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.BaseResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Employee"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/employee/login": {
            "post": {
                "description": "员工通过员工编号和密码登录系统，成功后返回JWT令牌",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "employees"
                ],
                "summary": "员工登录",
                "parameters": [
                    {
                        "description": "员工登录信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.BaseResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.LoginResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "认证失败",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/employee/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "根据员工ID获取员工的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "employees"
                ],
                "summary": "获取员工详情",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "员工ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.BaseResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Employee"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "员工不存在",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "更新员工的姓名和密码信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "employees"
                ],
                "summary": "更新员工信息",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "员工ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新的员工信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UpdateEmployeeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.BaseResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Employee"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "员工不存在",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "根据员工ID删除员工（软删除）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "employees"
                ],
                "summary": "删除员工",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "员工ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.BaseResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {}
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "员工不存在",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/employees": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取员工列表，支持分页和按姓名筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "employees"
                ],
                "summary": "获取员工列表",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "页码，默认1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "minimum": 1,
                        "type": "integer",
                        "default": 10,
                        "description": "每页记录数，默认10",
                        "name": "size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "按员工姓名筛选",
                        "name": "name",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.BaseResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.ListEmployeesResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/promotions": {
            "get": {
                "description": "获取所有促销活动，支持分页、搜索和筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "促销管理"
                ],
                "summary": "获取促销活动列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "促销活动状态筛选（如'ACTIVE'，'DRAFT'等）",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "促销活动类型筛选（如'PERCENTAGE'，'FIXED_AMOUNT'等）",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词（按名称搜索）",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页记录数，默认为10，最大100",
                        "name": "pageSize",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/promotions/edit/{id}": {
            "get": {
                "description": "获取新增或编辑促销活动所需的表单数据",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "促销管理"
                ],
                "summary": "获取促销活动表单数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "促销活动ID（编辑时提供）",
                        "name": "id",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "活动不存在",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/promotions/new": {
            "get": {
                "description": "获取新增或编辑促销活动所需的表单数据",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "促销管理"
                ],
                "summary": "获取促销活动表单数据",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "活动不存在",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/promotions/save": {
            "post": {
                "description": "创建新的促销活动或更新现有的促销活动",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "促销管理"
                ],
                "summary": "保存促销活动",
                "parameters": [
                    {
                        "type": "string",
                        "description": "促销活动名称",
                        "name": "name",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "促销活动描述",
                        "name": "description",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "促销类型",
                        "name": "type",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "促销范围",
                        "name": "scope",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "number",
                        "description": "促销值",
                        "name": "value",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "状态",
                        "name": "status",
                        "in": "formData"
                    },
                    {
                        "type": "number",
                        "description": "最低订单金额",
                        "name": "min_order_amount",
                        "in": "formData"
                    },
                    {
                        "type": "number",
                        "description": "最大折扣金额",
                        "name": "max_discount_amount",
                        "in": "formData"
                    },
                    {
                        "type": "integer",
                        "description": "优先级",
                        "name": "priority",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "开始时间（格式：2006-01-02T15:04）",
                        "name": "start_time",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "结束时间（格式：2006-01-02T15:04）",
                        "name": "end_time",
                        "in": "formData"
                    },
                    {
                        "type": "boolean",
                        "description": "是否可叠加",
                        "name": "stackable",
                        "in": "formData"
                    },
                    {
                        "type": "boolean",
                        "description": "是否需要优惠券",
                        "name": "requires_coupon",
                        "in": "formData"
                    },
                    {
                        "type": "integer",
                        "description": "最大使用次数",
                        "name": "max_use_count",
                        "in": "formData"
                    },
                    {
                        "type": "integer",
                        "description": "每用户最大使用次数",
                        "name": "max_per_user",
                        "in": "formData"
                    },
                    {
                        "type": "boolean",
                        "description": "是否适用会员",
                        "name": "for_members",
                        "in": "formData"
                    },
                    {
                        "type": "boolean",
                        "description": "是否适用非会员",
                        "name": "for_non_members",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "请求数据无效",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/promotions/save/{id}": {
            "post": {
                "description": "创建新的促销活动或更新现有的促销活动",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "促销管理"
                ],
                "summary": "保存促销活动",
                "parameters": [
                    {
                        "type": "string",
                        "description": "促销活动ID（更新时提供）",
                        "name": "id",
                        "in": "path"
                    },
                    {
                        "type": "string",
                        "description": "促销活动名称",
                        "name": "name",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "促销活动描述",
                        "name": "description",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "促销类型",
                        "name": "type",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "促销范围",
                        "name": "scope",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "number",
                        "description": "促销值",
                        "name": "value",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "状态",
                        "name": "status",
                        "in": "formData"
                    },
                    {
                        "type": "number",
                        "description": "最低订单金额",
                        "name": "min_order_amount",
                        "in": "formData"
                    },
                    {
                        "type": "number",
                        "description": "最大折扣金额",
                        "name": "max_discount_amount",
                        "in": "formData"
                    },
                    {
                        "type": "integer",
                        "description": "优先级",
                        "name": "priority",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "开始时间（格式：2006-01-02T15:04）",
                        "name": "start_time",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "结束时间（格式：2006-01-02T15:04）",
                        "name": "end_time",
                        "in": "formData"
                    },
                    {
                        "type": "boolean",
                        "description": "是否可叠加",
                        "name": "stackable",
                        "in": "formData"
                    },
                    {
                        "type": "boolean",
                        "description": "是否需要优惠券",
                        "name": "requires_coupon",
                        "in": "formData"
                    },
                    {
                        "type": "integer",
                        "description": "最大使用次数",
                        "name": "max_use_count",
                        "in": "formData"
                    },
                    {
                        "type": "integer",
                        "description": "每用户最大使用次数",
                        "name": "max_per_user",
                        "in": "formData"
                    },
                    {
                        "type": "boolean",
                        "description": "是否适用会员",
                        "name": "for_members",
                        "in": "formData"
                    },
                    {
                        "type": "boolean",
                        "description": "是否适用非会员",
                        "name": "for_non_members",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "请求数据无效",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/promotions/{id}": {
            "get": {
                "description": "根据ID获取单个促销活动的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "促销管理"
                ],
                "summary": "查看促销活动详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "促销活动ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "活动不存在",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            },
            "delete": {
                "description": "根据ID删除指定的促销活动",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "促销管理"
                ],
                "summary": "删除促销活动",
                "parameters": [
                    {
                        "type": "string",
                        "description": "促销活动ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功响应",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "错误响应",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/promotions/{id}/status": {
            "post": {
                "description": "更新指定促销活动的状态（如激活、暂停等）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "促销管理"
                ],
                "summary": "更新促销活动状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "促销活动ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "新状态",
                        "name": "status",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功响应",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "错误响应",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "handlers.DiscountItemResponse": {
            "type": "object",
            "properties": {
                "discountedPrice": {
                    "type": "number",
                    "example": 18.45
                },
                "itemId": {
                    "type": "string",
                    "example": "product-123"
                },
                "name": {
                    "type": "string",
                    "example": "商品名称"
                },
                "originalPrice": {
                    "type": "number",
                    "example": 20.5
                },
                "quantity": {
                    "type": "integer",
                    "example": 2
                }
            }
        },
        "handlers.DiscountResponse": {
            "type": "object",
            "properties": {
                "discountAmount": {
                    "type": "number",
                    "example": 10.05
                },
                "discountedAmount": {
                    "type": "number",
                    "example": 90.45
                },
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.DiscountItemResponse"
                    }
                },
                "message": {
                    "type": "string",
                    "example": "折扣计算成功"
                },
                "originalAmount": {
                    "type": "number",
                    "example": 100.5
                },
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "handlers.Order": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.OrderItem"
                    }
                },
                "orderAmount": {
                    "type": "number",
                    "example": 100.5
                },
                "orderTime": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "userId": {
                    "type": "string",
                    "example": "user123"
                }
            }
        },
        "handlers.OrderItem": {
            "type": "object",
            "properties": {
                "category": {
                    "type": "string",
                    "example": "电子产品"
                },
                "itemId": {
                    "type": "string",
                    "example": "product-123"
                },
                "name": {
                    "type": "string",
                    "example": "商品名称"
                },
                "price": {
                    "type": "number",
                    "example": 20.5
                },
                "quantity": {
                    "type": "integer",
                    "example": 2
                }
            }
        },
        "models.BaseResponse": {
            "description": "基础响应结构",
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer",
                    "example": 200
                },
                "data": {
                    "description": "响应数据"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string",
                    "example": "错误信息"
                },
                "message": {
                    "description": "响应消息",
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "models.CreateEmployeeRequest": {
            "type": "object",
            "required": [
                "employee_no",
                "name",
                "password"
            ],
            "properties": {
                "department": {
                    "description": "部门",
                    "type": "string",
                    "example": "运营部"
                },
                "email": {
                    "description": "邮箱",
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "employee_no": {
                    "description": "员工编号",
                    "type": "string",
                    "example": "EMP001"
                },
                "name": {
                    "description": "员工姓名",
                    "type": "string",
                    "example": "张三"
                },
                "password": {
                    "description": "密码",
                    "type": "string",
                    "example": "password123"
                },
                "phone": {
                    "description": "手机号",
                    "type": "string",
                    "example": "13800138000"
                },
                "position": {
                    "description": "职位",
                    "type": "string",
                    "example": "加油员"
                }
            }
        },
        "models.Employee": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "创建时间",
                    "type": "string",
                    "example": "2024-01-01T00:00:00Z"
                },
                "department": {
                    "description": "部门",
                    "type": "string",
                    "example": "运营部"
                },
                "email": {
                    "description": "邮箱",
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "employee_no": {
                    "description": "员工编号",
                    "type": "string",
                    "example": "EMP001"
                },
                "hire_date": {
                    "description": "入职日期",
                    "type": "string",
                    "example": "2024-01-01T00:00:00Z"
                },
                "id": {
                    "description": "员工ID",
                    "type": "integer",
                    "example": 1
                },
                "last_login_at": {
                    "description": "最后登录时间",
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                },
                "name": {
                    "description": "员工姓名",
                    "type": "string",
                    "example": "张三"
                },
                "password": {
                    "description": "员工密码（输出时隐藏）",
                    "type": "string",
                    "example": "password123"
                },
                "phone": {
                    "description": "手机号",
                    "type": "string",
                    "example": "13800138000"
                },
                "position": {
                    "description": "职位",
                    "type": "string",
                    "example": "加油员"
                },
                "status": {
                    "description": "状态",
                    "type": "string",
                    "example": "active"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                }
            }
        },
        "models.ErrorResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "错误代码",
                    "type": "string"
                },
                "detail": {
                    "description": "详细信息",
                    "type": "string"
                },
                "message": {
                    "description": "错误消息",
                    "type": "string"
                }
            }
        },
        "models.ListEmployeesResponse": {
            "type": "object",
            "properties": {
                "employees": {
                    "description": "员工列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Employee"
                    }
                },
                "total": {
                    "description": "总数",
                    "type": "integer"
                }
            }
        },
        "models.LoginRequest": {
            "type": "object",
            "required": [
                "employee_no",
                "password"
            ],
            "properties": {
                "employee_no": {
                    "description": "员工编号",
                    "type": "string",
                    "example": "EMP001"
                },
                "password": {
                    "description": "密码",
                    "type": "string",
                    "example": "password123"
                }
            }
        },
        "models.LoginResponse": {
            "type": "object",
            "properties": {
                "employee": {
                    "description": "员工信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.Employee"
                        }
                    ]
                },
                "token": {
                    "description": "JWT令牌",
                    "type": "string",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                }
            }
        },
        "models.UpdateEmployeeRequest": {
            "type": "object",
            "required": [
                "name",
                "password"
            ],
            "properties": {
                "department": {
                    "description": "部门",
                    "type": "string",
                    "example": "运营部"
                },
                "email": {
                    "description": "邮箱",
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "name": {
                    "description": "员工姓名",
                    "type": "string",
                    "example": "张三"
                },
                "password": {
                    "description": "密码",
                    "type": "string",
                    "example": "password123"
                },
                "phone": {
                    "description": "手机号",
                    "type": "string",
                    "example": "13800138000"
                },
                "position": {
                    "description": "职位",
                    "type": "string",
                    "example": "加油员"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "description": "Bearer token authentication",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    },
    "tags": [
        {
            "description": "燃油交易管理相关接口",
            "name": "fuel-transactions"
        },
        {
            "description": "订单管理相关接口",
            "name": "orders"
        },
        {
            "description": "报表管理相关接口",
            "name": "reports"
        },
        {
            "description": "增强报表管理相关接口",
            "name": "enhanced-reports"
        },
        {
            "description": "员工管理相关接口",
            "name": "employees"
        },
        {
            "description": "班次管理相关接口",
            "name": "shifts"
        },
        {
            "description": "促销管理相关接口",
            "name": "promotions"
        },
        {
            "description": "促销计算相关接口",
            "name": "calculator"
        },
        {
            "description": "会员管理相关接口",
            "name": "members"
        },
        {
            "description": "油品管理相关接口",
            "name": "oil"
        },
        {
            "description": "PTS2设备管理相关接口",
            "name": "pts2"
        },
        {
            "description": "客户管理相关接口",
            "name": "customers"
        },
        {
            "description": "车辆管理相关接口",
            "name": "vehicles"
        },
        {
            "description": "标签管理相关接口",
            "name": "labels"
        },
        {
            "description": "库存管理相关接口",
            "name": "inventory"
        },
        {
            "description": "支付管理相关接口",
            "name": "payments"
        },
        {
            "description": "员工卡管理相关接口",
            "name": "staff-cards"
        },
        {
            "description": "认证管理相关接口",
            "name": "auth"
        },
        {
            "description": "用户管理相关接口",
            "name": "users"
        },
        {
            "description": "角色管理相关接口",
            "name": "roles"
        },
        {
            "description": "权限管理相关接口",
            "name": "permissions"
        },
        {
            "description": "会话管理相关接口",
            "name": "sessions"
        },
        {
            "description": "日志管理相关接口",
            "name": "logs"
        },
        {
            "description": "站点管理相关接口",
            "name": "stations"
        },
        {
            "description": "设备管理相关接口",
            "name": "equipments"
        },
        {
            "description": "配置管理相关接口",
            "name": "configs"
        },
        {
            "description": "数据字典管理相关接口",
            "name": "dict"
        },
        {
            "description": "标签管理相关接口",
            "name": "tags"
        }
    ]
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0.0",
	Host:             "localhost:8080",
	BasePath:         "/api/v1",
	Schemes:          []string{},
	Title:            "BOS 加油站管理系统 API",
	Description:      "BOS（Business Operation System）加油站管理系统，提供燃油交易、订单管理、报表分析、员工管理、班次管理、促销管理、会员管理、油品管理、设备管理、客户管理、库存管理和支付管理等功能。",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
