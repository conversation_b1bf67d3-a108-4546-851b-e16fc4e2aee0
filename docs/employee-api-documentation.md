# 员工API接口文档

本文档详细描述了员工管理模块的API接口设计，包括员工登录、添加员工、获取员工列表、获取员工详情、更新员工信息和删除员工等功能。

## 基础信息

- 基础路径: `/api/v1`
- 响应格式: JSON
- 认证方式: JWT Token (除登录接口外，其他接口需要在请求头中携带`Authorization: Bearer {token}`进行认证)

## 响应格式

所有API响应都遵循以下统一格式：

```json
{
  "code": 200,          // 状态码，200表示成功，其他值表示失败
  "message": "成功",     // 响应消息
  "data": {             // 响应数据，失败时可能为null或包含错误详情
    // 具体响应数据
  }
}
```

## 错误响应

失败响应示例：

```json
{
  "code": "ERROR_CODE",          // 错误代码
  "message": "错误描述",          // 错误消息
  "detail": "错误详细信息"        // 详细错误信息
}
```

## API接口详情

### 1. 员工登录

员工通过员工号和密码登录系统。

#### 请求

- 路径: `/employee/login`
- 方法: POST
- 内容类型: application/json

请求参数:

```json
{
  "employee_no": "EMP001",    // 员工编号
  "password": "password123"   // 登录密码
}
```

#### 响应

**成功响应** (200):

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "employee": {
      "id": 1,
      "employee_no": "EMP001",
      "name": "张三",
      "created_at": "2025-04-16T10:30:00Z",
      "updated_at": "2025-04-16T10:30:00Z"
    }
  }
}
```

**失败响应** (400 - 请求错误):

```json
{
  "code": "MISSING_REQUIRED_FIELDS",
  "message": "缺少必填字段",
  "detail": "员工编号和密码不能为空"
}
```

**失败响应** (401 - 认证失败):

```json
{
  "code": "AUTHENTICATION_FAILED",
  "message": "认证失败",
  "detail": "员工编号或密码错误"
}
```

#### 示例

```bash
curl -X POST \
  http://localhost:8080/api/v1/employee/login \
  -H 'Content-Type: application/json' \
  -d '{
    "employee_no": "EMP001",
    "password": "password123"
}'
```

### 2. 添加员工

添加新员工到系统。需要管理员权限。

#### 请求

- 路径: `/employee`
- 方法: POST
- 内容类型: application/json
- 认证: 需要

请求参数:

```json
{
  "employee_no": "EMP002",    // 员工编号（必填）
  "name": "李四",             // 员工姓名（必填）
  "password": "password123"   // 登录密码（必填）
}
```

#### 响应

**成功响应** (200):

```json
{
  "code": 200,
  "message": "添加成功",
  "data": {
    "id": 2,
    "employee_no": "EMP002",
    "name": "李四",
    "created_at": "2025-04-16T11:30:00Z",
    "updated_at": "2025-04-16T11:30:00Z"
  }
}
```

**失败响应** (400 - 请求错误):

```json
{
  "code": "MISSING_REQUIRED_FIELDS",
  "message": "缺少必填字段",
  "detail": "员工编号、姓名和密码不能为空"
}
```

**失败响应** (500 - 服务器错误):

```json
{
  "code": "CREATE_EMPLOYEE_FAILED",
  "message": "创建员工失败",
  "detail": "员工编号 EMP002 已存在"
}
```

#### 示例

```bash
curl -X POST \
  http://localhost:8080/api/v1/employee \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -d '{
    "employee_no": "EMP002",
    "name": "李四",
    "password": "password123"
}'
```

### 3. 获取员工列表

获取员工列表，支持分页和按姓名筛选。

#### 请求

- 路径: `/employees`
- 方法: GET
- 认证: 需要

查询参数:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | int | 否 | 页码，默认为1 |
| size | int | 否 | 每页记录数，默认为10 |
| name | string | 否 | 按员工姓名筛选（模糊匹配） |

#### 响应

**成功响应** (200):

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "total": 100,
    "page": 1,
    "size": 10,
    "employees": [
      {
        "id": 1,
        "employee_no": "EMP001",
        "name": "张三",
        "created_at": "2025-04-16T10:30:00Z",
        "updated_at": "2025-04-16T10:30:00Z"
      },
      {
        "id": 2,
        "employee_no": "EMP002",
        "name": "李四",
        "created_at": "2025-04-16T11:30:00Z",
        "updated_at": "2025-04-16T11:30:00Z"
      }
      // ... 更多员工
    ]
  }
}
```

**失败响应** (500 - 服务器错误):

```json
{
  "code": "LIST_EMPLOYEES_FAILED",
  "message": "获取员工列表失败",
  "detail": "数据库连接错误"
}
```

#### 示例

基本查询:
```bash
curl -X GET \
  http://localhost:8080/api/v1/employees \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
```

带分页和筛选:
```bash
curl -X GET \
  'http://localhost:8080/api/v1/employees?page=1&size=10&name=张' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
```

### 4. 获取员工详情

根据ID获取员工的详细信息。

#### 请求

- 路径: `/employee/{id}`
- 方法: GET
- 认证: 需要

路径参数:

| 参数名 | 类型 | 描述 |
|--------|------|------|
| id | int | 员工ID，必须是正整数 |

#### 响应

**成功响应** (200):

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "id": 1,
    "employee_no": "EMP001",
    "name": "张三",
    "created_at": "2025-04-16T10:30:00Z",
    "updated_at": "2025-04-16T10:30:00Z"
  }
}
```

**失败响应** (400 - 请求错误):

```json
{
  "code": "INVALID_ID",
  "message": "无效的员工ID",
  "detail": "员工ID必须是正整数"
}
```

**失败响应** (404 - 资源不存在):

```json
{
  "code": "EMPLOYEE_NOT_FOUND",
  "message": "员工不存在",
  "detail": "未找到ID为1的员工"
}
```

#### 示例

```bash
curl -X GET \
  http://localhost:8080/api/v1/employee/1 \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
```

### 5. 更新员工信息

更新员工的姓名和密码。

#### 请求

- 路径: `/employee/{id}`
- 方法: PUT
- 内容类型: application/json
- 认证: 需要

路径参数:

| 参数名 | 类型 | 描述 |
|--------|------|------|
| id | int | 员工ID，必须是正整数 |

请求参数:

```json
{
  "name": "张三三",      // 可选，新的员工姓名
  "password": "newpassword123"  // 可选，新的登录密码
}
```

注意: 至少需要提供一个字段（姓名或密码）进行更新。

#### 响应

**成功响应** (200):

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "employee_no": "EMP001",
    "name": "张三三",
    "created_at": "2025-04-16T10:30:00Z",
    "updated_at": "2025-04-16T14:25:00Z"
  }
}
```

**失败响应** (400 - 请求错误):

```json
{
  "code": "MISSING_UPDATE_FIELDS",
  "message": "缺少更新字段",
  "detail": "姓名和密码至少需要提供一个"
}
```

**失败响应** (404 - 资源不存在):

```json
{
  "code": "EMPLOYEE_NOT_FOUND",
  "message": "员工不存在",
  "detail": "未找到ID为1的员工"
}
```

**失败响应** (500 - 服务器错误):

```json
{
  "code": "UPDATE_EMPLOYEE_FAILED",
  "message": "更新员工失败",
  "detail": "数据库更新操作失败"
}
```

#### 示例

```bash
curl -X PUT \
  http://localhost:8080/api/v1/employee/1 \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -d '{
    "name": "张三三",
    "password": "newpassword123"
}'
```

### 6. 删除员工

根据ID删除员工（软删除）。

#### 请求

- 路径: `/employee/{id}`
- 方法: DELETE
- 认证: 需要

路径参数:

| 参数名 | 类型 | 描述 |
|--------|------|------|
| id | int | 员工ID，必须是正整数 |

#### 响应

**成功响应** (200):

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

**失败响应** (400 - 请求错误):

```json
{
  "code": "INVALID_ID",
  "message": "无效的员工ID",
  "detail": "员工ID必须是正整数"
}
```

**失败响应** (500 - 服务器错误):

```json
{
  "code": "DELETE_EMPLOYEE_FAILED",
  "message": "删除员工失败",
  "detail": "数据库删除操作失败"
}
```

#### 示例

```bash
curl -X DELETE \
  http://localhost:8080/api/v1/employee/1 \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
```

## 数据结构

### Employee

员工信息对象结构：

```json
{
  "id": 1,                      // 员工ID
  "employee_no": "EMP001",      // 员工编号
  "name": "张三",               // 员工姓名
  "created_at": "2025-04-16T10:30:00Z",  // 创建时间
  "updated_at": "2025-04-16T10:30:00Z",  // 更新时间
  "deleted_at": null                     // 删除时间（软删除），未删除时为null
}
```

注意：`password`字段出于安全考虑，不会在任何响应中返回。

## 错误代码

| 错误代码 | 描述 |
|----------|------|
| INVALID_REQUEST | 无效的请求格式 |
| MISSING_REQUIRED_FIELDS | 缺少必填字段 |
| AUTHENTICATION_FAILED | 认证失败 |
| INVALID_ID | 无效的员工ID |
| EMPLOYEE_NOT_FOUND | 员工不存在 |
| CREATE_EMPLOYEE_FAILED | 创建员工失败 |
| UPDATE_EMPLOYEE_FAILED | 更新员工失败 |
| DELETE_EMPLOYEE_FAILED | 删除员工失败 |
| LIST_EMPLOYEES_FAILED | 获取员工列表失败 |
| MISSING_UPDATE_FIELDS | 缺少更新字段 |

## 安全性考虑

1. 除登录接口外，其他所有接口都需要JWT令牌进行认证
2. 密码在存储前会进行加密处理
3. 员工删除采用软删除方式，可以保留历史记录
4. 敏感字段（如密码）不会在响应中返回

## 性能优化建议

1. 获取员工列表API支持分页和筛选，避免返回过多数据
2. 对于频繁访问的API（如获取员工列表），可以考虑实现缓存机制
3. 需要进行大量员工操作时，建议使用批量处理接口而不是多次调用单个接口

#### 员工数据模型

| 字段 | 类型 | 描述 |
| ----- | ---- | ----- |
| id | string | 员工唯一标识符 |
| no | string | 员工编号 |
| name | string | 员工姓名 |
| phone | string | 员工电话号码 |
| active | boolean | 员工是否在职状态 |
| email | string | 员工电子邮件地址 |
| password | string | 员工密码（已加密存储） |
| role | string | 员工角色 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |
| deleted_at | string | 删除时间（软删除），未删除时为null |

#### 示例：员工数据

```json
{
  "id": "e1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d",
  "no": "EMP001",
  "name": "张三",
  "phone": "13800138000",
  "active": true,
  "email": "<EMAIL>",
  "role": "manager",
  "created_at": "2025-04-16T15:30:00Z",
  "updated_at": "2025-04-16T15:30:00Z",
  "deleted_at": null
}
```

### 创建员工

**接口**：POST /api/v1/employees

**请求参数**：

```json
{
  "no": "EMP001",
  "name": "张三",
  "phone": "13800138000",
  "active": true,
  "email": "<EMAIL>",
  "password": "password123",
  "role": "manager"
}
```

**响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "e1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d",
    "no": "EMP001",
    "name": "张三",
    "phone": "13800138000",
    "active": true,
    "email": "<EMAIL>",
    "role": "manager",
    "created_at": "2025-04-16T15:30:00Z",
    "updated_at": "2025-04-16T15:30:00Z",
    "deleted_at": null
  }
}
``` 