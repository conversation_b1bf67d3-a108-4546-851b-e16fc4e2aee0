# BOS API文档

## 燃油交易 API

### 获取燃油交易列表

获取燃油交易列表，支持多种过滤条件和分页。

**请求URL**

```
GET /api/v1/fuel-transactions
```

**请求参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| station_id | int | 否 | 加油站ID |
| status | string | 否 | 交易状态 (pending, processed, cancelled) |
| pump_id | string | 否 | 油枪ID |
| member_id | int | 否 | 会员ID |
| date_from | string | 否 | 开始日期 (格式: 2006-01-02) |
| date_to | string | 否 | 结束日期 (格式: 2006-01-02) |
| transaction_number | string | 否 | 交易编号 |
| fuel_type | string | 否 | 燃油类型 |
| fuel_grade | string | 否 | 燃油等级 |
| page | int | 否 | 页码，默认为1 |
| limit | int | 否 | 每页数量，默认为10 |
| sort_by | string | 否 | 排序字段，默认为created_at |
| sort_dir | string | 否 | 排序方向 (asc, desc)，默认为desc |

**成功响应**

```json
{
  "items": [
    {
      "id": 1,
      "transaction_number": "FT-20250410-001",
      "station_id": 101,
      "pump_id": "P01",
      "nozzle_id": "N01",
      "fuel_type": "汽油",
      "fuel_grade": "92#",
      "unit_price": 7.23,
      "volume": 50.5,
      "amount": 365.12,
      "total_volume": 50.5,
      "total_amount": 365.12,
      "status": "processed",
      "member_card_id": "M123456",
      "member_id": 1001,
      "employee_id": 2001,
      "fcc_transaction_id": "FCC-001",
      "pos_terminal_id": "POS-001",
      "metadata": {
        "payment_method": "cash",
        "loyalty_points": 100
      },
      "created_at": "2025-04-10T10:30:00Z",
      "updated_at": "2025-04-10T10:35:00Z",
      "processed_at": "2025-04-10T10:35:00Z",
      "cancelled_at": null
    }
  ],
  "total": 1,
  "page": 1,
  "page_size": 10,
  "total_page": 1
}
```

**响应说明**

| 字段名 | 类型 | 描述 |
|-------|------|------|
| items | array | 燃油交易列表 |
| items[].id | int | 交易ID |
| items[].transaction_number | string | 交易编号 |
| items[].station_id | int | 加油站ID |
| items[].pump_id | string | 油枪ID |
| items[].nozzle_id | string | 喷嘴ID |
| items[].fuel_type | string | 燃油类型 |
| items[].fuel_grade | string | 燃油等级 |
| items[].unit_price | float | 单价 |
| items[].volume | float | 加油量 |
| items[].amount | float | 总金额 |
| items[].total_volume | float | 总加油量 |
| items[].total_amount | float | 总交易金额 |
| items[].status | string | 交易状态 |
| items[].member_card_id | string | 会员卡ID |
| items[].member_id | int | 会员ID |
| items[].employee_id | int | 员工ID |
| items[].fcc_transaction_id | string | FCC交易ID |
| items[].pos_terminal_id | string | POS终端ID |
| items[].metadata | object | 元数据 |
| items[].created_at | string | 创建时间 |
| items[].updated_at | string | 更新时间 |
| items[].processed_at | string | 处理时间 |
| items[].cancelled_at | string | 取消时间 |
| total | int | 总记录数 |
| page | int | 当前页码 |
| page_size | int | 每页数量 |
| total_page | int | 总页数 |

**错误响应**

```json
{
  "code": "INTERNAL_ERROR",
  "message": "获取燃油交易列表失败",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
GET /api/v1/fuel-transactions?station_id=101&status=processed&date_from=2025-04-01&date_to=2025-04-10&page=1&limit=10
```

**示例响应**

```json
{
  "items": [
    {
      "id": 1,
      "transaction_number": "FT-20250410-001",
      "station_id": 101,
      "pump_id": "P01",
      "nozzle_id": "N01",
      "fuel_type": "汽油",
      "fuel_grade": "92#",
      "unit_price": 7.23,
      "volume": 50.5,
      "amount": 365.12,
      "total_volume": 50.5,
      "total_amount": 365.12,
      "status": "processed",
      "member_card_id": "M123456",
      "member_id": 1001,
      "employee_id": 2001,
      "fcc_transaction_id": "FCC-001",
      "pos_terminal_id": "POS-001",
      "metadata": {
        "payment_method": "cash",
        "loyalty_points": 100
      },
      "created_at": "2025-04-10T10:30:00Z",
      "updated_at": "2025-04-10T10:35:00Z",
      "processed_at": "2025-04-10T10:35:00Z",
      "cancelled_at": null
    }
  ],
  "total": 1,
  "page": 1,
  "page_size": 10,
  "total_page": 1
}
```

### 创建燃油交易

创建新的燃油交易记录。

**请求URL**

```
POST /api/v1/fuel-transactions
```

**请求参数**

```json
{
  "transaction_number": "FT-20250410-002",
  "station_id": 101,
  "pump_id": "P01",
  "nozzle_id": "N01",
  "fuel_type": "汽油",
  "fuel_grade": "92#",
  "unit_price": 7.23,
  "volume": 50.5,
  "amount": 365.12,
  "total_volume": 50.5,
  "total_amount": 365.12,
  "member_card_id": "M123456",
  "member_id": 1001,
  "employee_id": 2001,
  "fcc_transaction_id": "FCC-002",
  "pos_terminal_id": "POS-001",
  "metadata": {
    "payment_method": "cash",
    "loyalty_points": 100
  }
}
```

**参数说明**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| transaction_number | string | 是 | 交易编号 |
| station_id | int | 是 | 加油站ID |
| pump_id | string | 是 | 油枪ID |
| nozzle_id | string | 是 | 喷嘴ID |
| fuel_type | string | 是 | 燃油类型 |
| fuel_grade | string | 是 | 燃油等级 |
| unit_price | float | 是 | 单价 |
| volume | float | 是 | 加油量 |
| amount | float | 是 | 总金额 |
| total_volume | float | 是 | 总加油量 |
| total_amount | float | 是 | 总交易金额 |
| member_card_id | string | 否 | 会员卡ID |
| member_id | int | 否 | 会员ID |
| employee_id | int | 否 | 员工ID |
| fcc_transaction_id | string | 否 | FCC交易ID |
| pos_terminal_id | string | 否 | POS终端ID |
| metadata | object | 否 | 元数据 |

**注意**: 新创建的燃油交易状态默认为"pending"(待处理)。要将交易与订单关联，需要先将其状态更新为"processed"(已处理)。

**成功响应**

```json
{
  "id": 2,
  "transaction_number": "FT-20250410-002",
  "station_id": 101,
  "pump_id": "P01",
  "nozzle_id": "N01",
  "fuel_type": "汽油",
  "fuel_grade": "92#",
  "unit_price": 7.23,
  "volume": 50.5,
  "amount": 365.12,
  "total_volume": 50.5,
  "total_amount": 365.12,
  "status": "pending",
  "member_card_id": "M123456",
  "member_id": 1001,
  "employee_id": 2001,
  "fcc_transaction_id": "FCC-002",
  "pos_terminal_id": "POS-001",
  "metadata": {
    "payment_method": "cash",
    "loyalty_points": 100
  },
  "created_at": "2025-04-10T11:30:00Z",
  "updated_at": "2025-04-10T11:30:00Z",
  "processed_at": null,
  "cancelled_at": null
}
```

**错误响应**

```json
{
  "code": "VALIDATION_ERROR",
  "message": "请求参数验证失败",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_REQUEST | 无效的请求参数 |
| VALIDATION_ERROR | 请求参数验证失败 |
| INTERNAL_ERROR | 内部服务器错误 |

### 获取燃油交易详情

根据ID获取燃油交易详情。

**请求URL**

```
GET /api/v1/fuel-transactions/{id}
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 燃油交易ID |

**成功响应**

```json
{
  "id": 1,
  "transaction_number": "FT-20250410-001",
  "station_id": 101,
  "pump_id": "P01",
  "nozzle_id": "N01",
  "fuel_type": "汽油",
  "fuel_grade": "92#",
  "unit_price": 7.23,
  "volume": 50.5,
  "amount": 365.12,
  "total_volume": 50.5,
  "total_amount": 365.12,
  "status": "processed",
  "member_card_id": "M123456",
  "member_id": 1001,
  "employee_id": 2001,
  "fcc_transaction_id": "FCC-001",
  "pos_terminal_id": "POS-001",
  "metadata": {
    "payment_method": "cash",
    "loyalty_points": 100
  },
  "created_at": "2025-04-10T10:30:00Z",
  "updated_at": "2025-04-10T10:35:00Z",
  "processed_at": "2025-04-10T10:35:00Z",
  "cancelled_at": null
}
```

**错误响应**

```json
{
  "code": "NOT_FOUND",
  "message": "燃油交易不存在",
  "detail": ""
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ID | 无效的ID格式 |
| NOT_FOUND | 燃油交易不存在 |
| INTERNAL_ERROR | 内部服务器错误 |

### 关联燃油交易到订单

创建燃油交易与订单的关联关系。

**业务规则**

- 只有状态为"processed"(已处理)的燃油交易才能关联到订单
- 只能关联到状态为"new"(新建)或"processing"(处理中)的订单
- 分配金额必须大于零，且不能超过燃油交易的总金额

**请求URL**

```
POST /api/v1/fuel-transactions/{id}/link
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 燃油交易ID |

**请求参数**

```json
{
  "order_id": 101,
  "allocated_amount": 365.12
}
```

**参数说明**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| order_id | int | 是 | 订单ID |
| allocated_amount | float | 是 | 分配金额 |

**成功响应**

```json
{
  "id": 1,
  "fuel_transaction_id": 1,
  "order_id": 101,
  "allocated_amount": 365.12,
  "status": "active",
  "metadata": {},
  "created_at": "2025-04-10T12:30:00Z",
  "updated_at": "2025-04-10T12:30:00Z",
  "deactivated_at": null
}
```

**错误响应**

```json
{
  "code": "INTERNAL_ERROR",
  "message": "关联燃油交易到订单失败",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ID | 无效的ID格式 |
| INVALID_REQUEST | 无效的请求参数 |
| VALIDATION_ERROR | 请求参数验证失败 |
| INVALID_TRANSACTION_STATUS | 只能关联已处理状态的燃油交易 |
| INVALID_ORDER_STATUS | 只能关联到新建或处理中状态的订单 |
| INVALID_ALLOCATED_AMOUNT | 分配金额无效（必须大于零且不超过交易总金额） |
| LINK_ALREADY_EXISTS | 燃油交易与该订单已存在活跃关联 |
| INTERNAL_ERROR | 内部服务器错误 |

### 解除燃油交易与订单的关联

解除燃油交易与订单的关联关系。

**请求URL**

```
DELETE /api/v1/fuel-transactions/{id}/unlink/{order_id}
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 燃油交易ID |
| order_id | int | 是 | 订单ID |

**成功响应**

```
204 No Content
```

**错误响应**

```json
{
  "code": "INTERNAL_ERROR",
  "message": "解除关联失败",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ID | 无效的燃油交易ID格式 |
| INVALID_ORDER_ID | 无效的订单ID格式 |
| LINK_NOT_FOUND | 未找到燃油交易与订单的关联 |
| INACTIVE_LINK | 只能解除活跃状态的关联 |
| INTERNAL_ERROR | 内部服务器错误 |

### 获取订单关联的燃油交易

获取与指定订单关联的所有燃油交易。

**请求URL**

```
GET /api/v1/orders/{order_id}/fuel-transactions
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| order_id | int | 是 | 订单ID |

**成功响应**

```json
[
  {
    "id": 1,
    "transaction_number": "FT-20250410-001",
    "station_id": 101,
    "pump_id": "P01",
    "nozzle_id": "N01",
    "fuel_type": "汽油",
    "fuel_grade": "92#",
    "unit_price": 7.23,
    "volume": 50.5,
    "amount": 365.12,
    "total_volume": 50.5,
    "total_amount": 365.12,
    "status": "processed",
    "member_card_id": "M123456",
    "member_id": 1001,
    "employee_id": 2001,
    "fcc_transaction_id": "FCC-001",
    "pos_terminal_id": "POS-001",
    "metadata": {
      "payment_method": "cash",
      "loyalty_points": 100
    },
    "created_at": "2025-04-10T10:30:00Z",
    "updated_at": "2025-04-10T10:35:00Z",
    "processed_at": "2025-04-10T10:35:00Z",
    "cancelled_at": null
  }
]
```

**错误响应**

```json
{
  "code": "INTERNAL_ERROR",
  "message": "获取订单关联的燃油交易失败",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ORDER_ID | 无效的订单ID格式 |
| INTERNAL_ERROR | 内部服务器错误 |

### 获取燃油交易关联的订单

获取与指定燃油交易关联的所有订单。

**请求URL**

```
GET /api/v1/fuel-transactions/{id}/orders
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 燃油交易ID |

**成功响应**

```json
[
  {
    "id": 101,
    "order_number": "ORD-20250410-001",
    "customer_id": 1001,
    "station_id": 101,
    "total_amount": 500.00,
    "status": "completed",
    "created_at": "2025-04-10T09:30:00Z",
    "updated_at": "2025-04-10T10:35:00Z",
    "completed_at": "2025-04-10T10:35:00Z",
    "cancelled_at": null
  }
]
```

**错误响应**

```json
{
  "code": "INTERNAL_ERROR",
  "message": "获取燃油交易关联的订单失败",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ID | 无效的燃油交易ID格式 |
| INTERNAL_ERROR | 内部服务器错误 |

### 更新关联分配金额

更新燃油交易与订单关联的分配金额。

**业务规则**

- 关联必须存在且状态为活跃(active)
- 新的分配金额必须大于零，且不能超过燃油交易的总金额

**请求URL**

```
PUT /api/v1/fuel-transactions/{id}/link/{order_id}
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 燃油交易ID |
| order_id | int | 是 | 订单ID |

**请求参数**

```json
{
  "allocated_amount": 300.00
}
```

**参数说明**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| allocated_amount | float | 是 | 新的分配金额 |

**成功响应**

```json
{
  "id": 1,
  "fuel_transaction_id": 1,
  "order_id": 101,
  "allocated_amount": 300.00,
  "status": "active",
  "metadata": {},
  "created_at": "2025-04-10T12:30:00Z",
  "updated_at": "2025-04-10T14:30:00Z",
  "deactivated_at": null
}
```

**错误响应**

```json
{
  "code": "INTERNAL_ERROR",
  "message": "更新关联分配金额失败",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ID | 无效的燃油交易ID格式 |
| INVALID_ORDER_ID | 无效的订单ID格式 |
| VALIDATION_ERROR | 请求参数验证失败 |
| LINK_NOT_FOUND | 未找到燃油交易与订单的关联 |
| INVALID_ALLOCATED_AMOUNT | 分配金额无效（必须大于零且不超过交易总金额） |
| INACTIVE_LINK | 只能更新活跃状态的关联 |
| INTERNAL_ERROR | 内部服务器错误 |
