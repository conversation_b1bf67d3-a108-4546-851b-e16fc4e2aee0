# 订单服务 API 文档

## 概述

本文档详细描述了订单服务 (Order Service) 的 API 接口。订单服务提供了完整的订单管理功能，包括订单的创建、查询、修改、添加商品、应用促销以及订单状态流转等功能。

## 基础信息

- **基础URL**: `/api/v1`
- **内容类型**: `application/json`
- **认证方式**: 待定

## 错误响应格式

所有错误响应均遵循以下格式:

```json
{
  "code": "ERROR_CODE",
  "message": "错误消息",
  "detail": "错误详细信息"
}
```

## API 接口列表

### 1. 获取订单列表

获取订单列表，支持多种过滤条件和分页。

**请求URL**

```
GET /orders
```

**请求参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| customer_id | int | 否 | 客户ID |
| station_id | int | 否 | 加油站ID |
| status | string | 否 | 订单状态 (new, processing, completed, cancelled) |
| date_from | string | 否 | 开始日期 (格式: 2006-01-02) |
| date_to | string | 否 | 结束日期 (格式: 2006-01-02) |
| order_number | string | 否 | 订单编号 |
| product_type | string | 否 | 产品类型 |
| payment_method | string | 否 | 支付方式 |
| page | int | 否 | 页码，默认为1 |
| limit | int | 否 | 每页数量，默认为10 |
| sort_by | string | 否 | 排序字段，默认为created_at |
| sort_dir | string | 否 | 排序方向 (asc, desc)，默认为desc |

**成功响应**

```json
{
  "items": [
    {
      "id": 1,
      "order_number": "ORD-20250410-001",
      "customer_id": 1001,
      "customer_name": "张三",
      "station_id": 101,
      "status": "completed",
      "total_amount": 365.12,
      "discount_amount": 20.00,
      "final_amount": 345.12,
      "paid_amount": 345.12,
      "items": [
        {
          "id": 1,
          "product_id": 5001,
          "product_name": "92#汽油",
          "product_type": "fuel",
          "quantity": 50.5,
          "unit_price": 7.23,
          "total_price": 365.12,
          "fuel_grade": "92#",
          "pump_id": "P01"
        }
      ],
      "promotions": [
        {
          "id": 1,
          "promotion_id": 2001,
          "promotion_name": "会员折扣",
          "promotion_type": "discount",
          "discount_amount": 20.00
        }
      ],
      "payments": [
        {
          "id": 1,
          "payment_method": "cash",
          "amount": 345.12,
          "transaction_id": "TRX001",
          "status": "completed"
        }
      ],
      "metadata": {
        "source": "pos",
        "employee_id": 3001
      },
      "created_at": "2025-04-10T10:30:00Z",
      "updated_at": "2025-04-10T10:35:00Z",
      "completed_at": "2025-04-10T10:35:00Z",
      "cancelled_at": null
    }
  ],
  "total": 1,
  "page": 1,
  "page_size": 10,
  "total_page": 1
}
```

**错误响应**

```json
{
  "code": "INTERNAL_ERROR",
  "message": "获取订单列表失败",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
GET /orders?station_id=101&status=completed&date_from=2025-04-01&date_to=2025-04-10&page=1&limit=10
```

### 2. 创建订单

创建新的订单记录。

**请求URL**

```
POST /orders
```

**请求参数**

```json
{
  "fuel_transaction_id": 123456,
  "station_id": 101,
  "customer_id": 1001,
  "customer_name": "张三",
  "payment_method": "cash",
  "allocated_amount": 365.12,
  "metadata": {
    "source": "pos",
    "employee_id": 3001
  }
}
```

**参数说明**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| fuel_transaction_id | int | 是 | 燃油交易ID |
| station_id | int | 是 | 加油站ID |
| customer_id | int | 否 | 客户ID |
| customer_name | string | 否 | 客户名称 |
| payment_method | string | 是 | 支付方式 |
| allocated_amount | float | 否 | 分配金额，不提供则使用燃油交易的全部金额 |
| metadata | object | 否 | 元数据 |

**注意**: 
- 订单编号由系统自动生成
- 创建成功后，订单状态会直接设置为"completed"(已完成)
- 系统会自动检查燃油交易是否有足够的可用金额
- 系统会自动创建订单项、关联燃油交易并记录支付

**成功响应**

```json
{
  "id": 1,
  "order_number": "ORD202504101030001234",
  "customer_id": 1001,
  "customer_name": "张三",
  "station_id": 101,
  "status": "completed",
  "total_amount": 365.12,
  "discount_amount": 0,
  "final_amount": 365.12,
  "paid_amount": 365.12,
  "items": [
    {
      "id": 1,
      "product_id": 0,
      "product_name": "92# 汽油",
      "product_type": "fuel",
      "quantity": 50.5,
      "unit_price": 7.23,
      "total_price": 365.12,
      "fuel_grade": "92#",
      "pump_id": "P01"
    }
  ],
  "promotions": [],
  "payments": [
    {
      "id": 1,
      "payment_method": "cash",
      "amount": 365.12,
      "status": "completed"
    }
  ],
  "metadata": {
    "source": "pos",
    "employee_id": 3001
  },
  "created_at": "2025-04-10T10:30:00Z",
  "updated_at": "2025-04-10T10:30:00Z",
  "completed_at": "2025-04-10T10:30:00Z",
  "cancelled_at": null
}
```

**错误响应**

```json
{
  "code": "INSUFFICIENT_AMOUNT",
  "message": "燃油交易可用金额不足",
  "detail": "可用金额: 200.00, 请求分配: 365.12"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_REQUEST | 请求参数格式错误 |
| VALIDATION_ERROR | 请求参数验证失败 |
| INTERNAL_ERROR | 内部服务器错误 |
| INSUFFICIENT_AMOUNT | 燃油交易可用金额不足 |

### 3. 获取订单详情

根据ID获取订单详情信息。

**请求URL**

```
GET /orders/{id}
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 订单ID |

**成功响应**

返回订单的详细信息，格式与创建订单的响应相同。

**错误响应**

```json
{
  "code": "NOT_FOUND",
  "message": "订单不存在",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ID | 无效的ID格式 |
| NOT_FOUND | 订单不存在 |
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
GET /orders/1
```

### 4. 添加订单项

向订单添加新的订单项（商品）。

**请求URL**

```
POST /orders/{id}/items
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 订单ID |

**请求参数**

```json
{
  "product_id": 5001,
  "product_name": "92#汽油",
  "product_type": "fuel",
  "quantity": 50.5,
  "unit_price": 7.23,
  "fuel_grade": "92#",
  "pump_id": "P01",
  "metadata": {
    "transaction_id": "FT-20250410-001"
  }
}
```

**参数说明**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| product_id | int | 是 | 产品ID |
| product_name | string | 是 | 产品名称 |
| product_type | string | 是 | 产品类型 |
| quantity | float | 是 | 数量 |
| unit_price | float | 是 | 单价 |
| fuel_grade | string | 否 | 燃油等级，仅对燃油类型有效 |
| pump_id | string | 否 | 油枪ID，仅对燃油类型有效 |
| metadata | object | 否 | 元数据 |

**注意**: 
- total_price (总价) 会自动计算为 quantity * unit_price
- 只有状态为 "new" 或 "processing" 的订单才能添加订单项

**成功响应**

```json
{
  "id": 1,
  "order_id": 1,
  "product_id": 5001,
  "product_name": "92#汽油",
  "product_type": "fuel",
  "quantity": 50.5,
  "unit_price": 7.23,
  "total_price": 365.12,
  "fuel_grade": "92#",
  "pump_id": "P01",
  "metadata": {
    "transaction_id": "FT-20250410-001"
  },
  "created_at": "2025-04-10T10:31:00Z",
  "updated_at": "2025-04-10T10:31:00Z"
}
```

**错误响应**

```json
{
  "code": "INVALID_ORDER_STATUS",
  "message": "订单状态不允许添加商品",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ID | 无效的ID格式 |
| INVALID_REQUEST | 请求参数格式错误 |
| VALIDATION_ERROR | 请求参数验证失败 |
| ORDER_NOT_FOUND | 订单不存在 |
| INVALID_ORDER_STATUS | 订单状态不允许添加商品 |
| INTERNAL_ERROR | 内部服务器错误 |

### 5. 移除订单项

从订单中移除指定的订单项（商品）。

**请求URL**

```
DELETE /orders/{id}/items/{item_id}
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 订单ID |
| item_id | int | 是 | 订单项ID |

**注意**: 
- 只有状态为 "new" 或 "processing" 的订单才能移除订单项

**成功响应**

```
204 No Content
```

**错误响应**

```json
{
  "code": "INVALID_ORDER_STATUS",
  "message": "订单状态不允许移除商品",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ORDER_ID | 无效的订单ID格式 |
| INVALID_ITEM_ID | 无效的订单项ID格式 |
| NOT_FOUND | 订单或订单项不存在 |
| INVALID_ORDER_STATUS | 订单状态不允许移除商品 |
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
DELETE /orders/1/items/1
```

### 6. 应用促销

向订单应用促销折扣。

**请求URL**

```
POST /orders/{id}/promotions
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 订单ID |

**请求参数**

```json
{
  "promotion_id": 2001,
  "promotion_name": "会员折扣",
  "promotion_type": "discount",
  "discount_amount": 20.00,
  "free_item_id": null,
  "free_item_name": null,
  "free_item_quantity": null,
  "minimum_order_amount": 300.00,
  "metadata": {
    "reason": "会员优惠"
  }
}
```

**参数说明**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| promotion_id | int | 是 | 促销ID |
| promotion_name | string | 是 | 促销名称 |
| promotion_type | string | 是 | 促销类型 |
| discount_amount | float | 是 | 折扣金额 |
| free_item_id | int | 否 | 赠品ID |
| free_item_name | string | 否 | 赠品名称 |
| free_item_quantity | float | 否 | 赠品数量 |
| minimum_order_amount | float | 否 | 最低订单金额要求 |
| metadata | object | 否 | 元数据 |

**注意**: 
- 只有状态为 "new" 或 "processing" 的订单才能应用促销
- 同一个促销不能重复应用到同一订单

**成功响应**

```json
{
  "id": 1,
  "order_id": 1,
  "promotion_id": 2001,
  "promotion_name": "会员折扣",
  "promotion_type": "discount",
  "discount_amount": 20.00,
  "free_item_id": null,
  "free_item_name": null,
  "free_item_quantity": null,
  "minimum_order_amount": 300.00,
  "metadata": {
    "reason": "会员优惠"
  },
  "created_at": "2025-04-10T10:32:00Z",
  "updated_at": "2025-04-10T10:32:00Z"
}
```

**错误响应**

```json
{
  "code": "PROMOTION_ALREADY_APPLIED",
  "message": "该促销已应用于订单",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ID | 无效的ID格式 |
| INVALID_REQUEST | 请求参数格式错误 |
| VALIDATION_ERROR | 请求参数验证失败 |
| ORDER_NOT_FOUND | 订单不存在 |
| INVALID_ORDER_STATUS | 订单状态不允许应用促销 |
| PROMOTION_ALREADY_APPLIED | 该促销已应用于订单 |
| INTERNAL_ERROR | 内部服务器错误 |

### 7. 完成订单

将订单标记为已完成状态。

**请求URL**

```
POST /orders/{id}/complete
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 订单ID |

**注意**: 
- 只有状态为 "processing" 的订单才能被标记为已完成
- 订单支付金额必须满足或超过订单的最终金额

**成功响应**

返回更新后的订单信息，格式与获取订单详情的响应相同。

**错误响应**

```json
{
  "code": "INSUFFICIENT_PAYMENT",
  "message": "订单支付金额不足",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ID | 无效的ID格式 |
| ORDER_NOT_FOUND | 订单不存在 |
| INVALID_ORDER_STATUS | 订单状态不允许完成操作 |
| INSUFFICIENT_PAYMENT | 订单支付金额不足 |
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
POST /orders/1/complete
```

### 8. 取消订单

将订单标记为已取消状态。

**请求URL**

```
POST /orders/{id}/cancel
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 订单ID |

**注意**: 
- 只有状态为 "new" 或 "processing" 的订单才能被取消

**成功响应**

返回更新后的订单信息，格式与获取订单详情的响应相同。

**错误响应**

```json
{
  "code": "INVALID_ORDER_STATUS",
  "message": "订单状态不允许取消操作",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ID | 无效的ID格式 |
| ORDER_NOT_FOUND | 订单不存在 |
| INVALID_ORDER_STATUS | 订单状态不允许取消操作 |
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
POST /orders/1/cancel
```

## 订单状态流转

订单状态流转图：

```
new --> processing --> completed
  \                 /
   \---> cancelled <
```

- **new**: 新创建的订单
- **processing**: 处理中的订单
- **completed**: 已完成的订单
- **cancelled**: 已取消的订单

## 数据模型

### 订单 (Order)

| 字段 | 类型 | 描述 |
|-----|------|------|
| id | int | 订单ID |
| order_number | string | 订单编号 |
| customer_id | int | 客户ID |
| customer_name | string | 客户名称 |
| station_id | int | 加油站ID |
| status | string | 订单状态 |
| total_amount | float | 订单总金额 |
| discount_amount | float | 折扣金额 |
| final_amount | float | 最终金额 |
| paid_amount | float | 已支付金额 |
| items | array | 订单项列表 |
| promotions | array | 促销列表 |
| payments | array | 支付列表 |
| metadata | object | 元数据 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |
| completed_at | string | 完成时间 |
| cancelled_at | string | 取消时间 |
| employee_no | string | 处理订单的员工编号（可选，可为null） |
| promotion_codes | array | 应用的促销代码列表 |
| deleted_at | string | 删除时间（软删除），未删除时为null |

### 订单项 (OrderItem)

| 字段 | 类型 | 描述 |
|-----|------|------|
| id | int | 订单项ID |
| order_id | int | 订单ID |
| product_id | int | 产品ID |
| product_name | string | 产品名称 |
| product_type | string | 产品类型 |
| quantity | float | 数量 |
| unit_price | float | 单价 |
| total_price | float | 总价 |
| fuel_grade | string | 燃油等级 |
| pump_id | string | 油枪ID |
| metadata | object | 元数据 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |

### 订单促销 (OrderPromotion)

| 字段 | 类型 | 描述 |
|-----|------|------|
| id | int | 促销ID |
| order_id | int | 订单ID |
| promotion_id | int | 促销ID |
| promotion_name | string | 促销名称 |
| promotion_type | string | 促销类型 |
| discount_amount | float | 折扣金额 |
| free_item_id | int | 赠品ID |
| free_item_name | string | 赠品名称 |
| free_item_quantity | float | 赠品数量 |
| minimum_order_amount | float | 最低订单金额要求 |
| metadata | object | 元数据 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |

### 订单支付 (OrderPayment)

| 字段 | 类型 | 描述 |
|-----|------|------|
| id | int | 支付ID |
| order_id | int | 订单ID |
| payment_method | string | 支付方式 |
| amount | float | 支付金额 |
| transaction_id | string | 交易ID |
| status | string | 支付状态 |
| metadata | object | 元数据 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |

```json
{
  "order_id": 12345,             // 订单ID
  "order_items": [               // 订单项列表
    {
      "item_id": 101,            // 商品ID
      "quantity": 2,             // 数量
      "unit_price": 10.99,       // 单价
      "subtotal": 21.98          // 小计
    }
  ],
  "total_amount": 21.98,         // 订单总金额
  "status": "PENDING",           // 订单状态
  "customer_id": "CUST001",      // 客户ID
  "employee_no": "EMP001",       // 处理订单的员工编号（可选字段，可为null）
  "promotion_codes": ["SPRING10"],  // 促销代码
  "created_at": "2025-04-16T15:30:00Z",  // 创建时间
  "updated_at": "2025-04-16T15:30:00Z",  // 更新时间
  "deleted_at": null                     // 删除时间（软删除），未删除时为null
}
```

```json
{
  "order_items": [
    {
      "item_id": 101,
      "quantity": 2,
      "unit_price": 10.99
    }
  ],
  "customer_id": "CUST001",
  "employee_no": "EMP001"        // 可选字段，可为null，不提供时将设为null
}
```

```json
[
  {
    "order_id": 12345,
    "order_items": [
      {
        "item_id": 101,
        "quantity": 2,
        "unit_price": 10.99,
        "subtotal": 21.98
      }
    ],
    "total_amount": 21.98,
    "status": "PENDING",
    "customer_id": "CUST001",
    "employee_no": "EMP001",     // 可选字段，可为null
    "promotion_codes": ["SPRING10"],
    "created_at": "2025-04-16T15:30:00Z",
    "updated_at": "2025-04-16T15:30:00Z"
  },
  {
    "order_id": 12346,
    "order_items": [
      {
        "item_id": 102,
        "quantity": 1,
        "unit_price": 25.50,
        "subtotal": 25.50
      }
    ],
    "total_amount": 25.50,
    "status": "COMPLETED",
    "customer_id": "CUST002",
    "employee_no": null,         // 无处理员工的例子
    "promotion_codes": [],
    "created_at": "2025-04-15T14:20:00Z",
    "updated_at": "2025-04-15T16:30:00Z"
  }
]
```

```json
{
  "order_id": 12345,
  "order_items": [
    {
      "item_id": 101,
      "quantity": 2,
      "unit_price": 10.99,
      "subtotal": 21.98
    }
  ],
  "total_amount": 21.98,
  "status": "PENDING",
  "customer_id": "CUST001",
  "employee_no": "EMP001",       // 可选字段，可为null
  "promotion_codes": ["SPRING10"],
  "created_at": "2025-04-16T15:30:00Z",
  "updated_at": "2025-04-16T15:30:00Z"
}
``` 