{"swagger": "2.0", "info": {"title": "Payment Management API", "description": "支付和支付方式管理相关API接口文档", "version": "1.0.0"}, "host": "localhost:8080", "basePath": "/api/v1", "tags": [{"name": "payments", "description": "Payment processing operations"}, {"name": "payment-methods", "description": "Payment method management operations"}], "paths": {"/payments": {"post": {"tags": ["payments"], "summary": "Process Payment", "description": "处理支付请求", "parameters": [{"name": "payment_request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PaymentRequest"}}], "responses": {"200": {"description": "Payment processed successfully", "schema": {"$ref": "#/definitions/PaymentResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "get": {"tags": ["payments"], "summary": "List Payments", "description": "查询支付记录列表", "parameters": [{"name": "station_id", "in": "query", "type": "integer"}, {"name": "payment_type", "in": "query", "type": "string"}, {"name": "status", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "integer", "default": 1}, {"name": "page_size", "in": "query", "type": "integer", "default": 20}], "responses": {"200": {"description": "Payments retrieved successfully", "schema": {"$ref": "#/definitions/PaymentListResponse"}}}}}, "/payments/{id}": {"get": {"tags": ["payments"], "summary": "Get Payment", "description": "获取支付记录详情", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "Payment details retrieved", "schema": {"$ref": "#/definitions/PaymentDetailResponse"}}}}}, "/payments/{id}/status": {"get": {"tags": ["payments"], "summary": "Get Payment Status", "description": "查询支付状态", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "Payment status retrieved", "schema": {"$ref": "#/definitions/PaymentStatusResponse"}}}}}, "/payments/{id}/cancel": {"post": {"tags": ["payments"], "summary": "Cancel Payment", "description": "取消支付", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "reason", "in": "query", "type": "string"}], "responses": {"200": {"description": "Payment cancelled successfully", "schema": {"$ref": "#/definitions/SuccessResponse"}}}}}, "/payments/refunds": {"post": {"tags": ["payments"], "summary": "Process Refund", "description": "处理退款请求", "parameters": [{"name": "refund_request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RefundRequest"}}], "responses": {"200": {"description": "Refund processed successfully", "schema": {"$ref": "#/definitions/RefundResponse"}}}}}, "/payments/refunds/{id}/status": {"get": {"tags": ["payments"], "summary": "Get Refund Status", "description": "查询退款状态", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "Refund status retrieved", "schema": {"$ref": "#/definitions/RefundStatusResponse"}}}}}, "/payment-methods": {"get": {"tags": ["payment-methods"], "summary": "List Payment Methods", "description": "获取支付方式列表", "parameters": [{"name": "enabled", "in": "query", "type": "boolean"}, {"name": "station_id", "in": "query", "type": "integer"}, {"name": "type", "in": "query", "type": "string"}], "responses": {"200": {"description": "Payment methods retrieved", "schema": {"$ref": "#/definitions/PaymentMethodListResponse"}}}}, "post": {"tags": ["payment-methods"], "summary": "Create Payment Method", "description": "创建支付方式", "parameters": [{"name": "payment_method", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PaymentMethodCreateRequest"}}], "responses": {"201": {"description": "Payment method created", "schema": {"$ref": "#/definitions/PaymentMethodResponse"}}}}}, "/payment-methods/{id}": {"put": {"tags": ["payment-methods"], "summary": "Update Payment Method", "description": "更新支付方式", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "payment_method_update", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PaymentMethodUpdateRequest"}}], "responses": {"200": {"description": "Payment method updated", "schema": {"$ref": "#/definitions/PaymentMethodResponse"}}}}}, "/payment-methods/{id}/enable": {"post": {"tags": ["payment-methods"], "summary": "Enable Payment Method", "description": "启用支付方式", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "Payment method enabled", "schema": {"$ref": "#/definitions/SuccessResponse"}}}}}, "/payment-methods/{id}/disable": {"post": {"tags": ["payment-methods"], "summary": "Disable Payment Method", "description": "禁用支付方式", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "Payment method disabled", "schema": {"$ref": "#/definitions/SuccessResponse"}}}}}}, "definitions": {"PaymentRequest": {"type": "object", "required": ["order_id", "amount", "currency", "payment_type", "station_id"], "properties": {"order_id": {"type": "string", "example": "ORDER_001"}, "amount": {"type": "number", "format": "double", "example": 150000.0}, "currency": {"type": "string", "example": "IDR"}, "payment_type": {"type": "string", "enum": ["cash", "card", "mobile", "voucher"], "example": "cash"}, "payment_method": {"type": "integer", "format": "int64", "example": 1}, "customer_id": {"type": "string", "example": "CUST_001"}, "customer_name": {"type": "string", "example": "<PERSON>"}, "station_id": {"type": "integer", "format": "int64", "example": 1}, "terminal_id": {"type": "string", "example": "TERM_001"}, "operator_id": {"type": "string", "example": "OP_001"}, "payment_params": {"type": "object"}, "notify_url": {"type": "string", "example": "https://example.com/notify"}, "return_url": {"type": "string", "example": "https://example.com/return"}, "timeout_minutes": {"type": "integer", "example": 15}, "metadata": {"type": "object"}}}, "RefundRequest": {"type": "object", "required": ["payment_id", "amount", "reason"], "properties": {"payment_id": {"type": "integer", "format": "int64", "example": 123}, "amount": {"type": "number", "format": "double", "example": 75000.0}, "reason": {"type": "string", "example": "Customer request"}, "operator_id": {"type": "string", "example": "OP_001"}, "metadata": {"type": "object"}}}, "PaymentMethodCreateRequest": {"type": "object", "required": ["type", "name", "display_name", "gateway_type"], "properties": {"type": {"type": "string", "enum": ["cash", "card", "mobile", "voucher"], "example": "cash"}, "name": {"type": "string", "example": "cash"}, "display_name": {"type": "string", "example": "Cash Payment"}, "description": {"type": "string", "example": "Cash payment method"}, "icon": {"type": "string", "example": "cash-icon.png"}, "gateway_type": {"type": "string", "example": "cash_gateway"}, "gateway_config": {"type": "object"}, "enabled": {"type": "boolean", "default": true}, "min_amount": {"type": "number", "format": "double", "example": 1000.0}, "max_amount": {"type": "number", "format": "double", "example": 10000000.0}, "daily_limit": {"type": "number", "format": "double", "example": 50000000.0}, "fee_type": {"type": "string", "enum": ["fixed", "percentage"], "example": "fixed"}, "fee_value": {"type": "number", "format": "double", "example": 2500.0}, "sort_order": {"type": "integer", "example": 1}, "group_name": {"type": "string", "example": "Traditional Methods"}}}, "PaymentMethodUpdateRequest": {"type": "object", "properties": {"display_name": {"type": "string", "example": "Updated Cash Payment"}, "description": {"type": "string", "example": "Updated description"}, "icon": {"type": "string", "example": "new-icon.png"}, "gateway_config": {"type": "object"}, "min_amount": {"type": "number", "format": "double", "example": 2000.0}, "max_amount": {"type": "number", "format": "double", "example": 20000000.0}, "daily_limit": {"type": "number", "format": "double", "example": 100000000.0}, "fee_type": {"type": "string", "enum": ["fixed", "percentage"], "example": "percentage"}, "fee_value": {"type": "number", "format": "double", "example": 2.5}, "sort_order": {"type": "integer", "example": 2}}}, "Payment": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "example": 123}, "order_id": {"type": "string", "example": "ORDER_001"}, "payment_number": {"type": "string", "example": "PAY_20240115_001"}, "amount": {"type": "number", "format": "double", "example": 150000.0}, "currency": {"type": "string", "example": "IDR"}, "payment_type": {"type": "string", "enum": ["cash", "card", "mobile", "voucher"], "example": "cash"}, "payment_method": {"type": "integer", "format": "int64", "example": 1}, "status": {"type": "string", "enum": ["pending", "completed", "failed", "canceled", "refunded"], "example": "completed"}, "gateway_type": {"type": "string", "example": "cash_gateway"}, "gateway_order_no": {"type": "string", "example": "GW_001"}, "customer_id": {"type": "string", "example": "CUST_001"}, "customer_name": {"type": "string", "example": "<PERSON>"}, "station_id": {"type": "integer", "format": "int64", "example": 1}, "terminal_id": {"type": "string", "example": "TERM_001"}, "operator_id": {"type": "string", "example": "OP_001"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:35:00Z"}, "paid_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:35:00Z"}, "expires_at": {"type": "string", "format": "date-time", "example": "2024-01-15T11:30:00Z"}, "metadata": {"type": "object"}}}, "PaymentMethod": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "example": 1}, "type": {"type": "string", "enum": ["cash", "card", "mobile", "voucher"], "example": "cash"}, "name": {"type": "string", "example": "cash"}, "display_name": {"type": "string", "example": "Cash Payment"}, "description": {"type": "string", "example": "Cash payment method"}, "icon": {"type": "string", "example": "cash-icon.png"}, "gateway_type": {"type": "string", "example": "cash_gateway"}, "gateway_config": {"type": "object"}, "enabled": {"type": "boolean", "example": true}, "min_amount": {"type": "number", "format": "double", "example": 1000.0}, "max_amount": {"type": "number", "format": "double", "example": 10000000.0}, "daily_limit": {"type": "number", "format": "double", "example": 50000000.0}, "fee_type": {"type": "string", "enum": ["fixed", "percentage"], "example": "fixed"}, "fee_value": {"type": "number", "format": "double", "example": 2500.0}, "sort_order": {"type": "integer", "example": 1}, "group_name": {"type": "string", "example": "Traditional Methods"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:35:00Z"}}}, "PaymentResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"payment_id": {"type": "integer", "format": "int64", "example": 123}, "payment_number": {"type": "string", "example": "PAY_20240115_001"}, "status": {"type": "string", "enum": ["pending", "completed", "failed", "canceled", "refunded"], "example": "pending"}, "amount": {"type": "number", "format": "double", "example": 150000.0}, "extra": {"type": "object"}}}}}, "RefundResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"refund_id": {"type": "integer", "format": "int64", "example": 456}, "refund_number": {"type": "string", "example": "REF_20240115_001"}, "status": {"type": "string", "enum": ["pending", "completed", "failed"], "example": "pending"}, "amount": {"type": "number", "format": "double", "example": 75000.0}, "extra": {"type": "object"}}}}}, "PaymentDetailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/definitions/Payment"}}}, "PaymentStatusResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"payment_id": {"type": "string", "example": "123"}, "status": {"type": "string", "enum": ["pending", "completed", "failed", "canceled", "refunded"], "example": "completed"}}}}}, "RefundStatusResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"refund_id": {"type": "string", "example": "456"}, "status": {"type": "string", "enum": ["pending", "completed", "failed"], "example": "completed"}}}}}, "PaymentListResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"payments": {"type": "array", "items": {"$ref": "#/definitions/Payment"}}, "total": {"type": "integer", "example": 100}, "page": {"type": "integer", "example": 1}, "pageSize": {"type": "integer", "example": 20}}}}}, "PaymentMethodListResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/definitions/PaymentMethod"}}}}, "PaymentMethodResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/definitions/PaymentMethod"}}}, "SuccessResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Operation completed successfully"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "object", "properties": {"code": {"type": "string", "example": "PAYMENT_NOT_FOUND"}, "message": {"type": "string", "example": "Payment not found"}}}}}}}