# BOS项目结构

## 目录结构

```
bos/
├── cmd/                       # 命令行工具目录
│   └── api/                   # API服务命令
├── config/                    # 配置文件和配置管理
│   ├── config.go              # 配置加载与管理
│   └── config.yaml            # 配置文件
├── docs/                      # 文档
│   ├── api-documentation.md   # API文档
│   ├── order-api-documentation.md # 订单API文档
│   ├── fuel-transaction-api-documentation.md # 燃油交易API文档
│   ├── implementation-summary.md  # 实现总结
│   └── project-structure.md   # 项目结构文档
├── internal/                  # 内部代码，不可被外部导入
│   ├── api/                   # API相关代码
│   │   ├── handlers/          # API处理器
│   │   │   ├── fuel_transaction_handler.go  # 燃油交易处理器
│   │   │   └── order_handler.go        # 订单处理器
│   │   └── router/            # 路由管理
│   │       └── router.go      # 路由注册
│   └── models/                # 内部模型定义
│       └── error.go           # 错误响应模型
├── go.mod                     # Go模块定义
├── go.sum                     # Go依赖校验和
└── main.go                    # 主入口点
```

## 架构说明

BOS项目采用分层架构，主要分为以下几层：

1. **表示层(Presentation Layer)**：
   - 位于 `internal/api/handlers` 目录
   - 负责处理HTTP请求和响应
   - 参数验证和响应格式化
   - 包含燃油交易处理器(fuel_transaction_handler.go)和订单处理器(order_handler.go)

2. **路由层(Router Layer)**：
   - 位于 `internal/api/router` 目录
   - 负责API路由注册和管理
   - 提供API组织和版本控制

3. **服务层(Service Layer)**：
   - 使用order-service中的服务实现
   - 包含业务逻辑和规则
   - 协调存储库操作

4. **存储层(Repository Layer)**：
   - 使用order-service中的存储库实现
   - 负责数据持久化和查询
   - 隔离数据访问细节

5. **配置层(Configuration Layer)**：
   - 位于 `config/` 目录
   - 负责加载和管理配置
   - 支持多种配置源(文件、环境变量)

## 依赖管理

BOS项目主要使用以下外部依赖：

1. **Web框架**：
   - Echo框架 - 轻量级高性能Web框架

2. **数据库**：
   - PostgreSQL - 通过order-service的数据库连接管理

3. **配置管理**：
   - yaml.v3 - YAML配置文件解析

4. **其他依赖**：
   - order-service模块 - 包含存储库和服务实现

## 扩展点

BOS项目的设计考虑了以下扩展点：

1. **新增API端点**：
   - 在handlers目录中创建新的处理器
   - 在router中注册新的路由

2. **配置扩展**：
   - 在Config结构体中添加新的配置项
   - 修改LoadConfig方法支持新的配置源

3. **支持新的数据模型**：
   - 在internal/models目录添加新的模型定义
   - 使用order-service中的存储库或创建新的存储库

## API文档

BOS项目包含以下API文档：

1. **订单API文档**：
   - 位于 `docs/order-api-documentation.md`
   - 详细描述订单相关的所有API端点

2. **燃油交易API文档**：
   - 位于 `docs/fuel-transaction-api-documentation.md`
   - 详细描述燃油交易相关的所有API端点
