{"info": {"name": "BOS Promotion Service API", "description": "BOS促销服务REST API接口集合，基于真实测试结果", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8090", "type": "string"}], "item": [{"name": "促销套件创建", "item": [{"name": "创建BP92固定金额折扣", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"promotion\": {\n    \"name\": \"Hemat 28L BP92\",\n    \"description\": \"BP 92 Fuel 28L or more: Get 15000 IDR discount\",\n    \"type\": \"FIXED_DISCOUNT\",\n    \"status\": \"ACTIVE\",\n    \"scope\": \"ORDER\",\n    \"start_time\": \"2024-01-01T00:00:00Z\",\n    \"end_time\": \"2024-12-31T23:59:59Z\",\n    \"priority\": 10,\n    \"for_members\": true,\n    \"for_non_members\": true,\n    \"created_by\": \"admin\"\n  },\n  \"discount\": {\n    \"type\": \"FIXED_AMOUNT\",\n    \"code\": \"HEMAT_28L_BP92\",\n    \"discount_value\": 15000\n  },\n  \"rules\": [\n    {\n      \"type\": \"FUEL_TYPE\",\n      \"path\": \"items.*.category_ids\",\n      \"operator\": \"contains\",\n      \"value\": \"101\",\n      \"priority\": 1,\n      \"description\": \"仅限BP 92燃油\"\n    },\n    {\n      \"type\": \"FUEL_VOLUME\",\n      \"path\": \"items.fuel_volume\",\n      \"operator\": \"gte\",\n      \"value\": 28.0,\n      \"priority\": 2,\n      \"description\": \"燃油量必须达到28升以上\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/promotions/save", "host": ["{{baseUrl}}"], "path": ["api", "v1", "promotions", "save"]}}}, {"name": "创建Ultimate免费升数促销", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"promotion\": {\n    \"name\": \"Free Volume Ultimate Diesel\",\n    \"description\": \"Ultimate Diesel 25L or more: Get 2L free volume\",\n    \"type\": \"FREE_VOLUME\",\n    \"status\": \"ACTIVE\",\n    \"scope\": \"ORDER\",\n    \"start_time\": \"2024-01-01T00:00:00Z\",\n    \"end_time\": \"2024-12-31T23:59:59Z\",\n    \"priority\": 15,\n    \"for_members\": true,\n    \"for_non_members\": true,\n    \"created_by\": \"admin\"\n  },\n  \"discount\": {\n    \"type\": \"FREE_ITEM\",\n    \"code\": \"FREE_VOL_UD_25L\",\n    \"discount_value\": 2.0\n  },\n  \"rules\": [\n    {\n      \"type\": \"FUEL_TYPE\",\n      \"path\": \"items.*.category_ids\",\n      \"operator\": \"contains\",\n      \"value\": \"103\",\n      \"priority\": 1,\n      \"description\": \"仅限Ultimate Diesel燃油\"\n    },\n    {\n      \"type\": \"FUEL_VOLUME\",\n      \"path\": \"items.fuel_volume\",\n      \"operator\": \"gte\",\n      \"value\": 25.0,\n      \"priority\": 2,\n      \"description\": \"燃油量必须达到25升以上\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/promotions/save", "host": ["{{baseUrl}}"], "path": ["api", "v1", "promotions", "save"]}}}]}, {"name": "促销计算", "item": [{"name": "计算BP92 30升订单折扣", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"orderId\": \"order_bp92_30L\",\n  \"userId\": \"user_001\",\n  \"orderAmount\": 450000.0,\n  \"items\": [\n    {\n      \"itemId\": \"fuel_bp92\",\n      \"name\": \"BP 92燃油\",\n      \"categoryIds\": [\"101\"],\n      \"quantity\": 30.0,\n      \"price\": 15000.0\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/calculator/calculate", "host": ["{{baseUrl}}"], "path": ["api", "v1", "calculator", "calculate"]}}}, {"name": "计算Ultimate 30升订单折扣", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"orderId\": \"order_ultimate_30L\",\n  \"userId\": \"user_002\",\n  \"orderAmount\": 540000.0,\n  \"items\": [\n    {\n      \"itemId\": \"fuel_ultimate\",\n      \"name\": \"Ultimate燃油\",\n      \"categoryIds\": [\"102\"],\n      \"quantity\": 30.0,\n      \"price\": 18000.0\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/calculator/calculate", "host": ["{{baseUrl}}"], "path": ["api", "v1", "calculator", "calculate"]}}}, {"name": "计算不符合条件的订单", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"orderId\": \"order_bp92_2L\",\n  \"userId\": \"user_004\",\n  \"orderAmount\": 30000.0,\n  \"items\": [\n    {\n      \"itemId\": \"fuel_bp92\",\n      \"name\": \"BP 92燃油\",\n      \"categoryIds\": [\"101\"],\n      \"quantity\": 2.0,\n      \"price\": 15000.0\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/calculator/calculate", "host": ["{{baseUrl}}"], "path": ["api", "v1", "calculator", "calculate"]}}}]}, {"name": "促销管理", "item": [{"name": "查询促销列表", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/promotions", "host": ["{{baseUrl}}"], "path": ["api", "v1", "promotions"]}}}, {"name": "分页查询促销", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/promotions?page=1&pageSize=5", "host": ["{{baseUrl}}"], "path": ["api", "v1", "promotions"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "5"}]}}}, {"name": "按状态筛选促销", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/promotions?status=ACTIVE", "host": ["{{baseUrl}}"], "path": ["api", "v1", "promotions"], "query": [{"key": "status", "value": "ACTIVE"}]}}}, {"name": "查看促销详情", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/promotions/:id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "promotions", ":id"], "variable": [{"key": "id", "value": "fc244737-9cb6-43e5-bce8-d1b350fee747", "description": "促销ID"}]}}}, {"name": "更新促销状态", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACTIVE\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/promotions/:id/status", "host": ["{{baseUrl}}"], "path": ["api", "v1", "promotions", ":id", "status"], "variable": [{"key": "id", "value": "fc244737-9cb6-43e5-bce8-d1b350fee747", "description": "促销ID"}]}}}, {"name": "删除促销", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/api/v1/promotions/:id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "promotions", ":id"], "variable": [{"key": "id", "value": "fc244737-9cb6-43e5-bce8-d1b350fee747", "description": "促销ID"}]}}}]}]}