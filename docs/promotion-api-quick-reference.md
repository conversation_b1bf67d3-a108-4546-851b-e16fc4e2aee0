# BOS Promotion API 快速参考

## 🎯 核心接口

| 接口 | 方法 | 路径 | 功能 |
|------|------|------|------|
| 创建促销套件 | POST | `/api/v1/promotions/save` | 创建完整促销套件 |
| 计算促销折扣 | POST | `/api/v1/calculator/calculate` | 计算订单折扣 |
| 查询促销列表 | GET | `/api/v1/promotions` | 获取促销列表 |
| 查看促销详情 | GET | `/api/v1/promotions/:id` | 获取促销详情 |
| 更新促销状态 | POST | `/api/v1/promotions/:id/status` | 更新状态 |
| 删除促销 | DELETE | `/api/v1/promotions/:id` | 删除促销 |

## 📊 促销类型

### 促销类型组合
- **固定金额折扣**: `FIXED_DISCOUNT` + `FIXED_AMOUNT`
- **免费升数**: `FREE_VOLUME` + `FREE_ITEM`

### 燃油类型代码
- **101**: BP 92燃油
- **102**: Ultimate燃油  
- **103**: Ultimate Diesel燃油

## 🔧 快速创建示例

### 1. 固定金额折扣 (BP92 28升优惠15000印尼盾)

```json
{
  "promotion": {
    "name": "Hemat 28L BP92",
    "description": "BP 92 Fuel 28L or more: Get 15000 IDR discount",
    "type": "FIXED_DISCOUNT",
    "status": "ACTIVE",
    "scope": "ORDER",
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-12-31T23:59:59Z",
    "priority": 10,
    "for_members": true,
    "for_non_members": true
  },
  "discount": {
    "type": "FIXED_AMOUNT",
    "code": "HEMAT_28L_BP92",
    "discount_value": 15000
  },
  "rules": [
    {
      "type": "FUEL_TYPE",
      "path": "items.*.category_ids",
      "operator": "contains",
      "value": "101",
      "priority": 1,
      "description": "仅限BP 92燃油"
    },
    {
      "type": "FUEL_VOLUME",
      "path": "items.fuel_volume",
      "operator": "gte",
      "value": 28.0,
      "priority": 2,
      "description": "燃油量必须达到28升以上"
    }
  ]
}
```

### 2. 免费升数 (Ultimate Diesel 25升送2升)

```json
{
  "promotion": {
    "name": "Free Volume Ultimate Diesel",
    "description": "Ultimate Diesel 25L or more: Get 2L free volume",
    "type": "FREE_VOLUME",
    "status": "ACTIVE",
    "scope": "ORDER",
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-12-31T23:59:59Z",
    "priority": 15,
    "for_members": true,
    "for_non_members": true
  },
  "discount": {
    "type": "FREE_ITEM",
    "code": "FREE_VOL_UD_25L",
    "discount_value": 2.0
  },
  "rules": [
    {
      "type": "FUEL_TYPE",
      "path": "items.*.category_ids",
      "operator": "contains",
      "value": "103",
      "priority": 1,
      "description": "仅限Ultimate Diesel燃油"
    },
    {
      "type": "FUEL_VOLUME",
      "path": "items.fuel_volume",
      "operator": "gte",
      "value": 25.0,
      "priority": 2,
      "description": "燃油量必须达到25升以上"
    }
  ]
}
```

## 🧮 计算示例

### 订单数据格式

```json
{
  "orderId": "order_001",
  "userId": "user_001",
  "orderAmount": 450000.0,
  "items": [
    {
      "itemId": "fuel_bp92",
      "name": "BP 92燃油",
      "categoryIds": ["101"],
      "quantity": 30.0,
      "price": 15000.0
    }
  ]
}
```

### 计算结果格式

```json
{
  "success": true,
  "message": "折扣计算成功",
  "orderId": "order_001",
  "originalAmount": 450000,
  "discountedAmount": 435000,
  "discountAmount": 15000,
  "appliedPromotions": [
    {
      "promotionId": "xxx",
      "promotionName": "Gratis 1L BP 92",
      "discountType": "FREE_ITEM",
      "discountValue": 15000,
      "discountAmount": 15000,
      "description": "Promotion 'Gratis 1L BP 92' - Free Item Discount",
      "applicableItems": ["fuel_bp92"]
    }
  ],
  "items": [
    {
      "itemId": "fuel_bp92",
      "name": "BP 92燃油",
      "originalPrice": 15000,
      "discountedPrice": 14500,
      "quantity": 30,
      "categoryIds": ["101", "fuel"]
    }
  ],
  "calculationTime": "2025-08-12T13:08:56+08:00",
  "totalItems": 1,
  "totalQuantity": 30
}
```

## 📋 常用规则配置

### 燃油类型规则
```json
{
  "type": "FUEL_TYPE",
  "path": "items.*.category_ids",
  "operator": "contains",
  "value": "101",  // BP92: 101, Ultimate: 102, Ultimate Diesel: 103
  "priority": 1,
  "description": "仅限指定燃油类型"
}
```

### 燃油升数规则
```json
{
  "type": "FUEL_VOLUME",
  "path": "items.fuel_volume",
  "operator": "gte",
  "value": 28.0,  // 最低升数要求
  "priority": 2,
  "description": "燃油量必须达到指定升数以上"
}
```

### 最低金额规则
```json
{
  "type": "MIN_PURCHASE",
  "path": "order.total_amount",
  "operator": "gte",
  "value": 100000.0,
  "priority": 3,
  "description": "最低消费金额要求"
}
```

## 🎯 前端集成要点

### 1. 创建促销表单字段

**必需字段**:
- `promotion.name` - 促销名称
- `promotion.description` - 促销描述  
- `promotion.type` - 促销类型 (下拉选择)
- `promotion.status` - 状态 (下拉选择)
- `promotion.start_time` - 开始时间 (日期时间选择器)
- `promotion.end_time` - 结束时间 (日期时间选择器)
- `promotion.priority` - 优先级 (数字输入)
- `discount.type` - 折扣类型 (下拉选择)
- `discount.code` - 折扣代码 (文本输入，需验证唯一性)
- `discount.discount_value` - 折扣值 (数字输入)

**可选字段**:
- `promotion.for_members` - 适用于会员 (复选框)
- `promotion.for_non_members` - 适用于非会员 (复选框)
- `promotion.created_by` - 创建者 (自动填充)

### 2. 规则配置界面

**动态规则添加**:
- 规则类型下拉选择
- 根据规则类型动态显示操作符选项
- 根据规则类型动态显示值输入框
- 支持添加/删除多个规则
- 规则优先级设置

### 3. 时间周期配置

**可选配置**:
- 周期类型选择 (DAILY/WEEKLY/MONTHLY)
- 每日时间范围设置 (start_hour/end_hour)
- 周期开始/结束时间

### 4. 表单验证

**客户端验证**:
- 开始时间不能晚于结束时间
- 折扣值必须大于0
- 折扣代码格式验证
- 优先级必须为正整数
- 必需字段非空验证

### 5. 错误处理

**常见错误**:
- 折扣代码重复 (409 Conflict)
- 数据格式错误 (400 Bad Request)
- 权限不足 (403 Forbidden)
- 促销不存在 (404 Not Found)

### 6. 状态管理

**促销状态**:
- `ACTIVE` - 进行中 (绿色)
- `INACTIVE` - 已停用 (灰色)
- `EXPIRED` - 已过期 (红色)

### 7. 列表展示

**推荐字段**:
- 促销名称 (链接到详情页)
- 促销类型 (标签显示)
- 状态 (彩色标签)
- 优先级 (数字显示)
- 开始/结束时间 (格式化显示)
- 操作按钮 (编辑/删除/状态切换)

### 8. 计算结果展示

**折扣信息**:
- 原始金额
- 折扣金额 (突出显示)
- 最终金额
- 应用的促销列表
- 每个商品的折扣详情

## 📞 开发支持

**测试环境**: `http://localhost:8090`  
**生产环境**: `https://bos-api.bp.com`  
**文档**: `/docs/promotion-rest-api-documentation.md`
