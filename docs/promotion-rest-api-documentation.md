# BOS Promotion Service REST API 接口文档

## 概述

本文档基于真实测试结果，提供BOS系统中promotion-service的完整REST API接口说明，包含统一促销套件创建、促销计算和促销管理的所有接口。

## 🎯 核心接口概览

| 接口 | 方法 | 路径 | 功能 | 状态 |
|------|------|------|------|------|
| 创建促销套件 | POST | `/api/v1/promotions/save` | 统一创建促销套件 | ✅ 已测试 |
| 计算促销折扣 | POST | `/api/v1/calculator/calculate` | 计算订单适用折扣 | ✅ 已测试 |
| 查询促销列表 | GET | `/api/v1/promotions` | 获取促销活动列表 | ✅ 已测试 |
| 查看促销详情 | GET | `/api/v1/promotions/:id` | 获取单个促销详情 | ✅ 可用 |
| 删除促销 | DELETE | `/api/v1/promotions/:id` | 删除促销活动 | ✅ 可用 |
| 更新促销状态 | POST | `/api/v1/promotions/:id/status` | 更新促销状态 | ✅ 可用 |

## 📊 支持的促销类型

### 促销类型 (Promotion.Type)
- **FIXED_DISCOUNT**: 固定金额折扣促销
- **FREE_VOLUME**: 免费升数促销

### 折扣类型 (Discount.Type)
- **FIXED_AMOUNT**: 固定金额折扣
- **FREE_ITEM**: 免费商品/升数折扣

### 燃油类型代码
- **101**: BP 92燃油
- **102**: Ultimate燃油
- **103**: Ultimate Diesel燃油

## 🔧 接口详细说明

### 1. 创建促销套件

**接口**: `POST /api/v1/promotions/save`  
**功能**: 统一创建包含促销、折扣、规则、时间周期的完整促销套件  
**Content-Type**: `application/json`

#### 请求数据结构

```json
{
  "promotion": {
    "name": "string",              // 必需：促销名称
    "description": "string",       // 必需：促销描述
    "type": "string",             // 必需：促销类型 (FIXED_DISCOUNT|FREE_VOLUME)
    "status": "string",           // 必需：状态 (ACTIVE|INACTIVE|EXPIRED)
    "scope": "string",            // 必需：范围 (ORDER)
    "start_time": "string",       // 必需：开始时间 (ISO 8601)
    "end_time": "string",         // 必需：结束时间 (ISO 8601)
    "priority": "number",         // 必需：优先级 (数值越大优先级越高)
    "for_members": "boolean",     // 可选：是否适用于会员
    "for_non_members": "boolean", // 可选：是否适用于非会员
    "created_by": "string"        // 可选：创建者
  },
  "discount": {
    "type": "string",             // 必需：折扣类型 (FIXED_AMOUNT|FREE_ITEM)
    "code": "string",             // 必需：折扣代码 (唯一)
    "discount_value": "number"    // 必需：折扣值
  },
  "time_cycles": [                // 可选：时间周期配置
    {
      "type": "string",           // 时间周期类型 (DAILY|WEEKLY|MONTHLY)
      "start_time": "string",     // 周期开始时间
      "end_time": "string",       // 周期结束时间
      "start_hour": "number",     // 每日开始小时 (0-23)
      "end_hour": "number",       // 每日结束小时 (0-24)
      "priority": "number"        // 优先级
    }
  ],
  "rules": [                      // 可选：业务规则配置
    {
      "type": "string",           // 规则类型 (FUEL_TYPE|FUEL_VOLUME|MIN_PURCHASE等)
      "path": "string",           // 数据路径
      "operator": "string",       // 操作符 (contains|gte|lte|eq等)
      "value": "any",             // 规则值
      "priority": "number",       // 优先级
      "description": "string"     // 规则描述
    }
  ]
}
```

#### 响应数据结构

```json
{
  "id": "string",                 // 促销ID
  "name": "string",               // 促销名称
  "description": "string",        // 促销描述
  "type": "string",               // 促销类型
  "status": "string",             // 状态
  "scope": "string",              // 范围
  "value": "number",              // 促销值
  "start_time": "string",         // 开始时间
  "end_time": "string",           // 结束时间
  "priority": "number",           // 优先级
  "for_members": "boolean",       // 是否适用于会员
  "for_non_members": "boolean",   // 是否适用于非会员
  "created_by": "string",         // 创建者
  "version": "number",            // 版本号
  "created_at": "string",         // 创建时间
  "updated_at": "string"          // 更新时间
}
```

### 2. 计算促销折扣

**接口**: `POST /api/v1/calculator/calculate`  
**功能**: 根据订单信息计算适用的促销折扣  
**Content-Type**: `application/json`

#### 请求数据结构

```json
{
  "orderId": "string",            // 必需：订单ID
  "userId": "string",             // 必需：用户ID
  "orderAmount": "number",        // 必需：订单总金额
  "items": [                      // 必需：订单商品列表
    {
      "itemId": "string",         // 商品ID
      "name": "string",           // 商品名称
      "categoryIds": ["string"],  // 商品分类ID列表
      "quantity": "number",       // 数量
      "price": "number"           // 单价
    }
  ],
  "couponCode": "string"          // 可选：优惠券代码
}
```

#### 响应数据结构

```json
{
  "success": "boolean",           // 计算是否成功
  "message": "string",            // 响应消息
  "orderId": "string",            // 订单ID
  "originalAmount": "number",     // 原始金额
  "discountedAmount": "number",   // 折扣后金额
  "discountAmount": "number",     // 总折扣金额
  "appliedPromotions": [          // 应用的促销列表
    {
      "promotionId": "string",    // 促销ID
      "promotionName": "string",  // 促销名称
      "discountType": "string",   // 折扣类型
      "discountValue": "number",  // 折扣值
      "discountAmount": "number", // 折扣金额
      "description": "string",    // 描述
      "applicableItems": ["string"], // 适用商品
      "metadata": "object"        // 元数据
    }
  ],
  "items": [                      // 商品折扣详情
    {
      "itemId": "string",         // 商品ID
      "name": "string",           // 商品名称
      "originalPrice": "number",  // 原始单价
      "discountedPrice": "number", // 折扣后单价
      "quantity": "number",       // 数量
      "categoryIds": ["string"],  // 分类ID
      "attributes": "object"      // 商品属性
    }
  ],
  "calculationTime": "string",    // 计算时间
  "totalItems": "number",         // 商品种类数
  "totalQuantity": "number"       // 商品总数量
}
```

### 3. 查询促销列表

**接口**: `GET /api/v1/promotions`  
**功能**: 获取促销活动列表，支持分页和筛选

#### 查询参数

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| status | string | 否 | 状态筛选 |
| type | string | 否 | 类型筛选 |
| search | string | 否 | 搜索关键词 |

#### 响应数据结构

```json
{
  "currentPage": "number",        // 当前页码
  "pageSize": "number",           // 每页数量
  "total": "number",              // 总记录数
  "totalPages": "number",         // 总页数
  "hasNext": "boolean",           // 是否有下一页
  "hasPrev": "boolean",           // 是否有上一页
  "promotions": [                 // 促销列表
    {
      "ID": "string",             // 促销ID
      "Name": "string",           // 促销名称
      "Description": "string",    // 促销描述
      "Type": "string",           // 促销类型
      "Status": "string",         // 状态
      "Priority": "number",       // 优先级
      "StartTime": "string",      // 开始时间
      "EndTime": "string",        // 结束时间
      "CreatedAt": "string",      // 创建时间
      "UpdatedAt": "string"       // 更新时间
    }
  ]
}
```

## 📝 创建示例

### 示例1: 固定金额折扣促销 (FIXED_DISCOUNT + FIXED_AMOUNT)

#### BP92燃油28升优惠15000印尼盾

```bash
curl -X POST http://localhost:8090/api/v1/promotions/save \
  -H "Content-Type: application/json" \
  -d '{
    "promotion": {
      "name": "Hemat 28L BP92",
      "description": "BP 92 Fuel 28L or more: Get 15000 IDR discount",
      "type": "FIXED_DISCOUNT",
      "status": "ACTIVE",
      "scope": "ORDER",
      "start_time": "2024-01-01T00:00:00Z",
      "end_time": "2024-12-31T23:59:59Z",
      "priority": 10,
      "for_members": true,
      "for_non_members": true,
      "created_by": "admin"
    },
    "discount": {
      "type": "FIXED_AMOUNT",
      "code": "HEMAT_28L_BP92",
      "discount_value": 15000
    },
    "time_cycles": [
      {
        "type": "DAILY",
        "start_time": "2024-01-01T00:00:00Z",
        "end_time": "2024-12-31T23:59:59Z",
        "start_hour": 0,
        "end_hour": 24,
        "priority": 0
      }
    ],
    "rules": [
      {
        "type": "FUEL_TYPE",
        "path": "items.*.category_ids",
        "operator": "contains",
        "value": "101",
        "priority": 1,
        "description": "仅限BP 92燃油"
      },
      {
        "type": "FUEL_VOLUME",
        "path": "items.fuel_volume",
        "operator": "gte",
        "value": 28.0,
        "priority": 2,
        "description": "燃油量必须达到28升以上"
      }
    ]
  }'
```

**响应示例**:
```json
{
  "id": "fc244737-9cb6-43e5-bce8-d1b350fee747",
  "name": "Hemat 28L BP92",
  "description": "BP 92 Fuel 28L or more: Get 15000 IDR discount",
  "type": "FIXED_DISCOUNT",
  "status": "ACTIVE",
  "scope": "ORDER",
  "priority": 10,
  "for_members": true,
  "for_non_members": true,
  "created_by": "admin",
  "version": 1,
  "created_at": "2025-08-12T05:27:55.984548Z",
  "updated_at": "2025-08-12T05:27:55.984548Z"
}
```

#### Ultimate燃油28升优惠20000印尼盾

```bash
curl -X POST http://localhost:8090/api/v1/promotions/save \
  -H "Content-Type: application/json" \
  -d '{
    "promotion": {
      "name": "Hemat 28L Ultimate",
      "description": "Ultimate Fuel 28L or more: Get 20000 IDR discount",
      "type": "FIXED_DISCOUNT",
      "status": "ACTIVE",
      "scope": "ORDER",
      "start_time": "2024-01-01T00:00:00Z",
      "end_time": "2024-12-31T23:59:59Z",
      "priority": 10,
      "for_members": true,
      "for_non_members": true,
      "created_by": "admin"
    },
    "discount": {
      "type": "FIXED_AMOUNT",
      "code": "HEMAT_28L_ULTIMATE",
      "discount_value": 20000
    },
    "rules": [
      {
        "type": "FUEL_TYPE",
        "path": "items.*.category_ids",
        "operator": "contains",
        "value": "102",
        "priority": 1,
        "description": "仅限Ultimate燃油"
      },
      {
        "type": "FUEL_VOLUME",
        "path": "items.fuel_volume",
        "operator": "gte",
        "value": 28.0,
        "priority": 2,
        "description": "燃油量必须达到28升以上"
      }
    ]
  }'
```

#### 小量燃油优惠 (3升阈值)

```bash
curl -X POST http://localhost:8090/api/v1/promotions/save \
  -H "Content-Type: application/json" \
  -d '{
    "promotion": {
      "name": "Hemat 3L BP92",
      "description": "BP 92 Fuel 3L or more: Get 1000 IDR discount",
      "type": "FIXED_DISCOUNT",
      "status": "ACTIVE",
      "scope": "ORDER",
      "start_time": "2024-01-01T00:00:00Z",
      "end_time": "2024-12-31T23:59:59Z",
      "priority": 5,
      "for_members": true,
      "for_non_members": true,
      "created_by": "admin"
    },
    "discount": {
      "type": "FIXED_AMOUNT",
      "code": "HEMAT_3L_BP92",
      "discount_value": 1000
    },
    "rules": [
      {
        "type": "FUEL_TYPE",
        "path": "items.*.category_ids",
        "operator": "contains",
        "value": "101",
        "priority": 1,
        "description": "仅限BP 92燃油"
      },
      {
        "type": "FUEL_VOLUME",
        "path": "items.fuel_volume",
        "operator": "gte",
        "value": 3.0,
        "priority": 2,
        "description": "燃油量必须达到3升以上"
      }
    ]
  }'
```

### 示例2: 免费升数促销 (FREE_VOLUME + FREE_ITEM)

#### Ultimate Diesel免费升数促销

```bash
curl -X POST http://localhost:8090/api/v1/promotions/save \
  -H "Content-Type: application/json" \
  -d '{
    "promotion": {
      "name": "Free Volume Ultimate Diesel",
      "description": "Ultimate Diesel 25L or more: Get 2L free volume",
      "type": "FREE_VOLUME",
      "status": "ACTIVE",
      "scope": "ORDER",
      "start_time": "2024-01-01T00:00:00Z",
      "end_time": "2024-12-31T23:59:59Z",
      "priority": 15,
      "for_members": true,
      "for_non_members": true,
      "created_by": "admin"
    },
    "discount": {
      "type": "FREE_ITEM",
      "code": "FREE_VOL_UD_25L",
      "discount_value": 2.0
    },
    "time_cycles": [
      {
        "type": "DAILY",
        "start_time": "2024-01-01T00:00:00Z",
        "end_time": "2024-12-31T23:59:59Z",
        "start_hour": 0,
        "end_hour": 24,
        "priority": 0
      }
    ],
    "rules": [
      {
        "type": "FUEL_TYPE",
        "path": "items.*.category_ids",
        "operator": "contains",
        "value": "103",
        "priority": 1,
        "description": "仅限Ultimate Diesel燃油"
      },
      {
        "type": "FUEL_VOLUME",
        "path": "items.fuel_volume",
        "operator": "gte",
        "value": 25.0,
        "priority": 2,
        "description": "燃油量必须达到25升以上"
      }
    ]
  }'
```

**响应示例**:
```json
{
  "id": "66f3a9fa-4981-4feb-a32d-230e00caac36",
  "name": "Free Volume Ultimate Diesel",
  "description": "Ultimate Diesel 25L or more: Get 2L free volume",
  "type": "FREE_VOLUME",
  "status": "ACTIVE",
  "scope": "ORDER",
  "priority": 15,
  "for_members": true,
  "for_non_members": true,
  "created_by": "admin",
  "version": 1,
  "created_at": "2025-08-12T05:29:12.576879Z",
  "updated_at": "2025-08-12T05:29:12.576879Z"
}
```

#### BP92免费升数促销

```bash
curl -X POST http://localhost:8090/api/v1/promotions/save \
  -H "Content-Type: application/json" \
  -d '{
    "promotion": {
      "name": "Free Volume BP92",
      "description": "BP 92 Fuel 25L or more: Get 1.5L free volume",
      "type": "FREE_VOLUME",
      "status": "ACTIVE",
      "scope": "ORDER",
      "start_time": "2024-01-01T00:00:00Z",
      "end_time": "2024-12-31T23:59:59Z",
      "priority": 12,
      "for_members": true,
      "for_non_members": true,
      "created_by": "admin"
    },
    "discount": {
      "type": "FREE_ITEM",
      "code": "FREE_VOL_BP92_25L",
      "discount_value": 1.5
    },
    "rules": [
      {
        "type": "FUEL_TYPE",
        "path": "items.*.category_ids",
        "operator": "contains",
        "value": "101",
        "priority": 1,
        "description": "仅限BP 92燃油"
      },
      {
        "type": "FUEL_VOLUME",
        "path": "items.fuel_volume",
        "operator": "gte",
        "value": 25.0,
        "priority": 2,
        "description": "燃油量必须达到25升以上"
      }
    ]
  }'
```

## 🧮 计算示例

### 示例1: BP92 30升订单计算

```bash
curl -X POST http://localhost:8090/api/v1/calculator/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": "order_bp92_30L",
    "userId": "user_001",
    "orderAmount": 450000.0,
    "items": [
      {
        "itemId": "fuel_bp92",
        "name": "BP 92燃油",
        "categoryIds": ["101"],
        "quantity": 30.0,
        "price": 15000.0
      }
    ]
  }'
```

**响应示例**:
```json
{
  "success": true,
  "message": "折扣计算成功",
  "orderId": "order_bp92_30L",
  "originalAmount": 450000,
  "discountedAmount": 435000,
  "discountAmount": 15000,
  "appliedPromotions": [
    {
      "promotionId": "79aecfd1-5fa2-49d3-929e-a088ffdf4223",
      "promotionName": "Gratis 1L BP 92",
      "discountType": "FREE_ITEM",
      "discountValue": 15000,
      "discountAmount": 15000,
      "description": "Promotion 'Gratis 1L BP 92' - Free Item Discount",
      "applicableItems": ["fuel_bp92"]
    }
  ],
  "items": [
    {
      "itemId": "fuel_bp92",
      "name": "BP 92燃油",
      "originalPrice": 15000,
      "discountedPrice": 14500,
      "quantity": 30,
      "categoryIds": ["101", "fuel"]
    }
  ],
  "calculationTime": "2025-08-12T13:08:56+08:00",
  "totalItems": 1,
  "totalQuantity": 30
}
```

### 示例2: Ultimate 30升订单计算

```bash
curl -X POST http://localhost:8090/api/v1/calculator/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": "order_ultimate_30L",
    "userId": "user_002",
    "orderAmount": 540000.0,
    "items": [
      {
        "itemId": "fuel_ultimate",
        "name": "Ultimate燃油",
        "categoryIds": ["102"],
        "quantity": 30.0,
        "price": 18000.0
      }
    ]
  }'
```

**响应示例**:
```json
{
  "success": true,
  "message": "折扣计算成功",
  "orderId": "order_ultimate_30L",
  "originalAmount": 540000,
  "discountedAmount": 522000,
  "discountAmount": 18000,
  "appliedPromotions": [
    {
      "promotionId": "26e77a00-66d6-4bcb-965b-a5950b3d7559",
      "promotionName": "Gratis 1L BP Ultimate",
      "discountType": "FREE_ITEM",
      "discountValue": 18000,
      "discountAmount": 18000,
      "description": "Promotion 'Gratis 1L BP Ultimate' - Free Item Discount",
      "applicableItems": ["fuel_ultimate"]
    }
  ],
  "items": [
    {
      "itemId": "fuel_ultimate",
      "name": "Ultimate燃油",
      "originalPrice": 18000,
      "discountedPrice": 17400,
      "quantity": 30,
      "categoryIds": ["102", "fuel"]
    }
  ],
  "calculationTime": "2025-08-12T13:10:13+08:00",
  "totalItems": 1,
  "totalQuantity": 30
}
```

### 示例3: Ultimate Diesel 30升订单计算

```bash
curl -X POST http://localhost:8090/api/v1/calculator/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": "order_ultimate_diesel_30L",
    "userId": "user_003",
    "orderAmount": 600000.0,
    "items": [
      {
        "itemId": "fuel_ultimate_diesel",
        "name": "Ultimate Diesel燃油",
        "categoryIds": ["103"],
        "quantity": 30.0,
        "price": 20000.0
      }
    ]
  }'
```

**响应示例**:
```json
{
  "success": true,
  "message": "折扣计算成功",
  "orderId": "order_ultimate_diesel_30L",
  "originalAmount": 600000,
  "discountedAmount": 580000,
  "discountAmount": 20000,
  "appliedPromotions": [
    {
      "promotionId": "3e5e1ec8-9401-48c0-ac9f-87bd850a71b4",
      "promotionName": "Gratis 1L BP Ultimate Diesel",
      "discountType": "FREE_ITEM",
      "discountValue": 20000,
      "discountAmount": 20000,
      "description": "Promotion 'Gratis 1L BP Ultimate Diesel' - Free Item Discount",
      "applicableItems": ["fuel_ultimate_diesel"]
    }
  ],
  "items": [
    {
      "itemId": "fuel_ultimate_diesel",
      "name": "Ultimate Diesel燃油",
      "originalPrice": 20000,
      "discountedPrice": 19333,
      "quantity": 30,
      "categoryIds": ["103", "fuel"]
    }
  ],
  "calculationTime": "2025-08-12T13:12:21+08:00",
  "totalItems": 1,
  "totalQuantity": 30
}
```

### 示例4: 不符合条件的订单 (BP92 2升)

```bash
curl -X POST http://localhost:8090/api/v1/calculator/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": "order_bp92_2L",
    "userId": "user_004",
    "orderAmount": 30000.0,
    "items": [
      {
        "itemId": "fuel_bp92",
        "name": "BP 92燃油",
        "categoryIds": ["101"],
        "quantity": 2.0,
        "price": 15000.0
      }
    ]
  }'
```

**响应示例**:
```json
{
  "success": true,
  "message": "折扣计算成功",
  "orderId": "order_bp92_2L",
  "originalAmount": 30000,
  "discountedAmount": 30000,
  "discountAmount": 0,
  "appliedPromotions": [],
  "items": [
    {
      "itemId": "fuel_bp92",
      "name": "BP 92燃油",
      "originalPrice": 15000,
      "discountedPrice": 15000,
      "quantity": 2,
      "categoryIds": ["101", "fuel"]
    }
  ],
  "calculationTime": "2025-08-12T13:13:25+08:00",
  "totalItems": 1,
  "totalQuantity": 2
}
```

## 📋 其他接口示例

### 示例1: 查询促销列表

```bash
# 查询所有促销
curl -X GET http://localhost:8090/api/v1/promotions

# 分页查询
curl -X GET "http://localhost:8090/api/v1/promotions?page=1&pageSize=5"

# 按状态筛选
curl -X GET "http://localhost:8090/api/v1/promotions?status=ACTIVE"

# 按类型筛选
curl -X GET "http://localhost:8090/api/v1/promotions?type=FIXED_DISCOUNT"

# 搜索促销
curl -X GET "http://localhost:8090/api/v1/promotions?search=BP92"
```

**响应示例**:
```json
{
  "currentPage": 1,
  "pageSize": 10,
  "total": 7,
  "totalPages": 1,
  "hasNext": false,
  "hasPrev": false,
  "promotions": [
    {
      "ID": "66f3a9fa-4981-4feb-a32d-230e00caac36",
      "Name": "Free Volume Ultimate Diesel 25L",
      "Description": "Ultimate Diesel 25L or more: Get 2L free volume",
      "Type": "FREE_VOLUME",
      "Status": "ACTIVE",
      "Priority": 15,
      "StartTime": "2024-01-01T08:00:00+08:00",
      "EndTime": "2025-01-01T07:59:59+08:00",
      "CreatedAt": "2025-08-12T13:29:12.576879+08:00",
      "UpdatedAt": "2025-08-12T13:29:12.576879+08:00"
    },
    {
      "ID": "fc244737-9cb6-43e5-bce8-d1b350fee747",
      "Name": "Complete BP92 28L Promotion",
      "Description": "BP 92 Fuel 28L or more: Get 15000 IDR discount",
      "Type": "FIXED_DISCOUNT",
      "Status": "ACTIVE",
      "Priority": 10,
      "StartTime": "2024-01-01T08:00:00+08:00",
      "EndTime": "2025-01-01T07:59:59+08:00",
      "CreatedAt": "2025-08-12T13:27:55.984548+08:00",
      "UpdatedAt": "2025-08-12T13:27:55.984548+08:00"
    }
  ]
}
```

### 示例2: 查看促销详情

```bash
curl -X GET http://localhost:8090/api/v1/promotions/fc244737-9cb6-43e5-bce8-d1b350fee747
```

### 示例3: 更新促销状态

```bash
# 激活促销
curl -X POST http://localhost:8090/api/v1/promotions/fc244737-9cb6-43e5-bce8-d1b350fee747/status \
  -H "Content-Type: application/json" \
  -d '{"status": "ACTIVE"}'

# 停用促销
curl -X POST http://localhost:8090/api/v1/promotions/fc244737-9cb6-43e5-bce8-d1b350fee747/status \
  -H "Content-Type: application/json" \
  -d '{"status": "INACTIVE"}'

# 过期促销
curl -X POST http://localhost:8090/api/v1/promotions/fc244737-9cb6-43e5-bce8-d1b350fee747/status \
  -H "Content-Type: application/json" \
  -d '{"status": "EXPIRED"}'
```

### 示例4: 删除促销

```bash
curl -X DELETE http://localhost:8090/api/v1/promotions/fc244737-9cb6-43e5-bce8-d1b350fee747
```

## 🔧 业务规则配置

### 支持的规则类型

| 规则类型 | 说明 | 示例值 | 操作符 |
|----------|------|--------|--------|
| FUEL_TYPE | 燃油类型限制 | "101", "102", "103" | contains, eq |
| FUEL_VOLUME | 燃油升数要求 | 28.0, 25.0, 3.0 | gte, lte, eq |
| MIN_PURCHASE | 最低购买金额 | 100000.0 | gte |
| MAX_PURCHASE | 最高购买金额 | 1000000.0 | lte |
| MEMBER_TAG | 会员标签 | "VIP", "GOLD" | contains, not_contains |
| TIME_RANGE | 时间范围 | "06:00-22:00" | between |
| DAY_OF_WEEK | 星期限制 | "1,2,3,4,5" | in |

### 支持的操作符

| 操作符 | 说明 | 适用数据类型 |
|--------|------|--------------|
| eq | 等于 | string, number |
| ne | 不等于 | string, number |
| gt | 大于 | number |
| gte | 大于等于 | number |
| lt | 小于 | number |
| lte | 小于等于 | number |
| contains | 包含 | string, array |
| not_contains | 不包含 | string, array |
| in | 在列表中 | array |
| not_in | 不在列表中 | array |
| between | 在范围内 | number, date |

## 🎯 最佳实践

### 1. 促销优先级设置
- **高优先级 (15-20)**: 限时特殊活动、VIP专享
- **中优先级 (10-14)**: 常规促销活动
- **低优先级 (1-9)**: 基础折扣、新用户优惠

### 2. 折扣代码命名规范
- **固定折扣**: `HEMAT_{升数}L_{燃油类型}_{序号}`
- **免费升数**: `FREE_VOL_{燃油类型}_{升数}L_{序号}`
- **特殊活动**: `{活动名}_{日期}_{序号}`

### 3. 时间配置建议
- 使用UTC时间格式 (ISO 8601)
- 设置合理的开始和结束时间
- 考虑时区转换问题

### 4. 规则配置建议
- 按优先级排序规则 (priority字段)
- 使用清晰的描述说明
- 避免过于复杂的规则组合

### 5. 测试建议
- 创建促销后立即测试计算接口
- 验证边界条件 (最低升数、金额等)
- 测试不同燃油类型的组合
- 验证优先级排序是否正确

## ⚠️ 注意事项

### 1. 数据验证
- 折扣代码必须唯一
- 开始时间不能晚于结束时间
- 折扣值必须大于0
- 优先级必须为正整数

### 2. 性能考虑
- 避免创建过多重叠的促销规则
- 定期清理过期的促销活动
- 监控计算接口的响应时间

### 3. 安全考虑
- 验证用户权限
- 防止恶意创建大量促销
- 记录操作日志

### 4. 业务逻辑
- 同一订单可能匹配多个促销，按优先级应用
- 免费升数会转换为等值金额折扣
- 不符合条件的订单不会应用任何促销

## 📞 技术支持

如有问题，请联系：
- **开发团队**: <EMAIL>
- **技术文档**: [内部Wiki链接]
- **API测试工具**: Postman Collection
