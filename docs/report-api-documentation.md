# 报表 API 接口文档

本文档描述了与报表相关的 API 接口。

## 基础 URL

所有 API 都以 `/api/v1` 为前缀。

## 响应格式

所有的 API 响应都遵循以下 JSON 格式：

### 成功响应

成功的响应将返回 HTTP 状态码 200 (OK) 或 201 (Created)，以及包含请求结果的 JSON 对象。

### 错误响应

错误响应将返回适当的 HTTP 状态码（4xx 客户端错误或 5xx 服务器错误），以及包含错误详情的 JSON 对象：

```json
{
  "code": "ERROR_CODE",
  "message": "错误描述信息",
  "detail": "详细错误信息"
}
```

## 报表接口

### 获取支付方式汇总

获取按支付方式汇总的销售数据。

**请求**

```
GET /api/v1/reports/payment-methods
```

**查询参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| station_id | int | 否 | 站点ID |
| date_from | string | 否 | 开始日期，格式为 YYYY-MM-DD |
| date_to | string | 否 | 结束日期，格式为 YYYY-MM-DD |

**响应**

```json
{
  "items": [
    {
      "payment_method": "现金",
      "total_amount": 10000.50,
      "order_count": 25
    },
    {
      "payment_method": "信用卡",
      "total_amount": 15000.75,
      "order_count": 35
    }
  ]
}
```

### 获取聚合收入报表

获取聚合收入报表数据。

**请求**

```
GET /api/v1/reports/revenue
```

**查询参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| start_date | string | 是 | 开始日期，格式为 YYYY-MM-DD |
| end_date | string | 是 | 结束日期，格式为 YYYY-MM-DD |
| site_ids | string | 否 | 站点ID列表，逗号分隔 |
| granularity | string | 否 | 聚合粒度，可选值：day, week, month，默认为 day |

**响应**

```json
{
  "items": [
    {
      "dimension_key": "2023-11-01",
      "total_revenue": 75000.25
    },
    {
      "dimension_key": "2023-11-02",
      "total_revenue": 82500.50
    }
  ]
}
```

### 获取油品应收汇总

获取油品应收汇总数据。

**请求**

```
GET /api/v1/reports/receivable
```

**查询参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| start_date | string | 是 | 开始日期，格式为 YYYY-MM-DD |
| end_date | string | 是 | 结束日期，格式为 YYYY-MM-DD |
| site_ids | string | 否 | 站点ID列表，逗号分隔 |
| granularity | string | 否 | 聚合粒度，可选值：day, week, month，默认为 day |

**响应**

```json
{
  "items": [
    {
      "dimension_key": "2023-11-01",
      "total_receivable": 15000.50
    },
    {
      "dimension_key": "2023-11-02",
      "total_receivable": 18500.75
    }
  ]
}
```

### 获取油枪销售汇总

获取油枪销售汇总数据。

**请求**

```
GET /api/v1/reports/nozzle-sales
```

**查询参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| start_date | string | 是 | 开始日期，格式为 YYYY-MM-DD |
| end_date | string | 是 | 结束日期，格式为 YYYY-MM-DD |
| site_ids | string | 否 | 站点ID列表，逗号分隔 |
| granularity | string | 否 | 聚合粒度，可选值：day, week, month，默认为 day |

**响应**

```json
{
  "items": [
    {
      "site_id": 1,
      "nozzle_id": "N001",
      "fuel_type": "汽油",
      "fuel_grade": "92#",
      "total_volume": 2500.75,
      "total_amount": 18500.50
    },
    {
      "site_id": 1,
      "nozzle_id": "N002",
      "fuel_type": "汽油",
      "fuel_grade": "95#",
      "total_volume": 1800.25,
      "total_amount": 14400.75
    }
  ]
}
```

### 获取交易明细数据

获取交易明细数据，用于报表。

**请求**

```
GET /api/v1/reports/transactions
```

**查询参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| start_date | string | 是 | 开始日期，格式为 YYYY-MM-DD |
| end_date | string | 是 | 结束日期，格式为 YYYY-MM-DD |
| site_ids | string | 否 | 站点ID列表，逗号分隔 |

**响应**

```json
{
  "items": [
    {
      "txn_id": "TXN10001",
      "timestamp": "2023-11-01T08:35:42Z",
      "site_id": 1,
      "product_category": "汽油",
      "amount": 350.50,
      "volume": 50.75,
      "payment_method": "现金",
      "is_invoiced": true,
      "nozzle_id": "N001"
    },
    {
      "txn_id": "TXN10002",
      "timestamp": "2023-11-01T09:15:20Z",
      "site_id": 2,
      "product_category": "柴油",
      "amount": 750.25,
      "volume": 120.50,
      "payment_method": "信用卡",
      "is_invoiced": false,
      "nozzle_id": "N003"
    }
  ]
}
```

### 获取按商品分类销售汇总

获取按商品分类聚合的销售数据。

**请求**

```
GET /api/v1/reports/sales-by-category
```

**查询参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| start_date | string | 是 | 开始日期，格式为 YYYY-MM-DD |
| end_date | string | 是 | 结束日期，格式为 YYYY-MM-DD |
| site_ids | string | 否 | 站点ID列表，逗号分隔 |
| granularity | string | 否 | 聚合粒度，可选值：day, week, month，默认为 day |

**响应**

```json
{
  "items": [
    {
      "dimension_key": "汽油",
      "total_amount": 75000.50
    },
    {
      "dimension_key": "柴油",
      "total_amount": 50000.25
    },
    {
      "dimension_key": "食品",
      "total_amount": 15000.75
    }
  ]
}
``` 