# 班次服务 API 文档

## 概述

本文档详细描述了班次服务 (Shift Service) 的 API 接口。班次服务提供了完整的班次管理功能，包括班次的开始、结束、查询、删除和恢复等功能，以及班次员工加油情况的详细统计。

## 基础信息

- **基础URL**: `/api/v1`
- **内容类型**: `application/json`
- **认证方式**: 待定

## 错误响应格式

所有错误响应均遵循以下格式:

```json
{
  "code": "ERROR_CODE",
  "message": "错误消息",
  "detail": "错误详细信息"
}
```

## API 接口列表

### 1. 获取班次列表

获取班次列表，支持多种过滤条件和分页。

**请求URL**

```
GET /shifts
```

**请求参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| station_id | int | 否 | 站点ID |
| status | string | 否 | 班次状态 (active, closed) |
| shift_number | string | 否 | 班次编号 |
| date_from | string | 否 | 开始日期 (格式: 2006-01-02) |
| date_to | string | 否 | 结束日期 (格式: 2006-01-02) |
| include_deleted | bool | 否 | 是否包含已删除班次 |
| page | int | 否 | 页码，默认为1 |
| limit | int | 否 | 每页数量，默认为10 |
| sort_by | string | 否 | 排序字段，默认为created_at |
| sort_dir | string | 否 | 排序方向 (asc, desc)，默认为desc |

**成功响应**

```json
{
  "items": [
    {
      "id": 1,
      "station_id": 101,
      "shift_number": "SFT-20250410-001",
      "status": "active",
      "metadata": {
        "employee_id": 3001,
        "notes": "早班"
      },
      "created_at": "2025-04-10T08:00:00Z",
      "updated_at": "2025-04-10T08:00:00Z",
      "ended_at": null,
      "deleted_at": null
    }
  ],
  "total": 1,
  "page": 1,
  "page_size": 10,
  "total_page": 1
}
```

**错误响应**

```json
{
  "code": "INTERNAL_ERROR",
  "message": "获取班次列表失败",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
GET /shifts?station_id=101&status=active&date_from=2025-04-01&date_to=2025-04-10&page=1&limit=10
```

### 2. 开始新班次

在指定站点开始一个新的班次。

**请求URL**

```
POST /shifts/start
```

**请求参数**

```json
{
  "station_id": 101,
  "metadata": {
    "employee_id": 3001,
    "notes": "早班"
  }
}
```

**参数说明**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| station_id | int | 是 | 站点ID |
| metadata | object | 否 | 元数据 |

**注意**: 
- 班次编号由系统自动生成
- 如果指定站点已有活跃班次，则会返回错误

**成功响应**

```json
{
  "id": 1,
  "station_id": 101,
  "shift_number": "SFT-20250410-001",
  "status": "active",
  "metadata": {
    "employee_id": 3001,
    "notes": "早班"
  },
  "created_at": "2025-04-10T08:00:00Z",
  "updated_at": "2025-04-10T08:00:00Z",
  "ended_at": null,
  "deleted_at": null
}
```

**错误响应**

```json
{
  "code": "SHIFT_ALREADY_ACTIVE",
  "message": "该站点已有活跃班次",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_REQUEST | 请求参数格式错误 |
| VALIDATION_ERROR | 请求参数验证失败 |
| SHIFT_ALREADY_ACTIVE | 该站点已有活跃班次 |
| INTERNAL_ERROR | 内部服务器错误 |

### 3. 结束当前班次

结束指定站点的当前活跃班次。

**请求URL**

```
POST /shifts/{station_id}/end
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| station_id | int | 是 | 站点ID |

**注意**: 
- 如果指定站点没有活跃班次，则会返回错误
- 如果班次中有未处理的交易，则无法结束班次

**成功响应**

```json
{
  "id": 1,
  "station_id": 101,
  "shift_number": "SFT-20250410-001",
  "status": "closed",
  "metadata": {
    "employee_id": 3001,
    "notes": "早班"
  },
  "created_at": "2025-04-10T08:00:00Z",
  "updated_at": "2025-04-10T16:00:00Z",
  "ended_at": "2025-04-10T16:00:00Z",
  "deleted_at": null
}
```

**错误响应**

```json
{
  "code": "NO_ACTIVE_SHIFT",
  "message": "该站点没有活跃班次",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_STATION_ID | 站点ID格式无效 |
| NO_ACTIVE_SHIFT | 该站点没有活跃班次 |
| PENDING_TRANSACTIONS | 班次中有未处理的交易，无法结束 |
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
POST /shifts/101/end
```

### 4. 获取当前活跃班次状态

获取指定站点的当前活跃班次状态。该接口会返回明确的状态信息，无论站点是否有活跃班次。

**请求URL**

```
GET /shifts/current/{station_id}
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| station_id | int | 是 | 站点ID |

**成功响应**

**有活跃班次时：**

```json
{
  "has_active_shift": true,
  "shift": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "shift_number": "SFT-20250410-001",
    "station_id": 101,
    "start_time": "2025-04-10T08:00:00Z",
    "end_time": null,
    "status": "active",
    "metadata": {
      "employee_id": 3001,
      "notes": "早班"
    },
    "created_at": "2025-04-10T08:00:00Z",
    "updated_at": "2025-04-10T08:00:00Z",
    "deleted_at": null
  },
  "message": "获取当前活跃班次成功"
}
```

**无活跃班次时：**

```json
{
  "has_active_shift": false,
  "shift": null,
  "message": "该站点当前没有活跃班次"
}
```

**响应字段说明**

| 字段名 | 类型 | 描述 |
|-------|------|------|
| has_active_shift | boolean | 是否有活跃班次 |
| shift | object/null | 班次详细信息，无活跃班次时为null |
| message | string | 状态说明信息 |

**班次对象字段说明**

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | string | 班次UUID |
| shift_number | string | 班次编号 |
| station_id | int | 站点ID |
| start_time | string | 开始时间 (ISO 8601) |
| end_time | string/null | 结束时间，活跃班次为null |
| status | string | 显式状态字段 ("active"/"closed") |
| metadata | object | 元数据 |
| created_at | string | 创建时间 (ISO 8601) |
| updated_at | string | 更新时间 (ISO 8601) |
| deleted_at | string/null | 删除时间，未删除为null |

**错误响应**

```json
{
  "code": "INVALID_STATION_ID",
  "message": "站点ID格式无效",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_STATION_ID | 站点ID格式无效 |
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
GET /shifts/current/101
```

**设计改进说明**

1. **状态明确性**：通过 `has_active_shift` 字段明确表示是否有活跃班次
2. **HTTP状态码合理性**：无活跃班次时返回200而非404，因为这是正常的业务状态
3. **显式状态字段**：班次对象包含 `status` 字段，无需客户端推断状态
4. **向后兼容性**：保持原有的班次数据结构，仅增加状态信息

### 5. 确保班次已开始

确保指定站点有活跃班次，如果没有则自动创建。

**请求URL**

```
POST /shifts/{station_id}/ensure
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| station_id | int | 是 | 站点ID |

**成功响应**

返回现有活跃班次或新创建的班次的详细信息，格式与开始班次的响应相同。
如果是新创建的班次，返回状态码为201，否则为200。

**错误响应**

```json
{
  "code": "INTERNAL_ERROR",
  "message": "确保班次已开始失败",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_STATION_ID | 站点ID格式无效 |
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
POST /shifts/101/ensure
```

### 6. 获取班次详情

根据ID获取班次详情。

**请求URL**

```
GET /shifts/{id}
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 班次ID |

**成功响应**

返回班次的详细信息，格式与开始班次的响应相同。

**错误响应**

```json
{
  "code": "SHIFT_NOT_FOUND",
  "message": "班次不存在",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ID | ID格式无效 |
| SHIFT_NOT_FOUND | 班次不存在 |
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
GET /shifts/1
```

### 7. 根据编号获取班次

根据班次编号获取班次详情。

**请求URL**

```
GET /shifts/number/{number}
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| number | string | 是 | 班次编号 |

**成功响应**

返回班次的详细信息，格式与开始班次的响应相同。

**错误响应**

```json
{
  "code": "SHIFT_NOT_FOUND",
  "message": "班次不存在",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| SHIFT_NOT_FOUND | 班次不存在 |
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
GET /shifts/number/SFT-20250410-001
```

### 8. 获取班次员工加油情况

获取指定班次中所有员工的详细加油情况，包括油品销售、支付方式等统计数据。

**请求URL**

```
GET /shifts/{shift_id}/attendants
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| shift_id | int | 是 | 班次ID |

**查询参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| attendant_name | string | 否 | 员工姓名筛选（支持模糊匹配） |
| fuel_grade | string | 否 | 油品等级筛选 |
| payment_method | string | 否 | 支付方式筛选 |

**功能特点**

- **智能数据源选择**：已结束班次优先使用汇总表数据，进行中班次使用实时数据
- **员工姓名准确性**：所有员工姓名均从users表获取真实姓名
- **支付数据完整性**：提供详细的支付方式统计和分类
- **数据一致性保证**：确保支付数据与销售数据的关联准确性
- **多维度筛选**：支持按员工姓名、油品等级、支付方式筛选

**成功响应**

```json
{
  "success": true,
  "message": "班次员工加油情况获取成功",
  "data": {
    "shift_info": {
      "id": 18,
      "shift_number": "SHIFT-1-20250711005945",
      "station_id": 1,
      "station_name": "Meruya Ilir Station",
      "start_time": "2025-07-11T00:59:45.87395+07:00",
      "end_time": "2025-07-12T14:43:40.12986+07:00",
      "status": "closed"
    },
    "attendants": [
      {
        "attendant_info": {
          "attendant_name": "Jayson",
          "staff_card_id": 2
        },
        "transaction_count": 4,
        "sales_volume_ltr": 1.408,
        "sales_amount_idr": 21865,
        "fuel_sales": {
          "by_grade": [
            {
              "fuel_grade": "BP Ultimate",
              "fuel_name": "BP Ultimate",
              "fuel_type": "oil_102",
              "sales_volume": 0.985,
              "gross_amount": 15000,
              "discount_amount": 0,
              "net_amount": 15000,
              "unit_price": 15228.426395939086,
              "transaction_count": 2
            },
            {
              "fuel_grade": "BP Ultimate Diesel",
              "fuel_name": "BP Ultimate Diesel",
              "fuel_type": "diesel",
              "sales_volume": 0.423,
              "gross_amount": 6865,
              "discount_amount": 0,
              "net_amount": 6865,
              "unit_price": 16229.314420803783,
              "transaction_count": 2
            }
          ],
          "total": {
            "total_volume": 1.408,
            "total_gross_amount": 21865,
            "total_discount_amount": 0,
            "total_net_amount": 21865,
            "total_transactions": 4
          }
        },
        "payment_summary": {
          "cash": 11865,
          "non_cash_total": 10000,
          "pvs": 0,
          "cimb": 0,
          "bca": 0,
          "mandiri": 10000,
          "bri": 0,
          "bni": 0,
          "voucher": 0,
          "b2b": 0,
          "tera": 0,
          "by_method": [
            {
              "payment_method": "mandiri",
              "payment_method_name": "Mandiri",
              "total_amount": 10000,
              "transaction_count": 1,
              "percentage": 45.7
            },
            {
              "payment_method": "cash",
              "payment_method_name": "现金",
              "total_amount": 11865,
              "transaction_count": 3,
              "percentage": 54.3
            }
          ]
        },
        "dry_income": 0,
        "grand_total": 21865
      }
    ],
    "shift_summary": {
      "total_attendants": 3,
      "total_transactions": 12,
      "total_sales_volume": 11.758,
      "total_sales_amount": 183333,
      "total_cash": 143333,
      "total_non_cash": 35000,
      "total_pvc": 0,
      "total_cimb": 25000,
      "total_bca": 0,
      "total_mandiri": 10000,
      "total_bri": 0,
      "total_bni": 0,
      "total_voucher": 0,
      "total_b2b": 0,
      "total_tera": 0,
      "grand_total": 183333
    }
  },
  "meta": {
    "generated_at": "2025-07-13T04:01:27.531221968+07:00",
    "processing_time_ms": 454,
    "data_source": "realtime",
    "version": "1.0"
  }
}
```

**数据结构说明**

**shift_info**: 班次基本信息
- `id`: 班次ID
- `shift_number`: 班次编号
- `station_id`: 站点ID
- `station_name`: 站点名称（从stations接口获取）
- `start_time`: 班次开始时间
- `end_time`: 班次结束时间（进行中班次为null）
- `status`: 班次状态（active/closed）

**attendants**: 员工详细信息数组
- `attendant_info`: 员工基本信息
  - `attendant_name`: 员工真实姓名（从users表获取）
  - `staff_card_id`: 员工卡ID（用于关联）
- `transaction_count`: 交易笔数
- `sales_volume_ltr`: 销售油量（升，保留3位小数）
- `sales_amount_idr`: 销售金额（印尼盾）
- `fuel_sales`: 油品销售明细
  - `by_grade`: 按油品等级分组的销售数据
    - `fuel_grade`: 油品等级
    - `fuel_name`: 油品名称（与fuel_grade一致）
    - `fuel_type`: 油品类型
    - `sales_volume`: 销售油量（保留3位小数）
    - `gross_amount`: 毛销售额
    - `discount_amount`: 折扣金额
    - `net_amount`: 净销售额
    - `unit_price`: 单价
    - `transaction_count`: 交易笔数
  - `total`: 油品销售汇总
- `payment_summary`: 支付方式汇总
  - `cash`: 现金支付金额
  - `non_cash_total`: 非现金支付总额
  - `pvs`, `cimb`, `bca`, `mandiri`, `bri`, `bni`, `voucher`, `b2b`, `tera`: 各种支付方式金额
  - `by_method`: 支付方式明细数组
    - `payment_method`: 支付方式代码
    - `payment_method_name`: 支付方式显示名称
    - `total_amount`: 该支付方式总金额
    - `transaction_count`: 该支付方式交易笔数
    - `percentage`: 占比（%）
- `dry_income`: 非油品收入
- `grand_total`: 总计金额

**shift_summary**: 班次汇总信息
- `total_attendants`: 员工总数
- `total_transactions`: 交易总笔数
- `total_sales_volume`: 销售总油量
- `total_sales_amount`: 销售总金额
- 各种支付方式的汇总金额
- `grand_total`: 班次总计金额

**meta**: 元数据信息
- `generated_at`: 生成时间
- `processing_time_ms`: 处理时间（毫秒）
- `data_source`: 数据源（realtime/aggregated）
- `version`: API版本

**错误响应**

```json
{
  "success": false,
  "message": "获取班次员工加油情况失败",
  "error": {
    "code": "SHIFT_NOT_FOUND",
    "message": "班次不存在",
    "detail": "指定的班次ID不存在或已被删除"
  }
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_SHIFT_ID | 班次ID格式无效 |
| SHIFT_NOT_FOUND | 班次不存在 |
| INTERNAL_ERROR | 内部服务器错误 |
| DATA_ACCESS_ERROR | 数据访问错误 |

**示例请求**

```bash
# 获取班次18的所有员工加油情况
GET /shifts/18/attendants

# 按员工姓名筛选
GET /shifts/18/attendants?attendant_name=Jayson

# 按油品等级筛选
GET /shifts/18/attendants?fuel_grade=BP Ultimate

# 按支付方式筛选
GET /shifts/18/attendants?payment_method=cash

# 多条件筛选
GET /shifts/18/attendants?attendant_name=Jay&fuel_grade=BP Ultimate&payment_method=cash
```

**数据准确性保证**

1. **员工姓名获取**：
   - 优先从staff_cards表关联users表获取真实姓名
   - 对于employee_id，通过多层查找策略确保获取到正确的用户姓名
   - 支持数据不一致情况的容错处理

2. **支付数据关联**：
   - 通过燃油交易→订单链接→订单→支付记录的完整链路
   - 支持多种支付方式的细化统计
   - 确保支付数据与销售数据的一致性

3. **数据源选择**：
   - 已结束班次：优先使用汇总表数据，提高查询效率
   - 进行中班次：使用实时数据，确保数据时效性
   - 汇总表数据不完整时自动切换到实时数据

4. **性能优化**：
   - 减少重复查询，批量处理数据关联
   - 使用内存缓存提高员工姓名查找效率
   - 优化支付数据关联逻辑

### 9. 软删除班次

软删除指定班次。

**请求URL**

```
DELETE /shifts/{id}
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 班次ID |

**注意**: 
- 不能删除活跃状态的班次

**成功响应**

```
204 No Content
```

**错误响应**

```json
{
  "code": "CANNOT_DELETE_ACTIVE_SHIFT",
  "message": "无法删除活跃班次",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ID | ID格式无效 |
| SHIFT_NOT_FOUND | 班次不存在 |
| CANNOT_DELETE_ACTIVE_SHIFT | 无法删除活跃班次 |
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
DELETE /shifts/1
```

### 10. 恢复已删除班次

恢复已被软删除的班次。

**请求URL**

```
POST /shifts/{id}/restore
```

**路径参数**

| 参数名 | 类型 | 是否必须 | 描述 |
|-------|------|---------|------|
| id | int | 是 | 班次ID |

**成功响应**

```
204 No Content
```

**错误响应**

```json
{
  "code": "SHIFT_NOT_FOUND",
  "message": "班次不存在或未被删除",
  "detail": "错误详细信息"
}
```

**错误代码**

| 错误代码 | 描述 |
|---------|------|
| INVALID_ID | ID格式无效 |
| SHIFT_NOT_FOUND | 班次不存在或未被删除 |
| INTERNAL_ERROR | 内部服务器错误 |

**示例请求**

```
POST /shifts/1/restore
```

## 班次状态流转

班次状态流转图：

```
创建 --> active --> closed
          |
          v
        deleted
```

- **active**: 活跃班次
- **closed**: 已关闭班次
- **deleted**: 已删除班次（软删除）

## 数据模型

### 班次 (Shift)

| 字段 | 类型 | 描述 |
|-----|------|------|
| id | int | 班次ID |
| station_id | int | 站点ID |
| shift_number | string | 班次编号 |
| status | string | 班次状态 (active, closed) |
| metadata | object | 元数据，可存储额外信息 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |
| ended_at | string | 结束时间，未结束时为null |
| deleted_at | string | 删除时间（软删除），未删除时为null |

**示例数据**

```json
{
  "id": 1,
  "station_id": 101,
  "shift_number": "SFT-20250410-001",
  "status": "active",
  "metadata": {
    "employee_id": 3001,
    "notes": "早班",
    "starting_cash": 1000.00
  },
  "created_at": "2025-04-10T08:00:00Z",
  "updated_at": "2025-04-10T08:00:00Z",
  "ended_at": null,
  "deleted_at": null
}
```

**列表响应示例**

```json
{
  "items": [
    {
      "id": 1,
      "station_id": 101,
      "shift_number": "SFT-20250410-001",
      "status": "closed",
      "metadata": {
        "employee_id": 3001,
        "notes": "早班",
        "starting_cash": 1000.00,
        "ending_cash": 1500.00
      },
      "created_at": "2025-04-10T08:00:00Z",
      "updated_at": "2025-04-10T16:00:00Z",
      "ended_at": "2025-04-10T16:00:00Z",
      "deleted_at": null
    },
    {
      "id": 2,
      "station_id": 101,
      "shift_number": "SFT-20250410-002",
      "status": "active",
      "metadata": {
        "employee_id": 3002,
        "notes": "晚班",
        "starting_cash": 1500.00
      },
      "created_at": "2025-04-10T16:00:00Z",
      "updated_at": "2025-04-10T16:00:00Z",
      "ended_at": null,
      "deleted_at": null
    }
  ],
  "total": 2,
  "page": 1,
  "page_size": 10,
  "total_page": 1
}
```

## 最近更新

### 2025-07-13 版本更新

**新增功能**：
- 新增班次员工加油情况接口 (`GET /shifts/{shift_id}/attendants`)
- 支持按员工姓名、油品等级、支付方式筛选
- 提供完整的支付方式统计和分类

**功能优化**：
- 员工姓名获取优化：确保从users表获取真实姓名
- 支付数据关联优化：提高支付数据与销售数据的一致性
- 数据源智能选择：已结束班次优先使用汇总表数据

**修复问题**：
- 修复支付方式数据为空的问题
- 修复员工姓名获取不准确的问题
- 修复数据不一致情况的容错处理

**性能提升**：
- 优化查询逻辑，减少重复数据库查询
- 改进数据关联算法，提高处理效率
- 增强缓存机制，提升响应速度 