# 班结预检查接口文档

## 概述

班结预检查接口用于在结束班次前检查班次状态，包括班次基本信息、总量器连续性检查和是否可以直接结班的判断。

## 接口信息

- **接口路径**: `GET /api/v1/shifts/{station_id}/end-shift-validation`
- **请求方法**: GET
- **认证**: 需要Bearer Token认证

## 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| station_id | int64 | 是 | 站点ID |

## 响应格式

### 成功响应 (200)

```json
{
  "shift_info": {
    "shift_number": "S20250722001",
    "start_time": "2025-07-22T08:00:00Z",
    "current_time": "2025-07-22T16:00:00Z",
    "duration_hours": 8.0
  },
  "totalizer_continuity": {
    "has_abnormal": true,
    "abnormal_transaction_count": 3,
    "total_discrepancy_amount": 15.50,
    "abnormal_transactions": [
      {
        "transaction_id": "TXN001",
        "pump_id": "P01", 
        "nozzle_id": "N01",
        "discrepancy_amount": 5.20,
        "transaction_time": "2025-07-22T14:30:00Z"
      },
      {
        "transaction_id": "TXN002",
        "pump_id": "P02", 
        "nozzle_id": "N02",
        "discrepancy_amount": 10.30,
        "transaction_time": "2025-07-22T15:15:00Z"
      }
    ]
  },
  "can_end_directly": false
}
```

### 错误响应

#### 400 - 请求参数错误
```json
{
  "code": "INVALID_STATION_ID",
  "message": "站点ID格式无效",
  "detail": "strconv.ParseInt: parsing \"abc\": invalid syntax"
}
```

#### 404 - 没有活跃班次
```json
{
  "code": "NO_ACTIVE_SHIFT",
  "message": "该站点没有活跃班次",
  "detail": "当前站点没有活跃班次"
}
```

#### 500 - 服务器内部错误
```json
{
  "code": "INTERNAL_ERROR",
  "message": "班结预检查失败",
  "detail": "查询燃油交易时发生错误: ..."
}
```

## 响应字段说明

### shift_info (班次信息)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| shift_number | string | 班次编号 |
| start_time | string | 班次开始时间 (ISO 8601格式) |
| current_time | string | 当前时间 (ISO 8601格式) |
| duration_hours | float64 | 班次持续时长（小时） |

### totalizer_continuity (总量器连续性检查)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| has_abnormal | boolean | 是否有异常交易 |
| abnormal_transaction_count | int | 异常交易数量 |
| total_discrepancy_amount | float64 | 总差异金额 |
| abnormal_transactions | array | 异常交易列表 |

### abnormal_transactions (异常交易信息)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| transaction_id | string | 交易ID |
| pump_id | string | 油泵ID |
| nozzle_id | string | 油枪ID |
| discrepancy_amount | float64 | 差异金额 |
| transaction_time | string | 交易时间 (ISO 8601格式) |

### can_end_directly (是否可以直接结班)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| can_end_directly | boolean | 是否可以直接结班。判断条件：<br/>1. 没有未处理的交易<br/>2. 总差异金额小于100.0 |

## 使用示例

### cURL 示例

```bash
# 检查站点123的班结状态
curl -X GET "http://localhost:8080/api/v1/shifts/123/end-shift-validation" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### JavaScript 示例

```javascript
const response = await fetch('/api/v1/shifts/123/end-shift-validation', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json'
  }
});

const validationResult = await response.json();

if (validationResult.can_end_directly) {
  console.log('可以直接结班');
} else {
  console.log('需要处理异常后才能结班');
  console.log('异常交易数量:', validationResult.totalizer_continuity.abnormal_transaction_count);
}
```

## 业务逻辑说明

### 当班数据查询

系统使用 `fuel_transactions.shift_id` 字段来精确查询当班的交易数据：
- **异常交易查询**: `WHERE shift_id = ? AND totalizer_continuity_status = 'abnormal'`
- **未处理交易查询**: `WHERE shift_id = ? AND status = 'pending'`
- **优势**: 避免时间范围查询的边界问题，确保数据准确性

### 总量器连续性检查

系统通过查询数据库中的 `totalizer_continuity_status` 字段来快速识别异常交易，避免重复计算，提高查询效率。

### 差异金额计算

对于每个异常交易，系统基于总量器连续性计算不连续部分的差异：
- `不连续差异量 = |当前交易起始总量器 - 上一笔交易结束总量器|`
- `差异金额 = 不连续差异量 × 单价`

**计算逻辑说明**：
- 针对标记为 `abnormal` 的交易，查询同一nozzle的上一笔交易
- 计算当前交易的起始总量器读数与上一笔交易结束总量器读数之间的差值
- 该差值表示计量器的跳跃或缺失部分，反映真实的计量异常
- 如果找不到上一笔交易或缺少总量器数据，差异金额为0

### 结班条件判断

系统综合考虑以下因素判断是否可以直接结班：
1. **未处理交易**: 检查是否有状态为 `pending` 的交易
2. **总量器异常**: 检查总差异金额是否超过阈值（当前设置为100.0）

## 注意事项

1. 接口返回的时间均为UTC时间格式
2. 金额单位为印尼盾 (IDR)
3. 异常交易列表最多返回1000条记录

## 版本历史

- v1.0.0 (2025-07-22): 初始版本，支持基本的班结预检查功能
