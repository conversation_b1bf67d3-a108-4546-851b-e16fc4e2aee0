{"swagger": "2.0", "info": {"contact": {}}, "basePath": "/api/v1", "paths": {"/api/v1/customer-labels": {"post": {"description": "将指定标签分配给客户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "给客户分配标签", "parameters": [{"description": "分配信息", "name": "assignment", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.AssignLabelToCustomerRequest"}}], "responses": {"201": {"description": "分配成功", "schema": {"$ref": "#/definitions/dto.CustomerLabelResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/api/v1/customers": {"get": {"description": "分页获取客户列表，支持多种过滤条件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "获取客户列表", "parameters": [{"type": "integer", "default": 0, "description": "偏移量", "name": "offset", "in": "query"}, {"type": "integer", "default": 10, "description": "每页数量", "name": "limit", "in": "query"}, {"type": "integer", "description": "HOS系统ID", "name": "hos_id", "in": "query"}, {"type": "string", "description": "客户名称", "name": "name", "in": "query"}, {"type": "string", "description": "手机号", "name": "phone", "in": "query"}, {"type": "string", "description": "邮箱", "name": "email", "in": "query"}, {"enum": ["id", "hos_id", "name", "email", "phone", "created_at", "updated_at"], "type": "string", "description": "排序字段", "name": "order_by", "in": "query"}, {"type": "boolean", "default": false, "description": "是否降序", "name": "order_desc", "in": "query"}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.ListCustomersResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "post": {"description": "创建新的客户档案", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "创建新客户", "parameters": [{"description": "客户信息", "name": "customer", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateCustomerRequest"}}], "responses": {"201": {"description": "创建成功", "schema": {"$ref": "#/definitions/dto.CustomerResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/api/v1/customers/count": {"get": {"description": "根据过滤条件统计客户总数", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "统计客户数量", "parameters": [{"type": "integer", "description": "HOS系统ID", "name": "hos_id", "in": "query"}, {"type": "string", "description": "客户名称", "name": "name", "in": "query"}, {"type": "string", "description": "手机号", "name": "phone", "in": "query"}, {"type": "string", "description": "邮箱", "name": "email", "in": "query"}], "responses": {"200": {"description": "统计成功", "schema": {"$ref": "#/definitions/dto.CountCustomersResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/api/v1/customers/phone/{phone}": {"get": {"description": "根据手机号获取客户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "根据手机号获取客户", "parameters": [{"type": "string", "description": "手机号", "name": "phone", "in": "path", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.CustomerResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "客户不存在", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/api/v1/customers/{customer_id}/labels": {"get": {"description": "获取指定客户的所有标签", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "获取客户标签", "parameters": [{"type": "integer", "description": "客户ID", "name": "customer_id", "in": "path", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.GetCustomerLabelsResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/api/v1/customers/{customer_id}/vehicles": {"get": {"description": "根据客户ID获取其名下的车辆列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "获取客户车辆列表", "parameters": [{"type": "integer", "description": "客户ID", "name": "customer_id", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页数量", "name": "page_size", "in": "query"}, {"type": "string", "description": "车牌号（模糊查询）", "name": "number", "in": "query"}, {"type": "string", "description": "车型（模糊查询）", "name": "model", "in": "query"}, {"enum": ["id", "number", "model", "created_at", "updated_at"], "type": "string", "description": "排序字段", "name": "order_by", "in": "query"}, {"type": "boolean", "default": false, "description": "是否降序", "name": "order_desc", "in": "query"}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.ListVehiclesResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/api/v1/customers/{id}": {"get": {"description": "根据客户ID获取客户详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "获取客户信息", "parameters": [{"type": "integer", "description": "客户ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.CustomerResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "客户不存在", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "put": {"description": "更新指定客户的信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "更新客户信息", "parameters": [{"type": "integer", "description": "客户ID", "name": "id", "in": "path", "required": true}, {"description": "更新的客户信息", "name": "customer", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UpdateCustomerRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"$ref": "#/definitions/dto.CustomerResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "客户不存在", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/api/v1/inventory/categories": {"get": {"description": "获取分类列表，支持分页查询", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "获取分类列表", "parameters": [{"type": "integer", "default": 0, "description": "限制返回数量，0表示不限制", "name": "limit", "in": "query"}, {"type": "integer", "default": 0, "description": "偏移量，用于分页", "name": "offset", "in": "query"}, {"type": "string", "default": "\"id\"", "description": "排序字段", "name": "order_by", "in": "query"}, {"type": "string", "default": "\"asc\"", "description": "排序方式 (asc/desc)", "name": "order", "in": "query"}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.ListCategoriesResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"description": "创建新的商品分类，支持层级结构", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "创建新分类", "parameters": [{"description": "分类信息", "name": "category", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateCategoryRequest"}}], "responses": {"201": {"description": "创建成功", "schema": {"$ref": "#/definitions/dto.CategoryResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/categories/{id}": {"get": {"description": "根据分类ID获取分类详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "获取分类信息", "parameters": [{"type": "integer", "description": "分类ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.CategoryResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "分类不存在", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/inbound": {"post": {"description": "处理商品入库操作，如采购入库、调拨入库等", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "库存入库操作", "parameters": [{"description": "入库信息", "name": "inbound", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.AdjustInventoryRequest"}}], "responses": {"200": {"description": "入库成功", "schema": {"$ref": "#/definitions/dto.AdjustInventoryResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/outbound": {"post": {"description": "处理商品出库操作，如销售出库、报损出库等", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "库存出库操作", "parameters": [{"description": "出库信息", "name": "outbound", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.AdjustInventoryRequest"}}], "responses": {"200": {"description": "出库成功", "schema": {"$ref": "#/definitions/dto.AdjustInventoryResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/products": {"get": {"description": "获取商品列表，支持分页、排序和过滤", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "获取商品列表", "parameters": [{"type": "integer", "default": 20, "description": "限制返回数量，0表示不限制", "name": "limit", "in": "query"}, {"type": "integer", "default": 0, "description": "偏移量，用于分页", "name": "offset", "in": "query"}, {"type": "string", "default": "\"id\"", "description": "排序字段", "name": "order_by", "in": "query"}, {"type": "string", "default": "\"asc\"", "description": "排序方式 (asc/desc)", "name": "order", "in": "query"}, {"type": "string", "description": "按商品类型过滤", "name": "type", "in": "query"}, {"type": "string", "description": "按商品状态过滤", "name": "status", "in": "query"}, {"type": "integer", "description": "按分类ID过滤", "name": "category_id", "in": "query"}, {"type": "string", "description": "按名称或编码关键词搜索", "name": "keyword", "in": "query"}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.ListProductsResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"description": "创建新的商品档案", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "创建新商品", "parameters": [{"description": "商品信息", "name": "product", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateProductRequest"}}], "responses": {"201": {"description": "创建成功", "schema": {"$ref": "#/definitions/dto.ProductResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/products/{id}": {"get": {"description": "根据商品ID获取商品详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "获取商品信息", "parameters": [{"type": "integer", "description": "商品ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.ProductResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "商品不存在", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "delete": {"description": "根据商品ID删除商品及其关联数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "删除商品", "parameters": [{"type": "integer", "description": "商品ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "商品不存在", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "409": {"description": "商品有库存无法删除", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/reservations": {"post": {"description": "为订单创建库存预留", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "创建库存预留", "parameters": [{"description": "预留信息", "name": "reservation", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateReservationRequest"}}], "responses": {"201": {"description": "创建成功", "schema": {"$ref": "#/definitions/dto.ReservationResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/reservations/{id}/release": {"post": {"description": "释放指定的库存预留", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "释放库存预留", "parameters": [{"type": "integer", "description": "预留ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "释放成功", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/stocks": {"get": {"description": "获取库存列表，支持分页、过滤和排序", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "获取库存列表", "parameters": [{"type": "integer", "default": 20, "description": "限制返回数量，0表示不限制", "name": "limit", "in": "query"}, {"type": "integer", "default": 0, "description": "偏移量，用于分页", "name": "offset", "in": "query"}, {"type": "string", "default": "\"id\"", "description": "排序字段：id, product_id, physical_quantity, available_quantity, created_at, updated_at", "name": "order_by", "in": "query"}, {"type": "string", "default": "\"desc\"", "description": "排序方式 (asc/desc)", "name": "order", "in": "query"}, {"type": "integer", "description": "按商品ID过滤", "name": "product_id", "in": "query"}, {"type": "string", "description": "按商品类型过滤：OIL, NON_OIL, GIFT", "name": "product_type", "in": "query"}, {"type": "string", "description": "按商品状态过滤：ACTIVE, INACTIVE", "name": "product_status", "in": "query"}, {"type": "integer", "description": "按商品分类ID过滤", "name": "category_id", "in": "query"}, {"type": "string", "description": "按商品名称或编码关键词搜索", "name": "keyword", "in": "query"}, {"type": "boolean", "description": "是否只显示低库存商品", "name": "low_stock", "in": "query"}, {"type": "number", "description": "最小库存数量过滤", "name": "min_quantity", "in": "query"}, {"type": "number", "description": "最大库存数量过滤", "name": "max_quantity", "in": "query"}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.ListInventoryResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/stocks/{id}": {"get": {"description": "根据库存ID或商品ID获取库存详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "获取库存详情", "parameters": [{"type": "integer", "description": "库存记录ID", "name": "id", "in": "path"}, {"type": "integer", "description": "商品ID（与路径参数ID二选一）", "name": "product_id", "in": "query"}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.GetInventoryResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "库存不存在", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/stocktakes": {"get": {"description": "获取盘点任务列表，支持分页、排序和过滤", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "获取盘点任务列表", "parameters": [{"type": "integer", "default": 20, "description": "限制返回数量，0表示不限制", "name": "limit", "in": "query"}, {"type": "integer", "default": 0, "description": "偏移量，用于分页", "name": "offset", "in": "query"}, {"type": "string", "default": "\"created_at\"", "description": "排序字段", "name": "order_by", "in": "query"}, {"type": "string", "default": "\"desc\"", "description": "排序方式 (asc/desc)", "name": "order", "in": "query"}, {"type": "string", "description": "按盘点状态过滤 (IN_PROGRESS, COMPLETED, CANCELLED)", "name": "status", "in": "query"}, {"type": "integer", "description": "按创建人过滤", "name": "created_by", "in": "query"}, {"type": "string", "description": "按盘点单号关键词搜索", "name": "keyword", "in": "query"}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.ListStocktakeResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"description": "创建新的盘点任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "开始盘点任务", "parameters": [{"description": "盘点信息", "name": "stocktake", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.StartStocktakeRequest"}}], "responses": {"201": {"description": "创建成功", "schema": {"$ref": "#/definitions/dto.StartStocktakeResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/stocktakes/items/record": {"post": {"description": "更新盘点项的实际盘点数量", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "录入盘点数量", "parameters": [{"description": "盘点数量信息", "name": "record", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.RecordItemRequest"}}], "responses": {"200": {"description": "录入成功", "schema": {"$ref": "#/definitions/dto.RecordItemResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/stocktakes/{id}": {"get": {"description": "根据盘点任务ID获取盘点任务详细信息，包含所有盘点项", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "获取盘点任务详情", "parameters": [{"type": "integer", "description": "盘点任务ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.GetStocktakeResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "盘点任务不存在", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/stocktakes/{id}/complete": {"post": {"description": "完成盘点任务并生成库存调整", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "完成盘点任务", "parameters": [{"type": "integer", "description": "盘点任务ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "完成成功", "schema": {"$ref": "#/definitions/dto.CompleteStocktakeResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/inventory/warnings": {"get": {"description": "获取当前的库存预警信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["库存服务"], "summary": "获取库存预警", "parameters": [{"type": "integer", "default": 50, "description": "限制返回数量", "name": "limit", "in": "query"}], "responses": {"200": {"description": "获取成功", "schema": {"type": "array", "items": {"$ref": "#/definitions/dto.WarningResponse"}}}, "500": {"description": "内部服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/v1/labels": {"get": {"description": "分页获取标签列表，支持按名称过滤", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "获取标签列表", "parameters": [{"type": "integer", "default": 0, "description": "偏移量", "name": "offset", "in": "query"}, {"type": "integer", "default": 10, "description": "每页数量", "name": "limit", "in": "query"}, {"type": "string", "description": "标签名称", "name": "name", "in": "query"}, {"enum": ["id", "name", "created_at", "updated_at"], "type": "string", "description": "排序字段", "name": "order_by", "in": "query"}, {"type": "boolean", "default": false, "description": "是否降序", "name": "order_desc", "in": "query"}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.ListLabelsResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "post": {"description": "创建新的客户标签", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "创建新标签", "parameters": [{"description": "标签信息", "name": "label", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateLabelRequest"}}], "responses": {"201": {"description": "创建成功", "schema": {"$ref": "#/definitions/dto.LabelResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/api/v1/vehicles": {"post": {"description": "为客户创建新的车辆档案", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "创建新车辆", "parameters": [{"description": "车辆信息", "name": "vehicle", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateVehicleRequest"}}], "responses": {"201": {"description": "创建成功", "schema": {"$ref": "#/definitions/dto.VehicleResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/api/v1/vehicles/number/{number}": {"get": {"description": "根据车牌号获取车辆信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "根据车牌号获取车辆", "parameters": [{"type": "string", "description": "车牌号", "name": "number", "in": "path", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.VehicleResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "车辆不存在", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/api/v1/vehicles/{id}": {"get": {"description": "根据车辆ID获取车辆详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "获取车辆信息", "parameters": [{"type": "integer", "description": "车辆ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/dto.VehicleResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "车辆不存在", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "put": {"description": "更新指定车辆的信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["客户服务"], "summary": "更新车辆信息", "parameters": [{"type": "integer", "description": "车辆ID", "name": "id", "in": "path", "required": true}, {"description": "更新的车辆信息", "name": "vehicle", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UpdateVehicleRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"$ref": "#/definitions/dto.VehicleResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "车辆不存在", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/calculator": {"get": {"description": "返回促销计算服务的主页", "consumes": ["text/html"], "produces": ["text/html"], "tags": ["促销计算"], "summary": "促销计算器首页", "responses": {"200": {"description": "促销服务演示", "schema": {"type": "string"}}}}}, "/calculator/calculate": {"post": {"description": "计算给定订单的所有适用折扣（功能与ProcessOrder相同）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["促销计算"], "summary": "计算订单折扣", "parameters": [{"description": "订单信息", "name": "order", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.Order"}}], "responses": {"200": {"description": "折扣计算结果", "schema": {"$ref": "#/definitions/handlers.DiscountResponse"}}, "400": {"description": "请求数据无效", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "服务器错误", "schema": {"type": "object", "additionalProperties": true}}}}}, "/calculator/process": {"post": {"description": "接收订单数据，应用适用的促销规则，返回折扣计算结果", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["促销计算"], "summary": "处理订单并计算折扣", "parameters": [{"description": "订单信息", "name": "order", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.Order"}}], "responses": {"200": {"description": "折扣计算结果", "schema": {"$ref": "#/definitions/handlers.DiscountResponse"}}, "400": {"description": "请求数据无效", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "服务器错误", "schema": {"type": "object", "additionalProperties": true}}}}}, "/calculator/status": {"get": {"description": "返回促销计算服务的当前运行状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["促销计算"], "summary": "获取服务状态", "responses": {"200": {"description": "包含服务状态和当前时间", "schema": {"type": "object", "additionalProperties": true}}}}}, "/employee": {"post": {"description": "添加新员工到系统", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["员工"], "summary": "添加员工", "parameters": [{"description": "员工信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateEmployeeRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/repository.Employee"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/employee/login": {"post": {"description": "员工通过员工号和密码登录系统", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["员工"], "summary": "员工登录", "parameters": [{"description": "登录信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.LoginRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.LoginResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/employee/{id}": {"get": {"description": "根据ID获取员工详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["员工"], "summary": "获取员工详情", "parameters": [{"type": "integer", "description": "员工ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/repository.Employee"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "put": {"description": "更新员工的姓名和密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["员工"], "summary": "更新员工信息", "parameters": [{"type": "integer", "description": "员工ID", "name": "id", "in": "path", "required": true}, {"description": "更新的员工信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateEmployeeRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/repository.Employee"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "delete": {"description": "根据ID删除员工（软删除）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["员工"], "summary": "删除员工", "parameters": [{"type": "integer", "description": "员工ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/employees": {"get": {"description": "获取员工列表，支持分页和筛选", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["员工"], "summary": "获取员工列表", "parameters": [{"type": "integer", "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页记录数，默认10", "name": "size", "in": "query"}, {"type": "string", "description": "按员工姓名筛选", "name": "name", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.ListEmployeesResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/enhanced-reports/fuel-receivable": {"get": {"description": "获取指定时间段内的油品销售汇总数据，包括销售数量、金额、优惠、退款等详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["增强报表"], "summary": "获取油品应收汇总", "parameters": [{"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "start_date", "in": "query", "required": true}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "end_date", "in": "query", "required": true}, {"type": "string", "description": "站点ID列表 (逗号分隔)", "name": "site_ids", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.GetFuelReceivableSummaryResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/enhanced-reports/nozzle-sales": {"get": {"description": "获取各油枪的销售明细数据，包括站点、油枪ID、燃油类型、销售量等信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["增强报表"], "summary": "获取油枪销售明细", "parameters": [{"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "start_date", "in": "query", "required": true}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "end_date", "in": "query", "required": true}, {"type": "string", "description": "站点ID列表 (逗号分隔)", "name": "site_ids", "in": "query"}, {"type": "integer", "description": "每页记录数 (默认: 10)", "name": "limit", "in": "query"}, {"type": "integer", "description": "偏移量 (默认: 0)", "name": "offset", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.NozzleDetailedSalesResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/enhanced-reports/query-performance": {"get": {"description": "获取指定查询类型的性能指标，用于监控和优化查询性能", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["增强报表"], "summary": "获取查询性能指标", "parameters": [{"type": "string", "description": "查询类型 (total_revenue, fuel_receivable, payment_method_detail, product_detail, nozzle_detail)", "name": "query_type", "in": "query", "required": true}, {"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "start_date", "in": "query", "required": true}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "end_date", "in": "query", "required": true}, {"type": "string", "description": "站点ID列表 (逗号分隔)", "name": "site_ids", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.QueryPerformanceResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/enhanced-reports/sales-by-payment-method": {"get": {"description": "获取按支付方式分组的销售明细，包括各支付方式下的详细交易列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["增强报表"], "summary": "获取按支付方式销售明细", "parameters": [{"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "start_date", "in": "query", "required": true}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "end_date", "in": "query", "required": true}, {"type": "string", "description": "站点ID列表 (逗号分隔)", "name": "site_ids", "in": "query"}, {"type": "integer", "description": "每页记录数 (默认: 10)", "name": "limit", "in": "query"}, {"type": "integer", "description": "偏移量 (默认: 0)", "name": "offset", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.SalesDetailByPaymentMethodResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/enhanced-reports/sales-by-product": {"get": {"description": "获取按商品分组的销售明细，包括各商品的详细交易记录和小计", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["增强报表"], "summary": "获取按商品销售明细", "parameters": [{"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "start_date", "in": "query", "required": true}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "end_date", "in": "query", "required": true}, {"type": "string", "description": "站点ID列表 (逗号分隔)", "name": "site_ids", "in": "query"}, {"type": "integer", "description": "每页记录数 (默认: 10)", "name": "limit", "in": "query"}, {"type": "integer", "description": "偏移量 (默认: 0)", "name": "offset", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.SalesDetailByProductResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/enhanced-reports/total-revenue": {"get": {"description": "获取指定时间段内的总收入汇总数据，包括燃油和非燃油收入、退款等信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["增强报表"], "summary": "获取总收入汇总", "parameters": [{"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "start_date", "in": "query", "required": true}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "end_date", "in": "query", "required": true}, {"type": "string", "description": "站点ID列表 (逗号分隔)", "name": "site_ids", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.GetTotalRevenueResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/fuel-transactions": {"get": {"description": "获取燃油交易列表，支持分页和过滤", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["燃油交易"], "summary": "列出燃油交易", "parameters": [{"type": "integer", "description": "加油站ID", "name": "station_id", "in": "query"}, {"type": "string", "description": "交易状态 (pending, processed, cancelled)", "name": "status", "in": "query"}, {"type": "string", "description": "油枪ID", "name": "pump_id", "in": "query"}, {"type": "integer", "description": "会员ID", "name": "member_id", "in": "query"}, {"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "date_from", "in": "query"}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "date_to", "in": "query"}, {"type": "string", "description": "交易编号", "name": "transaction_number", "in": "query"}, {"type": "string", "description": "燃油类型", "name": "fuel_type", "in": "query"}, {"type": "string", "description": "燃油等级", "name": "fuel_grade", "in": "query"}, {"type": "integer", "description": "油罐编号", "name": "tank", "in": "query"}, {"type": "integer", "description": "页码，默认为1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量，默认为10", "name": "limit", "in": "query"}, {"type": "string", "description": "排序字段，默认为created_at", "name": "sort_by", "in": "query"}, {"type": "string", "description": "排序方向 (asc, desc)，默认为desc", "name": "sort_dir", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.ListFuelTransactionsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "post": {"description": "创建新的燃油交易记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["燃油交易"], "summary": "创建燃油交易", "parameters": [{"description": "燃油交易信息", "name": "transaction", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateFuelTransactionRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/repository.FuelTransaction"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/fuel-transactions/{id}": {"get": {"description": "根据ID获取燃油交易详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["燃油交易"], "summary": "获取燃油交易", "parameters": [{"type": "integer", "description": "燃油交易ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/repository.FuelTransaction"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/fuel-transactions/{id}/link": {"post": {"description": "创建燃油交易与订单的关联关系", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["燃油交易"], "summary": "关联燃油交易到订单", "parameters": [{"type": "integer", "description": "燃油交易ID", "name": "id", "in": "path", "required": true}, {"description": "关联信息", "name": "link", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.LinkFuelTransactionToOrderRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/repository.FuelTransactionOrderLink"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/fuel-transactions/{id}/link/{order_id}": {"put": {"description": "更新燃油交易与订单关联的分配金额", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["燃油交易"], "summary": "更新关联分配金额", "parameters": [{"type": "integer", "description": "燃油交易ID", "name": "id", "in": "path", "required": true}, {"type": "integer", "description": "订单ID", "name": "order_id", "in": "path", "required": true}, {"description": "更新请求", "name": "update", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateLinkAllocatedAmountRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/repository.FuelTransactionOrderLink"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/fuel-transactions/{id}/orders": {"get": {"description": "获取与指定燃油交易关联的所有订单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["燃油交易"], "summary": "获取燃油交易关联的订单", "parameters": [{"type": "integer", "description": "燃油交易ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/repository.Order"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/fuel-transactions/{id}/unlink/{order_id}": {"delete": {"description": "解除燃油交易与订单的关联关系", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["燃油交易"], "summary": "解除燃油交易与订单的关联", "parameters": [{"type": "integer", "description": "燃油交易ID", "name": "id", "in": "path", "required": true}, {"type": "integer", "description": "订单ID", "name": "order_id", "in": "path", "required": true}], "responses": {"204": {"description": "No Content", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/members": {"get": {"description": "分页获取会员列表，支持关键字搜索", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["members"], "summary": "分页获取会员列表", "parameters": [{"type": "integer", "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页条数，默认10", "name": "page_size", "in": "query"}, {"type": "string", "description": "搜索关键字", "name": "keyword", "in": "query"}], "responses": {"200": {"description": "会员列表和总数", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "获取会员列表失败", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"description": "创建新的会员账户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["members"], "summary": "创建会员", "parameters": [{"description": "会员信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateMemberRequest"}}], "responses": {"201": {"description": "会员信息", "schema": {"$ref": "#/definitions/model.Member"}}, "400": {"description": "无效的请求参数", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "409": {"description": "会员已存在", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "创建会员失败", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/members/phone/{phone}": {"get": {"description": "根据手机号获取会员详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["members"], "summary": "根据手机号获取会员信息", "parameters": [{"type": "string", "description": "会员手机号", "name": "phone", "in": "path", "required": true}], "responses": {"200": {"description": "会员信息", "schema": {"$ref": "#/definitions/model.Member"}}, "400": {"description": "手机号不能为空", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "会员不存在", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "获取会员失败", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/members/statistics": {"get": {"description": "获取会员统计数据，包括总数、活跃数、新增数等", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["members"], "summary": "获取会员统计数据", "parameters": [{"type": "string", "description": "起始日期，格式：YYYY-MM-DD", "name": "start_date", "in": "query"}, {"type": "string", "description": "结束日期，格式：YYYY-MM-DD", "name": "end_date", "in": "query"}], "responses": {"200": {"description": "会员统计数据", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "获取统计数据失败", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/members/verify": {"post": {"description": "验证会员登录凭证", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["members"], "summary": "验证会员登录", "parameters": [{"description": "登录信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.VerifyMemberRequest"}}], "responses": {"200": {"description": "会员信息", "schema": {"$ref": "#/definitions/model.Member"}}, "400": {"description": "无效的请求参数", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "手机号或密码错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "验证失败", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/members/{id}": {"get": {"description": "根据会员ID获取会员详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["members"], "summary": "获取会员信息", "parameters": [{"type": "string", "description": "会员ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "会员信息", "schema": {"$ref": "#/definitions/model.Member"}}, "400": {"description": "会员ID不能为空", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "会员不存在", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "获取会员失败", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "put": {"description": "更新会员基本信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["members"], "summary": "更新会员信息", "parameters": [{"type": "string", "description": "会员ID", "name": "id", "in": "path", "required": true}, {"description": "会员信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateMemberRequest"}}], "responses": {"200": {"description": "会员信息", "schema": {"$ref": "#/definitions/model.Member"}}, "400": {"description": "无效的请求参数", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "会员不存在", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "更新会员失败", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/members/{id}/status": {"patch": {"description": "更新会员账户状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["members"], "summary": "更新会员状态", "parameters": [{"type": "string", "description": "会员ID", "name": "id", "in": "path", "required": true}, {"description": "状态信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateMemberStatusRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "无效的请求参数", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "会员不存在", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "更新会员状态失败", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/oil/adjustments": {"get": {"description": "根据状态、发起人、关键词和日期范围获取价格调整流程列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品价格调整"], "summary": "获取价格调整流程列表", "parameters": [{"type": "string", "description": "状态过滤", "name": "status", "in": "query"}, {"type": "string", "description": "发起人过滤", "name": "initiated_by", "in": "query"}, {"type": "string", "description": "搜索关键词", "name": "keyword", "in": "query"}, {"type": "string", "description": "开始日期过滤，RFC3339格式", "name": "start_date", "in": "query"}, {"type": "string", "description": "结束日期过滤，RFC3339格式", "name": "end_date", "in": "query"}], "responses": {"200": {"description": "价格调整流程列表", "schema": {"type": "array", "items": {"$ref": "#/definitions/model.PriceAdjustmentProcess"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"description": "发起新的油品价格调整流程", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品价格调整"], "summary": "发起价格调整", "parameters": [{"description": "发起价格调整请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/oil.InitiatePriceAdjustmentRequest"}}], "responses": {"201": {"description": "创建的价格调整流程", "schema": {"$ref": "#/definitions/model.PriceAdjustmentProcess"}}, "400": {"description": "无效的请求数据", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/oil/adjustments/{id}": {"get": {"description": "获取指定ID的价格调整流程详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品价格调整"], "summary": "获取价格调整详情", "parameters": [{"type": "integer", "description": "调整ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "价格调整流程详情", "schema": {"$ref": "#/definitions/oil.AdjustmentProcessDetail"}}, "400": {"description": "无效的调整ID", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/oil/adjustments/{id}/approve": {"post": {"description": "审批指定ID的价格调整流程", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品价格调整"], "summary": "审批价格调整", "parameters": [{"type": "integer", "description": "调整ID", "name": "id", "in": "path", "required": true}, {"description": "审批请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.ApproveRequest"}}], "responses": {"200": {"description": "调整已审批提示", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "无效的调整ID或请求数据", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/oil/adjustments/{id}/submit": {"post": {"description": "将草稿状态的价格调整流程提交审批", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品价格调整"], "summary": "提交价格调整流程审批", "parameters": [{"type": "integer", "description": "调整ID", "name": "id", "in": "path", "required": true}, {"description": "提交请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.ApproveRequest"}}], "responses": {"200": {"description": "调整已提交审批提示", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "无效的调整ID或请求数据", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/oil/price": {"get": {"description": "根据产品ID、客户ID、客户组ID和数量获取油品销售价格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品管理"], "summary": "获取油品销售价格", "parameters": [{"type": "integer", "description": "产品ID", "name": "product_id", "in": "query", "required": true}, {"type": "integer", "description": "客户ID", "name": "customer_id", "in": "query"}, {"type": "integer", "description": "客户组ID", "name": "customer_group_id", "in": "query"}, {"type": "number", "description": "购买数量（默认1.0）", "name": "quantity", "in": "query"}], "responses": {"200": {"description": "油品价格信息", "schema": {"$ref": "#/definitions/oil.SalesPriceInfo"}}, "400": {"description": "无效的产品ID", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/oil/products": {"get": {"description": "根据分类、关键词和激活状态获取油品列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品管理"], "summary": "获取油品列表", "parameters": [{"type": "string", "description": "油品分类", "name": "category", "in": "query"}, {"type": "string", "description": "搜索关键词", "name": "keyword", "in": "query"}, {"type": "string", "description": "是否激活，true或false", "name": "is_active", "in": "query"}], "responses": {"200": {"description": "油品列表", "schema": {"type": "array", "items": {"$ref": "#/definitions/model.OilProduct"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"description": "创建新的油品产品", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品管理"], "summary": "创建新油品", "parameters": [{"description": "创建油品请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/oil.CreateProductRequest"}}], "responses": {"201": {"description": "创建的油品信息", "schema": {"$ref": "#/definitions/model.OilProduct"}}, "400": {"description": "无效的请求数据", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/oil/products/{id}": {"get": {"description": "根据产品ID获取油品详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品管理"], "summary": "获取单个油品详情", "parameters": [{"type": "integer", "description": "产品ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "油品详情", "schema": {"$ref": "#/definitions/model.OilProduct"}}, "400": {"description": "无效的产品ID", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "未找到产品", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "put": {"description": "更新指定ID的油品信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品管理"], "summary": "更新油品信息", "parameters": [{"type": "integer", "description": "产品ID", "name": "id", "in": "path", "required": true}, {"description": "更新油品请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/oil.UpdateProductRequest"}}], "responses": {"200": {"description": "更新成功提示", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "无效的产品ID或请求数据", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/oil/products/{id}/price": {"post": {"description": "设置指定油品的基础价格、成本价格、建议零售价和批发价格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品管理"], "summary": "设置油品基础价格", "parameters": [{"type": "integer", "description": "产品ID", "name": "id", "in": "path", "required": true}, {"description": "价格设置请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.PriceRequest"}}], "responses": {"200": {"description": "价格设置成功提示", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "无效的产品ID或请求数据", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/oil/strategies": {"get": {"description": "根据关键词、产品ID和激活状态获取定价策略列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品定价策略"], "summary": "获取定价策略列表", "parameters": [{"type": "string", "description": "搜索关键词", "name": "keyword", "in": "query"}, {"type": "integer", "description": "产品ID过滤", "name": "product_id", "in": "query"}, {"type": "string", "description": "是否激活，true或false", "name": "active", "in": "query"}], "responses": {"200": {"description": "策略列表", "schema": {"type": "array", "items": {"$ref": "#/definitions/model.PricingStrategy"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"description": "创建新的油品定价策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品定价策略"], "summary": "创建定价策略", "parameters": [{"description": "创建策略请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/oil.CreateStrategyRequest"}}], "responses": {"201": {"description": "创建的策略信息", "schema": {"$ref": "#/definitions/model.PricingStrategy"}}, "400": {"description": "无效的请求数据", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/oil/strategies/{id}/activate": {"post": {"description": "激活指定ID的定价策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品定价策略"], "summary": "激活定价策略", "parameters": [{"type": "integer", "description": "策略ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "策略已激活提示", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "无效的策略ID", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/oil/strategies/{id}/deactivate": {"post": {"description": "停用指定ID的定价策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["油品定价策略"], "summary": "停用定价策略", "parameters": [{"type": "integer", "description": "策略ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "策略已停用提示", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "无效的策略ID", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器内部错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/orders": {"get": {"description": "获取订单列表，支持分页和过滤", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单"], "summary": "列出订单", "parameters": [{"type": "integer", "description": "客户ID", "name": "customer_id", "in": "query"}, {"type": "integer", "description": "加油站ID", "name": "station_id", "in": "query"}, {"type": "string", "description": "订单状态 (new, processing, completed, cancelled)", "name": "status", "in": "query"}, {"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "date_from", "in": "query"}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "date_to", "in": "query"}, {"type": "string", "description": "订单编号", "name": "order_number", "in": "query"}, {"type": "string", "description": "产品类型", "name": "product_type", "in": "query"}, {"type": "string", "description": "支付方式", "name": "payment_method", "in": "query"}, {"type": "string", "description": "员工编号", "name": "employee_no", "in": "query"}, {"type": "integer", "description": "页码，默认为1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量，默认为10", "name": "limit", "in": "query"}, {"type": "string", "description": "排序字段，默认为created_at", "name": "sort_by", "in": "query"}, {"type": "string", "description": "排序方向 (asc, desc)，默认为desc", "name": "sort_dir", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.ListOrdersResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "post": {"description": "创建新的订单记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单"], "summary": "创建订单", "parameters": [{"description": "订单信息", "name": "order", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateOrderRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/repository.Order"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/orders/{id}": {"get": {"description": "根据ID获取订单详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单"], "summary": "获取订单", "parameters": [{"type": "integer", "description": "订单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/repository.Order"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/orders/{id}/cancel": {"post": {"description": "将订单标记为已取消状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单"], "summary": "取消订单", "parameters": [{"type": "integer", "description": "订单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/repository.Order"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/orders/{id}/complete": {"post": {"description": "将订单标记为已完成状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单"], "summary": "完成订单", "parameters": [{"type": "integer", "description": "订单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/repository.Order"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/orders/{id}/items": {"post": {"description": "向订单添加新的订单项", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单"], "summary": "添加订单项", "parameters": [{"type": "integer", "description": "订单ID", "name": "id", "in": "path", "required": true}, {"description": "订单项信息", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.AddOrderItemRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/repository.OrderItem"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/orders/{id}/items/{item_id}": {"delete": {"description": "从订单中移除订单项", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单"], "summary": "移除订单项", "parameters": [{"type": "integer", "description": "订单ID", "name": "id", "in": "path", "required": true}, {"type": "integer", "description": "订单项ID", "name": "item_id", "in": "path", "required": true}], "responses": {"204": {"description": "No Content", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/orders/{id}/promotions": {"post": {"description": "向订单应用促销", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单"], "summary": "应用促销", "parameters": [{"type": "integer", "description": "订单ID", "name": "id", "in": "path", "required": true}, {"description": "促销信息", "name": "promotion", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.ApplyPromotionRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/repository.OrderPromotion"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/orders/{order_id}/fuel-transactions": {"get": {"description": "获取与指定订单关联的所有燃油交易", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["燃油交易"], "summary": "获取订单关联的燃油交易", "parameters": [{"type": "integer", "description": "订单ID", "name": "order_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/repository.FuelTransaction"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/promotions": {"get": {"description": "获取所有促销活动，支持分页、搜索和筛选", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["促销管理"], "summary": "获取促销活动列表", "parameters": [{"type": "string", "description": "促销活动状态筛选（如'ACTIVE'，'DRAFT'等）", "name": "status", "in": "query"}, {"type": "string", "description": "促销活动类型筛选（如'PERCENTAGE'，'FIXED_AMOUNT'等）", "name": "type", "in": "query"}, {"type": "string", "description": "搜索关键词（按名称搜索）", "name": "search", "in": "query"}, {"type": "integer", "description": "页码，默认为1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页记录数，默认为10，最大100", "name": "pageSize", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/promotions/edit/{id}": {"get": {"description": "获取新增或编辑促销活动所需的表单数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["促销管理"], "summary": "获取促销活动表单数据", "parameters": [{"type": "string", "description": "促销活动ID（编辑时提供）", "name": "id", "in": "path"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "活动不存在", "schema": {"type": "string"}}, "500": {"description": "服务器错误", "schema": {"type": "string"}}}}}, "/promotions/new": {"get": {"description": "获取新增或编辑促销活动所需的表单数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["促销管理"], "summary": "获取促销活动表单数据", "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "活动不存在", "schema": {"type": "string"}}, "500": {"description": "服务器错误", "schema": {"type": "string"}}}}}, "/promotions/save": {"post": {"description": "创建新的促销活动或更新现有的促销活动", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["促销管理"], "summary": "保存促销活动", "parameters": [{"type": "string", "description": "促销活动名称", "name": "name", "in": "formData", "required": true}, {"type": "string", "description": "促销活动描述", "name": "description", "in": "formData"}, {"type": "string", "description": "促销类型", "name": "type", "in": "formData", "required": true}, {"type": "string", "description": "促销范围", "name": "scope", "in": "formData", "required": true}, {"type": "number", "description": "促销值", "name": "value", "in": "formData"}, {"type": "string", "description": "状态", "name": "status", "in": "formData"}, {"type": "number", "description": "最低订单金额", "name": "min_order_amount", "in": "formData"}, {"type": "number", "description": "最大折扣金额", "name": "max_discount_amount", "in": "formData"}, {"type": "integer", "description": "优先级", "name": "priority", "in": "formData"}, {"type": "string", "description": "开始时间（格式：2006-01-02T15:04）", "name": "start_time", "in": "formData"}, {"type": "string", "description": "结束时间（格式：2006-01-02T15:04）", "name": "end_time", "in": "formData"}, {"type": "boolean", "description": "是否可叠加", "name": "stackable", "in": "formData"}, {"type": "boolean", "description": "是否需要优惠券", "name": "requires_coupon", "in": "formData"}, {"type": "integer", "description": "最大使用次数", "name": "max_use_count", "in": "formData"}, {"type": "integer", "description": "每用户最大使用次数", "name": "max_per_user", "in": "formData"}, {"type": "boolean", "description": "是否适用会员", "name": "for_members", "in": "formData"}, {"type": "boolean", "description": "是否适用非会员", "name": "for_non_members", "in": "formData"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "请求数据无效", "schema": {"type": "string"}}, "500": {"description": "服务器错误", "schema": {"type": "string"}}}}}, "/promotions/save/{id}": {"post": {"description": "创建新的促销活动或更新现有的促销活动", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["促销管理"], "summary": "保存促销活动", "parameters": [{"type": "string", "description": "促销活动ID（更新时提供）", "name": "id", "in": "path"}, {"type": "string", "description": "促销活动名称", "name": "name", "in": "formData", "required": true}, {"type": "string", "description": "促销活动描述", "name": "description", "in": "formData"}, {"type": "string", "description": "促销类型", "name": "type", "in": "formData", "required": true}, {"type": "string", "description": "促销范围", "name": "scope", "in": "formData", "required": true}, {"type": "number", "description": "促销值", "name": "value", "in": "formData"}, {"type": "string", "description": "状态", "name": "status", "in": "formData"}, {"type": "number", "description": "最低订单金额", "name": "min_order_amount", "in": "formData"}, {"type": "number", "description": "最大折扣金额", "name": "max_discount_amount", "in": "formData"}, {"type": "integer", "description": "优先级", "name": "priority", "in": "formData"}, {"type": "string", "description": "开始时间（格式：2006-01-02T15:04）", "name": "start_time", "in": "formData"}, {"type": "string", "description": "结束时间（格式：2006-01-02T15:04）", "name": "end_time", "in": "formData"}, {"type": "boolean", "description": "是否可叠加", "name": "stackable", "in": "formData"}, {"type": "boolean", "description": "是否需要优惠券", "name": "requires_coupon", "in": "formData"}, {"type": "integer", "description": "最大使用次数", "name": "max_use_count", "in": "formData"}, {"type": "integer", "description": "每用户最大使用次数", "name": "max_per_user", "in": "formData"}, {"type": "boolean", "description": "是否适用会员", "name": "for_members", "in": "formData"}, {"type": "boolean", "description": "是否适用非会员", "name": "for_non_members", "in": "formData"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "请求数据无效", "schema": {"type": "string"}}, "500": {"description": "服务器错误", "schema": {"type": "string"}}}}}, "/promotions/{id}": {"get": {"description": "根据ID获取单个促销活动的详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["促销管理"], "summary": "查看促销活动详情", "parameters": [{"type": "string", "description": "促销活动ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "活动不存在", "schema": {"type": "string"}}, "500": {"description": "服务器错误", "schema": {"type": "string"}}}}, "delete": {"description": "根据ID删除指定的促销活动", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["促销管理"], "summary": "删除促销活动", "parameters": [{"type": "string", "description": "促销活动ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "错误响应", "schema": {"type": "object", "additionalProperties": true}}}}}, "/promotions/{id}/status": {"post": {"description": "更新指定促销活动的状态（如激活、暂停等）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["促销管理"], "summary": "更新促销活动状态", "parameters": [{"type": "string", "description": "促销活动ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "新状态", "name": "status", "in": "formData", "required": true}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "错误响应", "schema": {"type": "object", "additionalProperties": true}}}}}, "/pts2/devices": {"get": {"description": "获取系统中所有已配置的PTS2设备列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["PTS2设备管理"], "summary": "获取设备列表", "responses": {"200": {"description": "设备列表", "schema": {"type": "array", "items": {"$ref": "#/definitions/ptsmanager.DeviceConfig"}}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/pts2/devices/{deviceId}": {"get": {"description": "获取指定PTS2设备的详细配置信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["PTS2设备管理"], "summary": "获取设备详情", "parameters": [{"type": "string", "description": "设备ID", "name": "deviceId", "in": "path", "required": true}], "responses": {"200": {"description": "设备详情", "schema": {"$ref": "#/definitions/ptsmanager.DeviceConfig"}}, "404": {"description": "设备不存在", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/pts2/devices/{deviceId}/connect": {"post": {"description": "建立与指定PTS2设备的连接", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["PTS2设备管理"], "summary": "连接设备", "parameters": [{"type": "string", "description": "设备ID", "name": "deviceId", "in": "path", "required": true}], "responses": {"200": {"description": "连接结果", "schema": {"$ref": "#/definitions/handlers.StatusResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/pts2/devices/{deviceId}/disconnect": {"post": {"description": "断开与指定PTS2设备的连接", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["PTS2设备管理"], "summary": "断开设备连接", "parameters": [{"type": "string", "description": "设备ID", "name": "deviceId", "in": "path", "required": true}], "responses": {"200": {"description": "断开连接结果", "schema": {"$ref": "#/definitions/handlers.StatusResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/pts2/devices/{deviceId}/info": {"get": {"description": "获取指定PTS2设备的详细信息，包括设备类型、序列号、版本等", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["PTS2设备管理"], "summary": "获取设备信息", "parameters": [{"type": "string", "description": "设备ID", "name": "deviceId", "in": "path", "required": true}], "responses": {"200": {"description": "设备信息", "schema": {"$ref": "#/definitions/handlers.DeviceInfoResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/pts2/devices/{deviceId}/pumps/{pumpId}": {"get": {"description": "获取指定PTS2设备上指定油泵的当前状态信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["PTS2油泵管理"], "summary": "获取油泵状态", "parameters": [{"type": "string", "description": "设备ID", "name": "deviceId", "in": "path", "required": true}, {"type": "integer", "description": "油泵ID", "name": "pumpId", "in": "path", "required": true}], "responses": {"200": {"description": "油泵状态", "schema": {"$ref": "#/definitions/handlers.PumpStatusResponse"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}}}}, "/pts2/devices/{deviceId}/pumps/{pumpId}/authorize": {"post": {"description": "对指定PTS2设备上的油泵进行授权操作", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["PTS2油泵管理"], "summary": "授权油泵", "parameters": [{"type": "string", "description": "设备ID", "name": "deviceId", "in": "path", "required": true}, {"type": "integer", "description": "油泵ID", "name": "pumpId", "in": "path", "required": true}, {"description": "授权参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.AuthorizePumpRequest"}}], "responses": {"200": {"description": "授权结果", "schema": {"$ref": "#/definitions/handlers.AuthorizePumpResponse"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}}}}, "/pts2/devices/{deviceId}/pumps/{pumpId}/nozzles/{nozzleId}/price": {"post": {"description": "设置指定PTS2设备上油泵的单个喷枪价格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["PTS2油泵管理"], "summary": "设置喷枪价格", "parameters": [{"type": "string", "description": "设备ID", "name": "deviceId", "in": "path", "required": true}, {"type": "integer", "description": "油泵ID", "name": "pumpId", "in": "path", "required": true}, {"type": "integer", "description": "喷枪ID", "name": "nozzleId", "in": "path", "required": true}, {"description": "价格参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.SetNozzlePriceRequest"}}], "responses": {"200": {"description": "调价结果", "schema": {"$ref": "#/definitions/handlers.SetNozzlePriceResponse"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}}}}, "/pts2/devices/{deviceId}/pumps/{pumpId}/preset": {"post": {"description": "对指定PTS2设备上的油泵进行预设操作，可设置加油金额或容量", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["PTS2油泵管理"], "summary": "预设油泵", "parameters": [{"type": "string", "description": "设备ID", "name": "deviceId", "in": "path", "required": true}, {"type": "integer", "description": "油泵ID", "name": "pumpId", "in": "path", "required": true}, {"description": "预设参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.PresetPumpRequest"}}], "responses": {"200": {"description": "预设结果", "schema": {"$ref": "#/definitions/handlers.PresetPumpResponse"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}}}}, "/pts2/devices/{deviceId}/pumps/{pumpId}/prices": {"post": {"description": "设置指定PTS2设备上油泵的所有喷枪价格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["PTS2油泵管理"], "summary": "设置油泵价格", "parameters": [{"type": "string", "description": "设备ID", "name": "deviceId", "in": "path", "required": true}, {"type": "integer", "description": "油泵ID", "name": "pumpId", "in": "path", "required": true}, {"description": "价格参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.SetPumpPricesRequest"}}], "responses": {"200": {"description": "调价结果", "schema": {"$ref": "#/definitions/handlers.SetPumpPricesResponse"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}}}}, "/pts2/devices/{deviceId}/status": {"get": {"description": "获取指定PTS2设备的连接状态和错误信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["PTS2设备管理"], "summary": "获取设备状态", "parameters": [{"type": "string", "description": "设备ID", "name": "deviceId", "in": "path", "required": true}], "responses": {"200": {"description": "设备状态", "schema": {"$ref": "#/definitions/ptsmanager.DeviceState"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/reports/nozzle-sales": {"get": {"description": "获取油枪销售汇总", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["报表"], "summary": "获取油枪销售汇总", "parameters": [{"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "start_date", "in": "query", "required": true}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "end_date", "in": "query", "required": true}, {"type": "string", "description": "站点ID列表 (逗号分隔)", "name": "site_ids", "in": "query"}, {"type": "string", "description": "聚合粒度 (day, week, month)", "name": "granularity", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.GetNozzleSalesSummaryResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/reports/payment-methods": {"get": {"description": "获取按支付方式的销售汇总", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["报表"], "summary": "获取支付方式汇总", "parameters": [{"type": "integer", "description": "站点ID", "name": "station_id", "in": "query"}, {"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "date_from", "in": "query"}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "date_to", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.GetPaymentMethodSummaryResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/reports/receivable": {"get": {"description": "获取油品应收汇总", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["报表"], "summary": "获取油品应收汇总", "parameters": [{"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "start_date", "in": "query", "required": true}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "end_date", "in": "query", "required": true}, {"type": "string", "description": "站点ID列表 (逗号分隔)", "name": "site_ids", "in": "query"}, {"type": "string", "description": "聚合粒度 (day, week, month)", "name": "granularity", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.GetAggregatedReceivableResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/reports/revenue": {"get": {"description": "获取聚合收入报表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["报表"], "summary": "获取聚合收入报表", "parameters": [{"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "start_date", "in": "query", "required": true}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "end_date", "in": "query", "required": true}, {"type": "string", "description": "站点ID列表 (逗号分隔)", "name": "site_ids", "in": "query"}, {"type": "string", "description": "聚合粒度 (day, week, month)", "name": "granularity", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.GetAggregatedRevenueResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/reports/sales-by-category": {"get": {"description": "按商品分类聚合销售额", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["报表"], "summary": "获取按商品分类销售汇总", "parameters": [{"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "start_date", "in": "query", "required": true}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "end_date", "in": "query", "required": true}, {"type": "string", "description": "站点ID列表 (逗号分隔)", "name": "site_ids", "in": "query"}, {"type": "string", "description": "聚合粒度 (day, week, month)", "name": "granularity", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.GetAggregatedSalesByProductCategoryResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/reports/shift-eod": {"get": {"description": "获取指定站点、日期的班次日终报表数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["报表"], "summary": "获取班次日终报表", "parameters": [{"type": "integer", "description": "站点ID", "name": "station_id", "in": "query", "required": true}, {"type": "string", "description": "查询日期 (格式: 2024-01-04)", "name": "date", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.GetShiftEODReportResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/reports/transactions": {"get": {"description": "获取交易明细数据(用于报表)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["报表"], "summary": "获取交易明细", "parameters": [{"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "start_date", "in": "query", "required": true}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "end_date", "in": "query", "required": true}, {"type": "string", "description": "站点ID列表 (逗号分隔)", "name": "site_ids", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.GetTransactionsForReportResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/shifts": {"get": {"description": "获取班次列表，支持分页和过滤", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["班次"], "summary": "列出班次", "parameters": [{"type": "integer", "description": "站点ID", "name": "station_id", "in": "query"}, {"type": "string", "description": "班次状态 (active, closed)", "name": "status", "in": "query"}, {"type": "string", "description": "班次编号", "name": "shift_number", "in": "query"}, {"type": "string", "description": "开始日期 (格式: 2006-01-02)", "name": "date_from", "in": "query"}, {"type": "string", "description": "结束日期 (格式: 2006-01-02)", "name": "date_to", "in": "query"}, {"type": "boolean", "description": "是否包含已删除班次", "name": "include_deleted", "in": "query"}, {"type": "integer", "description": "页码，默认为1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量，默认为10", "name": "limit", "in": "query"}, {"type": "string", "description": "排序字段，默认为created_at", "name": "sort_by", "in": "query"}, {"type": "string", "description": "排序方向 (asc, desc)，默认为desc", "name": "sort_dir", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.ListShiftsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/shifts/current/{station_id}": {"get": {"description": "获取指定站点的当前活跃班次", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["班次"], "summary": "获取当前活跃班次", "parameters": [{"type": "integer", "description": "站点ID", "name": "station_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/repository.Shift"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "没有活跃班次", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/shifts/number/{number}": {"get": {"description": "根据班次编号获取班次详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["班次"], "summary": "根据编号获取班次", "parameters": [{"type": "string", "description": "班次编号", "name": "number", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/repository.Shift"}}, "404": {"description": "班次不存在", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/shifts/report/{id}": {"get": {"description": "根据ID获取班次报表数据，支持JSON格式和小票格式。默认返回JSON格式的结构化数据，当format=receipt时返回小票格式数据。响应结构：当format=json时data字段包含ShiftReportDTO结构，当format=receipt时data字段包含ShiftReceiptDTO结构", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["班次"], "summary": "获取班次报表", "parameters": [{"type": "string", "description": "班次ID", "name": "id", "in": "path", "required": true}, {"enum": ["json", "receipt"], "type": "string", "default": "json", "description": "响应格式 (json|receipt)", "name": "format", "in": "query"}, {"enum": ["IDR", "USD"], "type": "string", "default": "IDR", "description": "货币类型 (IDR|USD)", "name": "currency", "in": "query"}, {"type": "string", "default": "Asia/Jakarta", "description": "时区", "name": "timezone", "in": "query"}, {"type": "boolean", "default": true, "description": "是否包含详细数据", "name": "include_details", "in": "query"}], "responses": {"200": {"description": "成功响应，data字段根据format参数包含不同结构的数据", "schema": {"$ref": "#/definitions/repository.ShiftReportJSONResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "班次不存在", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/shifts/start": {"post": {"description": "在指定站点开始一个新的班次", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["班次"], "summary": "开始新班次", "parameters": [{"description": "班次信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.StartShiftRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/repository.Shift"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "409": {"description": "当前站点已有活跃班次", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/shifts/{id}": {"get": {"description": "根据ID获取班次详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["班次"], "summary": "获取班次详情", "parameters": [{"type": "string", "description": "班次ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/repository.Shift"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "班次不存在", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "delete": {"description": "软删除指定班次", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["班次"], "summary": "软删除班次", "parameters": [{"type": "string", "description": "班次ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "班次不存在", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "409": {"description": "无法删除活跃班次", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/shifts/{id}/restore": {"post": {"description": "恢复已被软删除的班次", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["班次"], "summary": "恢复已删除班次", "parameters": [{"type": "string", "description": "班次ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "班次不存在或未被删除", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/shifts/{station_id}/end": {"post": {"description": "结束指定站点的当前活跃班次", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["班次"], "summary": "结束当前班次", "parameters": [{"type": "integer", "description": "站点ID", "name": "station_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/repository.Shift"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "没有活跃班次", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "409": {"description": "班次中有未处理的交易", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/shifts/{station_id}/ensure": {"post": {"description": "确保指定站点有活跃班次，如果没有则自动创建", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["班次"], "summary": "确保班次已开始", "parameters": [{"type": "integer", "description": "站点ID", "name": "station_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/repository.Shift"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}}, "definitions": {"dto.AdjustInventoryRequest": {"type": "object", "properties": {"notes": {"description": "备注信息（可选）", "type": "string"}, "operator": {"description": "操作人", "type": "string"}, "product_id": {"description": "商品ID", "type": "integer"}, "quantity": {"description": "调整数量（正数表示入库，出库时会自动转为负数）", "type": "number"}, "reference_id": {"description": "引用单号（如订单号、采购单号等）", "type": "string"}}}, "dto.AdjustInventoryResponse": {"type": "object", "properties": {"adjusted_amount": {"description": "实际调整数量", "type": "number"}, "current_stock": {"description": "当前库存（调整后）", "type": "number"}, "message": {"description": "结果消息", "type": "string"}, "product_id": {"description": "商品ID", "type": "integer"}, "success": {"description": "操作是否成功", "type": "boolean"}}}, "dto.AssignLabelToCustomerRequest": {"type": "object", "required": ["customer_id", "label_id"], "properties": {"customer_id": {"description": "客户ID，必填", "type": "integer", "minimum": 1}, "label_id": {"description": "标签ID，必填", "type": "integer", "minimum": 1}}}, "dto.CategoryResponse": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "description": {"description": "分类描述", "type": "string"}, "id": {"description": "分类ID", "type": "integer"}, "name": {"description": "分类名称", "type": "string"}, "parent_id": {"description": "父分类ID", "type": "integer"}, "updated_at": {"description": "更新时间", "type": "string"}}}, "dto.CompleteStocktakeResponse": {"type": "object", "properties": {"message": {"description": "结果消息", "type": "string"}, "stocktake_id": {"description": "盘点任务ID", "type": "integer"}, "success": {"description": "操作是否成功", "type": "boolean"}}}, "dto.CountCustomersResponse": {"type": "object", "properties": {"count": {"description": "客户总数", "type": "integer"}}}, "dto.CreateCategoryRequest": {"type": "object", "properties": {"description": {"description": "分类描述", "type": "string"}, "name": {"description": "分类名称", "type": "string"}, "parent_id": {"description": "父分类ID，0表示根分类", "type": "integer"}}}, "dto.CreateCustomerRequest": {"type": "object", "required": ["hos_id"], "properties": {"email": {"description": "客户邮箱，可选，需要邮箱格式", "type": "string", "maxLength": 100}, "hos_id": {"description": "HOS系统ID，必填", "type": "integer", "minimum": 1}, "name": {"description": "客户名称，可选", "type": "string", "maxLength": 100, "minLength": 1}, "phone": {"description": "客户手机号，可选", "type": "string", "maxLength": 20, "minLength": 11}}}, "dto.CreateLabelRequest": {"type": "object", "required": ["name"], "properties": {"description": {"description": "标签描述，可选", "type": "string", "maxLength": 500}, "name": {"description": "标签名称，必填", "type": "string", "maxLength": 100, "minLength": 1}}}, "dto.CreateProductRequest": {"type": "object", "required": ["category_id", "code", "name", "type", "unit"], "properties": {"category_id": {"description": "商品分类ID", "type": "integer"}, "code": {"description": "商品编码，必须唯一，如果为空则自动生成", "type": "string"}, "name": {"description": "商品名称", "type": "string"}, "properties": {"description": "其他属性", "type": "object", "additionalProperties": true}, "specs": {"description": "规格", "type": "string"}, "status": {"description": "商品状态，默认为ACTIVE", "type": "string"}, "type": {"description": "商品类型 (OIL, NON_OIL, GIFT等)", "type": "string"}, "unit": {"description": "计量单位", "type": "string"}}}, "dto.CreateReservationRequest": {"type": "object", "properties": {"expires_at": {"description": "预留过期时间", "allOf": [{"$ref": "#/definitions/dto.CustomTime"}]}, "operator": {"description": "操作人", "type": "string"}, "order_id": {"description": "订单ID", "type": "string"}, "product_id": {"description": "商品ID", "type": "integer"}, "quantity": {"description": "预留数量", "type": "number"}}}, "dto.CreateVehicleRequest": {"type": "object", "required": ["customer_id", "number"], "properties": {"customer_id": {"description": "客户ID，必填", "type": "integer", "minimum": 1}, "identification_number": {"description": "车辆识别码，可选", "type": "string", "maxLength": 100, "minLength": 1}, "license": {"description": "行驶证照片路径/ID，可选", "type": "string", "maxLength": 100, "minLength": 1}, "model": {"description": "车型，可选", "type": "string", "maxLength": 100, "minLength": 1}, "number": {"description": "车牌号，必填", "type": "string", "maxLength": 100, "minLength": 1}}}, "dto.CustomTime": {"type": "object", "properties": {"time.Time": {"type": "string"}}}, "dto.CustomerLabelResponse": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "customer_id": {"description": "客户ID", "type": "integer"}, "deleted_at": {"description": "删除时间（软删除）", "type": "string"}, "id": {"description": "关联ID", "type": "integer"}, "label_id": {"description": "标签ID", "type": "integer"}, "updated_at": {"description": "更新时间", "type": "string"}}}, "dto.CustomerResponse": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "deleted_at": {"description": "删除时间（软删除）", "type": "string"}, "email": {"description": "客户邮箱", "type": "string"}, "hos_id": {"description": "HOS系统ID", "type": "integer"}, "id": {"description": "客户ID", "type": "integer"}, "name": {"description": "客户名称", "type": "string"}, "phone": {"description": "客户手机号", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}}, "dto.ErrorResponse": {"type": "object", "properties": {"code": {"description": "错误代码", "type": "string"}, "details": {"description": "错误详情（可选）", "type": "string"}, "message": {"description": "错误消息", "type": "string"}}}, "dto.GetCustomerLabelsResponse": {"type": "object", "properties": {"data": {"description": "客户标签关联列表", "type": "array", "items": {"$ref": "#/definitions/dto.CustomerLabelResponse"}}}}, "dto.GetInventoryResponse": {"type": "object", "properties": {"data": {"description": "库存详情数据", "allOf": [{"$ref": "#/definitions/dto.InventoryResponse"}]}, "message": {"description": "结果消息", "type": "string"}, "success": {"description": "操作是否成功", "type": "boolean"}}}, "dto.GetStocktakeResponse": {"type": "object", "properties": {"data": {"description": "盘点详情数据", "allOf": [{"$ref": "#/definitions/dto.StocktakeResponse"}]}, "message": {"description": "结果消息", "type": "string"}, "success": {"description": "操作是否成功", "type": "boolean"}}}, "dto.InventoryResponse": {"type": "object", "properties": {"available_quantity": {"description": "可用库存数量", "type": "number"}, "category_id": {"description": "商品分类ID", "type": "integer"}, "created_at": {"description": "创建时间", "type": "string"}, "id": {"description": "库存记录ID", "type": "integer"}, "is_low_stock": {"description": "是否低库存", "type": "boolean"}, "max_stock": {"description": "最大库存", "type": "number"}, "min_stock": {"description": "最小库存", "type": "number"}, "physical_quantity": {"description": "物理库存数量", "type": "number"}, "product_code": {"description": "商品编码", "type": "string"}, "product_id": {"description": "商品ID", "type": "integer"}, "product_name": {"description": "商品名称", "type": "string"}, "product_specs": {"description": "商品规格", "type": "string"}, "product_status": {"description": "商品状态", "type": "string"}, "product_type": {"description": "商品类型", "type": "string"}, "product_unit": {"description": "计量单位", "type": "string"}, "properties": {"description": "商品其他属性", "type": "object", "additionalProperties": true}, "reserved_quantity": {"description": "预留库存数量", "type": "number"}, "safety_stock": {"description": "安全库存", "type": "number"}, "stock_status": {"description": "库存状态：NORMAL（正常）、LOW（低库存）、OUT_OF_STOCK（缺货）", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}}, "dto.LabelResponse": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "deleted_at": {"description": "删除时间（软删除）", "type": "string"}, "description": {"description": "标签描述", "type": "string"}, "id": {"description": "标签ID", "type": "integer"}, "name": {"description": "标签名称", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}}, "dto.ListCategoriesResponse": {"type": "object", "properties": {"categories": {"description": "分类列表", "type": "array", "items": {"$ref": "#/definitions/dto.CategoryResponse"}}, "total": {"description": "总数量（如果需要支持分页）", "type": "integer"}}}, "dto.ListCustomersResponse": {"type": "object", "properties": {"data": {"description": "客户列表数据", "type": "array", "items": {"$ref": "#/definitions/dto.CustomerResponse"}}, "has_more": {"description": "是否还有更多数据", "type": "boolean"}, "limit": {"description": "当前限制数量", "type": "integer"}, "offset": {"description": "当前偏移量", "type": "integer"}, "total": {"description": "总记录数", "type": "integer"}}}, "dto.ListInventoryResponse": {"type": "object", "properties": {"has_more": {"description": "是否还有更多数据", "type": "boolean"}, "items": {"description": "库存列表", "type": "array", "items": {"$ref": "#/definitions/dto.InventoryResponse"}}, "limit": {"description": "限制数量", "type": "integer"}, "offset": {"description": "偏移量", "type": "integer"}, "total": {"description": "总数量", "type": "integer"}}}, "dto.ListLabelsResponse": {"type": "object", "properties": {"data": {"description": "标签列表数据", "type": "array", "items": {"$ref": "#/definitions/dto.LabelResponse"}}, "has_more": {"description": "是否还有更多数据", "type": "boolean"}, "limit": {"description": "当前限制数量", "type": "integer"}, "offset": {"description": "当前偏移量", "type": "integer"}, "total": {"description": "总记录数", "type": "integer"}}}, "dto.ListProductsResponse": {"type": "object", "properties": {"has_more": {"description": "是否还有更多数据", "type": "boolean"}, "items": {"description": "商品列表", "type": "array", "items": {"$ref": "#/definitions/dto.ProductResponse"}}, "limit": {"description": "限制数量", "type": "integer"}, "offset": {"description": "偏移量", "type": "integer"}, "total": {"description": "总数量", "type": "integer"}}}, "dto.ListStocktakeResponse": {"type": "object", "properties": {"has_more": {"description": "是否还有更多数据", "type": "boolean"}, "items": {"description": "盘点列表", "type": "array", "items": {"$ref": "#/definitions/dto.StocktakeResponse"}}, "limit": {"description": "限制数量", "type": "integer"}, "offset": {"description": "偏移量", "type": "integer"}, "total": {"description": "总数量", "type": "integer"}}}, "dto.ListVehiclesResponse": {"type": "object", "properties": {"data": {"description": "车辆列表数据", "type": "array", "items": {"$ref": "#/definitions/dto.VehicleResponse"}}, "has_more": {"description": "是否还有更多数据", "type": "boolean"}, "page": {"description": "当前页码", "type": "integer"}, "page_size": {"description": "当前每页数量", "type": "integer"}, "total": {"description": "总记录数", "type": "integer"}, "total_pages": {"description": "总页数", "type": "integer"}}}, "dto.ProductResponse": {"type": "object", "properties": {"category_id": {"description": "商品分类ID", "type": "integer"}, "code": {"description": "商品编码", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "id": {"description": "商品ID", "type": "integer"}, "name": {"description": "商品名称", "type": "string"}, "properties": {"description": "其他属性", "type": "object", "additionalProperties": true}, "specs": {"description": "规格", "type": "string"}, "status": {"description": "商品状态", "type": "string"}, "type": {"description": "商品类型", "type": "string"}, "unit": {"description": "计量单位", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}}, "dto.RecordItemRequest": {"type": "object", "properties": {"counted_quantity": {"description": "实际盘点数量", "type": "integer"}, "stocktake_item_id": {"description": "盘点项ID", "type": "integer"}}}, "dto.RecordItemResponse": {"type": "object", "properties": {"message": {"description": "结果消息", "type": "string"}, "stocktake_item_id": {"description": "盘点项ID", "type": "integer"}, "success": {"description": "操作是否成功", "type": "boolean"}}}, "dto.ReservationResponse": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "expires_at": {"description": "预留过期时间", "type": "string"}, "id": {"description": "预留记录ID", "type": "integer"}, "message": {"description": "结果消息", "type": "string"}, "order_id": {"description": "订单ID", "type": "string"}, "product_id": {"description": "商品ID", "type": "integer"}, "quantity": {"description": "预留数量", "type": "number"}, "status": {"description": "预留状态", "type": "string"}, "success": {"description": "操作是否成功", "type": "boolean"}, "updated_at": {"description": "更新时间", "type": "string"}}}, "dto.StartStocktakeRequest": {"type": "object", "properties": {"notes": {"description": "盘点备注", "type": "string"}, "product_ids": {"description": "需要盘点的商品ID列表", "type": "array", "items": {"type": "integer"}}}}, "dto.StartStocktakeResponse": {"type": "object", "properties": {"message": {"description": "结果消息", "type": "string"}, "reference": {"description": "盘点单号", "type": "string"}, "stocktake_id": {"description": "盘点任务ID", "type": "integer"}, "success": {"description": "操作是否成功", "type": "boolean"}}}, "dto.StocktakeItemResponse": {"type": "object", "properties": {"actual_quantity": {"description": "实际盘点数量", "type": "integer"}, "counted_at": {"description": "盘点时间", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "difference": {"description": "差异数量", "type": "integer"}, "id": {"description": "盘点项ID", "type": "integer"}, "inventory_id": {"description": "库存记录ID", "type": "integer"}, "notes": {"description": "备注", "type": "string"}, "product_code": {"description": "商品编码", "type": "string"}, "product_id": {"description": "商品ID", "type": "integer"}, "product_name": {"description": "商品名称", "type": "string"}, "product_unit": {"description": "计量单位", "type": "string"}, "status": {"description": "盘点项状态", "type": "string"}, "stocktake_id": {"description": "盘点任务ID", "type": "integer"}, "system_quantity": {"description": "系统库存数量", "type": "integer"}, "updated_at": {"description": "更新时间", "type": "string"}}}, "dto.StocktakeResponse": {"type": "object", "properties": {"completed_at": {"description": "完成时间", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "created_by": {"description": "创建人ID", "type": "integer"}, "id": {"description": "盘点任务ID", "type": "integer"}, "item_count": {"description": "盘点项数量", "type": "integer"}, "items": {"description": "盘点项列表（详情时包含）", "type": "array", "items": {"$ref": "#/definitions/dto.StocktakeItemResponse"}}, "notes": {"description": "盘点备注", "type": "string"}, "reference": {"description": "盘点单号", "type": "string"}, "status": {"description": "盘点状态", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}}, "dto.UpdateCustomerRequest": {"type": "object", "required": ["hos_id", "id"], "properties": {"email": {"description": "客户邮箱，可选，需要邮箱格式", "type": "string", "maxLength": 100}, "hos_id": {"description": "HOS系统ID，必填", "type": "integer", "minimum": 1}, "id": {"description": "客户ID，必填", "type": "integer", "minimum": 1}, "name": {"description": "客户名称，可选", "type": "string", "maxLength": 100, "minLength": 1}, "phone": {"description": "客户手机号，可选", "type": "string", "maxLength": 20, "minLength": 11}}}, "dto.UpdateVehicleRequest": {"type": "object", "required": ["customer_id", "id", "number"], "properties": {"customer_id": {"description": "客户ID，必填", "type": "integer", "minimum": 1}, "id": {"description": "车辆ID，必填", "type": "integer", "minimum": 1}, "identification_number": {"description": "车辆识别码，可选", "type": "string", "maxLength": 100, "minLength": 1}, "license": {"description": "行驶证照片路径/ID，可选", "type": "string", "maxLength": 100, "minLength": 1}, "model": {"description": "车型，可选", "type": "string", "maxLength": 100, "minLength": 1}, "number": {"description": "车牌号，必填", "type": "string", "maxLength": 100, "minLength": 1}}}, "dto.VehicleResponse": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "customer_id": {"description": "客户ID", "type": "integer"}, "id": {"description": "车辆ID", "type": "integer"}, "identification_number": {"description": "车辆识别码", "type": "string"}, "license": {"description": "行驶证照片路径/ID", "type": "string"}, "model": {"description": "车型", "type": "string"}, "number": {"description": "车牌号", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}}}, "dto.WarningResponse": {"type": "object", "properties": {"id": {"description": "预警ID", "type": "integer"}, "message": {"description": "预警消息", "type": "string"}, "product_code": {"description": "商品编码", "type": "string"}, "product_id": {"description": "商品ID", "type": "integer"}, "product_name": {"description": "商品名称", "type": "string"}, "status": {"description": "预警状态", "allOf": [{"$ref": "#/definitions/warning.Status"}]}, "triggered_at": {"description": "触发时间", "type": "string"}, "type": {"description": "预警类型", "allOf": [{"$ref": "#/definitions/warning.Type"}]}}}, "handlers.AddOrderItemRequest": {"type": "object", "required": ["product_id", "product_name", "product_type", "quantity", "unit_price"], "properties": {"fuel_grade": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": true}, "product_id": {"type": "integer"}, "product_name": {"type": "string"}, "product_type": {"type": "string"}, "pump_id": {"type": "string"}, "quantity": {"type": "number"}, "unit_price": {"type": "number"}}}, "handlers.ApplyPromotionRequest": {"type": "object", "required": ["discount_amount", "promotion_id", "promotion_name", "promotion_type"], "properties": {"discount_amount": {"type": "number", "minimum": 0}, "free_item_id": {"type": "integer"}, "free_item_name": {"type": "string"}, "free_item_quantity": {"type": "number"}, "metadata": {"type": "object", "additionalProperties": true}, "minimum_order_amount": {"type": "number"}, "promotion_id": {"type": "integer"}, "promotion_name": {"type": "string"}, "promotion_type": {"type": "string"}}}, "handlers.ApproveRequest": {"type": "object", "properties": {"approver": {"type": "string"}}}, "handlers.AuthorizePumpRequest": {"type": "object", "properties": {"nozzle_id": {"type": "integer", "example": 1}, "tag": {"type": "string", "example": "TAG123456"}}}, "handlers.AuthorizePumpResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "油泵授权成功"}, "nozzle_id": {"type": "integer", "example": 1}, "success": {"type": "boolean", "example": true}, "transaction_id": {"type": "integer", "example": 12345}}}, "handlers.CreateEmployeeRequest": {"type": "object", "properties": {"employee_no": {"type": "string"}, "name": {"type": "string"}, "password": {"type": "string"}}}, "handlers.CreateFuelTransactionRequest": {"type": "object", "required": ["amount", "fuel_grade", "fuel_type", "nozzle_id", "pump_id", "station_id", "tank", "total_amount", "total_volume", "transaction_number", "unit_price", "volume"], "properties": {"amount": {"type": "number"}, "employee_id": {"type": "integer"}, "fcc_transaction_id": {"type": "string"}, "fuel_grade": {"type": "string"}, "fuel_type": {"type": "string"}, "member_card_id": {"type": "string"}, "member_id": {"type": "integer"}, "metadata": {"type": "object", "additionalProperties": true}, "nozzle_id": {"type": "string"}, "pos_terminal_id": {"type": "string"}, "pump_id": {"type": "string"}, "station_id": {"type": "integer"}, "tank": {"type": "integer"}, "total_amount": {"type": "number"}, "total_volume": {"type": "number"}, "transaction_number": {"type": "string"}, "unit_price": {"type": "number"}, "volume": {"type": "number"}}}, "handlers.CreateMemberRequest": {"type": "object", "required": ["name", "password", "phone"], "properties": {"name": {"type": "string"}, "password": {"type": "string"}, "phone": {"type": "string"}}}, "handlers.CreateOrderRequest": {"type": "object", "required": ["fuel_transaction_id", "payment_method", "station_id"], "properties": {"allocated_amount": {"type": "number"}, "customer_id": {"type": "integer"}, "customer_name": {"type": "string"}, "employee_no": {"type": "string"}, "fuel_transaction_id": {"type": "integer"}, "metadata": {"type": "object", "additionalProperties": true}, "payment_method": {"type": "string"}, "station_id": {"type": "integer"}}}, "handlers.DeviceInfoResponse": {"type": "object", "properties": {"availableHeapFreeSize": {"description": "可用堆空间大小(字节)", "type": "integer"}, "bootTime": {"description": "最后启动时间", "type": "string"}, "bootloaderVersion": {"description": "固件详细信息", "type": "string"}, "buildDate": {"description": "构建日期", "type": "string"}, "firmware": {"description": "基本设备信息", "type": "string"}, "firmwareDateTime": {"description": "固件日期时间", "type": "string"}, "isDebug": {"description": "是否为调试版本", "type": "boolean"}, "manufacturer": {"description": "制造商", "type": "string"}, "minimalEverHeapFreeSize": {"description": "堆空间最小剩余大小(字节)", "type": "integer"}, "model": {"description": "设备型号", "type": "string"}, "numberOfFreeBlocks": {"description": "堆空间中空闲块的数量", "type": "integer"}, "numberOfSuccessfulAllocations": {"description": "堆空间成功分配的次数", "type": "integer"}, "numberOfSuccessfulFrees": {"description": "堆空间成功释放的次数", "type": "integer"}, "priceBoardProtocols": {"description": "支持的价格显示屏协议", "type": "array", "items": {"type": "integer"}}, "probeProtocols": {"description": "支持的探针协议", "type": "array", "items": {"type": "integer"}}, "pumpProtocols": {"description": "支持的加油机协议", "type": "array", "items": {"type": "integer"}}, "readerProtocols": {"description": "支持的读卡器协议", "type": "array", "items": {"type": "integer"}}, "sdFiles": {"description": "SD卡文件列表", "type": "array", "items": {"$ref": "#/definitions/models.SdFileInfo"}}, "sdFreeMemoryKB": {"description": "SD卡信息", "type": "integer"}, "sdTotalMemoryKB": {"description": "SD卡总空间(KB)", "type": "integer"}, "serial": {"description": "设备序列号", "type": "string"}, "sizeOfLargestFreeBlockInBytes": {"description": "最大空闲块的大小(字节)", "type": "integer"}, "sizeOfSmallestFreeBlockInBytes": {"description": "最小空闲块的大小(字节)", "type": "integer"}, "systemUpTime": {"description": "系统运行信息", "type": "integer"}, "tasks": {"description": "运行中的任务", "type": "array", "items": {"$ref": "#/definitions/models.TaskInfo"}}}}, "handlers.DiscountItemResponse": {"type": "object", "properties": {"discountedPrice": {"type": "number", "example": 18.45}, "itemId": {"type": "string", "example": "product-123"}, "name": {"type": "string", "example": "商品名称"}, "originalPrice": {"type": "number", "example": 20.5}, "quantity": {"type": "integer", "example": 2}}}, "handlers.DiscountResponse": {"type": "object", "properties": {"discountAmount": {"type": "number", "example": 10.05}, "discountedAmount": {"type": "number", "example": 90.45}, "items": {"type": "array", "items": {"$ref": "#/definitions/handlers.DiscountItemResponse"}}, "message": {"type": "string", "example": "折扣计算成功"}, "originalAmount": {"type": "number", "example": 100.5}, "success": {"type": "boolean", "example": true}}}, "handlers.ErrorResponse": {"type": "object", "properties": {"code": {"description": "错误代码", "type": "string"}, "detail": {"description": "详细信息", "type": "string"}, "message": {"description": "错误消息", "type": "string"}}}, "handlers.GetAggregatedReceivableResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/repository.AggregatedReceivableDTO"}}}}, "handlers.GetAggregatedRevenueResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/repository.AggregatedRevenueDTO"}}}}, "handlers.GetAggregatedSalesByProductCategoryResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/repository.AggregatedSalesDTO"}}}}, "handlers.GetFuelReceivableSummaryResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/repository.FuelReceivableSummaryDTO"}}}, "handlers.GetNozzleSalesSummaryResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/repository.NozzleSalesSummaryDTO"}}}}, "handlers.GetPaymentMethodSummaryResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/repository.PaymentMethodSummary"}}}}, "handlers.GetShiftEODReportResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/repository.ShiftEODReportResponse"}, "message": {"type": "string"}}}, "handlers.GetTotalRevenueResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/repository.TotalRevenueDTO"}}}, "handlers.GetTransactionsForReportResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/repository.TransactionDTO"}}}}, "handlers.LinkFuelTransactionToOrderRequest": {"type": "object", "required": ["allocated_amount", "order_id"], "properties": {"allocated_amount": {"type": "number"}, "order_id": {"type": "integer"}}}, "handlers.ListEmployeesResponse": {"type": "object", "properties": {"employees": {"type": "array", "items": {"$ref": "#/definitions/repository.Employee"}}, "page": {"type": "integer"}, "size": {"type": "integer"}, "total": {"type": "integer"}}}, "handlers.ListFuelTransactionsResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/repository.FuelTransaction"}}, "page": {"type": "integer"}, "page_size": {"type": "integer"}, "total": {"type": "integer"}, "total_page": {"type": "integer"}}}, "handlers.ListOrdersResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/repository.Order"}}, "page": {"type": "integer"}, "page_size": {"type": "integer"}, "total": {"type": "integer"}, "total_page": {"type": "integer"}}}, "handlers.ListShiftsResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/repository.Shift"}}, "page": {"type": "integer"}, "page_size": {"type": "integer"}, "total": {"type": "integer"}, "total_page": {"type": "integer"}}}, "handlers.LoginRequest": {"type": "object", "properties": {"employee_no": {"type": "string"}, "password": {"type": "string"}}}, "handlers.LoginResponse": {"type": "object", "properties": {"employee": {"$ref": "#/definitions/repository.Employee"}, "token": {"type": "string"}}}, "handlers.NozzleDetailedSalesResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/repository.NozzleDetailedSalesDTO"}}, "metadata": {"$ref": "#/definitions/repository.ReportMetadata"}}}, "handlers.Order": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/handlers.OrderItem"}}, "orderAmount": {"type": "number", "example": 100.5}, "orderTime": {"type": "string", "example": "2023-01-01T12:00:00Z"}, "userId": {"type": "string", "example": "user123"}}}, "handlers.OrderItem": {"type": "object", "properties": {"category": {"type": "string", "example": "电子产品"}, "itemId": {"type": "string", "example": "product-123"}, "name": {"type": "string", "example": "商品名称"}, "price": {"type": "number", "example": 20.5}, "quantity": {"type": "integer", "example": 2}}}, "handlers.PresetPumpRequest": {"type": "object", "properties": {"nozzle_id": {"type": "integer", "example": 1}, "preset_type": {"type": "string", "example": "amount"}, "tag": {"type": "string", "example": "TAG123456"}, "value": {"type": "number", "example": 100}}}, "handlers.PresetPumpResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "预设金额成功"}, "preset_type": {"type": "string", "example": "amount"}, "price": {"type": "number", "example": 7.25}, "success": {"type": "boolean", "example": true}, "transaction_id": {"type": "integer", "example": 12345}, "value": {"type": "number", "example": 100}}}, "handlers.PriceRequest": {"type": "object", "properties": {"cost_price": {"type": "number"}, "effective_at": {"type": "string"}, "operator": {"type": "string"}, "price": {"type": "number"}, "suggest_price": {"type": "number"}, "wholesale_price": {"type": "number"}}}, "handlers.PumpStatusResponse": {"type": "object", "properties": {"PumpDisplayData": {"$ref": "#/definitions/models.PumpDisplayData"}, "PumpEndOfTransactionStatus": {"$ref": "#/definitions/models.PumpEndOfTransactionStatus"}, "PumpFillingStatus": {"$ref": "#/definitions/models.PumpFillingStatus"}, "PumpIdleStatus": {"$ref": "#/definitions/models.PumpIdleStatus"}, "PumpOfflineStatus": {"$ref": "#/definitions/models.PumpOfflineStatus"}, "PumpPrices": {"type": "object", "additionalProperties": {"type": "number"}}, "PumpTag": {"$ref": "#/definitions/models.PumpTag"}, "PumpTotals": {"$ref": "#/definitions/models.PumpTotals"}}}, "handlers.QueryPerformanceResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": true}}}, "handlers.SalesDetailByPaymentMethodResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/repository.SalesDetailByPaymentMethodDTO"}}, "metadata": {"$ref": "#/definitions/repository.ReportMetadata"}}}, "handlers.SalesDetailByProductResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/repository.SalesDetailByProductDTO"}}, "metadata": {"$ref": "#/definitions/repository.ReportMetadata"}}}, "handlers.SetNozzlePriceRequest": {"type": "object", "properties": {"price": {"type": "number", "example": 7.25}}}, "handlers.SetNozzlePriceResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "Nozzle 1 价格调整成功"}, "nozzle_id": {"type": "integer", "example": 1}, "price": {"type": "number", "example": 7.25}, "pump_id": {"type": "integer", "example": 1}, "success": {"type": "boolean", "example": true}}}, "handlers.SetPumpPricesRequest": {"type": "object", "properties": {"prices": {"type": "object", "additionalProperties": {"type": "number"}}}}, "handlers.SetPumpPricesResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "油泵价格调整成功"}, "prices": {"type": "object", "additionalProperties": {"type": "number"}}, "success": {"type": "boolean", "example": true}}}, "handlers.StartShiftRequest": {"type": "object", "required": ["station_id"], "properties": {"metadata": {"type": "object", "additionalProperties": true}, "station_id": {"type": "integer"}}}, "handlers.StatusResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "操作成功"}, "success": {"type": "boolean", "example": true}}}, "handlers.UpdateEmployeeRequest": {"type": "object", "properties": {"name": {"type": "string"}, "password": {"type": "string"}}}, "handlers.UpdateLinkAllocatedAmountRequest": {"type": "object", "required": ["allocated_amount"], "properties": {"allocated_amount": {"type": "number"}}}, "handlers.UpdateMemberRequest": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}}}, "handlers.UpdateMemberStatusRequest": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}}, "handlers.VerifyMemberRequest": {"type": "object", "required": ["password", "phone"], "properties": {"password": {"type": "string"}, "phone": {"type": "string"}}}, "model.AdjustmentItemType": {"type": "string", "enum": ["percent", "fixed", "absolute", "fixed_price", "percentage", "amount"], "x-enum-varnames": ["AdjustmentTypePercent", "AdjustmentTypeFixed", "AdjustmentTypeAbsolute", "AdjustmentTypeFixedPrice", "AdjustmentTypePercentage", "AdjustmentTypeAmount"]}, "model.Member": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "string"}, "identity_type": {"description": "身份类型", "type": "string"}, "level": {"description": "会员等级", "type": "string"}, "name": {"type": "string"}, "phone": {"type": "string"}, "points": {"description": "积分", "type": "integer"}, "status": {"type": "string"}, "updated_at": {"type": "string"}}}, "model.OilProduct": {"type": "object", "properties": {"category": {"description": "产品类别，如汽油、柴油等", "type": "string"}, "code": {"description": "产品代码，唯一", "type": "string"}, "created_at": {"type": "string"}, "created_by": {"type": "string"}, "current_price": {"description": "当前价格", "type": "number"}, "default_price": {"description": "默认价格", "type": "number"}, "description": {"description": "产品描述", "type": "string"}, "grade": {"description": "产品等级，如92号、95号等", "type": "string"}, "id": {"type": "integer"}, "image_url": {"description": "产品图片URL", "type": "string"}, "is_active": {"description": "是否激活", "type": "boolean"}, "name": {"description": "产品名称", "type": "string"}, "specifications": {"description": "规格说明（JSON格式）", "type": "string"}, "unit": {"description": "计量单位，默认为升", "type": "string"}, "updated_at": {"type": "string"}}}, "model.PriceAdjustmentItem": {"type": "object", "properties": {"adjustment_type": {"$ref": "#/definitions/model.AdjustmentItemType"}, "adjustment_value": {"description": "调整值：百分比或固定值", "type": "number"}, "applied_at": {"description": "应用调整的时间", "type": "string"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "new_price": {"description": "调整后的价格", "type": "number"}, "old_price": {"type": "number"}, "price_change_amount": {"description": "价格变化金额", "type": "number"}, "price_change_percentage": {"description": "价格变化百分比", "type": "number"}, "process_id": {"type": "integer"}, "product_id": {"type": "integer"}, "remarks": {"description": "备注信息", "type": "string"}, "updated_at": {"type": "string"}}}, "model.PriceAdjustmentProcess": {"type": "object", "properties": {"adjustment_type": {"description": "全局调整、特定油品", "type": "string"}, "applied_at": {"type": "string"}, "applied_by": {"type": "string"}, "approved_at": {"type": "string"}, "approved_by": {"type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "effective_date": {"type": "string"}, "id": {"type": "integer"}, "initiated_by": {"type": "string"}, "name": {"type": "string"}, "pricing_policy_id": {"type": "integer"}, "product_ids": {"description": "用于记录要调整的产品ID列表，JSON格式", "type": "array", "items": {"type": "integer"}}, "reason": {"description": "调整原因说明", "type": "string"}, "reason_code": {"description": "调整原因代码", "type": "string"}, "rejected_at": {"type": "string"}, "rejected_by": {"type": "string"}, "rejection_reason": {"description": "拒绝原因", "type": "string"}, "status": {"description": "草稿、待审批、已批准、已拒绝、已完成", "type": "string"}, "submitted_at": {"type": "string"}, "submitted_by": {"type": "string"}, "updated_at": {"type": "string"}}}, "model.PricingStrategy": {"type": "object", "properties": {"active": {"type": "boolean"}, "applicable_to_all": {"description": "是否适用于所有油品", "type": "boolean"}, "created_at": {"type": "string"}, "created_by": {"type": "string"}, "description": {"type": "string"}, "end_date": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "parameters": {"description": "策略参数，JSON格式", "type": "array", "items": {"type": "integer"}}, "priority": {"description": "优先级，数字越小优先级越高", "type": "integer"}, "start_date": {"type": "string"}, "strategy_type": {"$ref": "#/definitions/model.PricingStrategyType"}, "updated_at": {"type": "string"}}}, "model.PricingStrategyType": {"type": "string", "enum": ["fixed", "percentage_discount", "amount_discount", "tiered_price", "time_price", "volume_discount", "customer_group"], "x-enum-varnames": ["FixedPriceStrategy", "PercentageDiscountStrategy", "AmountDiscountStrategy", "TieredPriceStrategy", "TimePriceStrategy", "VolumeDiscountStrategy", "CustomerGroupStrategy"]}, "models.ErrorResponse": {"type": "object", "properties": {"code": {"description": "错误代码", "type": "string"}, "detail": {"description": "详细信息", "type": "string"}, "message": {"description": "错误消息", "type": "string"}}}, "models.PumpDisplayData": {"type": "object", "properties": {"Amount": {"description": "当前金额", "type": "number"}, "DisplayText": {"description": "显示文本", "type": "string"}, "LastUpdateTime": {"description": "最后更新时间", "type": "string"}, "Nozzle": {"description": "NozzleID", "type": "integer"}, "Price": {"description": "单价", "type": "number"}, "Pump": {"description": "油泵ID", "type": "integer"}, "Volume": {"description": "当前加油量", "type": "number"}}}, "models.PumpEndOfTransactionStatus": {"type": "object", "properties": {"Amount": {"description": "金额", "type": "number"}, "LastUpdateTime": {"description": "最后更新时间", "type": "string"}, "Nozzle": {"description": "NozzleID", "type": "integer"}, "Price": {"description": "单价", "type": "number"}, "Pump": {"description": "油泵ID", "type": "integer"}, "Transaction": {"description": "交易编号", "type": "integer"}, "Volume": {"description": "加油量", "type": "number"}}}, "models.PumpFillingStatus": {"type": "object", "properties": {"Amount": {"description": "当前金额", "type": "number"}, "FlowRate": {"description": "当前流量（升/分钟）", "type": "number"}, "LastUpdateTime": {"description": "最后更新时间", "type": "string"}, "Nozzle": {"description": "NozzleID", "type": "integer"}, "Price": {"description": "单价", "type": "number"}, "Pump": {"description": "油泵ID", "type": "integer"}, "Volume": {"description": "当前加油量", "type": "number"}}}, "models.PumpIdleStatus": {"type": "object", "properties": {"LastAmount": {"description": "最后金额", "type": "number"}, "LastDateTime": {"description": "最后更新时间", "type": "string"}, "LastDateTimeStart": {"description": "最后开始时间", "type": "string"}, "LastFuelGradeId": {"description": "最后油品ID", "type": "integer"}, "LastFuelGradeName": {"description": "最后油品名称", "type": "string"}, "LastNozzle": {"description": "最后NozzleID", "type": "integer"}, "LastPrice": {"description": "最后单价", "type": "number"}, "LastTotalAmount": {"description": "最后总金额", "type": "number"}, "LastTotalVolume": {"description": "最后总加油量", "type": "number"}, "LastTransaction": {"description": "最后交易编号", "type": "integer"}, "LastUser": {"description": "最后用户", "type": "string"}, "LastVolume": {"description": "最后加油量", "type": "number"}, "NozzlePrices": {"description": "Nozzle价格", "type": "array", "items": {"type": "number"}}, "Pump": {"description": "油泵ID", "type": "integer"}, "User": {"description": "当前用户", "type": "string"}}}, "models.PumpOfflineStatus": {"type": "object", "properties": {"ErrorCode": {"description": "错误代码", "type": "integer"}, "ErrorMessage": {"description": "错误信息", "type": "string"}, "LastAmount": {"description": "最后金额", "type": "number"}, "LastNozzle": {"description": "最后NozzleID", "type": "integer"}, "LastPrice": {"description": "最后单价", "type": "number"}, "LastTransaction": {"description": "最后交易编号", "type": "integer"}, "LastUpdateTime": {"description": "最后更新时间", "type": "string"}, "LastVolume": {"description": "最后加油量", "type": "number"}, "Pump": {"description": "油泵ID", "type": "integer"}, "State": {"description": "状态", "type": "string"}, "User": {"description": "当前用户", "type": "string"}}}, "models.PumpTag": {"type": "object", "properties": {"LastUpdateTime": {"description": "最后更新时间", "type": "string"}, "Pump": {"description": "油泵ID", "type": "integer"}, "Tag": {"description": "标签ID", "type": "string"}}}, "models.PumpTotals": {"type": "object", "properties": {"amount": {"description": "总金额", "type": "number"}, "nozzleId": {"description": "NozzleID", "type": "integer"}, "pumpId": {"description": "油泵ID", "type": "integer"}, "volume": {"description": "总加油量（升）", "type": "number"}}}, "models.SdFileInfo": {"type": "object", "properties": {"name": {"description": "文件名", "type": "string"}, "size": {"description": "文件大小(字节)", "type": "string"}}}, "models.TaskInfo": {"type": "object", "properties": {"name": {"description": "任务名称", "type": "string"}, "priority": {"description": "任务优先级", "type": "integer"}, "runTimeCounter": {"description": "任务运行时间计数器", "type": "integer"}, "stackHighWaterMark": {"description": "任务堆栈高水位标记", "type": "integer"}, "state": {"description": "任务状态", "type": "string"}}}, "oil.AdjustmentItemInput": {"type": "object", "required": ["adjustment_type", "adjustment_value", "product_id"], "properties": {"adjustment_type": {"$ref": "#/definitions/model.AdjustmentItemType"}, "adjustment_value": {"type": "number"}, "product_id": {"type": "integer"}, "remarks": {"type": "string"}}}, "oil.AdjustmentProcessDetail": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.PriceAdjustmentItem"}}, "process": {"$ref": "#/definitions/model.PriceAdjustmentProcess"}}}, "oil.CreateProductRequest": {"type": "object", "required": ["category", "code", "created_by", "grade", "name"], "properties": {"category": {"type": "string"}, "code": {"type": "string"}, "created_by": {"type": "string"}, "current_price": {"type": "number"}, "default_price": {"type": "number"}, "description": {"type": "string"}, "grade": {"type": "string"}, "image_url": {"type": "string"}, "is_active": {"type": "boolean"}, "name": {"type": "string"}, "specifications": {"type": "string"}, "unit": {"type": "string"}}}, "oil.CreateStrategyRequest": {"type": "object", "required": ["created_by", "name", "parameters", "type"], "properties": {"active": {"type": "boolean"}, "created_by": {"type": "string"}, "customer_group_ids": {"type": "array", "items": {"type": "integer"}}, "customer_ids": {"type": "array", "items": {"type": "integer"}}, "description": {"type": "string"}, "end_date": {"type": "string"}, "name": {"type": "string"}, "parameters": {"type": "object", "additionalProperties": true}, "priority": {"type": "integer"}, "product_ids": {"type": "array", "items": {"type": "integer"}}, "start_date": {"type": "string"}, "type": {"$ref": "#/definitions/model.PricingStrategyType"}}}, "oil.InitiatePriceAdjustmentRequest": {"type": "object", "required": ["adjustment_type", "effective_date", "initiated_by", "items", "name"], "properties": {"adjustment_type": {"type": "string"}, "description": {"type": "string"}, "effective_date": {"type": "string"}, "initiated_by": {"type": "string"}, "items": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/oil.AdjustmentItemInput"}}, "name": {"type": "string"}, "reason": {"type": "string"}, "reason_code": {"type": "string"}}}, "oil.SalesPriceInfo": {"type": "object", "properties": {"applied_strategy": {"$ref": "#/definitions/model.PricingStrategy"}, "discount_amount": {"type": "number"}, "final_price": {"type": "number"}, "original_price": {"type": "number"}, "product_id": {"type": "integer"}}}, "oil.UpdateProductRequest": {"type": "object", "required": ["id"], "properties": {"category": {"type": "string"}, "code": {"type": "string"}, "current_price": {"type": "number"}, "default_price": {"type": "number"}, "description": {"type": "string"}, "grade": {"type": "string"}, "id": {"type": "integer"}, "image_url": {"type": "string"}, "is_active": {"type": "boolean"}, "name": {"type": "string"}, "specifications": {"type": "string"}, "unit": {"type": "string"}}}, "ptsmanager.ClientConfig": {"type": "object", "properties": {"apiprefix": {"description": "API 路径前缀", "type": "string"}, "authType": {"description": "认证类型 (\"Basic\" 或 \"Digest\")", "type": "string"}, "debug": {"description": "是否启用调试模式", "type": "boolean"}, "endpoint": {"description": "PTS-2 设备的 API 端点", "type": "string"}, "password": {"description": "认证密码", "type": "string"}, "skipVerify": {"description": "是否跳过 TLS 证书验证", "type": "boolean"}, "timeout": {"description": "请求超时时间", "allOf": [{"$ref": "#/definitions/time.Duration"}]}, "username": {"description": "认证用户名", "type": "string"}}}, "ptsmanager.DeviceConfig": {"type": "object", "properties": {"active": {"type": "boolean"}, "client": {"description": "配置部分", "allOf": [{"$ref": "#/definitions/ptsmanager.ClientConfig"}]}, "created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"description": "基本信息", "type": "string"}, "last_connected": {"description": "最后连接时间", "type": "string"}, "location": {"type": "string"}, "name": {"type": "string"}, "receiver": {"$ref": "#/definitions/ptsmanager.ReceiverConfig"}, "strategy": {"$ref": "#/definitions/ptsmanager.StrategyConfig"}, "updated_at": {"type": "string"}}}, "ptsmanager.DeviceState": {"type": "object", "properties": {"client_active": {"type": "boolean"}, "connected": {"type": "boolean"}, "device_id": {"type": "string"}, "error_count": {"type": "integer"}, "last_error": {"type": "string"}, "last_ping_time": {"type": "string"}, "receiver_active": {"type": "boolean"}, "updated_at": {"type": "string"}}}, "ptsmanager.ReceiverConfig": {"type": "object", "properties": {"address": {"description": "监听地址，如 \"0.0.0.0:8080\"", "type": "string"}, "password": {"description": "认证密码（如有）", "type": "string"}, "path": {"description": "HTTP路径（如果是HTTP接收器）", "type": "string"}, "protocol": {"description": "协议类型，如 \"tcp\", \"http\"", "type": "string"}, "secret_key": {"description": "用于验证签名的密钥（如有）", "type": "string"}, "timeout": {"description": "处理超时时间", "allOf": [{"$ref": "#/definitions/time.Duration"}]}, "username": {"description": "认证用户名（如有）", "type": "string"}}}, "ptsmanager.StrategyConfig": {"type": "object", "properties": {"auto_reconnect": {"description": "是否自动重连", "type": "boolean"}, "health_check_interval": {"description": "健康检查间隔时间", "allOf": [{"$ref": "#/definitions/time.Duration"}]}, "max_retries": {"description": "最大重试次数", "type": "integer"}, "retry_interval": {"description": "重试间隔时间", "allOf": [{"$ref": "#/definitions/time.Duration"}]}}}, "repository.AggregatedReceivableDTO": {"type": "object", "properties": {"dimensionKey": {"description": "通常是日期字符串 \"YYYY-MM-DD\" 或 站点 ID", "type": "string"}, "totalReceivable": {"type": "number"}}}, "repository.AggregatedRevenueDTO": {"type": "object", "properties": {"dimensionKey": {"description": "通常是日期字符串 \"YYYY-MM-DD\" 或 站点 ID", "type": "string"}, "totalRevenue": {"type": "number"}}}, "repository.AggregatedSalesDTO": {"type": "object", "properties": {"dimensionKey": {"type": "string"}, "totalAmount": {"type": "number"}}}, "repository.Employee": {"type": "object", "properties": {"created_at": {"type": "string"}, "deleted_at": {"type": "string"}, "employee_no": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "updated_at": {"type": "string"}}}, "repository.FuelGradeDetail": {"type": "object", "properties": {"average_price": {"type": "number"}, "discount_amount": {"type": "number"}, "grade": {"type": "string"}, "gross_amount": {"type": "number"}, "name": {"type": "string"}, "net_amount": {"type": "number"}, "transaction_count": {"type": "integer"}, "type": {"type": "string"}, "volume": {"type": "number"}, "volume_percentage": {"type": "number"}}}, "repository.FuelReceivableSummaryDTO": {"type": "object", "properties": {"actualReceivedAmount": {"description": "实收金额", "type": "number"}, "actualRefundAmount": {"description": "实退金额", "type": "number"}, "couponDiscountAmount": {"description": "优惠券金额", "type": "number"}, "discountAmount": {"description": "油品优惠数据", "type": "number"}, "fuelSalesAmount": {"description": "油品销售金额", "type": "number"}, "fuelSalesVolume": {"description": "油品销售数据", "type": "number"}, "pointsRedeemedAmount": {"description": "积分抵现金额", "type": "number"}, "pointsUsed": {"description": "使用积分", "type": "integer"}, "refundableAmount": {"description": "油品退款数据", "type": "number"}, "refundableDiscount": {"description": "应退优惠", "type": "number"}, "totalDiscountAmount": {"description": "优惠合计", "type": "number"}}}, "repository.FuelSummaryDTO": {"type": "object", "properties": {"fuel_grades": {"type": "array", "items": {"$ref": "#/definitions/repository.FuelGradeDetail"}}, "total_discount": {"type": "number"}, "total_gross_sales": {"type": "number"}, "total_net_sales": {"type": "number"}, "total_transactions": {"type": "integer"}, "total_volume": {"type": "number"}}}, "repository.FuelTransaction": {"type": "object", "properties": {"amount": {"type": "number"}, "cancelled_at": {"type": "string"}, "created_at": {"type": "string"}, "employee_id": {"type": "integer"}, "fcc_transaction_id": {"type": "string"}, "fuel_grade": {"type": "string"}, "fuel_type": {"type": "string"}, "id": {"type": "integer"}, "member_card_id": {"type": "string"}, "member_id": {"type": "integer"}, "metadata": {"type": "object", "additionalProperties": true}, "nozzle_id": {"type": "string"}, "pos_terminal_id": {"type": "string"}, "processed_at": {"type": "string"}, "pump_id": {"type": "string"}, "station_id": {"type": "integer"}, "status": {"$ref": "#/definitions/repository.FuelTransactionStatus"}, "tank": {"type": "integer"}, "total_amount": {"type": "number"}, "total_volume": {"type": "number"}, "transaction_number": {"type": "string"}, "unit_price": {"type": "number"}, "updated_at": {"type": "string"}, "volume": {"type": "number"}}}, "repository.FuelTransactionOrderLink": {"type": "object", "properties": {"allocated_amount": {"type": "number"}, "created_at": {"type": "string"}, "deactivated_at": {"type": "string"}, "fuel_transaction_id": {"type": "integer"}, "id": {"type": "integer"}, "metadata": {"type": "object", "additionalProperties": true}, "order_id": {"type": "integer"}, "status": {"$ref": "#/definitions/repository.LinkStatus"}, "updated_at": {"type": "string"}}}, "repository.FuelTransactionStatus": {"type": "string", "enum": ["pending", "processed", "cancelled"], "x-enum-varnames": ["FuelTransactionStatusPending", "FuelTransactionStatusProcessed", "FuelTransactionStatusCancelled"]}, "repository.LinkStatus": {"type": "string", "enum": ["active", "inactive"], "x-enum-varnames": ["LinkStatusActive", "LinkStatusInactive"]}, "repository.MerchandiseProduct": {"type": "object", "properties": {"category": {"type": "string"}, "discount_amount": {"type": "number"}, "gross_amount": {"type": "number"}, "net_amount": {"type": "number"}, "product_id": {"type": "integer"}, "product_name": {"type": "string"}, "product_type": {"type": "string"}, "quantity": {"type": "integer"}, "transaction_count": {"type": "integer"}, "unit_price": {"type": "number"}}}, "repository.MerchandiseSummaryDTO": {"type": "object", "properties": {"top_products": {"type": "array", "items": {"$ref": "#/definitions/repository.MerchandiseProduct"}}, "total_discount": {"type": "number"}, "total_gross_sales": {"type": "number"}, "total_net_sales": {"type": "number"}, "total_quantity": {"type": "integer"}, "total_transactions": {"type": "integer"}}}, "repository.NozzleDetailedSalesDTO": {"type": "object", "properties": {"actualReceivedAmount": {"description": "实收金额", "type": "number"}, "discountAmount": {"description": "优惠金额", "type": "number"}, "fuelGrade": {"description": "燃油标号", "type": "string"}, "fuelType": {"description": "燃油类型", "type": "string"}, "nozzleId": {"description": "油枪ID", "type": "string"}, "siteId": {"description": "站点ID", "type": "integer"}, "siteName": {"description": "站点名称", "type": "string"}, "totalAmount": {"description": "总销售金额", "type": "number"}, "totalVolume": {"description": "总销售体积", "type": "number"}, "transactionCount": {"description": "交易次数", "type": "integer"}, "unitPrice": {"description": "单价", "type": "number"}}}, "repository.NozzleSalesSummaryDTO": {"type": "object", "properties": {"fuelGrade": {"description": "来自 FuelTransaction.FuelGrade", "type": "string"}, "fuelType": {"description": "来自 FuelTransaction.FuelType", "type": "string"}, "nozzleId": {"description": "来自 FuelTransaction.NozzleID", "type": "string"}, "siteId": {"description": "来自 FuelTransaction.StationID", "type": "integer"}, "totalAmount": {"description": "SUM(FuelTransaction.Amount)", "type": "number"}, "totalVolume": {"description": "SUM(FuelTransaction.Volume)", "type": "number"}}}, "repository.Order": {"type": "object", "properties": {"cancelled_at": {"type": "string"}, "completed_at": {"type": "string"}, "created_at": {"type": "string"}, "customer_id": {"type": "integer"}, "customer_name": {"type": "string"}, "discount_amount": {"type": "number"}, "employee_no": {"type": "string"}, "final_amount": {"type": "number"}, "id": {"type": "integer"}, "items": {"type": "array", "items": {"$ref": "#/definitions/repository.OrderItem"}}, "metadata": {"type": "object", "additionalProperties": true}, "order_number": {"type": "string"}, "paid_amount": {"type": "number"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/repository.OrderPayment"}}, "promotions": {"type": "array", "items": {"$ref": "#/definitions/repository.OrderPromotion"}}, "station_id": {"type": "integer"}, "status": {"$ref": "#/definitions/repository.OrderStatus"}, "tax_amount": {"type": "number"}, "total_amount": {"type": "number"}, "updated_at": {"type": "string"}}}, "repository.OrderItem": {"type": "object", "properties": {"created_at": {"type": "string"}, "discount_amount": {"type": "number"}, "final_price": {"type": "number"}, "fuel_grade": {"type": "string"}, "id": {"type": "integer"}, "metadata": {"type": "object", "additionalProperties": true}, "order_id": {"type": "integer"}, "product_id": {"type": "integer"}, "product_name": {"type": "string"}, "product_type": {"type": "string"}, "pump_id": {"type": "string"}, "quantity": {"type": "number"}, "tax_amount": {"type": "number"}, "tax_rate": {"type": "number"}, "total_price": {"type": "number"}, "unit_price": {"type": "number"}, "updated_at": {"type": "string"}}}, "repository.OrderPayment": {"type": "object", "properties": {"amount": {"type": "number"}, "completed_at": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "metadata": {"type": "object", "additionalProperties": true}, "order_id": {"type": "integer"}, "payment_method": {"type": "string"}, "payment_reference": {"type": "string"}, "status": {"$ref": "#/definitions/repository.PaymentStatus"}, "transaction_id": {"type": "string"}, "updated_at": {"type": "string"}}}, "repository.OrderPromotion": {"type": "object", "properties": {"created_at": {"type": "string"}, "discount_amount": {"type": "number"}, "free_item_id": {"type": "integer"}, "free_item_name": {"type": "string"}, "free_item_quantity": {"type": "number"}, "id": {"type": "integer"}, "metadata": {"type": "object", "additionalProperties": true}, "minimum_order_amount": {"type": "number"}, "order_id": {"type": "integer"}, "promotion_id": {"type": "integer"}, "promotion_name": {"type": "string"}, "promotion_type": {"type": "string"}}}, "repository.OrderStatus": {"type": "string", "enum": ["new", "processing", "completed", "cancelled"], "x-enum-varnames": ["OrderStatusNew", "OrderStatusProcessing", "OrderStatusCompleted", "OrderStatusCancelled"]}, "repository.PaymentMethodDetail": {"type": "object", "properties": {"amount": {"type": "number"}, "method": {"type": "string"}, "method_name": {"type": "string"}, "percentage": {"type": "number"}, "transaction_count": {"type": "integer"}}}, "repository.PaymentMethodSummary": {"type": "object", "properties": {"orderCount": {"type": "integer"}, "paymentMethod": {"type": "string"}, "totalAmount": {"type": "number"}}}, "repository.PaymentStatus": {"type": "string", "enum": ["pending", "completed", "failed", "refunded"], "x-enum-varnames": ["PaymentStatusPending", "PaymentStatusCompleted", "PaymentStatusFailed", "PaymentStatusRefunded"]}, "repository.PaymentSummaryDTO": {"type": "object", "properties": {"payment_methods": {"type": "array", "items": {"$ref": "#/definitions/repository.PaymentMethodDetail"}}, "total_sales": {"type": "number"}, "total_transactions": {"type": "integer"}}}, "repository.ReceiptInfoDTO": {"type": "object", "properties": {"currency": {"type": "string"}, "print_time": {"type": "string"}, "receipt_number": {"type": "string"}, "timezone": {"type": "string"}}}, "repository.ReportMetadata": {"type": "object", "properties": {"additionalInfo": {"description": "附加元数据信息", "type": "object", "additionalProperties": true}, "currentPage": {"description": "当前页", "type": "integer"}, "dateRangeConverted": {"description": "转换后的日期范围描述", "type": "string"}, "filteredCount": {"description": "过滤后记录数 (传统字段)", "type": "integer"}, "filteredRecords": {"description": "筛选后记录数 (增强字段)", "type": "integer"}, "generatedAt": {"description": "报表生成时间", "type": "string"}, "pageCount": {"description": "总页数", "type": "integer"}, "queryTime": {"description": "查询耗时(毫秒)", "type": "integer"}, "totalCount": {"description": "总记录数 (传统字段)", "type": "integer"}, "totalRecords": {"description": "记录总数 (增强字段)", "type": "integer"}}}, "repository.SalesDetailByPaymentMethodDTO": {"type": "object", "properties": {"details": {"description": "明细列表", "type": "array", "items": {"$ref": "#/definitions/repository.SalesDetailItemDTO"}}, "paymentMethod": {"description": "支付方式", "type": "string"}}}, "repository.SalesDetailByProductDTO": {"type": "object", "properties": {"details": {"description": "明细列表", "type": "array", "items": {"$ref": "#/definitions/repository.SalesDetailItemDTO"}}, "productName": {"description": "商品名称", "type": "string"}, "subtotal": {"description": "小计", "allOf": [{"$ref": "#/definitions/repository.SalesDetailItemDTO"}]}}}, "repository.SalesDetailItemDTO": {"type": "object", "properties": {"actualReceivedAmount": {"description": "实收金额", "type": "number"}, "actualRefundAmount": {"description": "实退金额", "type": "number"}, "couponDiscountAmount": {"description": "优惠券金额", "type": "number"}, "discountAmount": {"description": "优惠相关", "type": "number"}, "liters": {"description": "升数", "type": "number"}, "paymentMethod": {"description": "支付方式", "type": "string"}, "pointsRedeemedAmount": {"description": "积分抵现金额", "type": "number"}, "pointsUsed": {"description": "使用积分", "type": "integer"}, "productName": {"description": "商品名称", "type": "string"}, "receivableAmount": {"description": "应收金额", "type": "number"}, "refundableAmount": {"description": "退款相关", "type": "number"}, "refundableDiscount": {"description": "应退优惠", "type": "number"}, "totalDiscountAmount": {"description": "优惠合计", "type": "number"}}}, "repository.Shift": {"type": "object", "properties": {"created_at": {"type": "string"}, "deleted_at": {"type": "string"}, "end_time": {"type": "string"}, "id": {"type": "integer"}, "metadata": {"type": "object", "additionalProperties": true}, "shift_number": {"type": "string"}, "start_time": {"type": "string"}, "station_id": {"type": "integer"}, "updated_at": {"type": "string"}}}, "repository.ShiftEODDailySummary": {"type": "object", "properties": {"total_discount": {"type": "number"}, "total_fuel_volume": {"type": "number"}, "total_gross_sales": {"type": "number"}, "total_net_sales": {"type": "number"}, "total_shifts": {"type": "integer"}, "total_transactions": {"type": "integer"}}}, "repository.ShiftEODData": {"type": "object", "properties": {"fuel_attendants": {"type": "array", "items": {"$ref": "#/definitions/repository.ShiftEODEmployee"}}, "fuel_sales": {"$ref": "#/definitions/repository.ShiftEODFuelSales"}, "other_income": {"$ref": "#/definitions/repository.ShiftEODOtherIncome"}, "payment_summary": {"$ref": "#/definitions/repository.ShiftEODPaymentSummary"}, "shift_info": {"$ref": "#/definitions/repository.ShiftEODInfo"}, "shift_summary": {"$ref": "#/definitions/repository.ShiftEODSummary"}}}, "repository.ShiftEODEmployee": {"type": "object", "properties": {"employee_code": {"type": "string"}, "employee_id": {"type": "integer"}, "employee_name": {"type": "string"}}}, "repository.ShiftEODFuelGrade": {"type": "object", "properties": {"discount_amount": {"type": "number"}, "free_liters": {"type": "number"}, "fuel_grade": {"type": "string"}, "fuel_name": {"type": "string"}, "fuel_type": {"type": "string"}, "gross_amount": {"type": "number"}, "net_amount": {"type": "number"}, "net_sales_volume": {"type": "number"}, "sales_volume": {"type": "number"}, "transaction_count": {"type": "integer"}, "unit_price": {"type": "number"}}}, "repository.ShiftEODFuelSales": {"type": "object", "properties": {"by_grade": {"type": "array", "items": {"$ref": "#/definitions/repository.ShiftEODFuelGrade"}}, "total_fuel_sales": {"$ref": "#/definitions/repository.ShiftEODFuelSalesTotal"}}}, "repository.ShiftEODFuelSalesTotal": {"type": "object", "properties": {"total_discount_amount": {"type": "number"}, "total_gross_amount": {"type": "number"}, "total_net_amount": {"type": "number"}, "total_transactions": {"type": "integer"}, "total_volume": {"type": "number"}}}, "repository.ShiftEODInfo": {"type": "object", "properties": {"end_time": {"type": "string"}, "shift_name": {"type": "string"}, "shift_number": {"type": "integer"}, "start_time": {"type": "string"}, "status": {"type": "string"}, "time_range": {"type": "string"}}}, "repository.ShiftEODOtherIncome": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/repository.ShiftEODOtherIncomeItem"}}, "total_other_income": {"type": "number"}, "total_other_transactions": {"type": "integer"}}}, "repository.ShiftEODOtherIncomeItem": {"type": "object", "properties": {"item_name": {"type": "string"}, "item_type": {"type": "string"}, "quantity": {"type": "number"}, "total_amount": {"type": "number"}, "transaction_count": {"type": "integer"}, "unit_price": {"type": "number"}}}, "repository.ShiftEODPaymentMethod": {"type": "object", "properties": {"payment_method": {"type": "string"}, "payment_method_name": {"type": "string"}, "percentage": {"type": "number"}, "total_amount": {"type": "number"}, "transaction_count": {"type": "integer"}}}, "repository.ShiftEODPaymentSummary": {"type": "object", "properties": {"by_method": {"type": "array", "items": {"$ref": "#/definitions/repository.ShiftEODPaymentMethod"}}, "total_payment": {"type": "number"}, "total_payment_transactions": {"type": "integer"}}}, "repository.ShiftEODReportHeader": {"type": "object", "properties": {"company_name": {"type": "string"}, "generated_at": {"type": "string"}, "report_date": {"type": "string"}, "report_type": {"type": "string"}, "station_id": {"type": "integer"}, "station_name": {"type": "string"}}}, "repository.ShiftEODReportResponse": {"type": "object", "properties": {"daily_summary": {"$ref": "#/definitions/repository.ShiftEODDailySummary"}, "report_header": {"$ref": "#/definitions/repository.ShiftEODReportHeader"}, "shifts": {"type": "array", "items": {"$ref": "#/definitions/repository.ShiftEODData"}}}}, "repository.ShiftEODSummary": {"type": "object", "properties": {"comments": {"type": "string"}, "control_point": {"type": "string"}, "grand_total": {"type": "number"}, "over_cash": {"type": "number"}}}, "repository.ShiftInfoDTO": {"type": "object", "properties": {"created_at": {"type": "string"}, "duration_hours": {"type": "number"}, "end_time": {"type": "string"}, "id": {"type": "integer"}, "shift_number": {"type": "string"}, "staff_id": {"type": "integer"}, "staff_name": {"type": "string"}, "start_time": {"type": "string"}, "station_id": {"type": "integer"}, "station_name": {"type": "string"}, "status": {"type": "string"}, "updated_at": {"type": "string"}}}, "repository.ShiftReportDTO": {"type": "object", "properties": {"fuel_summary": {"$ref": "#/definitions/repository.FuelSummaryDTO"}, "merchandise_summary": {"$ref": "#/definitions/repository.MerchandiseSummaryDTO"}, "payment_summary": {"$ref": "#/definitions/repository.PaymentSummaryDTO"}, "receipt_info": {"$ref": "#/definitions/repository.ReceiptInfoDTO"}, "shift_info": {"$ref": "#/definitions/repository.ShiftInfoDTO"}, "tera_summary": {"$ref": "#/definitions/repository.TeraSummaryDTO"}}}, "repository.ShiftReportJSONResponse": {"description": "班次报表JSON格式响应结构，当API参数format=json时使用此结构 data字段包含完整的班次报表数据，包括班次信息、支付汇总、油品汇总、非油品汇总、TERA汇总和小票信息", "type": "object", "properties": {"data": {"$ref": "#/definitions/repository.ShiftReportDTO"}, "message": {"type": "string", "example": "班次报表数据获取成功"}, "meta": {"$ref": "#/definitions/repository.ShiftReportMetaDTO"}, "success": {"type": "boolean", "example": true}}}, "repository.ShiftReportMetaDTO": {"type": "object", "properties": {"data_source": {"type": "string"}, "generated_at": {"type": "string"}, "processing_time_ms": {"type": "integer"}, "version": {"type": "string"}}}, "repository.TeraCategoryDTO": {"type": "object", "properties": {"gross_sales": {"type": "number"}, "net_sales": {"type": "number"}, "percentage": {"type": "number"}, "total_discount": {"type": "number"}}}, "repository.TeraSummaryDTO": {"type": "object", "properties": {"fuel": {"$ref": "#/definitions/repository.TeraCategoryDTO"}, "merchandise": {"$ref": "#/definitions/repository.TeraCategoryDTO"}, "total": {"$ref": "#/definitions/repository.TeraTotalDTO"}}}, "repository.TeraTotalDTO": {"type": "object", "properties": {"gross_sales": {"type": "number"}, "net_sales": {"type": "number"}, "total_discount": {"type": "number"}}}, "repository.TotalRevenueDTO": {"type": "object", "properties": {"bonusRefundAmount": {"description": "奖金退款", "type": "number"}, "cardAccountResetAmount": {"description": "卡账清零", "type": "number"}, "fuelReceivableAmount": {"description": "销售汇总部分", "type": "number"}, "fuelRefundAmount": {"description": "油品跨班退款", "type": "number"}, "nonFuelReceivableAmount": {"description": "非油品应收金额", "type": "number"}, "nonFuelRefundAmount": {"description": "非油品跨班退款", "type": "number"}, "parentAccountResetAmount": {"description": "母账清零", "type": "number"}, "principalRefundAmount": {"description": "本金退款", "type": "number"}, "topUpBonusAmount": {"description": "充值奖金", "type": "number"}, "topUpPrincipalAmount": {"description": "充值汇总部分", "type": "number"}}}, "repository.TransactionDTO": {"type": "object", "properties": {"amount": {"description": "FuelTransaction.Amount", "type": "number"}, "isInvoiced": {"description": "需要业务逻辑判断或关联字段", "type": "boolean"}, "nozzleId": {"description": "FuelTransaction.NozzleID", "type": "string"}, "paymentMethod": {"description": "需要关联 OrderPayment 获取", "type": "string"}, "productCategory": {"description": "需要关联获取", "type": "string"}, "siteId": {"description": "FuelTransaction.StationID", "type": "integer"}, "timestamp": {"description": "FuelTransaction.CreatedAt", "type": "string"}, "txnId": {"description": "FuelTransaction.ID", "type": "integer"}, "volume": {"description": "FuelTransaction.Volume", "type": "number"}}}, "time.Duration": {"type": "integer", "enum": [-9223372036854775808, 9223372036854775807, 1, 1000, 1000000, 1000000000, 60000000000, 3600000000000, -9223372036854775808, 9223372036854775807, 1, 1000, 1000000, 1000000000, 60000000000, 3600000000000], "x-enum-varnames": ["minDuration", "maxDuration", "Nanosecond", "Microsecond", "Millisecond", "Second", "Minute", "Hour", "minDuration", "maxDuration", "Nanosecond", "Microsecond", "Millisecond", "Second", "Minute", "Hour"]}, "warning.Status": {"type": "string", "enum": ["TRIGGERED", "TRIGGERED", "ACKNOWLEDGED", "RESOLVED"], "x-enum-varnames": ["DefaultStatus", "StatusTRIGGERED", "StatusACKNOWLEDGED", "StatusRESOLVED"]}, "warning.Type": {"type": "string", "enum": ["LOW_STOCK", "OUT_OF_STOCK", "HIGH_STOCK"], "x-enum-varnames": ["TypeLOW_STOCK", "TypeOUT_OF_STOCK", "TypeHIGH_STOCK"]}}}