basePath: /api/v1
definitions:
  handlers.DiscountItemResponse:
    properties:
      discountedPrice:
        example: 18.45
        type: number
      itemId:
        example: product-123
        type: string
      name:
        example: 商品名称
        type: string
      originalPrice:
        example: 20.5
        type: number
      quantity:
        example: 2
        type: integer
    type: object
  handlers.DiscountResponse:
    properties:
      discountAmount:
        example: 10.05
        type: number
      discountedAmount:
        example: 90.45
        type: number
      items:
        items:
          $ref: '#/definitions/handlers.DiscountItemResponse'
        type: array
      message:
        example: 折扣计算成功
        type: string
      originalAmount:
        example: 100.5
        type: number
      success:
        example: true
        type: boolean
    type: object
  handlers.Order:
    properties:
      items:
        items:
          $ref: '#/definitions/handlers.OrderItem'
        type: array
      orderAmount:
        example: 100.5
        type: number
      orderTime:
        example: "2023-01-01T12:00:00Z"
        type: string
      userId:
        example: user123
        type: string
    type: object
  handlers.OrderItem:
    properties:
      category:
        example: 电子产品
        type: string
      itemId:
        example: product-123
        type: string
      name:
        example: 商品名称
        type: string
      price:
        example: 20.5
        type: number
      quantity:
        example: 2
        type: integer
    type: object
  models.BaseResponse:
    description: 基础响应结构
    properties:
      code:
        description: 状态码
        example: 200
        type: integer
      data:
        description: 响应数据
      error:
        description: 错误信息
        example: 错误信息
        type: string
      message:
        description: 响应消息
        example: 操作成功
        type: string
    type: object
  models.CreateEmployeeRequest:
    properties:
      department:
        description: 部门
        example: 运营部
        type: string
      email:
        description: 邮箱
        example: <EMAIL>
        type: string
      employee_no:
        description: 员工编号
        example: EMP001
        type: string
      name:
        description: 员工姓名
        example: 张三
        type: string
      password:
        description: 密码
        example: password123
        type: string
      phone:
        description: 手机号
        example: "***********"
        type: string
      position:
        description: 职位
        example: 加油员
        type: string
    required:
    - employee_no
    - name
    - password
    type: object
  models.Employee:
    properties:
      created_at:
        description: 创建时间
        example: "2024-01-01T00:00:00Z"
        type: string
      department:
        description: 部门
        example: 运营部
        type: string
      email:
        description: 邮箱
        example: <EMAIL>
        type: string
      employee_no:
        description: 员工编号
        example: EMP001
        type: string
      hire_date:
        description: 入职日期
        example: "2024-01-01T00:00:00Z"
        type: string
      id:
        description: 员工ID
        example: 1
        type: integer
      last_login_at:
        description: 最后登录时间
        example: "2024-01-15T10:30:00Z"
        type: string
      name:
        description: 员工姓名
        example: 张三
        type: string
      password:
        description: 员工密码（输出时隐藏）
        example: password123
        type: string
      phone:
        description: 手机号
        example: "***********"
        type: string
      position:
        description: 职位
        example: 加油员
        type: string
      status:
        description: 状态
        example: active
        type: string
      updated_at:
        description: 更新时间
        example: "2024-01-15T10:30:00Z"
        type: string
    type: object
  models.ErrorResponse:
    properties:
      code:
        description: 错误代码
        type: string
      detail:
        description: 详细信息
        type: string
      message:
        description: 错误消息
        type: string
    type: object
  models.ListEmployeesResponse:
    properties:
      employees:
        description: 员工列表
        items:
          $ref: '#/definitions/models.Employee'
        type: array
      total:
        description: 总数
        type: integer
    type: object
  models.LoginRequest:
    properties:
      employee_no:
        description: 员工编号
        example: EMP001
        type: string
      password:
        description: 密码
        example: password123
        type: string
    required:
    - employee_no
    - password
    type: object
  models.LoginResponse:
    properties:
      employee:
        allOf:
        - $ref: '#/definitions/models.Employee'
        description: 员工信息
      token:
        description: JWT令牌
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
    type: object
  models.UpdateEmployeeRequest:
    properties:
      department:
        description: 部门
        example: 运营部
        type: string
      email:
        description: 邮箱
        example: <EMAIL>
        type: string
      name:
        description: 员工姓名
        example: 张三
        type: string
      password:
        description: 密码
        example: password123
        type: string
      phone:
        description: 手机号
        example: "***********"
        type: string
      position:
        description: 职位
        example: 加油员
        type: string
    required:
    - name
    - password
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: BOS（Business Operation System）加油站管理系统，提供燃油交易、订单管理、报表分析、员工管理、班次管理、促销管理、会员管理、油品管理、设备管理、客户管理、库存管理和支付管理等功能。
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: BOS 加油站管理系统 API
  version: 1.0.0
paths:
  /calculator:
    get:
      consumes:
      - text/html
      description: 返回促销计算服务的主页
      produces:
      - text/html
      responses:
        "200":
          description: 促销服务演示
          schema:
            type: string
      summary: 促销计算器首页
      tags:
      - 促销计算
  /calculator/calculate:
    post:
      consumes:
      - application/json
      description: 计算给定订单的所有适用折扣（功能与ProcessOrder相同）
      parameters:
      - description: 订单信息
        in: body
        name: order
        required: true
        schema:
          $ref: '#/definitions/handlers.Order'
      produces:
      - application/json
      responses:
        "200":
          description: 折扣计算结果
          schema:
            $ref: '#/definitions/handlers.DiscountResponse'
        "400":
          description: 请求数据无效
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 服务器错误
          schema:
            additionalProperties: true
            type: object
      summary: 计算订单折扣
      tags:
      - 促销计算
  /calculator/process:
    post:
      consumes:
      - application/json
      description: 接收订单数据，应用适用的促销规则，返回折扣计算结果
      parameters:
      - description: 订单信息
        in: body
        name: order
        required: true
        schema:
          $ref: '#/definitions/handlers.Order'
      produces:
      - application/json
      responses:
        "200":
          description: 折扣计算结果
          schema:
            $ref: '#/definitions/handlers.DiscountResponse'
        "400":
          description: 请求数据无效
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 服务器错误
          schema:
            additionalProperties: true
            type: object
      summary: 处理订单并计算折扣
      tags:
      - 促销计算
  /calculator/status:
    get:
      consumes:
      - application/json
      description: 返回促销计算服务的当前运行状态
      produces:
      - application/json
      responses:
        "200":
          description: 包含服务状态和当前时间
          schema:
            additionalProperties: true
            type: object
      summary: 获取服务状态
      tags:
      - 促销计算
  /employee:
    post:
      consumes:
      - application/json
      description: 添加新员工到系统中
      parameters:
      - description: 员工信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.CreateEmployeeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 添加成功
          schema:
            allOf:
            - $ref: '#/definitions/models.BaseResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.Employee'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 添加员工
      tags:
      - employees
  /employee/{id}:
    delete:
      consumes:
      - application/json
      description: 根据员工ID删除员工（软删除）
      parameters:
      - description: 员工ID
        in: path
        minimum: 1
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/models.BaseResponse'
            - properties:
                data: {}
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: 员工不存在
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 删除员工
      tags:
      - employees
    get:
      consumes:
      - application/json
      description: 根据员工ID获取员工的详细信息
      parameters:
      - description: 员工ID
        in: path
        minimum: 1
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/models.BaseResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.Employee'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: 员工不存在
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取员工详情
      tags:
      - employees
    put:
      consumes:
      - application/json
      description: 更新员工的姓名和密码信息
      parameters:
      - description: 员工ID
        in: path
        minimum: 1
        name: id
        required: true
        type: integer
      - description: 更新的员工信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UpdateEmployeeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/models.BaseResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.Employee'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: 员工不存在
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 更新员工信息
      tags:
      - employees
  /employee/login:
    post:
      consumes:
      - application/json
      description: 员工通过员工编号和密码登录系统，成功后返回JWT令牌
      parameters:
      - description: 员工登录信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功
          schema:
            allOf:
            - $ref: '#/definitions/models.BaseResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.LoginResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 员工登录
      tags:
      - employees
  /employees:
    get:
      consumes:
      - application/json
      description: 获取员工列表，支持分页和按姓名筛选
      parameters:
      - default: 1
        description: 页码，默认1
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        description: 每页记录数，默认10
        in: query
        maximum: 100
        minimum: 1
        name: size
        type: integer
      - description: 按员工姓名筛选
        in: query
        name: name
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/models.BaseResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.ListEmployeesResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取员工列表
      tags:
      - employees
  /promotions:
    get:
      consumes:
      - application/json
      description: 获取所有促销活动，支持分页、搜索和筛选
      parameters:
      - description: 促销活动状态筛选（如'ACTIVE'，'DRAFT'等）
        in: query
        name: status
        type: string
      - description: 促销活动类型筛选（如'PERCENTAGE'，'FIXED_AMOUNT'等）
        in: query
        name: type
        type: string
      - description: 搜索关键词（按名称搜索）
        in: query
        name: search
        type: string
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      - description: 每页记录数，默认为10，最大100
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: 获取促销活动列表
      tags:
      - 促销管理
  /promotions/{id}:
    delete:
      consumes:
      - application/json
      description: 根据ID删除指定的促销活动
      parameters:
      - description: 促销活动ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功响应
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 错误响应
          schema:
            additionalProperties: true
            type: object
      summary: 删除促销活动
      tags:
      - 促销管理
    get:
      consumes:
      - application/json
      description: 根据ID获取单个促销活动的详细信息
      parameters:
      - description: 促销活动ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "404":
          description: 活动不存在
          schema:
            type: string
        "500":
          description: 服务器错误
          schema:
            type: string
      summary: 查看促销活动详情
      tags:
      - 促销管理
  /promotions/{id}/status:
    post:
      consumes:
      - application/json
      description: 更新指定促销活动的状态（如激活、暂停等）
      parameters:
      - description: 促销活动ID
        in: path
        name: id
        required: true
        type: string
      - description: 新状态
        in: formData
        name: status
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功响应
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 错误响应
          schema:
            additionalProperties: true
            type: object
      summary: 更新促销活动状态
      tags:
      - 促销管理
  /promotions/edit/{id}:
    get:
      consumes:
      - application/json
      description: 获取新增或编辑促销活动所需的表单数据
      parameters:
      - description: 促销活动ID（编辑时提供）
        in: path
        name: id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "404":
          description: 活动不存在
          schema:
            type: string
        "500":
          description: 服务器错误
          schema:
            type: string
      summary: 获取促销活动表单数据
      tags:
      - 促销管理
  /promotions/new:
    get:
      consumes:
      - application/json
      description: 获取新增或编辑促销活动所需的表单数据
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "404":
          description: 活动不存在
          schema:
            type: string
        "500":
          description: 服务器错误
          schema:
            type: string
      summary: 获取促销活动表单数据
      tags:
      - 促销管理
  /promotions/save:
    post:
      consumes:
      - multipart/form-data
      description: 创建新的促销活动或更新现有的促销活动
      parameters:
      - description: 促销活动名称
        in: formData
        name: name
        required: true
        type: string
      - description: 促销活动描述
        in: formData
        name: description
        type: string
      - description: 促销类型
        in: formData
        name: type
        required: true
        type: string
      - description: 促销范围
        in: formData
        name: scope
        required: true
        type: string
      - description: 促销值
        in: formData
        name: value
        type: number
      - description: 状态
        in: formData
        name: status
        type: string
      - description: 最低订单金额
        in: formData
        name: min_order_amount
        type: number
      - description: 最大折扣金额
        in: formData
        name: max_discount_amount
        type: number
      - description: 优先级
        in: formData
        name: priority
        type: integer
      - description: 开始时间（格式：2006-01-02T15:04）
        in: formData
        name: start_time
        type: string
      - description: 结束时间（格式：2006-01-02T15:04）
        in: formData
        name: end_time
        type: string
      - description: 是否可叠加
        in: formData
        name: stackable
        type: boolean
      - description: 是否需要优惠券
        in: formData
        name: requires_coupon
        type: boolean
      - description: 最大使用次数
        in: formData
        name: max_use_count
        type: integer
      - description: 每用户最大使用次数
        in: formData
        name: max_per_user
        type: integer
      - description: 是否适用会员
        in: formData
        name: for_members
        type: boolean
      - description: 是否适用非会员
        in: formData
        name: for_non_members
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: 请求数据无效
          schema:
            type: string
        "500":
          description: 服务器错误
          schema:
            type: string
      summary: 保存促销活动
      tags:
      - 促销管理
  /promotions/save/{id}:
    post:
      consumes:
      - multipart/form-data
      description: 创建新的促销活动或更新现有的促销活动
      parameters:
      - description: 促销活动ID（更新时提供）
        in: path
        name: id
        type: string
      - description: 促销活动名称
        in: formData
        name: name
        required: true
        type: string
      - description: 促销活动描述
        in: formData
        name: description
        type: string
      - description: 促销类型
        in: formData
        name: type
        required: true
        type: string
      - description: 促销范围
        in: formData
        name: scope
        required: true
        type: string
      - description: 促销值
        in: formData
        name: value
        type: number
      - description: 状态
        in: formData
        name: status
        type: string
      - description: 最低订单金额
        in: formData
        name: min_order_amount
        type: number
      - description: 最大折扣金额
        in: formData
        name: max_discount_amount
        type: number
      - description: 优先级
        in: formData
        name: priority
        type: integer
      - description: 开始时间（格式：2006-01-02T15:04）
        in: formData
        name: start_time
        type: string
      - description: 结束时间（格式：2006-01-02T15:04）
        in: formData
        name: end_time
        type: string
      - description: 是否可叠加
        in: formData
        name: stackable
        type: boolean
      - description: 是否需要优惠券
        in: formData
        name: requires_coupon
        type: boolean
      - description: 最大使用次数
        in: formData
        name: max_use_count
        type: integer
      - description: 每用户最大使用次数
        in: formData
        name: max_per_user
        type: integer
      - description: 是否适用会员
        in: formData
        name: for_members
        type: boolean
      - description: 是否适用非会员
        in: formData
        name: for_non_members
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: 请求数据无效
          schema:
            type: string
        "500":
          description: 服务器错误
          schema:
            type: string
      summary: 保存促销活动
      tags:
      - 促销管理
securityDefinitions:
  ApiKeyAuth:
    description: Bearer token authentication
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
tags:
- description: 燃油交易管理相关接口
  name: fuel-transactions
- description: 订单管理相关接口
  name: orders
- description: 报表管理相关接口
  name: reports
- description: 增强报表管理相关接口
  name: enhanced-reports
- description: 员工管理相关接口
  name: employees
- description: 班次管理相关接口
  name: shifts
- description: 促销管理相关接口
  name: promotions
- description: 促销计算相关接口
  name: calculator
- description: 会员管理相关接口
  name: members
- description: 油品管理相关接口
  name: oil
- description: PTS2设备管理相关接口
  name: pts2
- description: 客户管理相关接口
  name: customers
- description: 车辆管理相关接口
  name: vehicles
- description: 标签管理相关接口
  name: labels
- description: 库存管理相关接口
  name: inventory
- description: 支付管理相关接口
  name: payments
- description: 员工卡管理相关接口
  name: staff-cards
- description: 认证管理相关接口
  name: auth
- description: 用户管理相关接口
  name: users
- description: 角色管理相关接口
  name: roles
- description: 权限管理相关接口
  name: permissions
- description: 会话管理相关接口
  name: sessions
- description: 日志管理相关接口
  name: logs
- description: 站点管理相关接口
  name: stations
- description: 设备管理相关接口
  name: equipments
- description: 配置管理相关接口
  name: configs
- description: 数据字典管理相关接口
  name: dict
- description: 标签管理相关接口
  name: tags
