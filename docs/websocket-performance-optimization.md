# BOS WebSocket 性能优化指南

## 概述

本文档提供了BOS WebSocket系统的性能优化策略和最佳实践，帮助您在生产环境中获得最佳性能。

## 性能基准

### 目标性能指标

| 指标 | 目标值 | 优秀 | 良好 | 一般 |
|------|--------|------|------|------|
| 连接成功率 | >99% | >95% | >90% | >80% |
| 消息丢失率 | <0.1% | <1% | <5% | <10% |
| P95延迟 | <10ms | <50ms | <100ms | <200ms |
| 吞吐量 | >2000 msg/s | >1000 msg/s | >500 msg/s | >100 msg/s |
| 并发连接 | >1000 | >500 | >200 | >100 |

### 与轮询方式对比

| 方面 | 轮询方式 | WebSocket | 改善幅度 |
|------|----------|-----------|----------|
| 请求频率 | 0.5 req/s | 实时推送 | 实时性提升 |
| 网络开销 | 高 (HTTP头) | 低 (帧头) | ~90% 减少 |
| 服务器资源 | 高 (连接建立) | 低 (长连接) | ~80% 减少 |
| 延迟 | 2-5秒 | <100ms | 95%+ 减少 |
| 客户端电量 | 高 (频繁请求) | 低 (被动接收) | ~70% 减少 |

## 服务器端优化

### 1. 连接管理优化

```go
// 优化连接池配置
hubConfig := &websocket.HubConfig{
    MaxClients:        2000,        // 根据服务器资源调整
    HeartbeatInterval: 30 * time.Second,
    ClientTimeout:     60 * time.Second,
    BufferSize:        512,         // 增加缓冲区大小
    EnableStats:       true,
}

// 使用连接池
hub := websocket.NewHub(hubConfig)
```

### 2. 消息处理优化

```go
// 批量消息处理
eventManagerConfig := &websocket.EventManagerConfig{
    BufferSize:      2000,          // 增加事件缓冲区
    ProcessInterval: 50 * time.Millisecond, // 减少处理间隔
    BatchSize:       100,           // 增加批处理大小
    EnableDedup:     true,          // 启用去重
    DedupWindow:     3 * time.Second,
}
```

### 3. 内存优化

```go
// 对象池复用
var messagePool = sync.Pool{
    New: func() interface{} {
        return &websocket.Message{}
    },
}

func getMessage() *websocket.Message {
    return messagePool.Get().(*websocket.Message)
}

func putMessage(msg *websocket.Message) {
    msg.Reset() // 重置消息内容
    messagePool.Put(msg)
}
```

### 4. 网络优化

```go
// WebSocket配置优化
upgrader := websocket.Upgrader{
    ReadBufferSize:  4096,  // 增加读缓冲区
    WriteBufferSize: 4096,  // 增加写缓冲区
    CheckOrigin: func(r *http.Request) bool {
        // 实现严格的跨域检查
        return isValidOrigin(r.Header.Get("Origin"))
    },
    EnableCompression: true, // 启用压缩
}
```

## 客户端优化

### 1. 连接管理

```javascript
// 优化重连策略
const wsClient = new BOSWebSocketClient({
    reconnectInterval: 1000,        // 初始重连间隔
    maxReconnectAttempts: 10,       // 最大重连次数
    heartbeatInterval: 30000,       // 心跳间隔
    exponentialBackoff: true,       // 指数退避
    maxReconnectInterval: 30000,    // 最大重连间隔
});
```

### 2. 消息优化

```javascript
// 消息队列和批处理
class OptimizedWebSocketClient extends BOSWebSocketClient {
    constructor(options) {
        super(options);
        this.messageQueue = [];
        this.batchSize = 10;
        this.batchInterval = 100; // ms
        this.setupBatchProcessing();
    }
    
    setupBatchProcessing() {
        setInterval(() => {
            if (this.messageQueue.length > 0) {
                this.processBatch();
            }
        }, this.batchInterval);
    }
    
    processBatch() {
        const batch = this.messageQueue.splice(0, this.batchSize);
        batch.forEach(message => this.handleMessage(message));
    }
}
```

### 3. UI更新优化

```javascript
// 使用requestAnimationFrame优化UI更新
class OptimizedRealtimeManager extends BOSRealtimeManager {
    constructor(options) {
        super(options);
        this.pendingUpdates = new Set();
        this.scheduleUpdate();
    }
    
    scheduleUpdate() {
        requestAnimationFrame(() => {
            this.flushUpdates();
            this.scheduleUpdate();
        });
    }
    
    flushUpdates() {
        this.pendingUpdates.forEach(updateFn => updateFn());
        this.pendingUpdates.clear();
    }
    
    updateNozzleStatusUI(payload) {
        this.pendingUpdates.add(() => {
            super.updateNozzleStatusUI(payload);
        });
    }
}
```

## 系统级优化

### 1. 负载均衡

```nginx
# Nginx配置示例
upstream websocket_backend {
    ip_hash; # 确保同一客户端连接到同一服务器
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}

server {
    listen 80;
    
    location /ws {
        proxy_pass http://websocket_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }
}
```

### 2. 操作系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化TCP参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65536" >> /etc/sysctl.conf
echo "net.core.netdev_max_backlog = 5000" >> /etc/sysctl.conf

# 应用配置
sysctl -p
```

### 3. 监控和告警

```go
// 性能监控
type PerformanceMonitor struct {
    hub           *websocket.HubImpl
    alertThresholds map[string]float64
}

func (pm *PerformanceMonitor) Monitor() {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()
    
    for range ticker.C {
        stats := pm.hub.GetStats()
        
        // 检查连接数
        if stats.ActiveConnections > int(pm.alertThresholds["max_connections"]) {
            pm.sendAlert("连接数过高", stats.ActiveConnections)
        }
        
        // 检查消息处理速度
        msgRate := float64(stats.MessagesSent) / time.Since(stats.LastUpdated).Seconds()
        if msgRate < pm.alertThresholds["min_message_rate"] {
            pm.sendAlert("消息处理速度过慢", msgRate)
        }
    }
}
```

## 性能测试

### 1. 运行基准测试

```bash
# 基本性能测试
go run cmd/websocket-benchmark/main.go \
    -url ws://localhost:8080/ws \
    -connections 1000 \
    -duration 300s \
    -output results.json

# 使用配置文件
go run cmd/websocket-benchmark/main.go \
    -config configs/websocket-benchmark.json \
    -verbose \
    -output production-test.json
```

### 2. 压力测试场景

```json
{
  "scenarios": [
    {
      "name": "高并发连接测试",
      "max_connections": 2000,
      "connection_rate": 100,
      "message_rate": 0,
      "test_duration": "60s"
    },
    {
      "name": "高频消息测试",
      "max_connections": 100,
      "connection_rate": 10,
      "message_rate": 5000,
      "test_duration": "300s"
    },
    {
      "name": "长时间稳定性测试",
      "max_connections": 500,
      "connection_rate": 20,
      "message_rate": 1000,
      "test_duration": "3600s"
    }
  ]
}
```

### 3. 性能分析

```bash
# CPU性能分析
go tool pprof http://localhost:8080/debug/pprof/profile

# 内存分析
go tool pprof http://localhost:8080/debug/pprof/heap

# 协程分析
go tool pprof http://localhost:8080/debug/pprof/goroutine
```

## 故障排除

### 1. 常见性能问题

| 问题 | 症状 | 解决方案 |
|------|------|----------|
| 连接数过多 | 内存占用高，响应慢 | 增加服务器资源，优化连接管理 |
| 消息积压 | 延迟增加，内存泄漏 | 增加处理协程，优化消息处理逻辑 |
| 频繁重连 | 连接不稳定 | 检查网络配置，优化心跳机制 |
| 内存泄漏 | 内存持续增长 | 检查对象生命周期，使用对象池 |

### 2. 监控指标

```go
// 关键监控指标
type Metrics struct {
    ActiveConnections    prometheus.Gauge
    MessagesSent         prometheus.Counter
    MessagesReceived     prometheus.Counter
    MessageLatency       prometheus.Histogram
    ConnectionErrors     prometheus.Counter
    MemoryUsage         prometheus.Gauge
}
```

### 3. 日志分析

```bash
# 分析连接模式
grep "WebSocket连接" /var/log/bos/websocket.log | \
    awk '{print $1, $2}' | sort | uniq -c

# 分析错误模式
grep "ERROR" /var/log/bos/websocket.log | \
    awk '{print $NF}' | sort | uniq -c | sort -nr

# 分析性能趋势
grep "性能统计" /var/log/bos/websocket.log | \
    awk '{print $3, $5}' | sort
```

## 最佳实践总结

1. **连接管理**：合理设置连接池大小，实现优雅的连接关闭
2. **消息处理**：使用批处理和异步处理，避免阻塞
3. **内存管理**：使用对象池，及时释放资源
4. **网络优化**：启用压缩，优化缓冲区大小
5. **监控告警**：实时监控关键指标，设置合理阈值
6. **测试验证**：定期进行性能测试，验证优化效果

通过以上优化措施，BOS WebSocket系统可以在生产环境中稳定运行，支持大规模并发连接和高频消息传输。
