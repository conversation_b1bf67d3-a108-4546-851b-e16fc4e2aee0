# BP加油站订单服务 - 增强报表服务对接文档

## 1. 服务概述

BP加油站订单服务是一个提供订单管理和报表功能的微服务，支持燃油和非燃油商品的交易处理、订单管理和数据分析。本文档主要介绍增强报表服务(EnhancedReportService)的对接方式，该服务提供了丰富的数据分析和报表生成功能。

## 2. 服务初始化

### 2.1 依赖项

增强报表服务需要以下依赖项:

```go
// 数据库连接
db *sqlx.DB

// 基础报表仓库
reportRepo repository.ReportRepository

// 增强报表仓库
enhancedReportRepo repository.EnhancedReportRepository

// 报表查询构建器
queryBuilder repository.ReportQueryBuilder

// 数据转换器
dataConverter repository.ReportDataConverter

// 日志记录器
logger logrus.FieldLogger
```

### 2.2 初始化代码

```go
// 1. 初始化数据库连接
db, err := sqlx.Connect("postgres", connectionString)
if err != nil {
    return nil, fmt.Errorf("连接数据库失败: %w", err)
}

// 2. 初始化基础组件
reportRepo := postgres.NewReportRepository(db)
queryBuilder := postgres.NewReportQueryBuilder()
dataConverter := postgres.NewReportDataConverterImpl()

// 3. 初始化增强报表仓库
enhancedReportRepo := postgres.NewEnhancedReportRepository(
    db, 
    queryBuilder, 
    dataConverter, 
    reportRepo,
)

// 4. 初始化日志记录器
logger := logrus.New()
logger.SetLevel(logrus.InfoLevel)

// 5. 创建增强报表服务
enhancedReportService := service.NewEnhancedReportService(
    reportRepo, 
    enhancedReportRepo, 
    dataConverter, 
    logger,
)
```

## 3. 主要接口

增强报表服务提供以下主要接口:

### 3.1 总收入汇总报表

```go
// 获取总收入汇总报表
func (s *EnhancedReportService) GetTotalRevenue(
    ctx context.Context, 
    filter repository.EnhancedReportFilter,
) (*repository.TotalRevenueDTO, error)
```

#### 3.1.1 详细说明

- **功能**: 获取指定时间段内的总收入汇总数据，包括燃油和非燃油收入、退款等信息
- **入参**:
  - `ctx context.Context`: 请求上下文，可包含超时、取消等控制信息
  - `filter repository.EnhancedReportFilter`: 过滤条件，必须提供日期范围
- **返回值**:
  - `*repository.TotalRevenueDTO`: 总收入汇总数据结构体指针
  - `error`: 可能的错误信息

#### 3.1.2 调用示例

```go
// 创建过滤条件
filter := repository.EnhancedReportFilter{
    DateRange: repository.DateRangeFilter{
        StartDate: "2024-01-01",  // 开始日期，格式YYYY-MM-DD
        EndDate:   "2024-01-31",  // 结束日期，格式YYYY-MM-DD
    },
    Sites: repository.SiteFilter{
        SiteIDs: []int{1, 2, 3},  // 可选，指定站点ID列表
    },
}

// 调用接口
result, err := enhancedReportService.GetTotalRevenue(ctx, filter)
if err != nil {
    // 错误处理
    log.Errorf("获取总收入报表失败: %v", err)
    return err
}

// 使用返回数据
fmt.Printf("燃油应收金额: %.2f\n", result.FuelReceivableAmount)
fmt.Printf("非燃油应收金额: %.2f\n", result.NonFuelReceivableAmount)
fmt.Printf("燃油退款金额: %.2f\n", result.FuelRefundAmount)
fmt.Printf("充值本金总额: %.2f\n", result.TopUpPrincipalAmount)
```

### 3.2 油品应收汇总报表

```go
// 获取油品应收汇总报表
func (s *EnhancedReportService) GetFuelReceivableSummary(
    ctx context.Context, 
    filter repository.EnhancedReportFilter,
) (*repository.FuelReceivableSummaryDTO, error)
```

#### 3.2.1 详细说明

- **功能**: 获取指定时间段内的油品销售汇总数据，包括销售数量、金额、优惠、退款等详细信息
- **入参**:
  - `ctx context.Context`: 请求上下文，可包含超时、取消等控制信息
  - `filter repository.EnhancedReportFilter`: 过滤条件，必须提供日期范围
- **返回值**:
  - `*repository.FuelReceivableSummaryDTO`: 油品应收汇总数据结构体指针
  - `error`: 可能的错误信息

#### 3.2.2 调用示例

```go
// 创建过滤条件
filter := repository.EnhancedReportFilter{
    DateRange: repository.DateRangeFilter{
        StartDate: "2024-01-01",
        EndDate:   "2024-01-31",
    },
    // 可选的站点筛选
    Sites: repository.SiteFilter{
        SiteIDs: []int{1},  // 仅查询站点ID为1的数据
    },
}

// 调用接口
result, err := enhancedReportService.GetFuelReceivableSummary(ctx, filter)
if err != nil {
    // 错误处理
    log.Errorf("获取油品应收汇总失败: %v", err)
    return err
}

// 使用返回数据
fmt.Printf("油品销售数量(升): %.2f\n", result.FuelSalesVolume)
fmt.Printf("油品销售金额: %.2f\n", result.FuelSalesAmount)
fmt.Printf("折扣金额: %.2f\n", result.DiscountAmount)
fmt.Printf("优惠券金额: %.2f\n", result.CouponDiscountAmount)
fmt.Printf("实际收到金额: %.2f\n", result.ActualReceivedAmount)
```

### 3.3 按支付方式销售明细

```go
// 获取按支付方式的销售明细
func (s *EnhancedReportService) GetSalesDetailByPaymentMethod(
    ctx context.Context, 
    filter repository.EnhancedReportFilter,
) ([]repository.SalesDetailByPaymentMethodDTO, *repository.ReportMetadata, error)
```

#### 3.3.1 详细说明

- **功能**: 获取按支付方式分组的销售明细，包括各支付方式下的详细交易列表
- **入参**:
  - `ctx context.Context`: 请求上下文，可包含超时、取消等控制信息
  - `filter repository.EnhancedReportFilter`: 过滤条件，必须提供日期范围
    - 需要设置 `Limit` 和 `Offset` 参数以实现分页
- **返回值**:
  - `[]repository.SalesDetailByPaymentMethodDTO`: 按支付方式分组的销售明细数组
  - `*repository.ReportMetadata`: 报表元数据，包含分页信息、总记录数等
  - `error`: 可能的错误信息

#### 3.3.2 调用示例

```go
// 创建分页过滤条件
filter := repository.EnhancedReportFilter{
    DateRange: repository.DateRangeFilter{
        StartDate: "2024-01-01",
        EndDate:   "2024-01-31",
    },
    // 分页参数
    Limit:  10,  // 每页10条记录
    Offset: 0,   // 从第0条开始
}

// 调用接口
results, metadata, err := enhancedReportService.GetSalesDetailByPaymentMethod(ctx, filter)
if err != nil {
    // 错误处理
    log.Errorf("获取支付方式销售明细失败: %v", err)
    return err
}

// 使用返回数据
fmt.Printf("总记录数: %d\n", metadata.TotalCount)
fmt.Printf("查询耗时: %d ms\n", metadata.QueryTime)

// 遍历支付方式明细
for _, paymentDetail := range results {
    fmt.Printf("支付方式: %s\n", paymentDetail.PaymentMethod)
    
    // 遍历该支付方式下的交易明细
    for _, detail := range paymentDetail.Details {
        fmt.Printf("  商品: %s, 应收金额: %.2f, 实收金额: %.2f\n", 
            detail.ProductName, 
            detail.ReceivableAmount, 
            detail.ActualReceivedAmount)
    }
}

// 处理分页，获取下一页数据
if metadata.TotalCount > filter.Limit {
    // 设置偏移量，获取下一页
    filter.Offset += filter.Limit
    nextPageResults, _, err := enhancedReportService.GetSalesDetailByPaymentMethod(ctx, filter)
    // ...处理下一页数据...
}
```

### 3.4 按商品销售明细

```go
// 获取按商品的销售明细
func (s *EnhancedReportService) GetSalesDetailByProduct(
    ctx context.Context, 
    filter repository.EnhancedReportFilter,
) ([]repository.SalesDetailByProductDTO, *repository.ReportMetadata, error)
```

#### 3.4.1 详细说明

- **功能**: 获取按商品分组的销售明细，包括各商品的详细交易记录和小计
- **入参**:
  - `ctx context.Context`: 请求上下文，可包含超时、取消等控制信息
  - `filter repository.EnhancedReportFilter`: 过滤条件，必须提供日期范围
    - 需要设置 `Limit` 和 `Offset` 参数以实现分页
- **返回值**:
  - `[]repository.SalesDetailByProductDTO`: 按商品分组的销售明细数组
  - `*repository.ReportMetadata`: 报表元数据，包含分页信息、总记录数等
  - `error`: 可能的错误信息

#### 3.4.2 调用示例

```go
// 创建分页过滤条件
filter := repository.EnhancedReportFilter{
    DateRange: repository.DateRangeFilter{
        StartDate: "2024-01-01",
        EndDate:   "2024-01-31",
    },
    // 分页参数
    Limit:  10,
    Offset: 0,
}

// 调用接口
results, metadata, err := enhancedReportService.GetSalesDetailByProduct(ctx, filter)
if err != nil {
    // 错误处理
    log.Errorf("获取商品销售明细失败: %v", err)
    return err
}

// 使用返回数据
fmt.Printf("总记录数: %d\n", metadata.TotalCount)

// 遍历商品明细
for _, productDetail := range results {
    fmt.Printf("商品名称: %s\n", productDetail.ProductName)
    
    // 查看商品小计
    fmt.Printf("  小计 - 应收金额: %.2f, 实收金额: %.2f\n", 
        productDetail.Subtotal.ReceivableAmount, 
        productDetail.Subtotal.ActualReceivedAmount)
    
    // 遍历该商品的交易明细
    for _, detail := range productDetail.Details {
        fmt.Printf("  支付方式: %s, 升数: %.2f, 优惠金额: %.2f\n", 
            detail.PaymentMethod, 
            detail.Liters, 
            detail.TotalDiscountAmount)
    }
}
```

### 3.5 油枪销售明细

```go
// 获取油枪销售明细
func (s *EnhancedReportService) GetNozzleDetailedSales(
    ctx context.Context, 
    filter repository.EnhancedReportFilter,
) ([]repository.NozzleDetailedSalesDTO, *repository.ReportMetadata, error)
```

#### 3.5.1 详细说明

- **功能**: 获取各油枪的销售明细数据，包括站点、油枪ID、燃油类型、销售量等信息
- **入参**:
  - `ctx context.Context`: 请求上下文，可包含超时、取消等控制信息
  - `filter repository.EnhancedReportFilter`: 过滤条件，必须提供日期范围
    - 可以使用 `Sites.SiteIDs` 筛选特定站点的油枪数据
    - 需要设置 `Limit` 和 `Offset` 参数以实现分页
- **返回值**:
  - `[]repository.NozzleDetailedSalesDTO`: 油枪销售明细数组
  - `*repository.ReportMetadata`: 报表元数据，包含分页信息、总记录数等
  - `error`: 可能的错误信息

#### 3.5.2 调用示例

```go
// 创建分页过滤条件
filter := repository.EnhancedReportFilter{
    DateRange: repository.DateRangeFilter{
        StartDate: "2024-01-01",
        EndDate:   "2024-01-31",
    },
    // 站点筛选
    Sites: repository.SiteFilter{
        SiteIDs: []int{1, 2},  // 仅查询站点1和2的油枪数据
    },
    // 分页参数
    Limit:  20,
    Offset: 0,
}

// 调用接口
results, metadata, err := enhancedReportService.GetNozzleDetailedSales(ctx, filter)
if err != nil {
    // 错误处理
    log.Errorf("获取油枪销售明细失败: %v", err)
    return err
}

// 使用返回数据
fmt.Printf("总记录数: %d\n", metadata.TotalCount)

// 遍历油枪销售数据
for _, nozzle := range results {
    fmt.Printf("站点ID: %d, 油枪ID: %s, 燃油类型: %s\n", 
        nozzle.SiteID, 
        nozzle.NozzleID, 
        nozzle.FuelType)
    
    fmt.Printf("  单价: %.2f, 总销售量: %.2f, 总金额: %.2f\n", 
        nozzle.UnitPrice, 
        nozzle.TotalVolume, 
        nozzle.TotalAmount)
    
    fmt.Printf("  优惠金额: %.2f, 实收金额: %.2f, 交易次数: %d\n", 
        nozzle.DiscountAmount, 
        nozzle.ActualReceivedAmount, 
        nozzle.TransactionCount)
}
```

### 3.6 生成报表响应

```go
// 生成统一的报表响应
func (s *EnhancedReportService) GenerateReportResponse(
    ctx context.Context, 
    data interface{}, 
    metadata *repository.ReportMetadata,
) (*repository.ReportResponse, error)
```

#### 3.6.1 详细说明

- **功能**: 将报表数据和元数据封装为统一的响应格式，便于客户端统一处理
- **入参**:
  - `ctx context.Context`: 请求上下文
  - `data interface{}`: 任意类型的报表数据，通常是前面接口返回的DTO数据
  - `metadata *repository.ReportMetadata`: 报表元数据，包含分页信息、总记录数等
- **返回值**:
  - `*repository.ReportResponse`: 封装了数据和元数据的统一响应对象
  - `error`: 可能的错误信息

#### 3.6.2 调用示例

```go
// 假设已经获取了油枪销售数据
nozzleData, nozzleMetadata, err := enhancedReportService.GetNozzleDetailedSales(ctx, filter)
if err != nil {
    return err
}

// 生成统一报表响应
response, err := enhancedReportService.GenerateReportResponse(ctx, nozzleData, nozzleMetadata)
if err != nil {
    log.Errorf("生成报表响应失败: %v", err)
    return err
}

// 使用响应对象
fmt.Printf("生成时间: %s\n", response.Metadata.GeneratedAt)
fmt.Printf("总记录数: %d\n", response.Metadata.TotalCount)
fmt.Printf("查询耗时: %d ms\n", response.Metadata.QueryTime)

// 响应转为JSON
jsonData, err := json.Marshal(response)
if err != nil {
    log.Errorf("序列化报表响应失败: %v", err)
    return err
}

// 返回给客户端
w.Header().Set("Content-Type", "application/json")
w.Write(jsonData)
```

### 3.7 查询性能指标

```go
// 获取查询性能指标
func (s *EnhancedReportService) GetQueryPerformance(
    ctx context.Context, 
    queryType string, 
    filter repository.EnhancedReportFilter,
) (map[string]interface{}, error)
```

#### 3.7.1 详细说明

- **功能**: 获取指定查询类型的性能指标，用于监控和优化查询性能
- **入参**:
  - `ctx context.Context`: 请求上下文
  - `queryType string`: 查询类型，有效值包括:
    - `"total_revenue"`: 总收入汇总查询
    - `"fuel_receivable"`: 油品应收汇总查询
    - `"payment_method_detail"`: 支付方式明细查询
    - `"product_detail"`: 商品明细查询
    - `"nozzle_detail"`: 油枪明细查询
  - `filter repository.EnhancedReportFilter`: 过滤条件，与对应报表查询相同
- **返回值**:
  - `map[string]interface{}`: 性能指标映射，包含各种性能数据
  - `error`: 可能的错误信息，包括无效查询类型错误

#### 3.7.2 调用示例

```go
// 创建过滤条件
filter := repository.EnhancedReportFilter{
    DateRange: repository.DateRangeFilter{
        StartDate: "2024-01-01",
        EndDate:   "2024-01-31",
    },
}

// 获取总收入查询的性能指标
metrics, err := enhancedReportService.GetQueryPerformance(ctx, "total_revenue", filter)
if err != nil {
    log.Errorf("获取查询性能指标失败: %v", err)
    return err
}

// 使用性能指标
fmt.Printf("查询耗时: %v ms\n", metrics["query_time"])
fmt.Printf("查询行数: %v\n", metrics["rows_processed"])
fmt.Printf("内存使用: %v MB\n", metrics["memory_usage"])
fmt.Printf("缓存命中率: %v%%\n", metrics["cache_hit_rate"])

// 获取油枪明细查询的性能指标
nozzleMetrics, err := enhancedReportService.GetQueryPerformance(ctx, "nozzle_detail", filter)
if err != nil {
    // 处理错误
}

// 比较不同查询的性能
fmt.Printf("总收入查询耗时: %v ms, 油枪明细查询耗时: %v ms\n", 
    metrics["query_time"], 
    nozzleMetrics["query_time"])
```

## 4. 过滤条件说明

所有报表接口都使用 `EnhancedReportFilter` 结构作为过滤条件:

```go
type EnhancedReportFilter struct {
    // 日期范围过滤
    DateRange DateRangeFilter `json:"dateRange"`
    
    // 站点过滤
    Sites SiteFilter `json:"sites"`
    
    // 分页参数
    Limit  int `json:"limit"`
    Offset int `json:"offset"`
    
    // 其他过滤条件可根据需要扩展
}

type DateRangeFilter struct {
    StartDate string `json:"startDate"` // 格式: YYYY-MM-DD
    EndDate   string `json:"endDate"`   // 格式: YYYY-MM-DD
}

type SiteFilter struct {
    SiteIDs []int `json:"siteIds"` // 站点ID列表
}
```

## 5. 调用链路

### 5.1 典型调用链路

以获取总收入汇总报表为例:

1. 上位服务调用 `enhancedReportService.GetTotalRevenue()`
2. EnhancedReportService 处理请求参数，包括日期转换
3. EnhancedReportService 将请求传递给 EnhancedReportRepository
4. EnhancedReportRepository 使用 ReportQueryBuilder 构建查询
5. EnhancedReportRepository 执行数据库查询
6. 查询结果通过 ReportDataConverter 转换为 DTO
7. EnhancedReportService 返回结果给上位服务

```
上位服务 → EnhancedReportService → EnhancedReportRepository → 
   ReportQueryBuilder → 数据库 → ReportDataConverter → DTO → 上位服务
```

### 5.2 数据流向图

```
┌─────────────┐    请求     ┌──────────────────┐    查询构建    ┌─────────────────┐
│             │ ────────→  │                  │ ────────────→ │                 │
│  上位服务    │            │  报表服务层      │                │  仓库层         │
│             │ ←─────────  │                  │ ←──────────── │                 │
└─────────────┘    结果     └──────────────────┘    查询结果    └─────────────────┘
                                   ↓  ↑                             ↓  ↑
                                   │  │                             │  │
                              转换请求 转换结果                  执行查询 原始结果
                                   │  │                             │  │
                              ┌──────────────────┐             ┌─────────────────┐
                              │                  │             │                 │
                              │  数据转换器      │             │  数据库         │
                              │                  │             │                 │
                              └──────────────────┘             └─────────────────┘
```

## 6. 使用示例

### 6.1 基本调用示例

```go
// 创建上下文
ctx := context.Background()

// 创建过滤条件
filter := repository.EnhancedReportFilter{
    DateRange: repository.DateRangeFilter{
        StartDate: "2024-01-01",
        EndDate:   "2024-05-01",
    },
    Sites: repository.SiteFilter{
        SiteIDs: []int{1, 2, 3}, // 指定站点ID
    },
    Limit:  100,
    Offset: 0,
}

// 获取总收入汇总报表
totalRevenue, err := enhancedReportService.GetTotalRevenue(ctx, filter)
if err != nil {
    log.Errorf("获取总收入汇总报表失败: %v", err)
    return err
}

// 使用报表数据
log.Infof("燃油应收金额: %.2f", totalRevenue.FuelReceivableAmount)
log.Infof("非燃油应收金额: %.2f", totalRevenue.NonFuelReceivableAmount)
```

### 6.2 生成统一响应示例

```go
// 获取按支付方式的销售明细
paymentDetails, metadata, err := enhancedReportService.GetSalesDetailByPaymentMethod(ctx, filter)
if err != nil {
    log.Errorf("获取销售明细失败: %v", err)
    return err
}

// 生成统一的报表响应
response, err := enhancedReportService.GenerateReportResponse(ctx, paymentDetails, metadata)
if err != nil {
    log.Errorf("生成报表响应失败: %v", err)
    return err
}

// 转换为JSON
jsonBytes, err := json.Marshal(response)
if err != nil {
    log.Errorf("序列化响应失败: %v", err)
    return err
}

// 返回给客户端
fmt.Fprintf(w, "%s", jsonBytes)
```

## 7. 错误处理

服务可能返回以下错误类型:

```go
// 无效的过滤条件
fmt.Errorf("无效的日期格式: %s", filter.DateRange.StartDate)

// 数据库查询错误
fmt.Errorf("执行查询失败: %w", err)

// 无效的查询类型
fmt.Errorf("未知的查询类型: %s", queryType)

// 数据转换错误
fmt.Errorf("转换数据失败: %w", err)
```

上位服务应当妥善处理这些错误并提供适当的反馈。

## 8. 性能考虑

- 对大数据量查询，建议使用分页参数(`Limit`和`Offset`)限制返回结果
- 尽量缩小日期范围查询，以提高查询性能
- 使用站点ID过滤可以显著提高查询速度
- 所有查询都会记录性能指标，可通过`GetQueryPerformance`接口查询

## 9. 注意事项

1. 所有日期格式应为`YYYY-MM-DD`格式
2. 站点ID必须是有效的整数
3. 服务会自动进行日期格式验证和转换
4. 所有金额和数量字段均为`float64`类型
5. 服务内部会记录性能日志，便于调试和优化

## 10. 数据结构示例

### 10.1 TotalRevenueDTO

```json
{
  "fuelReceivableAmount": 100,
  "nonFuelReceivableAmount": 0,
  "fuelRefundAmount": 0,
  "nonFuelRefundAmount": 0,
  "topUpPrincipalAmount": 0,
  "topUpBonusAmount": 0,
  "principalRefundAmount": 0,
  "bonusRefundAmount": 0,
  "parentAccountResetAmount": 0,
  "cardAccountResetAmount": 0
}
```

### 10.2 FuelReceivableSummaryDTO

```json
{
  "fuelSalesVolume": 20,
  "fuelSalesAmount": 100,
  "discountAmount": 0,
  "couponDiscountAmount": 0,
  "pointsRedeemedAmount": 0,
  "totalDiscountAmount": 0,
  "pointsUsed": 0,
  "refundableAmount": 0,
  "refundableDiscount": 0,
  "actualRefundAmount": 0,
  "actualReceivedAmount": 100
}
```

### 10.3 SalesDetailByPaymentMethodDTO

```json
{
  "paymentMethod": "CARD",
  "details": [
    {
      "paymentMethod": "CARD",
      "productName": "Food Item",
      "liters": 0,
      "receivableAmount": 0,
      "discountAmount": 0,
      "couponDiscountAmount": 0,
      "pointsRedeemedAmount": 0,
      "totalDiscountAmount": 0,
      "pointsUsed": 0,
      "refundableAmount": 0,
      "refundableDiscount": 0,
      "actualRefundAmount": 0,
      "actualReceivedAmount": 0
    }
  ]
}
```

### 10.4 NozzleDetailedSalesDTO

```json
{
  "siteID": 1,
  "siteName": "",
  "nozzleId": "NOZZLE-001",
  "fuelType": "GASOLINE",
  "fuelGrade": "",
  "unitPrice": 5,
  "totalVolume": 20,
  "totalAmount": 100,
  "discountAmount": 0,
  "actualReceivedAmount": 100,
  "transactionCount": 1
}
```

## 11. 未来扩展计划

1. 支持更多报表类型，如会员积分和交易报表
2. 提供导出为Excel和PDF格式的功能
3. 增加更多的过滤条件和排序选项
4. 支持自定义报表模板
5. 提供实时数据分析功能

---

如有任何问题或需要进一步的支持，请联系技术支持团队。
