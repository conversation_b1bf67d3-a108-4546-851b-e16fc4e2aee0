# BOS 订单促销处理完整文档

## 1. 核心概念说明

### 1.1 重要说明
**`CreateOrder` 方法本身不直接处理促销**。在 BOS 系统中，订单创建和促销应用是分离的两个步骤：

1. **第一步**：调用 `CreateOrder` 创建基础订单
2. **第二步**：调用 `ApplyPromotion` 接口应用促销

### 1.2 设计原因
这种分离设计有以下优势：
- **职责分离**：订单创建专注于核心业务逻辑
- **灵活性**：可以在订单创建后动态应用或移除促销
- **可扩展性**：便于后续添加复杂的促销规则和验证

## 2. 促销数据结构

### 2.1 ApplyPromotionRequest (API 请求格式)
```json
{
  "promotion_id": 2001,                    // 必需：促销ID
  "promotion_name": "会员折扣",              // 必需：促销名称
  "promotion_type": "discount",            // 必需：促销类型
  "discount_amount": 20.00,                // 必需：折扣金额 (>=0)
  "free_item_id": null,                    // 可选：赠品ID
  "free_item_name": null,                  // 可选：赠品名称
  "free_item_quantity": null,              // 可选：赠品数量
  "minimum_order_amount": 300.00,          // 可选：最低订单金额要求
  "metadata": {                            // 可选：元数据
    "reason": "会员优惠",
    "campaign_id": "SUMMER2024"
  }
}
```

### 2.2 OrderPromotion (数据库模型)
```go
type OrderPromotion struct {
    ID                 ID                     `json:"id"`
    OrderID            ID                     `json:"order_id"`
    PromotionID        ID                     `json:"promotion_id"`
    PromotionName      string                 `json:"promotion_name"`
    PromotionType      string                 `json:"promotion_type"`
    DiscountAmount     float64                `json:"discount_amount"`
    FreeItemID         *ID                    `json:"free_item_id,omitempty"`
    FreeItemName       *string                `json:"free_item_name,omitempty"`
    FreeItemQuantity   *float64               `json:"free_item_quantity,omitempty"`
    MinimumOrderAmount *float64               `json:"minimum_order_amount,omitempty"`
    Metadata           map[string]interface{} `json:"metadata"`
    CreatedAt          time.Time              `json:"created_at"`
}
```

### 2.3 数据库表结构
```sql
CREATE TABLE order_promotions (
    id BIGSERIAL PRIMARY KEY,
    order_id BIGINT NOT NULL,
    promotion_id BIGINT NOT NULL,
    promotion_name VARCHAR(100) NOT NULL,
    promotion_type VARCHAR(50) NOT NULL,
    discount_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.0,
    free_item_id BIGINT NULL,
    free_item_name VARCHAR(100) NULL,
    free_item_quantity DECIMAL(10, 3) NULL,
    minimum_order_amount DECIMAL(12, 2) NULL,
    metadata JSONB NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

## 3. 促销处理流程

### 3.1 完整订单创建 + 促销应用流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant OrderAPI as 订单API
    participant PromotionAPI as 促销API
    participant OrderService as 订单服务
    participant PromotionService as 促销服务
    participant DB as 数据库

    Note over Client, DB: 第一步：创建订单
    Client->>OrderAPI: POST /orders (CreateOrder)
    OrderAPI->>OrderService: CreateOrder()
    OrderService->>DB: 创建订单、订单项、关联燃油交易
    OrderService->>Client: 返回创建的订单

    Note over Client, DB: 第二步：应用促销
    Client->>OrderAPI: POST /orders/{id}/promotions
    OrderAPI->>OrderService: ApplyPromotion()
    OrderService->>DB: 验证订单状态和条件
    OrderService->>DB: 添加促销记录
    OrderService->>DB: 更新订单折扣金额
    OrderService->>Client: 返回促销记录
```

### 3.2 促销计算流程（可选）

系统还提供独立的促销计算服务：

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant CalcAPI as 促销计算API
    participant PromotionSDK as 促销SDK
    participant PromotionDB as 促销数据库

    Client->>CalcAPI: POST /calculator/calculate
    CalcAPI->>PromotionSDK: CalculateDiscounts()
    PromotionSDK->>PromotionDB: 查询促销规则
    PromotionSDK->>PromotionSDK: 计算折扣
    PromotionSDK->>CalcAPI: 返回计算结果
    CalcAPI->>Client: 返回折扣详情
```

## 4. API 接口详情

### 4.1 应用促销接口

**请求**
```http
POST /orders/{id}/promotions
Content-Type: application/json

{
  "promotion_id": 2001,
  "promotion_name": "会员折扣",
  "promotion_type": "discount",
  "discount_amount": 20.00,
  "minimum_order_amount": 300.00,
  "metadata": {
    "reason": "会员优惠"
  }
}
```

**成功响应**
```json
{
  "id": 1,
  "order_id": 123,
  "promotion_id": 2001,
  "promotion_name": "会员折扣",
  "promotion_type": "discount",
  "discount_amount": 20.00,
  "free_item_id": null,
  "free_item_name": null,
  "free_item_quantity": null,
  "minimum_order_amount": 300.00,
  "metadata": {
    "reason": "会员优惠"
  },
  "created_at": "2025-04-10T10:32:00Z"
}
```

### 4.2 促销计算接口

**请求**
```http
POST /calculator/calculate
Content-Type: application/json

{
  "orderId": "WEB-2025-07-13T1734",
  "userId": "user123",
  "orderAmount": 450000,
  "orderTime": "2023-01-01T12:00:00Z",
  "vehicleType": "CAR",
  "items": [
    {
      "itemId": "BP_92",
      "name": "燃油",
      "categoryIds": ["BP_92", "fuel", "CAR"],
      "price": 15000,
      "quantity": 30.125,
      "attributes": {
        "fuel_volume": 30.125
      }
    }
  ]
}
```

**成功响应**
```json
{
  "success": true,
  "orderAmount": 450000,
  "discountedAmount": 435000,
  "totalDiscount": 15000,
  "discountedItems": [
    {
      "itemId": "BP_92",
      "name": "燃油",
      "originalPrice": 15000,
      "discountedPrice": 14500,
      "quantity": 30.125,
      "categoryIds": ["BP_92", "fuel", "CAR"]
    }
  ],
  "appliedPromotions": [
    {
      "promotionId": "volume_discount_001",
      "promotionName": "大量加油折扣",
      "discountType": "FIXED_AMOUNT",
      "discountValue": 500,
      "discountAmount": 15000,
      "description": "25升以上享受1升折扣"
    }
  ]
}
```

## 5. 业务规则和限制

### 5.1 订单状态限制
只有以下状态的订单才能应用促销：
- `new` (新建)
- `processing` (处理中)

### 5.2 促销验证规则
1. **最低订单金额**：如果设置了 `minimum_order_amount`，订单总金额必须达到要求
2. **重复促销检查**：同一个促销不能重复应用到同一订单
3. **订单存在性**：必须确保订单存在且有效

### 5.3 金额计算逻辑
应用促销后的金额计算：
```
最终金额 = 订单总金额 - 折扣总金额 + 税费
```

## 6. 促销类型支持

### 6.1 支持的促销类型
- **discount**: 金额折扣
- **voucher**: 代金券
- **free_item**: 赠品

### 6.2 高级促销规则
- **体积折扣**: 基于加油量的折扣
- **梯度折扣**: 基于消费阶梯的折扣
- **B2B排斥**: 排除企业客户的促销

## 7. 错误处理

### 7.1 常见错误代码
| 错误代码 | 描述 | HTTP状态码 |
|---------|------|-----------|
| `INVALID_ID` | 无效的ID格式 | 400 |
| `ORDER_NOT_FOUND` | 订单不存在 | 404 |
| `INVALID_ORDER_STATUS` | 订单状态不允许应用促销 | 400 |
| `PROMOTION_ALREADY_APPLIED` | 促销已应用 | 400 |
| `MINIMUM_AMOUNT_NOT_MET` | 未达到最低订单金额 | 400 |

### 7.2 错误响应格式
```json
{
  "code": "PROMOTION_ALREADY_APPLIED",
  "message": "该促销已应用于订单",
  "detail": "错误详细信息"
}
```

## 8. 实际使用示例

### 8.1 完整的订单+促销流程
```bash
# 1. 创建订单
curl -X POST /orders \
  -H "Content-Type: application/json" \
  -d '{
    "fuel_transaction_id": 12345,
    "station_id": 1,
    "customer_id": 123,
    "payment_type": "CASH",
    "payment_method": 1
  }'

# 返回: { "id": 789, "order_number": "ORD1250630064512345", ... }

# 2. 应用促销
curl -X POST /orders/789/promotions \
  -H "Content-Type: application/json" \
  -d '{
    "promotion_id": 2001,
    "promotion_name": "会员折扣",
    "promotion_type": "discount",
    "discount_amount": 20.00,
    "minimum_order_amount": 300.00
  }'
```

### 8.2 促销计算示例
```bash
# 计算可用促销
curl -X POST /calculator/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": "WEB-2025-07-13T1734",
    "userId": "user123",
    "orderAmount": 450000,
    "items": [...]
  }'
```

## 9. 总结

BOS 系统的促销处理采用了**分离式设计**：

1. **CreateOrder**: 专注于核心订单创建逻辑
2. **ApplyPromotion**: 专门处理促销应用
3. **PromotionCalculator**: 提供独立的促销计算服务

这种设计确保了系统的**模块化**、**可维护性**和**可扩展性**，便于后续添加更复杂的促销规则和业务逻辑。 