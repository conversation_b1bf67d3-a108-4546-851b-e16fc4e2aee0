{"$schema": "http://json-schema.org/draft-07/schema#", "title": "促销活动基本信息Schema", "description": "促销活动基本信息的JSON Schema验证", "type": "object", "properties": {"id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "促销活动唯一标识"}, "name": {"type": "string", "minLength": 1, "maxLength": 255, "description": "促销活动名称"}, "description": {"type": "string", "maxLength": 1000, "description": "促销活动描述"}, "type": {"type": "string", "enum": ["PERCENTAGE", "FIXED_AMOUNT", "GIFT", "FUEL_DISCOUNT", "STORE_WIDE", "BUNDLE", "DISCOUNT", "COUPON", "CUMULATIVE_COUNT"], "description": "促销活动类型"}, "status": {"type": "string", "enum": ["DRAFT", "PENDING", "APPROVED", "REJECTED", "ACTIVE", "PAUSED", "EXPIRED", "CANCELLED", "DELETED"], "description": "促销活动状态"}, "scope": {"type": "string", "enum": ["ORDER", "PRODUCT", "CATEGORY", "STORE"], "description": "促销活动作用范围"}, "value": {"type": "number", "minimum": 0, "description": "促销活动基础值"}, "min_order_amount": {"type": "number", "minimum": 0, "description": "最低订单金额要求"}, "max_discount_amount": {"type": "number", "minimum": 0, "description": "最大折扣金额限制"}, "start_time": {"type": "string", "format": "date-time", "description": "促销活动开始时间"}, "end_time": {"type": "string", "format": "date-time", "description": "促销活动结束时间"}, "max_use_count": {"type": "integer", "minimum": 0, "description": "最大使用次数，0表示无限制"}, "used_count": {"type": "integer", "minimum": 0, "description": "已使用次数"}, "max_per_user": {"type": "integer", "minimum": 0, "description": "每用户最大使用次数，0表示无限制"}, "target_ids": {"type": "array", "items": {"type": "string"}, "description": "目标ID列表，如商品ID、分类ID等"}, "requires_coupon": {"type": "boolean", "description": "是否需要优惠券"}, "priority": {"type": "integer", "minimum": 0, "maximum": 100, "description": "优先级，数值越大优先级越高"}, "stackable": {"type": "boolean", "description": "是否可与其他促销叠加"}, "for_members": {"type": "boolean", "description": "是否适用于会员"}, "for_non_members": {"type": "boolean", "description": "是否适用于非会员"}, "member_levels": {"type": "array", "items": {"type": "string"}, "description": "适用的会员等级列表"}, "member_tags": {"type": "array", "items": {"type": "string", "enum": ["MEMBER", "NON_MEMBER", "B2B", "VIP", "NEW_USER"]}, "description": "适用的会员标签列表"}, "metadata": {"type": "object", "additionalProperties": true, "description": "扩展元数据"}, "site_ids": {"type": "array", "items": {"type": "string"}, "description": "适用站点ID列表，空则不限制"}, "site_exclude_ids": {"type": "array", "items": {"type": "string"}, "description": "排除站点ID列表"}, "all_sites": {"type": "boolean", "description": "是否适用所有站点"}, "reviewer_id": {"type": "string", "description": "审核人ID"}, "review_time": {"type": "string", "format": "date-time", "description": "审核时间"}, "review_comments": {"type": "string", "description": "审核意见"}, "last_status_change": {"type": "string", "format": "date-time", "description": "最后状态变更时间"}, "status_change_reason": {"type": "string", "description": "状态变更原因"}, "last_changed_by": {"type": "string", "description": "最后修改人"}, "created_by": {"type": "string", "minLength": 1, "description": "创建人"}, "version": {"type": "integer", "minimum": 1, "description": "版本号"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}, "deleted_at": {"type": "string", "format": "date-time", "description": "删除时间"}}, "required": ["name", "type", "status", "scope", "value", "start_time", "end_time", "created_by", "version"], "additionalProperties": false}