package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"

	customerDB "gitlab4.weicheche.cn/indo-bp/customer-service/infra/database"
	customerLg "gitlab4.weicheche.cn/indo-bp/customer-service/infra/logger"
	customer "gitlab4.weicheche.cn/indo-bp/customer-service/init"
	customerService "gitlab4.weicheche.cn/indo-bp/customer-service/service"
	customerDTO "gitlab4.weicheche.cn/indo-bp/customer-service/service/dto"
)

// CustomerHandler 客户管理处理器
type CustomerHandler struct {
	customerService        *customerService.CustomerService
	customerVehicleService *customerService.CustomerVehicleService
	labelService           *customerService.LabelService
	customerLabelService   *customerService.CustomerLabelService
	logger                 customerLg.Logger
}

// NewCustomerHandler 创建新的客户处理器实例
func NewCustomerHandler(
	ctx context.Context,
	dsn string,
) (*CustomerHandler, func(), error) {
	ent, cleanup, err := customerDB.NewEntClient(ctx, &customerDB.Config{
		DSN:             dsn,
		MaxOpenConns:    10,
		MaxIdleConns:    5,
		ConnMaxLifetime: 2 * time.Hour,
	})
	if err != nil {
		return nil, nil, fmt.Errorf("初始化客户服务数据库失败: %v", err)
	}

	customerLog := customerLg.NewLogger(&customerLg.Config{Level: "debug"})
	cusManager := customer.NewManager(ent, customerLog)

	return &CustomerHandler{
		customerService:        cusManager.GetCustomerService(),
		customerVehicleService: cusManager.GetCustomerVehicleService(),
		labelService:           cusManager.GetLabelService(),
		customerLabelService:   cusManager.GetCustomerLabelService(),
		logger:                 customerLog,
	}, cleanup, nil
}

// CreateCustomer 创建客户
// @Summary 创建新客户
// @Description 创建新的客户档案
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param customer body customerDTO.CreateCustomerRequest true "客户信息"
// @Success 201 {object} customerDTO.CustomerResponse "创建成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/customers [post]
func (h *CustomerHandler) CreateCustomer(c echo.Context) error {
	var req customerDTO.CreateCustomerRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		h.logger.Error("绑定客户创建请求参数失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "BIND_ERROR",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
	}

	// 调用客户服务创建客户
	customer, err := h.customerService.CreateCustomer(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("创建客户失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusInternalServerError, customerDTO.ErrorResponse{
			Code:    "CREATE_CUSTOMER_ERROR",
			Message: "创建客户失败",
			Details: err.Error(),
		})
	}

	return c.JSON(http.StatusOK, customer)
}

// GetCustomer 根据ID获取客户
// @Summary 获取客户信息
// @Description 根据客户ID获取客户详细信息
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param id path int true "客户ID"
// @Success 200 {object} customerDTO.CustomerResponse "获取成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 404 {object} customerDTO.ErrorResponse "客户不存在"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/customers/{id} [get]
func (h *CustomerHandler) GetCustomer(c echo.Context) error {
	// 解析客户ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		h.logger.Error("解析客户ID失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "INVALID_ID",
			Message: "客户ID格式错误",
			Details: "ID必须是有效的数字",
		})
	}

	// 构建请求
	req := &customerDTO.GetCustomerByIDRequest{
		ID: id,
	}

	// 调用客户服务获取客户
	customer, err := h.customerService.GetCustomerByID(c.Request().Context(), req)
	if err != nil {
		h.logger.Error("获取客户失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusNotFound, customerDTO.ErrorResponse{
			Code:    "CUSTOMER_NOT_FOUND",
			Message: "客户不存在",
			Details: err.Error(),
		})
	}

	return c.JSON(http.StatusOK, customer)
}

// GetCustomerByPhone 根据手机号获取客户
// @Summary 根据手机号获取客户
// @Description 根据手机号获取客户信息
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param phone path string true "手机号"
// @Success 200 {object} customerDTO.CustomerResponse "获取成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 404 {object} customerDTO.ErrorResponse "客户不存在"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/customers/phone/{phone} [get]
func (h *CustomerHandler) GetCustomerByPhone(c echo.Context) error {
	phone := c.Param("phone")
	if phone == "" {
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "INVALID_PHONE",
			Message: "手机号不能为空",
		})
	}

	// 构建请求
	req := &customerDTO.GetCustomerByPhoneRequest{
		Phone: phone,
	}

	// 调用客户服务获取客户
	customer, err := h.customerService.GetCustomerByPhone(c.Request().Context(), req)
	if err != nil {
		h.logger.Error("根据手机号获取客户失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusNotFound, customerDTO.ErrorResponse{
			Code:    "CUSTOMER_NOT_FOUND",
			Message: "客户不存在",
			Details: err.Error(),
		})
	}

	return c.JSON(http.StatusOK, customer)
}

// ListCustomers 获取客户列表
// @Summary 获取客户列表
// @Description 分页获取客户列表，支持多种过滤条件
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param offset query int false "偏移量" default(0)
// @Param limit query int false "每页数量" default(10)
// @Param hos_id query int false "HOS系统ID"
// @Param name query string false "客户名称"
// @Param phone query string false "手机号"
// @Param email query string false "邮箱"
// @Param order_by query string false "排序字段" Enums(id,hos_id,name,email,phone,created_at,updated_at)
// @Param order_desc query bool false "是否降序" default(false)
// @Success 200 {object} customerDTO.ListCustomersResponse "获取成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/customers [get]
func (h *CustomerHandler) ListCustomers(c echo.Context) error {
	// 解析查询参数
	req := &customerDTO.ListCustomersRequest{}

	// 解析分页参数
	if offsetStr := c.QueryParam("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil {
			req.Offset = offset
		}
	}

	if limitStr := c.QueryParam("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			req.Limit = limit
		}
	} else {
		req.Limit = 10 // 默认每页10条
	}

	// 解析过滤条件
	if hosIDStr := c.QueryParam("hos_id"); hosIDStr != "" {
		if hosID, err := strconv.ParseInt(hosIDStr, 10, 64); err == nil {
			req.HosID = &hosID
		}
	}

	if name := c.QueryParam("name"); name != "" {
		req.Name = &name
	}

	if phone := c.QueryParam("phone"); phone != "" {
		req.Phone = &phone
	}

	if email := c.QueryParam("email"); email != "" {
		req.Email = &email
	}

	// 解析排序参数
	if orderBy := c.QueryParam("order_by"); orderBy != "" {
		req.OrderBy = &orderBy
	}

	if orderDescStr := c.QueryParam("order_desc"); orderDescStr == "true" {
		req.OrderDesc = true
	}

	// 调用客户服务获取客户列表
	customers, err := h.customerService.ListCustomers(c.Request().Context(), req)
	if err != nil {
		h.logger.Error("获取客户列表失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusInternalServerError, customerDTO.ErrorResponse{
			Code:    "LIST_CUSTOMERS_ERROR",
			Message: "获取客户列表失败",
			Details: err.Error(),
		})
	}

	return c.JSON(http.StatusOK, customers)
}

// UpdateCustomer 更新客户信息
// @Summary 更新客户信息
// @Description 更新指定客户的信息
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param id path int true "客户ID"
// @Param customer body customerDTO.UpdateCustomerRequest true "更新的客户信息"
// @Success 200 {object} customerDTO.CustomerResponse "更新成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 404 {object} customerDTO.ErrorResponse "客户不存在"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/customers/{id} [put]
func (h *CustomerHandler) UpdateCustomer(c echo.Context) error {
	// 解析客户ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "INVALID_ID",
			Message: "客户ID格式错误",
		})
	}

	var req customerDTO.UpdateCustomerRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		h.logger.Error("绑定客户更新请求参数失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "BIND_ERROR",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
	}

	// 设置客户ID
	req.ID = id

	// 调用客户服务更新客户
	customer, err := h.customerService.UpdateCustomer(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("更新客户失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusInternalServerError, customerDTO.ErrorResponse{
			Code:    "UPDATE_CUSTOMER_ERROR",
			Message: "更新客户失败",
			Details: err.Error(),
		})
	}

	h.logger.Info("客户更新成功", customerLg.Field{Key: "customer_id", Value: id})
	return c.JSON(http.StatusOK, customer)
}

// CountCustomers 统计客户数量
// @Summary 统计客户数量
// @Description 根据过滤条件统计客户总数
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param hos_id query int false "HOS系统ID"
// @Param name query string false "客户名称"
// @Param phone query string false "手机号"
// @Param email query string false "邮箱"
// @Success 200 {object} customerDTO.CountCustomersResponse "统计成功"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/customers/count [get]
func (h *CustomerHandler) CountCustomers(c echo.Context) error {
	// 解析查询参数
	req := &customerDTO.CountCustomersRequest{}

	// 解析过滤条件
	if hosIDStr := c.QueryParam("hos_id"); hosIDStr != "" {
		if hosID, err := strconv.ParseInt(hosIDStr, 10, 64); err == nil {
			req.HosID = &hosID
		}
	}

	if name := c.QueryParam("name"); name != "" {
		req.Name = &name
	}

	if phone := c.QueryParam("phone"); phone != "" {
		req.Phone = &phone
	}

	if email := c.QueryParam("email"); email != "" {
		req.Email = &email
	}

	// 调用客户服务统计客户数量
	count, err := h.customerService.CountCustomers(c.Request().Context(), req)
	if err != nil {
		h.logger.Error("统计客户数量失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusInternalServerError, customerDTO.ErrorResponse{
			Code:    "COUNT_CUSTOMERS_ERROR",
			Message: "统计客户数量失败",
			Details: err.Error(),
		})
	}

	return c.JSON(http.StatusOK, count)
}

// ===== 车辆管理相关接口 =====

// CreateVehicle 创建车辆
// @Summary 创建新车辆
// @Description 为客户创建新的车辆档案
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param vehicle body customerDTO.CreateVehicleRequest true "车辆信息"
// @Success 201 {object} customerDTO.VehicleResponse "创建成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/vehicles [post]
func (h *CustomerHandler) CreateVehicle(c echo.Context) error {
	var req customerDTO.CreateVehicleRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		h.logger.Error("绑定车辆创建请求参数失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "BIND_ERROR",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
	}

	// 调用车辆服务创建车辆
	vehicle, err := h.customerVehicleService.CreateVehicle(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("创建车辆失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusInternalServerError, customerDTO.ErrorResponse{
			Code:    "CREATE_VEHICLE_ERROR",
			Message: "创建车辆失败",
			Details: err.Error(),
		})
	}

	h.logger.Info("车辆创建成功", customerLg.Field{Key: "vehicle_id", Value: vehicle.ID})
	return c.JSON(http.StatusCreated, vehicle)
}

// GetVehicle 根据ID获取车辆
// @Summary 获取车辆信息
// @Description 根据车辆ID获取车辆详细信息
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param id path int true "车辆ID"
// @Success 200 {object} customerDTO.VehicleResponse "获取成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 404 {object} customerDTO.ErrorResponse "车辆不存在"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/vehicles/{id} [get]
func (h *CustomerHandler) GetVehicle(c echo.Context) error {
	// 解析车辆ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		h.logger.Error("解析车辆ID失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "INVALID_ID",
			Message: "车辆ID格式错误",
			Details: "ID必须是有效的数字",
		})
	}

	// 构建请求
	req := &customerDTO.GetVehicleByIDRequest{
		ID: id,
	}

	// 调用车辆服务获取车辆
	vehicle, err := h.customerVehicleService.GetVehicleByID(c.Request().Context(), req)
	if err != nil {
		h.logger.Error("获取车辆失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusNotFound, customerDTO.ErrorResponse{
			Code:    "VEHICLE_NOT_FOUND",
			Message: "车辆不存在",
			Details: err.Error(),
		})
	}

	return c.JSON(http.StatusOK, vehicle)
}

// GetVehicleByNumber 根据车牌号获取车辆
// @Summary 根据车牌号获取车辆
// @Description 根据车牌号获取车辆信息
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param number path string true "车牌号"
// @Success 200 {object} customerDTO.VehicleResponse "获取成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 404 {object} customerDTO.ErrorResponse "车辆不存在"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/vehicles/number/{number} [get]
func (h *CustomerHandler) GetVehicleByNumber(c echo.Context) error {
	number := c.Param("number")
	if number == "" {
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "INVALID_NUMBER",
			Message: "车牌号不能为空",
		})
	}

	// 构建请求
	req := &customerDTO.GetVehicleByNumberRequest{
		Number: number,
	}

	// 调用车辆服务获取车辆
	vehicle, err := h.customerVehicleService.GetVehicleByNumber(c.Request().Context(), req)
	if err != nil {
		h.logger.Error("根据车牌号获取车辆失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusNotFound, customerDTO.ErrorResponse{
			Code:    "VEHICLE_NOT_FOUND",
			Message: "车辆不存在",
			Details: err.Error(),
		})
	}

	return c.JSON(http.StatusOK, vehicle)
}

// ListVehiclesByCustomer 获取客户的车辆列表
// @Summary 获取客户车辆列表
// @Description 根据客户ID获取其名下的车辆列表
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param customer_id path int true "客户ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param number query string false "车牌号（模糊查询）"
// @Param model query string false "车型（模糊查询）"
// @Param order_by query string false "排序字段" Enums(id,number,model,created_at,updated_at)
// @Param order_desc query bool false "是否降序" default(false)
// @Success 200 {object} customerDTO.ListVehiclesResponse "获取成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/customers/{customer_id}/vehicles [get]
func (h *CustomerHandler) ListVehiclesByCustomer(c echo.Context) error {
	// 解析客户ID
	customerIDStr := c.Param("customer_id")
	customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "INVALID_CUSTOMER_ID",
			Message: "客户ID格式错误",
		})
	}

	// 解析查询参数
	req := &customerDTO.ListVehiclesByCustomerIDRequest{
		CustomerID: customerID,
	}

	// 解析分页参数
	if pageStr := c.QueryParam("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		}
	} else {
		req.Page = 1 // 默认第1页
	}

	if pageSizeStr := c.QueryParam("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 {
			req.PageSize = pageSize
		}
	} else {
		req.PageSize = 20 // 默认每页20条
	}

	// 解析过滤条件
	if number := c.QueryParam("number"); number != "" {
		req.Number = &number
	}

	if model := c.QueryParam("model"); model != "" {
		req.Model = &model
	}

	// 解析排序参数
	if orderBy := c.QueryParam("order_by"); orderBy != "" {
		req.OrderBy = &orderBy
	}

	if orderDescStr := c.QueryParam("order_desc"); orderDescStr == "true" {
		req.OrderDesc = true
	}

	// 调用车辆服务获取车辆列表
	vehicles, err := h.customerVehicleService.ListVehiclesByCustomerID(c.Request().Context(), req)
	if err != nil {
		h.logger.Error("获取客户车辆列表失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusInternalServerError, customerDTO.ErrorResponse{
			Code:    "LIST_VEHICLES_ERROR",
			Message: "获取车辆列表失败",
			Details: err.Error(),
		})
	}

	return c.JSON(http.StatusOK, vehicles)
}

// UpdateVehicle 更新车辆信息
// @Summary 更新车辆信息
// @Description 更新指定车辆的信息
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param id path int true "车辆ID"
// @Param vehicle body customerDTO.UpdateVehicleRequest true "更新的车辆信息"
// @Success 200 {object} customerDTO.VehicleResponse "更新成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 404 {object} customerDTO.ErrorResponse "车辆不存在"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/vehicles/{id} [put]
func (h *CustomerHandler) UpdateVehicle(c echo.Context) error {
	// 解析车辆ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "INVALID_ID",
			Message: "车辆ID格式错误",
		})
	}

	var req customerDTO.UpdateVehicleRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		h.logger.Error("绑定车辆更新请求参数失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "BIND_ERROR",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
	}

	// 设置车辆ID
	req.ID = id

	// 调用车辆服务更新车辆
	vehicle, err := h.customerVehicleService.UpdateVehicle(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("更新车辆失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusInternalServerError, customerDTO.ErrorResponse{
			Code:    "UPDATE_VEHICLE_ERROR",
			Message: "更新车辆失败",
			Details: err.Error(),
		})
	}

	h.logger.Info("车辆更新成功", customerLg.Field{Key: "vehicle_id", Value: id})
	return c.JSON(http.StatusOK, vehicle)
}

// ===== 标签管理相关接口 =====

// CreateLabel 创建标签
// @Summary 创建新标签
// @Description 创建新的客户标签
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param label body customerDTO.CreateLabelRequest true "标签信息"
// @Success 201 {object} customerDTO.LabelResponse "创建成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/labels [post]
func (h *CustomerHandler) CreateLabel(c echo.Context) error {
	var req customerDTO.CreateLabelRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		h.logger.Error("绑定标签创建请求参数失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "BIND_ERROR",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
	}

	// 调用标签服务创建标签
	label, err := h.labelService.CreateLabel(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("创建标签失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusInternalServerError, customerDTO.ErrorResponse{
			Code:    "CREATE_LABEL_ERROR",
			Message: "创建标签失败",
			Details: err.Error(),
		})
	}

	h.logger.Info("标签创建成功", customerLg.Field{Key: "label_id", Value: label.ID})
	return c.JSON(http.StatusCreated, label)
}

// ListLabels 获取标签列表
// @Summary 获取标签列表
// @Description 分页获取标签列表，支持按名称过滤
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param offset query int false "偏移量" default(0)
// @Param limit query int false "每页数量" default(10)
// @Param name query string false "标签名称"
// @Param order_by query string false "排序字段" Enums(id,name,created_at,updated_at)
// @Param order_desc query bool false "是否降序" default(false)
// @Success 200 {object} customerDTO.ListLabelsResponse "获取成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/labels [get]
func (h *CustomerHandler) ListLabels(c echo.Context) error {
	// 解析查询参数
	req := &customerDTO.ListLabelsRequest{}

	// 解析分页参数
	if offsetStr := c.QueryParam("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil {
			req.Offset = offset
		}
	}

	if limitStr := c.QueryParam("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			req.Limit = limit
		}
	} else {
		req.Limit = 10 // 默认每页10条
	}

	// 解析过滤条件
	if name := c.QueryParam("name"); name != "" {
		req.Name = &name
	}

	// 解析排序参数
	if orderBy := c.QueryParam("order_by"); orderBy != "" {
		req.OrderBy = &orderBy
	}

	if orderDescStr := c.QueryParam("order_desc"); orderDescStr == "true" {
		req.OrderDesc = true
	}

	// 调用标签服务获取标签列表
	labels, err := h.labelService.ListLabels(c.Request().Context(), req)
	if err != nil {
		h.logger.Error("获取标签列表失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusInternalServerError, customerDTO.ErrorResponse{
			Code:    "LIST_LABELS_ERROR",
			Message: "获取标签列表失败",
			Details: err.Error(),
		})
	}

	return c.JSON(http.StatusOK, labels)
}

// AssignLabelToCustomer 给客户分配标签
// @Summary 给客户分配标签
// @Description 将指定标签分配给客户
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param assignment body customerDTO.AssignLabelToCustomerRequest true "分配信息"
// @Success 201 {object} customerDTO.CustomerLabelResponse "分配成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/customer-labels [post]
func (h *CustomerHandler) AssignLabelToCustomer(c echo.Context) error {
	var req customerDTO.AssignLabelToCustomerRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		h.logger.Error("绑定标签分配请求参数失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "BIND_ERROR",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
	}

	// 调用客户标签服务分配标签
	assignment, err := h.customerLabelService.AssignLabelToCustomer(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("分配标签给客户失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusInternalServerError, customerDTO.ErrorResponse{
			Code:    "ASSIGN_LABEL_ERROR",
			Message: "分配标签失败",
			Details: err.Error(),
		})
	}

	h.logger.Info("标签分配成功", customerLg.Field{Key: "customer_id", Value: req.CustomerID}, customerLg.Field{Key: "label_id", Value: req.LabelID})

	return c.JSON(http.StatusCreated, assignment)
}

// GetCustomerLabels 获取客户的标签列表
// @Summary 获取客户标签
// @Description 获取指定客户的所有标签
// @Tags 客户服务
// @Accept json
// @Produce json
// @Param customer_id path int true "客户ID"
// @Success 200 {object} customerDTO.GetCustomerLabelsResponse "获取成功"
// @Failure 400 {object} customerDTO.ErrorResponse "请求参数错误"
// @Failure 500 {object} customerDTO.ErrorResponse "内部服务器错误"
// @Router /api/v1/customers/{customer_id}/labels [get]
func (h *CustomerHandler) GetCustomerLabels(c echo.Context) error {
	// 解析客户ID
	customerIDStr := c.Param("customer_id")
	customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, customerDTO.ErrorResponse{
			Code:    "INVALID_CUSTOMER_ID",
			Message: "客户ID格式错误",
		})
	}

	// 构建请求
	req := &customerDTO.GetCustomerLabelsRequest{
		CustomerID: customerID,
	}

	// 调用客户标签服务获取客户标签
	labels, err := h.customerLabelService.GetCustomerLabels(c.Request().Context(), req)
	if err != nil {
		h.logger.Error("获取客户标签失败", customerLg.Field{Key: "error", Value: err})
		return c.JSON(http.StatusInternalServerError, customerDTO.ErrorResponse{
			Code:    "GET_CUSTOMER_LABELS_ERROR",
			Message: "获取客户标签失败",
			Details: err.Error(),
		})
	}

	return c.JSON(http.StatusOK, labels)
}
