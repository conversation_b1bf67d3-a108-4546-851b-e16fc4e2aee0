package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"

	"gitlab4.weicheche.cn/indo-bp/bos/internal/service"
)

// DashboardHandler Dashboard处理器
type DashboardHandler struct {
	service *service.DashboardService
}

// NewDashboardHandler 创建新的Dashboard处理器
func NewDashboardHandler(service *service.DashboardService) *DashboardHandler {
	return &DashboardHandler{
		service: service,
	}
}

// APIResponse 统一API响应结构
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   *APIError   `json:"error,omitempty"`
	Meta    *APIMeta    `json:"meta"`
}

// APIError API错误结构
type APIError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

// APIMeta API元数据结构
type APIMeta struct {
	GeneratedAt      string `json:"generated_at"`
	ProcessingTimeMs int64  `json:"processing_time_ms"`
	DataSource       string `json:"data_source"`
	Version          string `json:"version"`
	QueryDate        string `json:"query_date,omitempty"`
}

// GetDashboardSummary 获取Dashboard汇总数据
// @Summary 获取Dashboard汇总数据
// @Description 获取指定日期的站点运营汇总数据
// @Tags Dashboard
// @Accept json
// @Produce json
// @Param station_id query int false "站点ID"
// @Param date query string false "查询日期，格式YYYY-MM-DD"
// @Success 200 {object} APIResponse "成功响应"
// @Failure 400 {object} APIResponse "参数错误"
// @Failure 401 {object} APIResponse "未授权"
// @Failure 500 {object} APIResponse "服务器错误"
// @Router /dashboard/summary [get]
func (h *DashboardHandler) GetDashboardSummary(c echo.Context) error {
	startTime := time.Now()
	
	// 解析查询参数
	stationID, err := h.parseStationID(c)
	if err != nil {
		return h.errorResponse(c, http.StatusBadRequest, "INVALID_PARAMETER", err.Error(), startTime)
	}
	
	dateStr := c.QueryParam("date")
	
	// 验证站点访问权限 - 添加容错处理
	// 从JWT token中获取用户ID
	userID := h.getUserIDFromContext(c)
	if userID != "" {
		if err := h.service.ValidateStationAccess(c.Request().Context(), userID, stationID); err != nil {
			// 权限验证失败时记录日志但不阻止访问，允许继续获取数据
			fmt.Printf("站点访问权限验证失败，但允许继续访问: %v\n", err)
		}
	}
	
	// 获取Dashboard汇总数据
	summary, err := h.service.GetDashboardSummary(c.Request().Context(), stationID, dateStr)
	if err != nil {
		return h.handleServiceError(c, err, startTime)
	}
	
	return h.successResponse(c, "Dashboard汇总数据获取成功", summary, "realtime", summary.QueryDate, startTime)
}

// GetSalesTrend 获取销售趋势数据
// @Summary 获取销售趋势数据
// @Description 获取指定日期向前N天的销售趋势数据
// @Tags Dashboard
// @Accept json
// @Produce json
// @Param station_id query int false "站点ID"
// @Param date query string false "结束日期，格式YYYY-MM-DD"
// @Param days query int false "查询天数，默认7天"
// @Success 200 {object} APIResponse "成功响应"
// @Failure 400 {object} APIResponse "参数错误"
// @Failure 401 {object} APIResponse "未授权"
// @Failure 500 {object} APIResponse "服务器错误"
// @Router /dashboard/sales-trend [get]
func (h *DashboardHandler) GetSalesTrend(c echo.Context) error {
	startTime := time.Now()
	
	// 解析查询参数
	stationID, err := h.parseStationID(c)
	if err != nil {
		return h.errorResponse(c, http.StatusBadRequest, "INVALID_PARAMETER", err.Error(), startTime)
	}
	
	dateStr := c.QueryParam("date")
	
	// 解析天数参数
	days := 7 // 默认7天
	if daysStr := c.QueryParam("days"); daysStr != "" {
		if parsedDays, err := strconv.Atoi(daysStr); err == nil && parsedDays > 0 {
			days = parsedDays
		} else {
			return h.errorResponse(c, http.StatusBadRequest, "INVALID_PARAMETER", "天数参数无效", startTime)
		}
	}
	
	// 验证站点访问权限 - 添加容错处理
	userID := h.getUserIDFromContext(c)
	if userID != "" {
		if err := h.service.ValidateStationAccess(c.Request().Context(), userID, stationID); err != nil {
			fmt.Printf("站点访问权限验证失败，但允许继续访问: %v\n", err)
		}
	}
	
	// 获取销售趋势数据
	trendData, err := h.service.GetSalesTrend(c.Request().Context(), stationID, dateStr, days)
	if err != nil {
		return h.handleServiceError(c, err, startTime)
	}
	
	return h.successResponse(c, "销售趋势数据获取成功", trendData, "aggregated", trendData.QueryDate, startTime)
}

// GetFuelSalesMix 获取燃油销售组合详情
// @Summary 获取燃油销售组合详情
// @Description 获取指定日期的燃油销售组合详细数据
// @Tags Dashboard
// @Accept json
// @Produce json
// @Param station_id query int false "站点ID"
// @Param date query string false "查询日期，格式YYYY-MM-DD"
// @Success 200 {object} APIResponse "成功响应"
// @Failure 400 {object} APIResponse "参数错误"
// @Failure 401 {object} APIResponse "未授权"
// @Failure 500 {object} APIResponse "服务器错误"
// @Router /dashboard/fuel-sales-mix [get]
func (h *DashboardHandler) GetFuelSalesMix(c echo.Context) error {
	startTime := time.Now()
	
	// 解析查询参数
	stationID, err := h.parseStationID(c)
	if err != nil {
		return h.errorResponse(c, http.StatusBadRequest, "INVALID_PARAMETER", err.Error(), startTime)
	}
	
	dateStr := c.QueryParam("date")
	
	// 验证站点访问权限 - 添加容错处理
	userID := h.getUserIDFromContext(c)
	if userID != "" {
		if err := h.service.ValidateStationAccess(c.Request().Context(), userID, stationID); err != nil {
			fmt.Printf("站点访问权限验证失败，但允许继续访问: %v\n", err)
		}
	}
	
	// 获取燃油销售组合数据
	fuelSalesDetail, err := h.service.GetFuelSalesDetail(c.Request().Context(), stationID, dateStr)
	if err != nil {
		return h.handleServiceError(c, err, startTime)
	}
	
	return h.successResponse(c, "燃油销售组合数据获取成功", fuelSalesDetail, "realtime", fuelSalesDetail.QueryDate, startTime)
}

// parseStationID 解析站点ID参数（支持HOS全站点模式）
func (h *DashboardHandler) parseStationID(c echo.Context) (int64, error) {
	stationIDStr := c.QueryParam("station_id")

	// HOS系统优化：如果没有提供station_id，检查是否为HOS系统全站点查询
	if stationIDStr == "" {
		// 检查是否有site_ids参数（HOS系统多站点查询标识）
		siteIDsStr := c.QueryParam("site_ids")
		if siteIDsStr != "" {
			// HOS系统全站点查询，返回特殊值0表示查询所有站点
			fmt.Printf("HOS系统全站点查询模式，site_ids: %s\n", siteIDsStr)
			return 0, nil // 0表示全站点查询
		}

		// 传统模式：从用户信息中获取默认站点ID
		return h.service.GetDefaultStationID(c.Request().Context(), 1)
	}

	// 解析站点ID
	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("站点ID格式无效")
	}

	if stationID <= 0 {
		return 0, fmt.Errorf("站点ID必须大于0")
	}

	return stationID, nil
}

// handleServiceError 处理服务层错误
func (h *DashboardHandler) handleServiceError(c echo.Context, err error, startTime time.Time) error {
	errMsg := err.Error()
	
	// 根据错误类型返回不同的HTTP状态码
	switch {
	case contains(errMsg, "日期格式无效"):
		return h.errorResponse(c, http.StatusBadRequest, "INVALID_DATE_FORMAT", errMsg, startTime)
	case contains(errMsg, "查询日期"):
		return h.errorResponse(c, http.StatusBadRequest, "DATE_OUT_OF_RANGE", errMsg, startTime)
	case contains(errMsg, "天数参数无效"):
		return h.errorResponse(c, http.StatusBadRequest, "INVALID_PARAMETER", errMsg, startTime)
	case contains(errMsg, "站点不存在"):
		return h.errorResponse(c, http.StatusNotFound, "STATION_NOT_FOUND", errMsg, startTime)
	default:
		return h.errorResponse(c, http.StatusInternalServerError, "INTERNAL_ERROR", "服务器内部错误", startTime)
	}
}

// successResponse 成功响应
func (h *DashboardHandler) successResponse(c echo.Context, message string, data interface{}, dataSource, queryDate string, startTime time.Time) error {
	processingTime := time.Since(startTime).Milliseconds()
	
	response := APIResponse{
		Success: true,
		Message: message,
		Data:    data,
		Meta: &APIMeta{
			GeneratedAt:      time.Now().Format("2006-01-02T15:04:05-07:00"),
			ProcessingTimeMs: processingTime,
			DataSource:       dataSource,
			Version:          "1.0",
			QueryDate:        queryDate,
		},
	}
	
	return c.JSON(http.StatusOK, response)
}

// errorResponse 错误响应
func (h *DashboardHandler) errorResponse(c echo.Context, status int, code, message string, startTime time.Time) error {
	processingTime := time.Since(startTime).Milliseconds()
	
	response := APIResponse{
		Success: false,
		Message: "操作失败",
		Error: &APIError{
			Code:    code,
			Message: message,
		},
		Meta: &APIMeta{
			GeneratedAt:      time.Now().Format("2006-01-02T15:04:05-07:00"),
			ProcessingTimeMs: processingTime,
			Version:          "1.0",
		},
	}
	
	return c.JSON(status, response)
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || (len(s) > len(substr) && 
		(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr || 
		 indexOfSubstring(s, substr) != -1)))
}

// indexOfSubstring 查找子字符串位置
func indexOfSubstring(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// getUserIDFromContext 从echo context中获取用户ID
func (h *DashboardHandler) getUserIDFromContext(c echo.Context) string {
	// 尝试从JWT中间件设置的context中获取用户ID
	if userID := c.Get("user_id"); userID != nil {
		if userIDStr, ok := userID.(string); ok {
			return userIDStr
		}
	}

	// 如果获取失败，返回空字符串
	return ""
}