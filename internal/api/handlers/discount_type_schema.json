{"$schema": "http://json-schema.org/draft-07/schema#", "title": "折扣配置Schema", "description": "折扣配置的JSON Schema验证，支持梯度折扣", "type": "object", "properties": {"id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "折扣唯一标识"}, "promotion_id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "关联的促销活动ID"}, "coupon_id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "关联的优惠券ID，可选"}, "code": {"type": "string", "description": "折扣代码"}, "type": {"type": "string", "enum": ["PERCENTAGE", "FIXED_AMOUNT", "FREE_SHIPPING", "FREE_ITEM", "BUY_X_GET_Y"], "description": "折扣类型"}, "discount_value": {"type": "number", "minimum": 0, "description": "折扣值，可以是百分比或固定金额"}, "used_count": {"type": "integer", "minimum": 0, "description": "已使用次数"}, "metadata": {"type": "object", "additionalProperties": true, "description": "折扣相关的元数据配置"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}, "gradient_level": {"type": "integer", "minimum": 1, "description": "梯度级别，用于梯度折扣"}, "threshold_type": {"type": "string", "enum": ["QUANTITY", "AMOUNT", "VOLUME"], "description": "门槛类型：数量、金额或体积"}, "threshold_value": {"type": "number", "minimum": 0, "description": "门槛数值"}, "max_discount_amount": {"type": "number", "minimum": 0, "description": "最大折扣金额限制"}}, "required": ["id", "promotion_id", "type", "discount_value"], "additionalProperties": false, "definitions": {"GradientDiscountDTO": {"type": "object", "title": "梯度折扣", "description": "支持多层级的梯度折扣配置", "properties": {"id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "梯度折扣唯一标识"}, "promotion_id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "关联的促销活动ID"}, "name": {"type": "string", "minLength": 1, "description": "梯度折扣名称"}, "description": {"type": "string", "description": "梯度折扣描述"}, "threshold_type": {"type": "string", "enum": ["QUANTITY", "AMOUNT", "VOLUME"], "description": "门槛类型：数量、金额或体积"}, "levels": {"type": "array", "items": {"$ref": "#/definitions/GradientLevelDTO"}, "minItems": 1, "description": "梯度级别列表，按门槛值升序排列"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}, "required": ["id", "promotion_id", "name", "threshold_type", "levels"], "additionalProperties": false}, "GradientLevelDTO": {"type": "object", "title": "梯度级别", "description": "梯度折扣中的单个级别配置", "properties": {"level": {"type": "integer", "minimum": 1, "description": "级别序号，从1开始"}, "threshold_value": {"type": "number", "minimum": 0, "description": "门槛值，达到此值触发该级别折扣"}, "discount_type": {"type": "string", "enum": ["PERCENTAGE", "FIXED_AMOUNT", "FREE_SHIPPING", "FREE_ITEM", "BUY_X_GET_Y"], "description": "折扣类型"}, "discount_value": {"type": "number", "minimum": 0, "description": "折扣值"}, "max_discount_amount": {"type": "number", "minimum": 0, "description": "该级别的最大折扣金额"}, "description": {"type": "string", "description": "级别描述"}}, "required": ["level", "threshold_value", "discount_type", "discount_value"], "additionalProperties": false}}}