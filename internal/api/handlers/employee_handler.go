package handlers

import (
	"context"
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"

	"gitlab4.weicheche.cn/indo-bp/bos/internal/models"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"
)

// EmployeeHandler 处理与员工相关的请求
type EmployeeHandler struct {
	employeeService service.EmployeeService
}

// NewEmployeeHandler 创建新的员工处理器
func NewEmployeeHandler(employeeService service.EmployeeService) *EmployeeHandler {
	return &EmployeeHandler{
		employeeService: employeeService,
	}
}

// LoginRequest 定义员工登录请求结构
type LoginRequest struct {
	EmployeeNo string `json:"employee_no"`
	Password   string `json:"password"`
}

// LoginResponse 定义员工登录响应结构
type LoginResponse struct {
	Token    string              `json:"token"`
	Employee repository.Employee `json:"employee"`
}

// Login 处理员工登录请求
// @Summary 员工登录
// @Description 员工通过员工号和密码登录系统
// @Tags 员工
// @Accept json
// @Produce json
// @Param request body LoginRequest true "登录信息"
// @Success 200 {object} LoginResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /employee/login [post]
func (h *EmployeeHandler) Login(c echo.Context) error {
	ctx := context.Background()

	// 解析请求
	req := new(LoginRequest)
	if err := c.Bind(req); err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "无效的请求格式",
			Detail:  err.Error(),
		})
	}

	// 参数验证
	if req.EmployeeNo == "" || req.Password == "" {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "MISSING_REQUIRED_FIELDS",
			Message: "缺少必填字段",
			Detail:  "员工编号和密码不能为空",
		})
	}

	// 调用服务进行身份验证
	employee, err := h.employeeService.AuthenticateEmployee(ctx, req.EmployeeNo, req.Password)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Code:    "AUTHENTICATION_FAILED",
			Message: "认证失败",
			Detail:  "员工编号或密码错误",
		})
	}

	// 生成JWT令牌 (实际项目中需要实现JWT生成逻辑)
	token := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." // 示例令牌，实际项目中应该使用JWT库生成

	// 返回成功响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    200,
		"message": "登录成功",
		"data": LoginResponse{
			Token:    token,
			Employee: employee,
		},
	})
}

// CreateEmployeeRequest 定义创建员工请求结构
type CreateEmployeeRequest struct {
	EmployeeNo string `json:"employee_no"`
	Name       string `json:"name"`
	Password   string `json:"password"`
}

// CreateEmployee 处理添加员工请求
// @Summary 添加员工
// @Description 添加新员工到系统
// @Tags 员工
// @Accept json
// @Produce json
// @Param request body CreateEmployeeRequest true "员工信息"
// @Success 200 {object} repository.Employee
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /employee [post]
func (h *EmployeeHandler) CreateEmployee(c echo.Context) error {
	ctx := context.Background()

	// 解析请求
	req := new(CreateEmployeeRequest)
	if err := c.Bind(req); err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "无效的请求格式",
			Detail:  err.Error(),
		})
	}

	// 参数验证
	if req.EmployeeNo == "" || req.Name == "" || req.Password == "" {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "MISSING_REQUIRED_FIELDS",
			Message: "缺少必填字段",
			Detail:  "员工编号、姓名和密码不能为空",
		})
	}

	// 创建员工对象
	employee := repository.Employee{
		EmployeeNo: req.EmployeeNo,
		Name:       req.Name,
		Password:   req.Password,
	}

	// 调用服务创建员工
	createdEmployee, err := h.employeeService.CreateEmployee(ctx, employee)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "CREATE_EMPLOYEE_FAILED",
			Message: "创建员工失败",
			Detail:  err.Error(),
		})
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    200,
		"message": "添加成功",
		"data":    createdEmployee,
	})
}

// ListEmployeesResponse 定义获取员工列表响应结构
type ListEmployeesResponse struct {
	Total     int                   `json:"total"`
	Page      int                   `json:"page"`
	Size      int                   `json:"size"`
	Employees []repository.Employee `json:"employees"`
}

// ListEmployees 处理获取员工列表请求
// @Summary 获取员工列表
// @Description 获取员工列表，支持分页和筛选
// @Tags 员工
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param size query int false "每页记录数，默认10"
// @Param name query string false "按员工姓名筛选"
// @Success 200 {object} ListEmployeesResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /employees [get]
func (h *EmployeeHandler) ListEmployees(c echo.Context) error {
	ctx := context.Background()

	// 构建筛选条件
	filter := repository.EmployeeFilter{}

	// 解析名称参数
	name := c.QueryParam("name")
	if name != "" {
		filter.Name = &name
	}

	// 解析分页参数
	page := 1
	if c.QueryParam("page") != "" {
		pageParam, err := strconv.Atoi(c.QueryParam("page"))
		if err == nil && pageParam > 0 {
			page = pageParam
		}
	}

	size := 10
	if c.QueryParam("size") != "" {
		sizeParam, err := strconv.Atoi(c.QueryParam("size"))
		if err == nil && sizeParam > 0 {
			size = sizeParam
		}
	}

	pagination := repository.Pagination{
		Page:  page,
		Limit: size,
	}

	// 设置排序
	sort := repository.SortOrder{
		Field:     "id",
		Direction: "asc",
	}

	// 调用服务获取员工列表
	employees, total, err := h.employeeService.ListEmployees(ctx, filter, pagination, sort)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "LIST_EMPLOYEES_FAILED",
			Message: "获取员工列表失败",
			Detail:  err.Error(),
		})
	}

	// 确保返回空数组而不是null
	if employees == nil {
		employees = []repository.Employee{}
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    200,
		"message": "成功",
		"data": ListEmployeesResponse{
			Total:     total,
			Page:      page,
			Size:      size,
			Employees: employees,
		},
	})
}

// GetEmployee 处理获取员工详情请求
// @Summary 获取员工详情
// @Description 根据ID获取员工详细信息
// @Tags 员工
// @Accept json
// @Produce json
// @Param id path int true "员工ID"
// @Success 200 {object} repository.Employee
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /employee/{id} [get]
func (h *EmployeeHandler) GetEmployee(c echo.Context) error {
	ctx := context.Background()

	// 解析员工ID
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil || id <= 0 {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_ID",
			Message: "无效的员工ID",
			Detail:  "员工ID必须是正整数",
		})
	}

	// 调用服务获取员工详情
	employee, err := h.employeeService.GetEmployee(ctx, repository.IDFromInt64(id))
	if err != nil {
		return c.JSON(http.StatusNotFound, models.ErrorResponse{
			Code:    "EMPLOYEE_NOT_FOUND",
			Message: "员工不存在",
			Detail:  err.Error(),
		})
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    200,
		"message": "成功",
		"data":    employee,
	})
}

// UpdateEmployeeRequest 定义更新员工请求结构
type UpdateEmployeeRequest struct {
	Name     string `json:"name"`
	Password string `json:"password"`
}

// UpdateEmployee 处理更新员工信息请求
// @Summary 更新员工信息
// @Description 更新员工的姓名和密码
// @Tags 员工
// @Accept json
// @Produce json
// @Param id path int true "员工ID"
// @Param request body UpdateEmployeeRequest true "更新的员工信息"
// @Success 200 {object} repository.Employee
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /employee/{id} [put]
func (h *EmployeeHandler) UpdateEmployee(c echo.Context) error {
	ctx := context.Background()

	// 解析员工ID
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil || id <= 0 {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_ID",
			Message: "无效的员工ID",
			Detail:  "员工ID必须是正整数",
		})
	}

	// 解析请求
	req := new(UpdateEmployeeRequest)
	if err := c.Bind(req); err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "无效的请求格式",
			Detail:  err.Error(),
		})
	}

	// 参数验证 - 至少需要一个非空字段
	if req.Name == "" && req.Password == "" {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "MISSING_UPDATE_FIELDS",
			Message: "缺少更新字段",
			Detail:  "姓名和密码至少需要提供一个",
		})
	}

	// 获取现有员工信息
	existingEmployee, err := h.employeeService.GetEmployee(ctx, repository.IDFromInt64(id))
	if err != nil {
		return c.JSON(http.StatusNotFound, models.ErrorResponse{
			Code:    "EMPLOYEE_NOT_FOUND",
			Message: "员工不存在",
			Detail:  err.Error(),
		})
	}

	// 更新字段
	updateEmployee := existingEmployee
	if req.Name != "" {
		updateEmployee.Name = req.Name
	}
	if req.Password != "" {
		updateEmployee.Password = req.Password
	}

	// 调用服务更新员工
	updatedEmployee, err := h.employeeService.UpdateEmployee(ctx, updateEmployee)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "UPDATE_EMPLOYEE_FAILED",
			Message: "更新员工失败",
			Detail:  err.Error(),
		})
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    200,
		"message": "更新成功",
		"data":    updatedEmployee,
	})
}

// DeleteEmployee 处理删除员工请求
// @Summary 删除员工
// @Description 根据ID删除员工（软删除）
// @Tags 员工
// @Accept json
// @Produce json
// @Param id path int true "员工ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /employee/{id} [delete]
func (h *EmployeeHandler) DeleteEmployee(c echo.Context) error {
	ctx := context.Background()

	// 解析员工ID
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil || id <= 0 {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_ID",
			Message: "无效的员工ID",
			Detail:  "员工ID必须是正整数",
		})
	}

	// 调用服务删除员工
	err = h.employeeService.DeleteEmployee(ctx, repository.IDFromInt64(id))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "DELETE_EMPLOYEE_FAILED",
			Message: "删除员工失败",
			Detail:  err.Error(),
		})
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    200,
		"message": "删除成功",
		"data":    nil,
	})
}
