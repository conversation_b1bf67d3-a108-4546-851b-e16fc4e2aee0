package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/service"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/types"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/utils"
)

/**
 * End of Day Report Handler (Redesigned)
 *
 * 处理日终报表相关的HTTP请求 - 重构版本
 */
type EndOfDayReportHandler struct {
	service *service.EndOfDayReportService
}

// NewEndOfDayReportHandler 创建新的日终报表处理器
func NewEndOfDayReportHandler(service *service.EndOfDayReportService) *EndOfDayReportHandler {
	return &EndOfDayReportHandler{
		service: service,
	}
}

// GetEndOfDayReport 获取日终报表 (重构版本)
// @Summary 获取日终报表
// @Description 获取指定站点和日期的日终报表数据，整合当日所有班次的员工加油情况、销售统计和支付数据 (重构版本，复用shifts/attendants逻辑)
// @Tags 报表
// @Accept json
// @Produce json
// @Param station_id query int true "站点ID"
// @Param date query string true "报表日期 (YYYY-MM-DD)"
// @Param attendant_name query string false "员工姓名筛选（支持模糊匹配）"
// @Param fuel_grade query string false "油品等级筛选"
// @Param payment_method query string false "支付方式筛选"
// @Param include_summary query bool false "是否包含汇总统计"
// @Success 200 {object} types.EndOfDayReportResponse
// @Failure 400 {object} types.ErrorResponse
// @Failure 404 {object} types.ErrorResponse
// @Failure 500 {object} types.ErrorResponse
// @Router /api/v1/reports/end-of-day [get]
func (h *EndOfDayReportHandler) GetEndOfDayReport(c echo.Context) error {
	// 解析请求参数
	req, err := h.parseRequest(c)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"message": "请求参数无效",
			"error": map[string]interface{}{
				"code":    "INVALID_REQUEST",
				"message": "请求参数无效",
				"detail":  err.Error(),
			},
		})
	}

	// 调用重构后的服务层获取报表数据
	response, err := h.service.GetEndOfDayReport(c.Request().Context(), req)
	if err != nil {
		statusCode := h.getHTTPStatusCode(err)
		return c.JSON(statusCode, map[string]interface{}{
			"success": false,
			"message": "获取日终报表失败",
			"error": map[string]interface{}{
				"code":    h.getErrorCode(err),
				"message": "获取日终报表失败",
				"detail":  err.Error(),
			},
		})
	}

	// 转换响应中的时间字段为雅加达时间
	h.convertResponseTimesToJakarta(response)

	return c.JSON(http.StatusOK, response)
}

// convertResponseTimesToJakarta 将响应中的所有时间字段转换为雅加达时间
func (h *EndOfDayReportHandler) convertResponseTimesToJakarta(response *types.EndOfDayReportResponse) {
	// 转换报表头部的生成时间
	response.Data.ReportHeader.GeneratedAt = utils.ConvertToJakartaTime(response.Data.ReportHeader.GeneratedAt)
	
	// 转换班次数据中的时间字段
	for i := range response.Data.Shifts {
		shift := &response.Data.Shifts[i]
		// 强制转换班次时间到雅加达时区
		shift.ShiftInfo.StartTime = utils.ForceToJakartaTime(shift.ShiftInfo.StartTime)
		shift.ShiftInfo.EndTime = utils.ForceToJakartaTime(shift.ShiftInfo.EndTime)
	}
	
	// 转换元数据中的生成时间
	response.Meta.GeneratedAt = utils.ConvertToJakartaTime(response.Meta.GeneratedAt)
}

// parseRequest 解析HTTP请求参数
func (h *EndOfDayReportHandler) parseRequest(c echo.Context) (*types.EndOfDayReportRequest, error) {
	req := &types.EndOfDayReportRequest{}

	// 解析station_id
	stationIDStr := c.QueryParam("station_id")
	if stationIDStr == "" {
		return nil, echo.NewHTTPError(http.StatusBadRequest, "station_id is required")
	}

	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil {
		return nil, echo.NewHTTPError(http.StatusBadRequest, "invalid station_id format")
	}
	req.StationID = stationID

	// 解析date
	req.Date = c.QueryParam("date")
	if req.Date == "" {
		return nil, echo.NewHTTPError(http.StatusBadRequest, "date is required")
	}

	// 验证日期格式
	if _, err := time.Parse("2006-01-02", req.Date); err != nil {
		return nil, echo.NewHTTPError(http.StatusBadRequest, "invalid date format, expected YYYY-MM-DD")
	}

	// 解析可选参数
	if attendantName := c.QueryParam("attendant_name"); attendantName != "" {
		req.AttendantName = &attendantName
	}

	if fuelGrade := c.QueryParam("fuel_grade"); fuelGrade != "" {
		req.FuelGrade = &fuelGrade
	}

	if paymentMethod := c.QueryParam("payment_method"); paymentMethod != "" {
		req.PaymentMethod = &paymentMethod
	}

	if includeSummaryStr := c.QueryParam("include_summary"); includeSummaryStr != "" {
		includeSummary, err := strconv.ParseBool(includeSummaryStr)
		if err == nil {
			req.IncludeSummary = &includeSummary
		}
	}

	return req, nil
}

// getHTTPStatusCode 根据错误类型返回对应的HTTP状态码
func (h *EndOfDayReportHandler) getHTTPStatusCode(err error) int {
	switch err.Error() {
	case "INVALID_STATION_ID", "INVALID_DATE_FORMAT", "DATE_OUT_OF_RANGE":
		return http.StatusBadRequest
	case "STATION_NOT_FOUND", "NO_DATA_FOUND":
		return http.StatusNotFound
	case "AUTHENTICATION_FAILED":
		return http.StatusUnauthorized
	case "PERMISSION_DENIED":
		return http.StatusForbidden
	default:
		return http.StatusInternalServerError
	}
}

// getErrorCode 根据错误类型返回对应的错误代码
func (h *EndOfDayReportHandler) getErrorCode(err error) string {
	switch err.Error() {
	case "INVALID_STATION_ID":
		return "INVALID_STATION_ID"
	case "STATION_NOT_FOUND":
		return "STATION_NOT_FOUND"
	case "INVALID_DATE_FORMAT":
		return "INVALID_DATE_FORMAT"
	case "DATE_OUT_OF_RANGE":
		return "DATE_OUT_OF_RANGE"
	case "NO_DATA_FOUND":
		return "NO_DATA_FOUND"
	case "AUTHENTICATION_FAILED":
		return "AUTHENTICATION_FAILED"
	case "PERMISSION_DENIED":
		return "PERMISSION_DENIED"
	default:
		return "INTERNAL_ERROR"
	}
} 