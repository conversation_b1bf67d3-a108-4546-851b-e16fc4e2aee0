// @title 加油站管理系统 API
// @version 1.0
// @description 这是一个加油站管理系统的API服务，提供订单、燃油交易、员工管理和报表等功能。
// @contact.name API Support
// @contact.url http://www.example.com/support
// @contact.email <EMAIL>
// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html
// @BasePath /api/v1

package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/labstack/echo/v4"

	"gitlab4.weicheche.cn/indo-bp/bos/internal/models"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"
)

// EnhancedReportHandler 处理与增强报表相关的请求
type EnhancedReportHandler struct {
	enhancedReportService service.EnhancedReportService
}

// NewEnhancedReportHandler 创建新的增强报表处理器
func NewEnhancedReportHandler(enhancedReportService service.EnhancedReportService) *EnhancedReportHandler {
	return &EnhancedReportHandler{
		enhancedReportService: enhancedReportService,
	}
}

// GetTotalRevenueResponse 定义总收入汇总响应
type GetTotalRevenueResponse struct {
	Data *repository.TotalRevenueDTO `json:"data"`
}

// GetTotalRevenue 处理获取总收入汇总的请求
// @Summary 获取总收入汇总
// @Description 获取指定时间段内的总收入汇总数据，包括燃油和非燃油收入、退款等信息
// @Tags 增强报表
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期 (格式: 2006-01-02)"
// @Param end_date query string true "结束日期 (格式: 2006-01-02)"
// @Param site_ids query string false "站点ID列表 (逗号分隔)"
// @Success 200 {object} GetTotalRevenueResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /enhanced-reports/total-revenue [get]
func (h *EnhancedReportHandler) GetTotalRevenue(c echo.Context) error {
	ctx := context.Background()

	filter, err := parseEnhancedReportFilter(c)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_PARAMETERS",
			Message: "无效的请求参数",
			Detail:  err.Error(),
		})
	}

	// 调用服务获取数据
	result, err := h.enhancedReportService.GetTotalRevenue(ctx, filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取总收入汇总失败",
			Detail:  err.Error(),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, GetTotalRevenueResponse{
		Data: result,
	})
}

// GetFuelReceivableSummaryResponse 定义油品应收汇总响应
type GetFuelReceivableSummaryResponse struct {
	Data *repository.FuelReceivableSummaryDTO `json:"data"`
}

// GetFuelReceivableSummary 处理获取油品应收汇总的请求
// @Summary 获取油品应收汇总
// @Description 获取指定时间段内的油品销售汇总数据，包括销售数量、金额、优惠、退款等详细信息
// @Tags 增强报表
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期 (格式: 2006-01-02)"
// @Param end_date query string true "结束日期 (格式: 2006-01-02)"
// @Param site_ids query string false "站点ID列表 (逗号分隔)"
// @Success 200 {object} GetFuelReceivableSummaryResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /enhanced-reports/fuel-receivable [get]
func (h *EnhancedReportHandler) GetFuelReceivableSummary(c echo.Context) error {
	ctx := context.Background()

	filter, err := parseEnhancedReportFilter(c)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_PARAMETERS",
			Message: "无效的请求参数",
			Detail:  err.Error(),
		})
	}

	// 调用服务获取数据
	result, err := h.enhancedReportService.GetFuelReceivableSummary(ctx, filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取油品应收汇总失败",
			Detail:  err.Error(),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, GetFuelReceivableSummaryResponse{
		Data: result,
	})
}

// SalesDetailByPaymentMethodResponse 定义按支付方式销售明细响应
type SalesDetailByPaymentMethodResponse struct {
	Data     []repository.SalesDetailByPaymentMethodDTO `json:"data"`
	Metadata *repository.ReportMetadata                 `json:"metadata"`
}

// GetSalesDetailByPaymentMethod 处理获取按支付方式销售明细的请求
// @Summary 获取按支付方式销售明细
// @Description 获取按支付方式分组的销售明细，包括各支付方式下的详细交易列表
// @Tags 增强报表
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期 (格式: 2006-01-02)"
// @Param end_date query string true "结束日期 (格式: 2006-01-02)"
// @Param site_ids query string false "站点ID列表 (逗号分隔)"
// @Param limit query int false "每页记录数 (默认: 10)"
// @Param offset query int false "偏移量 (默认: 0)"
// @Success 200 {object} SalesDetailByPaymentMethodResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /enhanced-reports/sales-by-payment-method [get]
func (h *EnhancedReportHandler) GetSalesDetailByPaymentMethod(c echo.Context) error {
	ctx := context.Background()

	filter, err := parseEnhancedReportFilter(c)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_PARAMETERS",
			Message: "无效的请求参数",
			Detail:  err.Error(),
		})
	}

	// 调用服务获取数据
	result, metadata, err := h.enhancedReportService.GetSalesDetailByPaymentMethod(ctx, filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取支付方式销售明细失败",
			Detail:  err.Error(),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, SalesDetailByPaymentMethodResponse{
		Data:     result,
		Metadata: metadata,
	})
}

// SalesDetailByProductResponse 定义按商品销售明细响应
type SalesDetailByProductResponse struct {
	Data     []repository.SalesDetailByProductDTO `json:"data"`
	Metadata *repository.ReportMetadata           `json:"metadata"`
}

// GetSalesDetailByProduct 处理获取按商品销售明细的请求
// @Summary 获取按商品销售明细
// @Description 获取按商品分组的销售明细，包括各商品的详细交易记录和小计
// @Tags 增强报表
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期 (格式: 2006-01-02)"
// @Param end_date query string true "结束日期 (格式: 2006-01-02)"
// @Param site_ids query string false "站点ID列表 (逗号分隔)"
// @Param limit query int false "每页记录数 (默认: 10)"
// @Param offset query int false "偏移量 (默认: 0)"
// @Success 200 {object} SalesDetailByProductResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /enhanced-reports/sales-by-product [get]
func (h *EnhancedReportHandler) GetSalesDetailByProduct(c echo.Context) error {
	ctx := context.Background()

	filter, err := parseEnhancedReportFilter(c)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_PARAMETERS",
			Message: "无效的请求参数",
			Detail:  err.Error(),
		})
	}

	// 调用服务获取数据
	result, metadata, err := h.enhancedReportService.GetSalesDetailByProduct(ctx, filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取商品销售明细失败",
			Detail:  err.Error(),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, SalesDetailByProductResponse{
		Data:     result,
		Metadata: metadata,
	})
}

// NozzleDetailedSalesResponse 定义油枪销售明细响应
type NozzleDetailedSalesResponse struct {
	Data     []repository.NozzleDetailedSalesDTO `json:"data"`
	Metadata *repository.ReportMetadata          `json:"metadata"`
}

// GetNozzleDetailedSales 处理获取油枪销售明细的请求
// @Summary 获取油枪销售明细
// @Description 获取各油枪的销售明细数据，包括站点、油枪ID、燃油类型、销售量等信息
// @Tags 增强报表
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期 (格式: 2006-01-02)"
// @Param end_date query string true "结束日期 (格式: 2006-01-02)"
// @Param site_ids query string false "站点ID列表 (逗号分隔)"
// @Param limit query int false "每页记录数 (默认: 10)"
// @Param offset query int false "偏移量 (默认: 0)"
// @Success 200 {object} NozzleDetailedSalesResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /enhanced-reports/nozzle-sales [get]
func (h *EnhancedReportHandler) GetNozzleDetailedSales(c echo.Context) error {
	ctx := context.Background()

	filter, err := parseEnhancedReportFilter(c)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_PARAMETERS",
			Message: "无效的请求参数",
			Detail:  err.Error(),
		})
	}

	// 调用服务获取数据
	result, metadata, err := h.enhancedReportService.GetNozzleDetailedSales(ctx, filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取油枪销售明细失败",
			Detail:  err.Error(),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, NozzleDetailedSalesResponse{
		Data:     result,
		Metadata: metadata,
	})
}

// QueryPerformanceResponse 定义查询性能指标响应
type QueryPerformanceResponse struct {
	Data map[string]interface{} `json:"data"`
}

// GetQueryPerformance 处理获取查询性能指标的请求
// @Summary 获取查询性能指标
// @Description 获取指定查询类型的性能指标，用于监控和优化查询性能
// @Tags 增强报表
// @Accept json
// @Produce json
// @Param query_type query string true "查询类型 (total_revenue, fuel_receivable, payment_method_detail, product_detail, nozzle_detail)"
// @Param start_date query string true "开始日期 (格式: 2006-01-02)"
// @Param end_date query string true "结束日期 (格式: 2006-01-02)"
// @Param site_ids query string false "站点ID列表 (逗号分隔)"
// @Success 200 {object} QueryPerformanceResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /enhanced-reports/query-performance [get]
func (h *EnhancedReportHandler) GetQueryPerformance(c echo.Context) error {
	ctx := context.Background()

	queryType := c.QueryParam("query_type")
	if queryType == "" {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_QUERY_TYPE",
			Message: "查询类型不能为空",
			Detail:  "有效的查询类型包括: total_revenue, fuel_receivable, payment_method_detail, product_detail, nozzle_detail",
		})
	}

	filter, err := parseEnhancedReportFilter(c)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_PARAMETERS",
			Message: "无效的请求参数",
			Detail:  err.Error(),
		})
	}

	// 调用服务获取数据
	result, err := h.enhancedReportService.GetQueryPerformance(ctx, queryType, filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取查询性能指标失败",
			Detail:  err.Error(),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, QueryPerformanceResponse{
		Data: result,
	})
}

// 辅助函数：解析增强报表过滤器
func parseEnhancedReportFilter(c echo.Context) (repository.EnhancedReportFilter, error) {
	// 解析开始日期和结束日期
	startDateStr := c.QueryParam("start_date")
	endDateStr := c.QueryParam("end_date")

	if startDateStr == "" || endDateStr == "" {
		return repository.EnhancedReportFilter{},
			fmt.Errorf("开始日期和结束日期为必填参数")
	}

	// 解析站点ID列表
	var siteIDs []int
	if c.QueryParam("site_ids") != "" {
		siteIDStrings := strings.Split(c.QueryParam("site_ids"), ",")
		for _, idStr := range siteIDStrings {
			id, err := strconv.Atoi(strings.TrimSpace(idStr))
			if err == nil && id > 0 {
				siteIDs = append(siteIDs, id)
			}
		}
	}

	// 解析分页参数
	limit := 10 // 默认每页10条
	if c.QueryParam("limit") != "" {
		parsedLimit, err := strconv.Atoi(c.QueryParam("limit"))
		if err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	offset := 0 // 默认从第0条开始
	if c.QueryParam("offset") != "" {
		parsedOffset, err := strconv.Atoi(c.QueryParam("offset"))
		if err == nil && parsedOffset >= 0 {
			offset = parsedOffset
		}
	}

	return repository.EnhancedReportFilter{
		DateRange: repository.DateRangeFilter{
			StartDate: startDateStr,
			EndDate:   endDateStr,
		},
		Sites: repository.SiteFilter{
			SiteIDs: siteIDs,
		},
		Limit:  limit,
		Offset: offset,
	}, nil
}
