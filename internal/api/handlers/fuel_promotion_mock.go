// Package handlers - Fuel Promotion Mock Implementation
// 燃油促销活动Mock实现，用于debug和演示
package handlers

import (
	"context"
	"fmt"
	"log"
	"strings"

	"gitlab4.weicheche.cn/indo-bp/promotion-service/pkg/types"
)

// FuelPromotionMock 燃油促销活动Mock实现
type FuelPromotionMock struct {
	// 当前使用的规则集 ("volume_discount" 或 "gradient_discount")
	CurrentRuleSet string
}

// NewFuelPromotionMock 创建新的Mock实例
func NewFuelPromotionMock() *FuelPromotionMock {
	return &FuelPromotionMock{
		CurrentRuleSet: "volume_discount", // 默认使用体积折扣规则
	}
}

// SetRuleSet 设置当前使用的规则集
func (m *FuelPromotionMock) SetRuleSet(ruleSet string) {
	m.CurrentRuleSet = ruleSet
}

// CalculateDiscounts Mock实现折扣计算
func (m *FuelPromotionMock) CalculateDiscounts(ctx context.Context, orderData types.OrderData) ([]types.CalculatedDiscount, error) {
	log.Printf("🎯 [MOCK计算] 开始计算折扣，规则集: %s", m.CurrentRuleSet)
	log.Printf("🎯 [MOCK计算] 订单详情: OrderID=%s, UserID=%s, TotalAmount=%.2f",
		orderData.OrderID, orderData.UserID, orderData.TotalAmount)

	for i, item := range orderData.Items {
		log.Printf("🎯 [MOCK计算] 商品%d: ID=%s, ProductID=%s, Quantity=%.2f, UnitPrice=%.2f",
			i+1, item.ItemID, item.ProductID, item.Quantity, item.UnitPrice)
	}

	var discounts []types.CalculatedDiscount
	var err error

	switch m.CurrentRuleSet {
	case "volume_discount":
		log.Printf("🎯 [MOCK计算] 使用体积折扣规则")
		discounts, err = m.calculateVolumeDiscount(orderData)
	case "gradient_discount":
		log.Printf("🎯 [MOCK计算] 使用梯度折扣规则")
		discounts, err = m.calculateGradientDiscount(orderData)
	default:
		log.Printf("🎯 [MOCK计算] 未知规则集，返回空折扣")
		return []types.CalculatedDiscount{}, nil
	}

	if err != nil {
		log.Printf("🎯 [MOCK计算] 计算出错: %v", err)
		return nil, err
	}

	log.Printf("🎯 [MOCK计算] 计算完成，找到 %d 个折扣", len(discounts))
	for i, discount := range discounts {
		log.Printf("🎯 [MOCK计算] 折扣%d: ID=%s, Name=%s, Type=%s, Value=%.2f",
			i+1, discount.PromotionID, discount.PromotionName, discount.DiscountType, discount.DiscountValue)
	}

	return discounts, nil
}

// calculateVolumeDiscount 体积折扣规则：25升以上获得1升折扣
func (m *FuelPromotionMock) calculateVolumeDiscount(orderData types.OrderData) ([]types.CalculatedDiscount, error) {
	var discounts []types.CalculatedDiscount

	// 计算总燃油体积
	totalLiters := 0.0
	fuelUnitPrice := 0.0
	var applicableItems []string

	for _, item := range orderData.Items {
		if m.isFuelProduct(item.ProductID) {
			// 假设Quantity就是升数，UnitPrice是每升价格
			totalLiters += float64(item.Quantity)
			if fuelUnitPrice == 0 {
				fuelUnitPrice = item.UnitPrice
			}
			applicableItems = append(applicableItems, item.ProductID)
		}
	}

	// 规则1：最低购买25升以上可以获得1升的折扣
	if totalLiters >= 25.0 {
		discountValue := fuelUnitPrice * 1.0 // 1升的价值

		discount := types.CalculatedDiscount{
			PromotionID:     "FUEL_VOLUME_PROMO_001",
			PromotionName:   "25升以上获得1升免费",
			DiscountType:    "VOLUME_DISCOUNT",
			DiscountValue:   discountValue,
			Description:     fmt.Sprintf("符合条件：%.1f升≥25升，免费1升价值：Rp %.0f", totalLiters, discountValue),
			ApplicableItems: applicableItems,
		}

		discounts = append(discounts, discount)
	}

	return discounts, nil
}

// calculateGradientDiscount 梯度折扣规则
func (m *FuelPromotionMock) calculateGradientDiscount(orderData types.OrderData) ([]types.CalculatedDiscount, error) {
	var discounts []types.CalculatedDiscount

	// 按燃油类型分组计算
	fuelGroups := m.groupByFuelType(orderData.Items)

	for fuelType, items := range fuelGroups {
		totalLiters := 0.0
		var applicableItems []string
		for _, item := range items {
			totalLiters += float64(item.Quantity)
			applicableItems = append(applicableItems, item.ProductID)
		}

		discount := m.calculateFuelTypeDiscount(fuelType, totalLiters, applicableItems)
		if discount.DiscountValue > 0 {
			discounts = append(discounts, discount)
		}
	}

	return discounts, nil
}

// calculateFuelTypeDiscount 根据燃油类型和数量计算折扣
func (m *FuelPromotionMock) calculateFuelTypeDiscount(fuelType string, liters float64, applicableItems []string) types.CalculatedDiscount {
	var discountValue float64
	var promotionName string
	var minLiters float64

	// 判断燃油类型并应用对应规则
	if strings.Contains(strings.ToLower(fuelType), "bp 92") || strings.Contains(strings.ToLower(fuelType), "bp92") {
		// BP 92 规则
		if liters >= 28.0 {
			discountValue = 15000.0 // Rp 15,000
			promotionName = "BP 92 - 28升以上优惠 Rp 15,000"
			minLiters = 28.0
		} else if liters >= 3.0 {
			discountValue = 1000.0 // Rp 1,000
			promotionName = "BP 92 - 3升以上优惠 Rp 1,000"
			minLiters = 3.0
		}
	} else if strings.Contains(strings.ToLower(fuelType), "ultimate") {
		// Ultimate / Ultimate Diesel 规则
		if liters >= 28.0 {
			discountValue = 20000.0 // Rp 20,000
			promotionName = "Ultimate - 28升以上优惠 Rp 20,000"
			minLiters = 28.0
		} else if liters >= 3.0 {
			discountValue = 1500.0 // Rp 1,500
			promotionName = "Ultimate - 3升以上优惠 Rp 1,500"
			minLiters = 3.0
		}
	}

	return types.CalculatedDiscount{
		PromotionID:     fmt.Sprintf("FUEL_GRADIENT_%s", strings.ToUpper(strings.ReplaceAll(fuelType, " ", "_"))),
		PromotionName:   promotionName,
		DiscountType:    "GRADIENT_DISCOUNT",
		DiscountValue:   discountValue,
		Description:     fmt.Sprintf("%s：%.1f升≥%.0f升，折扣Rp %.0f", fuelType, liters, minLiters, discountValue),
		ApplicableItems: applicableItems,
	}
}

// groupByFuelType 按燃油类型分组
func (m *FuelPromotionMock) groupByFuelType(items []types.OrderItemData) map[string][]types.OrderItemData {
	groups := make(map[string][]types.OrderItemData)

	for _, item := range items {
		if m.isFuelProduct(item.ProductID) {
			fuelType := m.getFuelType(item.ProductID)
			groups[fuelType] = append(groups[fuelType], item)
		}
	}

	return groups
}

// isFuelProduct 判断是否是燃油产品
func (m *FuelPromotionMock) isFuelProduct(productID string) bool {
	// 简单的产品ID模式匹配
	productID = strings.ToLower(productID)
	fuelKeywords := []string{"fuel", "gas", "petrol", "diesel", "bp", "ultimate", "92", "95", "pertamax"}

	for _, keyword := range fuelKeywords {
		if strings.Contains(productID, keyword) {
			return true
		}
	}

	return false
}

// getFuelType 根据产品ID获取燃油类型
func (m *FuelPromotionMock) getFuelType(productID string) string {
	productID = strings.ToLower(productID)

	if strings.Contains(productID, "ultimate") && strings.Contains(productID, "diesel") {
		return "Ultimate Diesel"
	} else if strings.Contains(productID, "ultimate") {
		return "Ultimate"
	} else if strings.Contains(productID, "bp") && (strings.Contains(productID, "92") || strings.Contains(productID, "pertamax")) {
		return "BP 92"
	} else if strings.Contains(productID, "diesel") {
		return "Diesel"
	} else {
		return "Regular Fuel"
	}
}

// GetAvailableRuleSets 获取可用的规则集列表
func (m *FuelPromotionMock) GetAvailableRuleSets() []map[string]string {
	return []map[string]string{
		{
			"id":          "volume_discount",
			"name":        "体积折扣规则",
			"description": "25升以上获得1升免费",
		},
		{
			"id":          "gradient_discount",
			"name":        "梯度折扣规则",
			"description": "基于燃油类型和数量的分级折扣",
		},
	}
}
