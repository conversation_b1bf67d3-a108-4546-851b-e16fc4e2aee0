package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/labstack/echo/v4"

	inventoryservice "gitlab4.weicheche.cn/indo-bp/inventory-service"
	"gitlab4.weicheche.cn/indo-bp/inventory-service/service/dto"
)

// InventoryHandler 库存管理处理器
type InventoryHandler struct {
	manager *inventoryservice.Manager
}

// NewInventoryHandler 创建新的库存处理器实例
func NewInventoryHandler(
	ctx context.Context,
	dsn string,
) (*InventoryHandler, func(), error) {
	manager, cleanup, err := inventoryservice.NewManager(ctx, dsn)
	if err != nil {
		return nil, nil, fmt.Errorf("初始化库存服务失败: %v", err)
	}

	return &InventoryHandler{
		manager: manager,
	}, cleanup, nil
}

// ===== 商品管理相关接口 =====

// CreateProduct 创建商品
// @Summary 创建新商品
// @Description 创建新的商品档案
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param product body dto.CreateProductRequest true "商品信息"
// @Success 201 {object} dto.ProductResponse "创建成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/products [post]
func (h *InventoryHandler) CreateProduct(c echo.Context) error {
	var req dto.CreateProductRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用商品服务创建商品
	product, err := h.manager.ProductService.CreateProduct(c.Request().Context(), &req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "CREATE_PRODUCT_ERROR",
			"message": "创建商品失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusCreated, product)
}

// GetProduct 根据ID获取商品
// @Summary 获取商品信息
// @Description 根据商品ID获取商品详细信息
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param id path int true "商品ID"
// @Success 200 {object} dto.ProductResponse "获取成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 404 {object} map[string]string "商品不存在"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/products/{id} [get]
func (h *InventoryHandler) GetProduct(c echo.Context) error {
	// 解析商品ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "INVALID_ID",
			"message": "商品ID格式错误",
			"details": "ID必须是有效的数字",
		})
	}

	// 调用商品服务获取商品
	product, err := h.manager.ProductService.GetProductByID(c.Request().Context(), id)
	if err != nil {
		return c.JSON(http.StatusNotFound, map[string]string{
			"error":   "PRODUCT_NOT_FOUND",
			"message": "商品不存在",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, product)
}

// ListProducts 获取商品列表
// @Summary 获取商品列表
// @Description 获取商品列表，支持分页、排序和过滤
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param limit query int false "限制返回数量，0表示不限制" default(20)
// @Param offset query int false "偏移量，用于分页" default(0)
// @Param order_by query string false "排序字段" default("id")
// @Param order query string false "排序方式 (asc/desc)" default("asc")
// @Param type query string false "按商品类型过滤"
// @Param status query string false "按商品状态过滤"
// @Param category_id query int false "按分类ID过滤"
// @Param keyword query string false "按名称或编码关键词搜索"
// @Success 200 {object} dto.ListProductsResponse "获取成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/products [get]
func (h *InventoryHandler) ListProducts(c echo.Context) error {
	// 解析查询参数
	req := &dto.ListProductsRequest{}

	if err := c.Bind(req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用商品服务获取商品列表
	response, err := h.manager.ProductService.ListProducts(c.Request().Context(), req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "LIST_PRODUCTS_ERROR",
			"message": "获取商品列表失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// DeleteProduct 删除商品
// @Summary 删除商品
// @Description 根据商品ID删除商品及其关联数据
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param id path int true "商品ID"
// @Success 200 {object} map[string]interface{} "删除成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 404 {object} map[string]string "商品不存在"
// @Failure 409 {object} map[string]string "商品有库存无法删除"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/products/{id} [delete]
func (h *InventoryHandler) DeleteProduct(c echo.Context) error {
	// 解析商品ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "INVALID_ID",
			"message": "商品ID格式错误",
			"details": "ID必须是有效的数字",
		})
	}

	// 调用商品服务删除商品
	err = h.manager.ProductService.DeleteProduct(c.Request().Context(), id)
	if err != nil {
		// 根据错误类型返回不同的状态码
		if strings.Contains(err.Error(), "商品不存在") {
			return c.JSON(http.StatusNotFound, map[string]string{
				"error":   "PRODUCT_NOT_FOUND",
				"message": "商品不存在",
				"details": err.Error(),
			})
		}
		if strings.Contains(err.Error(), "有库存") {
			return c.JSON(http.StatusConflict, map[string]string{
				"error":   "PRODUCT_HAS_INVENTORY",
				"message": "商品有库存，无法删除",
				"details": err.Error(),
			})
		}
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "DELETE_PRODUCT_ERROR",
			"message": "删除商品失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "商品删除成功",
		"id":      id,
	})
}

// ===== 库存查询相关接口 =====

// ListInventory 获取库存列表
// @Summary 获取库存列表
// @Description 获取库存列表，支持分页、过滤和排序
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param limit query int false "限制返回数量，0表示不限制" default(20)
// @Param offset query int false "偏移量，用于分页" default(0)
// @Param order_by query string false "排序字段：id, product_id, physical_quantity, available_quantity, created_at, updated_at" default("id")
// @Param order query string false "排序方式 (asc/desc)" default("desc")
// @Param product_id query int false "按商品ID过滤"
// @Param product_type query string false "按商品类型过滤：OIL, NON_OIL, GIFT"
// @Param product_status query string false "按商品状态过滤：ACTIVE, INACTIVE"
// @Param category_id query int false "按商品分类ID过滤"
// @Param keyword query string false "按商品名称或编码关键词搜索"
// @Param low_stock query bool false "是否只显示低库存商品"
// @Param min_quantity query number false "最小库存数量过滤"
// @Param max_quantity query number false "最大库存数量过滤"
// @Success 200 {object} dto.ListInventoryResponse "获取成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/stocks [get]
func (h *InventoryHandler) ListInventory(c echo.Context) error {
	// 解析查询参数
	req := &dto.ListInventoryRequest{}

	if err := c.Bind(req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用库存服务获取库存列表
	response, err := h.manager.InventoryService.ListInventory(c.Request().Context(), req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "LIST_INVENTORY_ERROR",
			"message": "获取库存列表失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// GetInventory 获取库存详情
// @Summary 获取库存详情
// @Description 根据库存ID或商品ID获取库存详细信息
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param id path int false "库存记录ID"
// @Param product_id query int false "商品ID（与路径参数ID二选一）"
// @Success 200 {object} dto.GetInventoryResponse "获取成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 404 {object} map[string]string "库存不存在"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/stocks/{id} [get]
func (h *InventoryHandler) GetInventory(c echo.Context) error {
	req := &dto.GetInventoryRequest{}

	// 尝试从路径参数获取ID
	if idStr := c.Param("id"); idStr != "" {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{
				"error":   "INVALID_ID",
				"message": "库存ID格式错误",
				"details": "ID必须是有效的数字",
			})
		}
		req.ID = id
	}

	// 尝试从查询参数获取商品ID
	if productIDStr := c.QueryParam("product_id"); productIDStr != "" {
		productID, err := strconv.ParseInt(productIDStr, 10, 64)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{
				"error":   "INVALID_PRODUCT_ID",
				"message": "商品ID格式错误",
				"details": "商品ID必须是有效的数字",
			})
		}
		req.ProductID = productID
	}

	// 检查是否提供了查询条件
	if req.ID == 0 && req.ProductID == 0 {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "MISSING_PARAMETER",
			"message": "缺少查询参数",
			"details": "必须提供库存ID或商品ID中的一个",
		})
	}

	// 调用库存服务获取库存详情
	response, err := h.manager.InventoryService.GetInventory(c.Request().Context(), req)
	if err != nil {
		if strings.Contains(err.Error(), "不存在") {
			return c.JSON(http.StatusNotFound, map[string]string{
				"error":   "INVENTORY_NOT_FOUND",
				"message": "库存不存在",
				"details": err.Error(),
			})
		}
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "GET_INVENTORY_ERROR",
			"message": "获取库存详情失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// ===== 库存操作相关接口 =====

// InboundInventory 库存入库
// @Summary 库存入库操作
// @Description 处理商品入库操作，如采购入库、调拨入库等
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param inbound body dto.AdjustInventoryRequest true "入库信息"
// @Success 200 {object} dto.AdjustInventoryResponse "入库成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/inbound [post]
func (h *InventoryHandler) InboundInventory(c echo.Context) error {
	var req dto.AdjustInventoryRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用库存服务执行入库操作
	response, err := h.manager.InventoryService.Inbound(c.Request().Context(), &req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "INBOUND_ERROR",
			"message": "入库操作失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// OutboundInventory 库存出库
// @Summary 库存出库操作
// @Description 处理商品出库操作，如销售出库、报损出库等
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param outbound body dto.AdjustInventoryRequest true "出库信息"
// @Success 200 {object} dto.AdjustInventoryResponse "出库成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/outbound [post]
func (h *InventoryHandler) OutboundInventory(c echo.Context) error {
	var req dto.AdjustInventoryRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用库存服务执行出库操作
	response, err := h.manager.InventoryService.Outbound(c.Request().Context(), &req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "OUTBOUND_ERROR",
			"message": "出库操作失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// ===== 库存预留相关接口 =====

// CreateReservation 创建库存预留
// @Summary 创建库存预留
// @Description 为订单创建库存预留
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param reservation body dto.CreateReservationRequest true "预留信息"
// @Success 201 {object} dto.ReservationResponse "创建成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/reservations [post]
func (h *InventoryHandler) CreateReservation(c echo.Context) error {
	var req dto.CreateReservationRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用预留服务创建预留
	reservation, err := h.manager.ReservationService.CreateReservation(c.Request().Context(), &req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "CREATE_RESERVATION_ERROR",
			"message": "创建预留失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusCreated, reservation)
}

// ReleaseReservation 释放库存预留
// @Summary 释放库存预留
// @Description 释放指定的库存预留
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param id path int true "预留ID"
// @Success 200 {object} map[string]interface{} "释放成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/reservations/{id}/release [post]
func (h *InventoryHandler) ReleaseReservation(c echo.Context) error {
	var req dto.ReleaseReservationRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用预留服务释放预留
	response, err := h.manager.ReservationService.ReleaseReservation(c.Request().Context(), &req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "RELEASE_RESERVATION_ERROR",
			"message": "释放预留失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// ===== 盘点相关接口 =====

// StartStocktake 开始盘点
// @Summary 开始盘点任务
// @Description 创建新的盘点任务
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param stocktake body dto.StartStocktakeRequest true "盘点信息"
// @Success 201 {object} dto.StartStocktakeResponse "创建成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/stocktakes [post]
func (h *InventoryHandler) StartStocktake(c echo.Context) error {
	var req dto.StartStocktakeRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用库存服务开始盘点
	response, err := h.manager.InventoryService.StartStocktake(c.Request().Context(), &req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "START_STOCKTAKE_ERROR",
			"message": "开始盘点失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusCreated, response)
}

// RecordStocktakeItem 录入盘点数量
// @Summary 录入盘点数量
// @Description 更新盘点项的实际盘点数量
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param record body dto.RecordItemRequest true "盘点数量信息"
// @Success 200 {object} dto.RecordItemResponse "录入成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/stocktakes/items/record [post]
func (h *InventoryHandler) RecordStocktakeItem(c echo.Context) error {
	var req dto.RecordItemRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用库存服务录入盘点数量
	response, err := h.manager.InventoryService.RecordStocktakeItem(c.Request().Context(), &req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "RECORD_STOCKTAKE_ITEM_ERROR",
			"message": "录入盘点数量失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// CompleteStocktake 完成盘点
// @Summary 完成盘点任务
// @Description 完成盘点任务并生成库存调整
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param id path int true "盘点任务ID"
// @Success 200 {object} dto.CompleteStocktakeResponse "完成成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/stocktakes/{id}/complete [post]
func (h *InventoryHandler) CompleteStocktake(c echo.Context) error {
	var req dto.CompleteStocktakeRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用库存服务完成盘点
	response, err := h.manager.InventoryService.CompleteStocktake(c.Request().Context(), &req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "COMPLETE_STOCKTAKE_ERROR",
			"message": "完成盘点失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// ListStocktake 获取盘点任务列表
// @Summary 获取盘点任务列表
// @Description 获取盘点任务列表，支持分页、排序和过滤
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param limit query int false "限制返回数量，0表示不限制" default(20)
// @Param offset query int false "偏移量，用于分页" default(0)
// @Param order_by query string false "排序字段" default("created_at")
// @Param order query string false "排序方式 (asc/desc)" default("desc")
// @Param status query string false "按盘点状态过滤 (IN_PROGRESS, COMPLETED, CANCELLED)"
// @Param created_by query int false "按创建人过滤"
// @Param keyword query string false "按盘点单号关键词搜索"
// @Success 200 {object} dto.ListStocktakeResponse "获取成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/stocktakes [get]
func (h *InventoryHandler) ListStocktake(c echo.Context) error {
	// 解析查询参数
	req := &dto.ListStocktakeRequest{}

	if err := c.Bind(req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用库存服务获取盘点任务列表
	response, err := h.manager.InventoryService.ListStocktake(c.Request().Context(), req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "LIST_STOCKTAKE_ERROR",
			"message": "获取盘点任务列表失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// GetStocktake 获取盘点任务详情
// @Summary 获取盘点任务详情
// @Description 根据盘点任务ID获取盘点任务详细信息，包含所有盘点项
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param id path int true "盘点任务ID"
// @Success 200 {object} dto.GetStocktakeResponse "获取成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 404 {object} map[string]string "盘点任务不存在"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/stocktakes/{id} [get]
func (h *InventoryHandler) GetStocktake(c echo.Context) error {
	var req dto.GetStocktakeRequest

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用库存服务获取盘点任务详情
	response, err := h.manager.InventoryService.GetStocktake(c.Request().Context(), &req)
	if err != nil {
		// 根据错误类型返回不同的状态码
		if strings.Contains(err.Error(), "盘点任务不存在") {
			return c.JSON(http.StatusNotFound, map[string]string{
				"error":   "STOCKTAKE_NOT_FOUND",
				"message": "盘点任务不存在",
				"details": err.Error(),
			})
		}
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "GET_STOCKTAKE_ERROR",
			"message": "获取盘点任务详情失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// ===== 预警相关接口 =====

// GetWarnings 获取库存预警列表
// @Summary 获取库存预警
// @Description 获取当前的库存预警信息
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param limit query int false "限制返回数量" default(50)
// @Success 200 {array} dto.WarningResponse "获取成功"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/warnings [get]
func (h *InventoryHandler) GetWarnings(c echo.Context) error {
	// 调用预警服务获取预警列表
	warnings, err := h.manager.WarningService.ListActiveWarnings(c.Request().Context())
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "GET_WARNINGS_ERROR",
			"message": "获取预警列表失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, warnings)
}

// ===== 分类管理相关接口 =====

// CreateCategory 创建商品分类
// @Summary 创建新分类
// @Description 创建新的商品分类，支持层级结构
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param category body dto.CreateCategoryRequest true "分类信息"
// @Success 201 {object} dto.CategoryResponse "创建成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/categories [post]
func (h *InventoryHandler) CreateCategory(c echo.Context) error {
	var req dto.CreateCategoryRequest

	// 绑定请求参数
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用分类服务创建分类
	category, err := h.manager.CategoryService.CreateCategory(c.Request().Context(), &req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "CREATE_CATEGORY_ERROR",
			"message": "创建分类失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusCreated, category)
}

// GetCategory 根据ID获取分类
// @Summary 获取分类信息
// @Description 根据分类ID获取分类详细信息
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param id path int true "分类ID"
// @Success 200 {object} dto.CategoryResponse "获取成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 404 {object} map[string]string "分类不存在"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/categories/{id} [get]
func (h *InventoryHandler) GetCategory(c echo.Context) error {
	// 解析分类ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "INVALID_ID",
			"message": "分类ID格式错误",
			"details": "ID必须是有效的数字",
		})
	}

	// 调用分类服务获取分类
	category, err := h.manager.CategoryService.GetCategoryByID(c.Request().Context(), id)
	if err != nil {
		return c.JSON(http.StatusNotFound, map[string]string{
			"error":   "CATEGORY_NOT_FOUND",
			"message": "分类不存在",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, category)
}

// ListCategories 获取分类列表
// @Summary 获取分类列表
// @Description 获取分类列表，支持分页查询
// @Tags 库存服务
// @Accept json
// @Produce json
// @Param limit query int false "限制返回数量，0表示不限制" default(0)
// @Param offset query int false "偏移量，用于分页" default(0)
// @Param order_by query string false "排序字段" default("id")
// @Param order query string false "排序方式 (asc/desc)" default("asc")
// @Success 200 {object} dto.ListCategoriesResponse "获取成功"
// @Failure 400 {object} map[string]string "请求参数错误"
// @Failure 500 {object} map[string]string "内部服务器错误"
// @Router /api/v1/inventory/categories [get]
func (h *InventoryHandler) ListCategories(c echo.Context) error {
	// 解析查询参数
	req := &dto.ListCategoriesRequest{}

	if err := c.Bind(req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error":   "BIND_ERROR",
			"message": "请求参数格式错误",
			"details": err.Error(),
		})
	}

	// 调用分类服务获取分类列表
	response, err := h.manager.CategoryService.ListCategories(c.Request().Context(), req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{
			"error":   "LIST_CATEGORIES_ERROR",
			"message": "获取分类列表失败",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, response)
}
