package handlers

import (
	"net/http"
	"strconv"
	"time"

	"gitlab4.weicheche.cn/indo-bp/oil-service/sdk/oil"

	"github.com/labstack/echo/v4"
)

// PriceRequest 价格设置请求
type PriceRequest struct {
	Price          float64   `json:"price"`
	CostPrice      float64   `json:"cost_price"`
	SuggestPrice   float64   `json:"suggest_price"`
	WholesalePrice float64   `json:"wholesale_price"`
	EffectiveAt    time.Time `json:"effective_at"`
	Operator       string    `json:"operator"`
}

// OilHandler 油品管理处理器
type OilHandler struct {
	oilSDK oil.OilSDK
}

// NewOilHandler 创建新的油品管理处理器
func NewOilHandler(oilSDK oil.OilSDK) *OilHandler {
	return &OilHandler{
		oilSDK: oilSDK,
	}
}

// ListOilProducts 获取油品列表
// @Summary 获取油品列表
// @Description 根据分类、关键词和激活状态获取油品列表
// @Tags 油品管理
// @Accept json
// @Produce json
// @Param category query string false "油品分类"
// @Param keyword query string false "搜索关键词"
// @Param is_active query string false "是否激活，true或false"
// @Success 200 {array} model.OilProduct "油品列表"
// @Failure 500 {object} map[string]string "服务器内部错误"
// @Router /oil/products [get]
func (h *OilHandler) ListOilProducts(c echo.Context) error {
	// 构造过滤条件
	filter := &oil.ProductFilter{
		Category: c.QueryParam("category"),
		Keyword:  c.QueryParam("keyword"),
	}

	// 处理是否激活的过滤
	if isActiveStr := c.QueryParam("is_active"); isActiveStr != "" {
		isActive := isActiveStr == "true"
		filter.IsActive = &isActive
	}

	// 调用SDK获取油品列表
	products, err := h.oilSDK.ListOilProducts(c.Request().Context(), filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, products)
}

// GetOilProduct 获取单个油品详情
// @Summary 获取单个油品详情
// @Description 根据产品ID获取油品详细信息
// @Tags 油品管理
// @Accept json
// @Produce json
// @Param id path uint true "产品ID"
// @Success 200 {object} model.OilProduct "油品详情"
// @Failure 400 {object} map[string]string "无效的产品ID"
// @Failure 404 {object} map[string]string "未找到产品"
// @Failure 500 {object} map[string]string "服务器内部错误"
// @Router /oil/products/{id} [get]
func (h *OilHandler) GetOilProduct(c echo.Context) error {
	// 获取产品ID
	productID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "无效的产品ID"})
	}

	// 调用SDK获取油品信息
	product, err := h.oilSDK.GetOilProduct(c.Request().Context(), uint(productID))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	if product == nil {
		return c.JSON(http.StatusNotFound, map[string]string{"error": "未找到产品"})
	}

	return c.JSON(http.StatusOK, product)
}

// CreateOilProduct 创建新油品
// @Summary 创建新油品
// @Description 创建新的油品产品
// @Tags 油品管理
// @Accept json
// @Produce json
// @Param request body oil.CreateProductRequest true "创建油品请求"
// @Success 201 {object} model.OilProduct "创建的油品信息"
// @Failure 400 {object} map[string]string "无效的请求数据"
// @Failure 500 {object} map[string]string "服务器内部错误"
// @Router /oil/products [post]
func (h *OilHandler) CreateOilProduct(c echo.Context) error {
	// 解析请求体
	createReq := new(oil.CreateProductRequest)
	if err := c.Bind(createReq); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "无效的请求数据"})
	}

	// 调用SDK创建油品
	product, err := h.oilSDK.CreateOilProduct(c.Request().Context(), createReq)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusCreated, product)
}

// UpdateOilProduct 更新油品信息
// @Summary 更新油品信息
// @Description 更新指定ID的油品信息
// @Tags 油品管理
// @Accept json
// @Produce json
// @Param id path uint true "产品ID"
// @Param request body oil.UpdateProductRequest true "更新油品请求"
// @Success 200 {object} map[string]string "更新成功提示"
// @Failure 400 {object} map[string]string "无效的产品ID或请求数据"
// @Failure 500 {object} map[string]string "服务器内部错误"
// @Router /oil/products/{id} [put]
func (h *OilHandler) UpdateOilProduct(c echo.Context) error {
	// 获取产品ID
	productID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "无效的产品ID"})
	}

	// 解析请求体
	updateReq := new(oil.UpdateProductRequest)
	if err := c.Bind(updateReq); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "无效的请求数据"})
	}

	// 确保ID一致
	updateReq.ID = uint(productID)

	// 调用SDK更新油品
	err = h.oilSDK.UpdateOilProduct(c.Request().Context(), updateReq)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "更新成功"})
}

// DeleteOilProduct 删除油品
// @Summary 删除油品
// @Description 删除指定的油品，删除前会检查是否存在关联的价格记录
// @Tags 油品管理
// @Accept json
// @Produce json
// @Param id path uint true "产品ID"
// @Success 200 {object} map[string]string "删除成功提示"
// @Failure 400 {object} map[string]string "无效的产品ID"
// @Failure 404 {object} map[string]string "产品不存在"
// @Failure 409 {object} map[string]interface{} "存在依赖关系，无法删除"
// @Failure 500 {object} map[string]string "服务器内部错误"
// @Router /oil/products/{id} [delete]
func (h *OilHandler) DeleteOilProduct(c echo.Context) error {
	// 获取产品ID
	productID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "无效的产品ID"})
	}

	// 调用SDK删除油品
	err = h.oilSDK.DeleteOilProduct(c.Request().Context(), uint(productID))
	if err != nil {
		// 检查是否是依赖错误
		if depErr, ok := err.(*DependencyError); ok {
			return c.JSON(http.StatusConflict, map[string]interface{}{
				"error": depErr.Message,
				"dependencies": map[string]interface{}{
					"price_records": depErr.PriceCount,
					"product_name":  depErr.ProductName,
				},
			})
		}

		// 检查是否是产品不存在错误
		if err.Error() == "油品不存在" {
			return c.JSON(http.StatusNotFound, map[string]string{"error": err.Error()})
		}

		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "产品删除成功"})
}

// DependencyError 依赖错误类型（与service中的保持一致）
type DependencyError struct {
	Message     string
	ProductID   uint
	PriceCount  int64
	ProductName string
}

func (e *DependencyError) Error() string {
	return e.Message
}

// 价格调整相关方法已移除 - 简化架构
