package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
	"gitlab4.weicheche.cn/indo-bp/oil-service/pkg/oil/pricing"
	"gitlab4.weicheche.cn/indo-bp/oil-service/pkg/oil/repository/postgres"
	"gorm.io/gorm"
)

// OilPriceHandler 油品价格管理处理器
type OilPriceHandler struct {
	stationPriceService pricing.StationPriceService
}

// NewOilPriceHandler 创建新的油品价格处理器
func NewOilPriceHandler(db *gorm.DB) *OilPriceHandler {
	// 创建仓储和服务
	repo := postgres.NewPostgresStationPriceRepository(db)
	service := pricing.NewStationPriceService(repo)

	return &OilPriceHandler{
		stationPriceService: service,
	}
}

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// PaginatedResponse 分页响应结构
type PaginatedResponse struct {
	Items      interface{} `json:"items"`
	Pagination Pagination  `json:"pagination"`
}

// Pagination 分页信息
type Pagination struct {
	Page  int `json:"page"`
	Limit int `json:"limit"`
	Total int `json:"total"`
	Pages int `json:"pages"`
}

// GetStationPrices 获取站点价格列表
func (h *OilPriceHandler) GetStationPrices(c echo.Context) error {
	stationIDStr := c.Param("station_id")
	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的站点ID",
			Data:    nil,
		})
	}

	prices, err := h.stationPriceService.GetStationPrices(c.Request().Context(), stationID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取站点价格失败: " + err.Error(),
			Data:    nil,
		})
	}

	return c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "success",
		Data:    prices,
	})
}

// GetEffectivePrice 获取有效价格
func (h *OilPriceHandler) GetEffectivePrice(c echo.Context) error {
	stationIDStr := c.QueryParam("station_id")
	productIDStr := c.QueryParam("product_id")

	if stationIDStr == "" || productIDStr == "" {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "station_id和product_id参数不能为空",
			Data:    nil,
		})
	}

	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的站点ID",
			Data:    nil,
		})
	}

	productID, err := strconv.ParseUint(productIDStr, 10, 32)
	if err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的油品ID",
			Data:    nil,
		})
	}

	price, err := h.stationPriceService.GetEffectivePrice(c.Request().Context(), stationID, uint(productID))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取有效价格失败: " + err.Error(),
			Data:    nil,
		})
	}

	if price == nil {
		return c.JSON(http.StatusNotFound, Response{
			Code:    404,
			Message: "未找到有效价格",
			Data:    nil,
		})
	}

	return c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "success",
		Data:    price,
	})
}

// SetStationPrice 设置站点价格
func (h *OilPriceHandler) SetStationPrice(c echo.Context) error {
	stationIDStr := c.Param("station_id")
	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的站点ID",
			Data:    nil,
		})
	}

	var req StationPriceRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
			Data:    nil,
		})
	}

	// 验证请求参数
	if req.Price <= 0 {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "价格必须大于0",
			Data:    nil,
		})
	}

	// 设置默认生效时间
	effectiveAt := time.Now()
	if req.EffectiveAt != nil {
		effectiveAt = *req.EffectiveAt
	}

	// 获取操作人信息（从JWT token或其他方式）
	operator := "system"
	if userID := c.Get("user_id"); userID != nil {
		if userIDStr, ok := userID.(string); ok {
			operator = userIDStr
		}
	}

	priceID, err := h.stationPriceService.SetStationPrice(
		c.Request().Context(),
		stationID,
		req.ProductID,
		req.Price,
		req.CostPrice,
		req.SuggestPrice,
		req.WholesalePrice,
		effectiveAt,
		req.ExpiresAt,
		operator,
	)

	if err != nil {
		return c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "设置站点价格失败: " + err.Error(),
			Data:    nil,
		})
	}

	return c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "设置站点价格成功",
		Data: map[string]interface{}{
			"id":           priceID,
			"effective_at": effectiveAt,
		},
	})
}

// StationPriceRequest 站点价格请求结构
type StationPriceRequest struct {
	ProductID      uint       `json:"product_id" validate:"required"`
	Price          float64    `json:"price" validate:"required,gt=0"`
	CostPrice      float64    `json:"cost_price"`
	SuggestPrice   float64    `json:"suggest_price"`
	WholesalePrice float64    `json:"wholesale_price"`
	EffectiveAt    *time.Time `json:"effective_at"`
	ExpiresAt      *time.Time `json:"expires_at"`
}

// BatchPriceRequest 批量价格请求结构
type BatchPriceRequest struct {
	Prices      []pricing.PriceUpdate `json:"prices" validate:"required,dive"`
	EffectiveAt *time.Time            `json:"effective_at"`
}

// BatchSetStationPrices 批量设置站点价格
func (h *OilPriceHandler) BatchSetStationPrices(c echo.Context) error {
	stationIDStr := c.Param("station_id")
	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的站点ID",
			Data:    nil,
		})
	}

	var req BatchPriceRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
			Data:    nil,
		})
	}

	// 验证请求参数
	if len(req.Prices) == 0 {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "价格列表不能为空",
			Data:    nil,
		})
	}

	for i, price := range req.Prices {
		if price.Price <= 0 {
			return c.JSON(http.StatusBadRequest, Response{
				Code:    400,
				Message: "第" + strconv.Itoa(i+1) + "个价格必须大于0",
				Data:    nil,
			})
		}
	}

	// 设置默认生效时间
	effectiveAt := time.Now()
	if req.EffectiveAt != nil {
		effectiveAt = *req.EffectiveAt
	}

	// 获取操作人信息
	operator := "system"
	if userID := c.Get("user_id"); userID != nil {
		if userIDStr, ok := userID.(string); ok {
			operator = userIDStr
		}
	}

	err = h.stationPriceService.BatchSetStationPrices(
		c.Request().Context(),
		stationID,
		req.Prices,
		effectiveAt,
		operator,
	)

	if err != nil {
		return c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "批量设置站点价格失败: " + err.Error(),
			Data:    nil,
		})
	}

	return c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "批量设置站点价格成功",
		Data:    nil,
	})
}

// GetStationProductPrice 获取站点特定产品价格
func (h *OilPriceHandler) GetStationProductPrice(c echo.Context) error {
	stationIDStr := c.Param("station_id")
	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的站点ID",
			Data:    nil,
		})
	}

	productIDStr := c.Param("product_id")
	productID, err := strconv.ParseUint(productIDStr, 10, 32)
	if err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的产品ID",
			Data:    nil,
		})
	}

	price, err := h.stationPriceService.GetEffectivePrice(c.Request().Context(), stationID, uint(productID))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取站点产品价格失败: " + err.Error(),
			Data:    nil,
		})
	}

	if price == nil {
		return c.JSON(http.StatusNotFound, Response{
			Code:    404,
			Message: "未找到该站点的产品价格",
			Data:    nil,
		})
	}

	return c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "success",
		Data:    price,
	})
}

// SetGlobalPrice 设置全局价格
func (h *OilPriceHandler) SetGlobalPrice(c echo.Context) error {
	var req StationPriceRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
			Data:    nil,
		})
	}

	// 验证请求参数
	if req.Price <= 0 {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "价格必须大于0",
			Data:    nil,
		})
	}

	// 设置默认生效时间
	effectiveAt := time.Now()
	if req.EffectiveAt != nil {
		effectiveAt = *req.EffectiveAt
	}

	// 获取操作人信息
	operator := "system"
	if userID := c.Get("user_id"); userID != nil {
		if userIDStr, ok := userID.(string); ok {
			operator = userIDStr
		}
	}

	err := h.stationPriceService.SetGlobalPrice(
		c.Request().Context(),
		req.ProductID,
		req.Price,
		req.CostPrice,
		req.SuggestPrice,
		req.WholesalePrice,
		effectiveAt,
		operator,
	)

	if err != nil {
		return c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "设置全局价格失败: " + err.Error(),
			Data:    nil,
		})
	}

	return c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "设置全局价格成功",
		Data:    nil,
	})
}

// GetPriceHistory 获取价格历史
func (h *OilPriceHandler) GetPriceHistory(c echo.Context) error {
	// 解析分页参数
	page, err := strconv.Atoi(c.QueryParam("page"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.QueryParam("limit"))
	if err != nil || limit < 1 {
		limit = 20
	}
	if limit > 100 {
		limit = 100 // 最大限制100条
	}

	// 可选的product_id参数
	var productID *uint
	productIDStr := c.QueryParam("product_id")
	if productIDStr != "" {
		id, err := strconv.ParseUint(productIDStr, 10, 32)
		if err != nil {
			return c.JSON(http.StatusBadRequest, Response{
				Code:    400,
				Message: "无效的product_id参数",
				Data:    nil,
			})
		}
		pid := uint(id)
		productID = &pid
	}



	var stationID *int64
	if stationIDStr := c.QueryParam("station_id"); stationIDStr != "" {
		id, err := strconv.ParseInt(stationIDStr, 10, 64)
		if err != nil {
			return c.JSON(http.StatusBadRequest, Response{
				Code:    400,
				Message: "无效的站点ID",
				Data:    nil,
			})
		}
		stationID = &id
	}

	// 解析日期范围
	var startTime, endTime time.Time

	if startDate := c.QueryParam("start_date"); startDate != "" {
		startTime, err = time.Parse("2006-01-02", startDate)
		if err != nil {
			return c.JSON(http.StatusBadRequest, Response{
				Code:    400,
				Message: "开始日期格式错误",
				Data:    nil,
			})
		}
	} else {
		startTime = time.Now().AddDate(0, -1, 0) // 默认查询最近一个月
	}

	if endDate := c.QueryParam("end_date"); endDate != "" {
		endTime, err = time.Parse("2006-01-02", endDate)
		if err != nil {
			return c.JSON(http.StatusBadRequest, Response{
				Code:    400,
				Message: "结束日期格式错误",
				Data:    nil,
			})
		}
	} else {
		endTime = time.Now()
	}

	// 调用服务层的分页查询方法
	result, err := h.stationPriceService.GetPriceHistoryPaginated(
		c.Request().Context(),
		productID,
		stationID,
		startTime,
		endTime,
		page,
		limit,
	)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取价格历史失败: " + err.Error(),
			Data:    nil,
		})
	}

	return c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "success",
		Data: PaginatedResponse{
			Items: result.Items,
			Pagination: Pagination{
				Page:  page,
				Limit: limit,
				Total: result.Total,
				Pages: (result.Total + limit - 1) / limit,
			},
		},
	})
}

// GetPriceHierarchy 获取价格层级分析
func (h *OilPriceHandler) GetPriceHierarchy(c echo.Context) error {
	var productID *uint
	var stationID *int64

	if productIDStr := c.QueryParam("product_id"); productIDStr != "" {
		id, err := strconv.ParseUint(productIDStr, 10, 32)
		if err != nil {
			return c.JSON(http.StatusBadRequest, Response{
				Code:    400,
				Message: "无效的油品ID",
				Data:    nil,
			})
		}
		pid := uint(id)
		productID = &pid
	}

	if stationIDStr := c.QueryParam("station_id"); stationIDStr != "" {
		id, err := strconv.ParseInt(stationIDStr, 10, 64)
		if err != nil {
			return c.JSON(http.StatusBadRequest, Response{
				Code:    400,
				Message: "无效的站点ID",
				Data:    nil,
			})
		}
		stationID = &id
	}

	hierarchy, err := h.stationPriceService.GetPriceHierarchy(c.Request().Context(), productID, stationID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取价格层级失败: " + err.Error(),
			Data:    nil,
		})
	}

	return c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "success",
		Data:    hierarchy,
	})
}

// GetPriceAnalysis 获取价格分析报表
func (h *OilPriceHandler) GetPriceAnalysis(c echo.Context) error {
	startDate := c.QueryParam("start_date")
	endDate := c.QueryParam("end_date")

	if startDate == "" || endDate == "" {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "start_date和end_date参数不能为空",
			Data:    nil,
		})
	}

	var productID *uint
	var stationID *int64

	if productIDStr := c.QueryParam("product_id"); productIDStr != "" {
		id, err := strconv.ParseUint(productIDStr, 10, 32)
		if err != nil {
			return c.JSON(http.StatusBadRequest, Response{
				Code:    400,
				Message: "无效的油品ID",
				Data:    nil,
			})
		}
		pid := uint(id)
		productID = &pid
	}

	if stationIDStr := c.QueryParam("station_id"); stationIDStr != "" {
		id, err := strconv.ParseInt(stationIDStr, 10, 64)
		if err != nil {
			return c.JSON(http.StatusBadRequest, Response{
				Code:    400,
				Message: "无效的站点ID",
				Data:    nil,
			})
		}
		stationID = &id
	}

	analysis, err := h.stationPriceService.GetPriceAnalysis(c.Request().Context(), startDate, endDate, productID, stationID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取价格分析失败: " + err.Error(),
			Data:    nil,
		})
	}

	return c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "success",
		Data:    analysis,
	})
}

// DeleteFuturePrice 删除未生效的价格
func (h *OilPriceHandler) DeleteFuturePrice(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的价格ID",
			Data:    nil,
		})
	}

	err = h.stationPriceService.DeleteFuturePrice(c.Request().Context(), uint(id))
	if err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: err.Error(),
			Data:    nil,
		})
	}

	return c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "未生效价格删除成功",
		Data:    nil,
	})
}

// UpdateFuturePrice 更新未生效的价格
func (h *OilPriceHandler) UpdateFuturePrice(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的价格ID",
			Data:    nil,
		})
	}

	var req struct {
		ProductID   uint    `json:"product_id" validate:"required"`
		Price       float64 `json:"price" validate:"required,gt=0"`
		EffectiveAt string  `json:"effective_at" validate:"required"`
		Operator    string  `json:"operator" validate:"required"`
	}

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
			Data:    nil,
		})
	}

	// 解析生效时间
	effectiveAt, err := time.Parse("2006-01-02T15:04:05-07:00", req.EffectiveAt)
	if err != nil {
		// 尝试其他时间格式
		if effectiveAt, err = time.Parse("2006-01-02T15:04:05Z", req.EffectiveAt); err != nil {
			if effectiveAt, err = time.Parse("2006-01-02T15:04:05+07:00", req.EffectiveAt); err != nil {
				return c.JSON(http.StatusBadRequest, Response{
					Code:    400,
					Message: "生效时间格式错误，请使用 ISO 8601 格式",
					Data:    nil,
				})
			}
		}
	}

	err = h.stationPriceService.UpdateFuturePrice(c.Request().Context(), uint(id), req.Price, effectiveAt, req.Operator)
	if err != nil {
		return c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: err.Error(),
			Data:    nil,
		})
	}

	return c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "未生效价格更新成功",
		Data:    nil,
	})
}
