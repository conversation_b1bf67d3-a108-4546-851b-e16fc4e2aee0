// bos/internal/api/handlers/payment_handler.go
package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"

	// 支付服务相关导入 - 只使用公开接口
	paymentErrors "gitlab4.weicheche.cn/indo-bp/payment-service/pkg/payment/errors"
	paymentModel "gitlab4.weicheche.cn/indo-bp/payment-service/pkg/payment/model"
	paymentService "gitlab4.weicheche.cn/indo-bp/payment-service/pkg/payment/service"
)

// PaymentHandler 支付处理器
type PaymentHandler struct {
	paymentService paymentService.PaymentService
	// 注意：由于Go的internal包限制，我们需要等待payment-service提供公开的初始化SDK
	// 目前使用接口定义，具体实现需要payment-service提供公开的工厂方法
}

// NewPaymentHandler 创建新的支付处理器实例
func NewPaymentHandler(
	paymentService paymentService.PaymentService,
) *PaymentHandler {
	// 直接使用传入的支付服务实例
	handler := &PaymentHandler{
		paymentService: paymentService,
	}

	return handler
}

// PaymentRequest HTTP请求结构
type PaymentRequest struct {
	OrderID        string                 `json:"order_id" validate:"required"`
	Amount         float64                `json:"amount" validate:"required,gt=0"`
	Currency       string                 `json:"currency" validate:"required"`
	PaymentType    string                 `json:"payment_type" validate:"required"`
	PaymentMethod  int64                  `json:"payment_method"`
	CustomerID     string                 `json:"customer_id"`
	CustomerName   string                 `json:"customer_name"`
	StationID      int64                  `json:"station_id" validate:"required"`
	TerminalID     string                 `json:"terminal_id"`
	OperatorID     string                 `json:"operator_id"`
	PaymentParams  map[string]interface{} `json:"payment_params"`
	NotifyURL      string                 `json:"notify_url"`
	ReturnURL      string                 `json:"return_url"`
	TimeoutMinutes int                    `json:"timeout_minutes"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// RefundRequest HTTP退款请求结构
type RefundRequest struct {
	PaymentID  int64                  `json:"payment_id" validate:"required"`
	Amount     float64                `json:"amount" validate:"required,gt=0"`
	Reason     string                 `json:"reason" validate:"required"`
	OperatorID string                 `json:"operator_id"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// ProcessPayment 处理支付请求
func (h *PaymentHandler) ProcessPayment(c echo.Context) error {
	var req PaymentRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "Invalid request format",
		})
	}

	// 检查服务是否已初始化
	if h.paymentService == nil {
		return c.JSON(http.StatusServiceUnavailable, map[string]interface{}{
			"success": false,
			"error": map[string]interface{}{
				"code":    "SERVICE_NOT_AVAILABLE",
				"message": "Payment service is not initialized. Please configure payment-service SDK.",
			},
		})
	}

	// 转换为支付服务请求格式
	paymentReq := &paymentModel.PaymentRequest{
		OrderID:        req.OrderID,
		Amount:         req.Amount,
		Currency:       req.Currency,
		PaymentType:    paymentModel.PaymentType(req.PaymentType),
		PaymentMethod:  req.PaymentMethod,
		CustomerID:     req.CustomerID,
		CustomerName:   req.CustomerName,
		StationID:      req.StationID,
		TerminalID:     req.TerminalID,
		OperatorID:     req.OperatorID,
		PaymentParams:  req.PaymentParams,
		NotifyURL:      req.NotifyURL,
		ReturnURL:      req.ReturnURL,
		TimeoutMinutes: req.TimeoutMinutes,
		Metadata:       req.Metadata,
	}

	resp, err := h.paymentService.ProcessPayment(c.Request().Context(), paymentReq)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    resp,
	})
}

// GetPayment 获取支付记录详情
func (h *PaymentHandler) GetPayment(c echo.Context) error {
	if h.paymentService == nil {
		return h.serviceNotAvailable(c)
	}

	paymentID := c.Param("id")
	payment, err := h.paymentService.GetPayment(c.Request().Context(), paymentID)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    payment,
	})
}

// GetPaymentStatus 查询支付状态
func (h *PaymentHandler) GetPaymentStatus(c echo.Context) error {
	if h.paymentService == nil {
		return h.serviceNotAvailable(c)
	}

	paymentID := c.Param("id")
	status, err := h.paymentService.QueryPaymentStatus(c.Request().Context(), paymentID)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"payment_id": paymentID,
			"status":     status,
		},
	})
}

// CancelPayment 取消支付
func (h *PaymentHandler) CancelPayment(c echo.Context) error {
	if h.paymentService == nil {
		return h.serviceNotAvailable(c)
	}

	paymentID := c.Param("id")
	reason := c.QueryParam("reason")
	if reason == "" {
		reason = "User cancelled"
	}

	err := h.paymentService.CancelPayment(c.Request().Context(), paymentID, reason)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "Payment cancelled successfully",
	})
}

// ProcessRefund 处理退款请求
func (h *PaymentHandler) ProcessRefund(c echo.Context) error {
	if h.paymentService == nil {
		return h.serviceNotAvailable(c)
	}

	var req RefundRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "Invalid request format",
		})
	}

	// 转换为支付服务退款请求格式
	refundReq := &paymentModel.RefundRequest{
		PaymentID:  req.PaymentID,
		Amount:     req.Amount,
		Reason:     req.Reason,
		OperatorID: req.OperatorID,
		Metadata:   req.Metadata,
	}

	resp, err := h.paymentService.ProcessRefund(c.Request().Context(), refundReq)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    resp,
	})
}

// GetRefundStatus 查询退款状态
func (h *PaymentHandler) GetRefundStatus(c echo.Context) error {
	if h.paymentService == nil {
		return h.serviceNotAvailable(c)
	}

	refundID := c.Param("id")
	status, err := h.paymentService.QueryRefundStatus(c.Request().Context(), refundID)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"refund_id": refundID,
			"status":    status,
		},
	})
}

// ListPaymentMethods 获取支付方式列表
func (h *PaymentHandler) ListPaymentMethods(c echo.Context) error {
	if h.paymentService == nil {
		return h.serviceNotAvailable(c)
	}

	// 构造过滤条件
	filter := &paymentModel.PaymentMethodFilter{}

	// 解析查询参数
	if enabled := c.QueryParam("enabled"); enabled != "" {
		if e, err := strconv.ParseBool(enabled); err == nil {
			filter.Enabled = &e
		}
	}

	if stationID := c.QueryParam("station_id"); stationID != "" {
		if s, err := strconv.ParseInt(stationID, 10, 64); err == nil {
			filter.StationID = &s
		}
	}

	if paymentType := c.QueryParam("type"); paymentType != "" {
		pt := paymentModel.PaymentType(paymentType)
		filter.Type = &pt
	}

	if gatewayType := c.QueryParam("gateway_type"); gatewayType != "" {
		filter.GatewayType = &gatewayType
	}

	// 支持关键词搜索，同时匹配name和display_name字段
	if keyword := c.QueryParam("keyword"); keyword != "" {
		filter.Keyword = &keyword
	}

	methods, err := h.paymentService.GetPaymentMethods(c.Request().Context(), filter)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    methods,
	})
}

// CreatePaymentMethod 创建支付方式
func (h *PaymentHandler) CreatePaymentMethod(c echo.Context) error {
	if h.paymentService == nil {
		return h.serviceNotAvailable(c)
	}

	var method paymentModel.PaymentMethod
	if err := c.Bind(&method); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "Invalid request format",
		})
	}

	createdMethod, err := h.paymentService.CreatePaymentMethod(c.Request().Context(), &method)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusCreated, map[string]interface{}{
		"success": true,
		"data":    createdMethod,
	})
}

// UpdatePaymentMethod 更新支付方式
func (h *PaymentHandler) UpdatePaymentMethod(c echo.Context) error {
	if h.paymentService == nil {
		return h.serviceNotAvailable(c)
	}

	methodID := c.Param("id")
	var updates paymentModel.PaymentMethodUpdate
	if err := c.Bind(&updates); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "Invalid request format",
		})
	}

	updatedMethod, err := h.paymentService.UpdatePaymentMethod(c.Request().Context(), methodID, &updates)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    updatedMethod,
	})
}

// EnablePaymentMethod 启用支付方式
func (h *PaymentHandler) EnablePaymentMethod(c echo.Context) error {
	if h.paymentService == nil {
		return h.serviceNotAvailable(c)
	}

	methodID := c.Param("id")
	err := h.paymentService.EnablePaymentMethod(c.Request().Context(), methodID)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "Payment method enabled successfully",
	})
}

// DisablePaymentMethod 禁用支付方式
func (h *PaymentHandler) DisablePaymentMethod(c echo.Context) error {
	if h.paymentService == nil {
		return h.serviceNotAvailable(c)
	}

	methodID := c.Param("id")
	err := h.paymentService.DisablePaymentMethod(c.Request().Context(), methodID)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "Payment method disabled successfully",
	})
}

// ListPayments 查询支付记录列表
func (h *PaymentHandler) ListPayments(c echo.Context) error {
	if h.paymentService == nil {
		return h.serviceNotAvailable(c)
	}

	// 构造过滤条件
	filter := &paymentModel.PaymentFilter{}

	// 解析查询参数
	if stationID := c.QueryParam("station_id"); stationID != "" {
		if s, err := strconv.ParseInt(stationID, 10, 64); err == nil {
			filter.StationID = &s
		}
	}

	if paymentType := c.QueryParam("payment_type"); paymentType != "" {
		pt := paymentModel.PaymentType(paymentType)
		filter.PaymentType = &pt
	}

	if status := c.QueryParam("status"); status != "" {
		st := paymentModel.PaymentStatus(status)
		filter.Status = &st
	}

	if customerID := c.QueryParam("customer_id"); customerID != "" {
		filter.CustomerID = &customerID
	}

	if minAmount := c.QueryParam("min_amount"); minAmount != "" {
		if ma, err := strconv.ParseFloat(minAmount, 64); err == nil {
			filter.MinAmount = &ma
		}
	}

	if maxAmount := c.QueryParam("max_amount"); maxAmount != "" {
		if ma, err := strconv.ParseFloat(maxAmount, 64); err == nil {
			filter.MaxAmount = &ma
		}
	}

	// 解析时间过滤
	if startTime := c.QueryParam("start_time"); startTime != "" {
		if st, err := time.Parse(time.RFC3339, startTime); err == nil {
			filter.StartTime = &st
		}
	}

	if endTime := c.QueryParam("end_time"); endTime != "" {
		if et, err := time.Parse(time.RFC3339, endTime); err == nil {
			filter.EndTime = &et
		}
	}

	// 分页参数
	pagination := &paymentModel.Pagination{
		Page:     1,
		PageSize: 20,
		OrderBy:  "created_at",
		Order:    "DESC",
	}

	if page := c.QueryParam("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			pagination.Page = p
		}
	}

	if pageSize := c.QueryParam("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			pagination.PageSize = ps
		}
	}

	if orderBy := c.QueryParam("order_by"); orderBy != "" {
		pagination.OrderBy = orderBy
	}

	if order := c.QueryParam("order"); order != "" {
		pagination.Order = order
	}

	payments, total, err := h.paymentService.ListPayments(c.Request().Context(), filter, pagination)
	if err != nil {
		return h.handleError(c, err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"payments": payments,
			"total":    total,
			"page":     pagination.Page,
			"pageSize": pagination.PageSize,
		},
	})
}

// serviceNotAvailable 服务不可用错误响应
func (h *PaymentHandler) serviceNotAvailable(c echo.Context) error {
	return c.JSON(http.StatusServiceUnavailable, map[string]interface{}{
		"success": false,
		"error": map[string]interface{}{
			"code":    "SERVICE_NOT_AVAILABLE",
			"message": "Payment service is not initialized. Please configure payment-service SDK.",
		},
	})
}

// handleError 处理错误响应
func (h *PaymentHandler) handleError(c echo.Context, err error) error {
	// 根据错误类型返回相应的HTTP状态码
	status := http.StatusInternalServerError
	message := err.Error()
	code := "INTERNAL_ERROR"

	// 检查是否是支付服务的业务错误
	if paymentErrors.IsPaymentError(err) {
		code = paymentErrors.GetErrorCode(err)
		switch code {
		case paymentErrors.CodePaymentNotFound,
			paymentErrors.CodeRefundNotFound,
			paymentErrors.CodePaymentMethodNotFound,
			paymentErrors.CodeResourceNotFound:
			status = http.StatusNotFound
		case paymentErrors.CodeInvalidParameter,
			paymentErrors.CodeInvalidAmount:
			status = http.StatusBadRequest
		case paymentErrors.CodePaymentAlreadyPaid,
			paymentErrors.CodeRefundAlreadyProcessed:
			status = http.StatusConflict
		case paymentErrors.CodeUnauthorized:
			status = http.StatusUnauthorized
		case paymentErrors.CodePaymentMethodDisabled,
			paymentErrors.CodePaymentLimitExceeded,
			paymentErrors.CodeRefundNotAllowed:
			status = http.StatusForbidden
		case paymentErrors.CodeGatewayUnavailable,
			paymentErrors.CodeGatewayTimeout:
			status = http.StatusServiceUnavailable
		default:
			status = http.StatusInternalServerError
		}
	} else {
		// 处理其他类型的错误
		if err.Error() == "validation failed" {
			status = http.StatusBadRequest
			code = "VALIDATION_ERROR"
		}
	}

	return c.JSON(status, map[string]interface{}{
		"success": false,
		"error": map[string]interface{}{
			"code":    code,
			"message": message,
		},
	})
}
