// Package handlers 处理HTTP请求
// @title 促销计算API
// @version 1.0
// @description 提供促销活动计算和规则应用功能
// @BasePath /api/v1
package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"strings"
	"time"

	"github.com/labstack/echo/v4"
	"gitlab4.weicheche.cn/indo-bp/promotion-service/pkg/types"
	"gitlab4.weicheche.cn/indo-bp/promotion-service/sdk"
)

// PromotionCalcHandler 处理所有API请求的结构体
type PromotionCalcHandler struct {
	PromotionSDK sdk.PromotionSDK
	// Mock相关字段
	UseMockMode bool
	MockService *FuelPromotionMock
}

// Order 表示传入的订单信息
type Order struct {
	OrderID     string      `json:"orderId" example:"WEB-2025-07-13T1734" description:"订单ID"`
	UserID      string      `json:"userId" example:"user123" description:"用户ID"`
	OrderAmount float64     `json:"orderAmount" example:"450000" description:"订单总金额（印尼盾）"`
	OrderTime   time.Time   `json:"orderTime" example:"2023-01-01T12:00:00Z" description:"下单时间"`
	VehicleType string      `json:"vehicleType,omitempty" example:"CAR" description:"车辆类型：CAR（汽车）或MOTORCYCLE（摩托车）"`
	Items       []OrderItem `json:"items" description:"订单商品列表"`
}

// OrderItem 表示订单中的商品项
type OrderItem struct {
	ItemID string `json:"itemId" example:"BP_92" description:"商品ID"`
	Name   string `json:"name" example:"燃油" description:"商品名称"`
	// 向后兼容：保留单一category字段
	Category string `json:"category,omitempty" example:"BP_92" description:"商品分类（兼容性字段）"`
	// 新增：支持多标签分类
	CategoryIDs []string `json:"categoryIds,omitempty" example:"[\"BP_92\", \"fuel\"]" description:"商品分类标签数组（不包含车辆类型）"`
	Price       float64  `json:"price" example:"15000" description:"商品单价（印尼盾）"`
	// 支持精确升数（印尼3位小数）
	Quantity float64 `json:"quantity" example:"30.125" description:"商品数量（升，支持3位小数）"`
	// 移除Attributes字段，fuel_volume直接使用Quantity
}

// DiscountItemResponse 包含单个折扣商品项的响应
type DiscountItemResponse struct {
	ItemID          string                 `json:"itemId" example:"BP_92" description:"商品ID"`
	Name            string                 `json:"name" example:"燃油" description:"商品名称"`
	OriginalPrice   float64                `json:"originalPrice" example:"15000" description:"原始单价（印尼盾）"`
	DiscountedPrice float64                `json:"discountedPrice" example:"14500" description:"折扣后单价（印尼盾）"`
	Quantity        float64                `json:"quantity" example:"30.125" description:"商品数量（升，支持3位小数）"`
	CategoryIDs     []string               `json:"categoryIds,omitempty" example:"[\"BP_92\", \"fuel\", \"CAR\"]" description:"商品分类标签"`
	Attributes      map[string]interface{} `json:"attributes,omitempty" description:"商品属性"`
}

// AppliedPromotion 表示应用的促销活动信息
type AppliedPromotion struct {
	PromotionID     string                 `json:"promotionId" example:"123e4567-e89b-12d3-a456-426614174000" description:"促销活动ID"`
	PromotionName   string                 `json:"promotionName" example:"25升免费1升促销" description:"促销活动名称"`
	DiscountType    string                 `json:"discountType" example:"FREE_ITEM" description:"折扣类型"`
	DiscountValue   float64                `json:"discountValue" example:"15000" description:"折扣值"`
	DiscountAmount  float64                `json:"discountAmount" example:"15000" description:"实际折扣金额"`
	Description     string                 `json:"description" example:"购买25升及以上燃油可享受1升免费" description:"促销描述"`
	ApplicableItems []string               `json:"applicableItems,omitempty" example:"BP_92" description:"适用商品"`
	Metadata        map[string]interface{} `json:"metadata,omitempty" description:"促销元数据"`
}

// DiscountResponse 包含应用折扣后的订单响应
type DiscountResponse struct {
	Success bool   `json:"success" example:"true" description:"是否成功"`
	Message string `json:"message" example:"折扣计算成功" description:"结果消息"`
	// 订单基础信息
	OrderID     string `json:"orderId" example:"WEB-2025-07-13T1734" description:"订单ID"`
	VehicleType string `json:"vehicleType,omitempty" example:"CAR" description:"车辆类型"`
	// 金额信息（确保自洽性）
	OriginalAmount   float64 `json:"originalAmount" example:"450000" description:"原始总金额（印尼盾）"`
	DiscountedAmount float64 `json:"discountedAmount" example:"435000" description:"折扣后总金额（印尼盾）"`
	DiscountAmount   float64 `json:"discountAmount" example:"15000" description:"总折扣金额（印尼盾）"`
	// 促销详情
	AppliedPromotions []AppliedPromotion `json:"appliedPromotions,omitempty" description:"应用的促销活动列表"`
	// 商品详情
	Items []DiscountItemResponse `json:"items" description:"折扣后的商品列表"`
	// 计算元数据
	CalculationTime string  `json:"calculationTime" description:"计算时间"`
	TotalItems      int     `json:"totalItems" description:"商品项总数"`
	TotalQuantity   float64 `json:"totalQuantity" description:"商品总数量（升）"`
}

// NewPromotionCalcHandler 创建新的处理器实例
func NewPromotionCalcHandler(promotionSDK sdk.PromotionSDK) *PromotionCalcHandler {
	return &PromotionCalcHandler{
		PromotionSDK: promotionSDK,
		UseMockMode:  false, // 默认使用真实SDK
		MockService:  NewFuelPromotionMock(),
	}
}

// IndexPromotionCalcHandler 处理主页访问
// @Summary 促销计算器首页
// @Description 返回促销计算服务的主页
// @Tags 促销计算
// @Accept html
// @Produce html
// @Success 200 {string} string "促销服务演示"
// @Router /calculator [get]
func (h *PromotionCalcHandler) IndexPromotionCalcHandler(c echo.Context) error {
	return c.HTML(http.StatusOK, "<html><body><h1>促销服务演示</h1></body></html>")
}

// GetStatus 返回服务状态
// @Summary 获取服务状态
// @Description 返回促销计算服务的当前运行状态
// @Tags 促销计算
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "包含服务状态和当前时间"
// @Router /calculator/status [get]
func (h *PromotionCalcHandler) GetStatus(c echo.Context) error {
	return c.JSON(http.StatusOK, map[string]interface{}{
		"status": "运行中",
		"time":   time.Now().Format(time.RFC3339),
	})
}

// ProcessOrder 处理订单并应用折扣
// @Summary 处理订单并计算折扣
// @Description 接收订单数据，应用适用的促销规则，返回折扣计算结果
// @Tags 促销计算
// @Accept json
// @Produce json
// @Param order body Order true "订单信息"
// @Success 200 {object} DiscountResponse "折扣计算结果"
// @Failure 400 {object} map[string]interface{} "请求数据无效"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /calculator/process [post]
func (h *PromotionCalcHandler) ProcessOrder(c echo.Context) error {
	var order Order
	if err := c.Bind(&order); err != nil {
		log.Printf("解析JSON失败：%v", err)
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"message": "无效的请求数据: " + err.Error(),
		})
	}

	// 校验订单数据
	if order.UserID == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"message": "用户ID不能为空",
		})
	}

	if len(order.Items) == 0 {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"message": "订单必须包含至少一个商品",
		})
	}

	// 准备评估上下文
	ctx := c.Request().Context()

	// 使用SDK或Mock评估折扣
	orderData := createOrderData(order)
	var discounts []types.CalculatedDiscount
	var err error

	if h.UseMockMode {
		// 使用Mock模式
		log.Printf("🔥 [MOCK模式] 使用Mock模式计算折扣，当前规则集: %s", h.MockService.CurrentRuleSet)
		log.Printf("🔥 [MOCK模式] 订单数据: UserID=%s, TotalAmount=%.2f, Items=%d",
			orderData.UserID, orderData.TotalAmount, len(orderData.Items))
		discounts, err = h.MockService.CalculateDiscounts(ctx, orderData)
		log.Printf("🔥 [MOCK模式] Mock计算完成，折扣数量: %d", len(discounts))
	} else {
		// 使用真实SDK
		log.Printf("⚡ [真实SDK] 使用真实SDK计算折扣")
		log.Printf("⚡ [真实SDK] 订单数据: UserID=%s, TotalAmount=%.2f, Items=%d",
			orderData.UserID, orderData.TotalAmount, len(orderData.Items))
		discounts, err = h.PromotionSDK.CalculateDiscounts(ctx, orderData)
		log.Printf("⚡ [真实SDK] SDK计算完成，折扣数量: %d", len(discounts))
	}

	if err != nil {
		log.Printf("计算折扣时出错: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"message": "计算折扣时出错: " + err.Error(),
		})
	}

	// 打印计算结果
	jsonData, _ := json.MarshalIndent(discounts, "", "  ")
	fmt.Printf("\n============= 计算结果 =============\n%s\n", string(jsonData))

	// 构建响应 - 使用新的金额处理逻辑
	response := h.buildDiscountResponse(order, discounts)

	return c.JSON(http.StatusOK, response)
}

// CalculateHandler 处理折扣计算请求 (与ProcessOrder相同，只是为了保持API一致性)
// @Summary 计算订单折扣
// @Description 计算给定订单的所有适用折扣（功能与ProcessOrder相同）
// @Tags 促销计算
// @Accept json
// @Produce json
// @Param order body Order true "订单信息"
// @Success 200 {object} DiscountResponse "折扣计算结果"
// @Failure 400 {object} map[string]interface{} "请求数据无效"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /calculator/calculate [post]
func (h *PromotionCalcHandler) CalculateHandler(c echo.Context) error {
	return h.ProcessOrder(c)
}

// createOrderData 将handler的Order转换为SDK的types.OrderData
func createOrderData(order Order) types.OrderData {
	orderData := types.OrderData{
		OrderID:     order.OrderID,
		UserID:      order.UserID,
		VehicleType: order.VehicleType, // 新增车辆类型支持
		TotalAmount: order.OrderAmount,
		Items:       make([]types.OrderItemData, len(order.Items)),
	}

	for i, item := range order.Items {
		// 处理CategoryIDs：支持多标签和向后兼容性
		categoryIDs := processCategoryIDs(item, order.VehicleType)

		// 处理Attributes：确保包含fuel_volume
		attributes := processAttributes(item)

		orderData.Items[i] = types.OrderItemData{
			ItemID:      item.ItemID,
			ProductID:   item.ItemID,   // 简化处理，使用ItemID作为ProductID
			Quantity:    item.Quantity, // 现在支持float64精确升数
			UnitPrice:   item.Price,
			CategoryIDs: categoryIDs,
			Attributes:  attributes,
		}
	}

	return orderData
}

// processCategoryIDs 处理分类标签，支持多标签和向后兼容性
func processCategoryIDs(item OrderItem, vehicleType string) []string {
	var categoryIDs []string

	// 优先使用新的CategoryIDs字段
	if len(item.CategoryIDs) > 0 {
		categoryIDs = make([]string, len(item.CategoryIDs))
		copy(categoryIDs, item.CategoryIDs)
	} else if item.Category != "" {
		// 向后兼容：从单一Category字段构建
		categoryIDs = []string{item.Category}
	}

	// 确保包含基础标签
	if !containsString(categoryIDs, "fuel") {
		categoryIDs = append(categoryIDs, "fuel")
	}

	// 如果有车辆类型，确保包含在CategoryIDs中
	if vehicleType != "" && !containsString(categoryIDs, vehicleType) {
		categoryIDs = append(categoryIDs, vehicleType)
	}

	return categoryIDs
}

// processAttributes 处理商品属性，fuel_volume直接使用quantity
func processAttributes(item OrderItem) map[string]interface{} {
	attributes := make(map[string]interface{})

	// 直接使用quantity作为fuel_volume（燃油升数）
	attributes["fuel_volume"] = item.Quantity

	return attributes
}

// containsString 检查字符串切片是否包含指定字符串
func containsString(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// buildDiscountResponse 构建完整的折扣响应，确保金额自洽性
func (h *PromotionCalcHandler) buildDiscountResponse(order Order, discounts []types.CalculatedDiscount) DiscountResponse {
	// 计算总折扣金额（使用四舍五入后的金额）
	totalDiscount := calculateTotalDiscountAmount(discounts)

	// 四舍五入原始金额
	originalAmount := roundToIDR(order.OrderAmount)

	// 确保金额自洽：优惠后金额 = 原始金额 - 折扣金额
	discountedAmount := roundToIDR(originalAmount - totalDiscount)

	// 构建应用的促销活动列表
	appliedPromotions := h.convertToAppliedPromotions(discounts)

	// 构建响应
	response := DiscountResponse{
		Success:           true,
		Message:           "折扣计算成功",
		OrderID:           order.OrderID,
		VehicleType:       order.VehicleType,
		OriginalAmount:    originalAmount,
		DiscountedAmount:  discountedAmount,
		DiscountAmount:    totalDiscount,
		AppliedPromotions: appliedPromotions,
		Items:             h.buildDiscountedItems(order, totalDiscount),
		CalculationTime:   time.Now().Format(time.RFC3339),
		TotalItems:        len(order.Items),
		TotalQuantity:     calculateTotalQuantity(order.Items),
	}

	// 记录调试日志
	log.Printf("📊 响应构建完成: 原始=%.0f, 折扣=%.0f, 优惠后=%.0f, 促销数量=%d",
		originalAmount, totalDiscount, discountedAmount, len(appliedPromotions))

	return response
}

// convertToAppliedPromotions 将SDK折扣信息转换为API响应格式
func (h *PromotionCalcHandler) convertToAppliedPromotions(discounts []types.CalculatedDiscount) []AppliedPromotion {
	promotions := make([]AppliedPromotion, len(discounts))

	for i, discount := range discounts {
		promotions[i] = AppliedPromotion{
			PromotionID:     discount.PromotionID,
			PromotionName:   discount.PromotionName,
			DiscountType:    discount.DiscountType,
			DiscountValue:   roundToIDR(discount.DiscountValue),  // 原始折扣值
			DiscountAmount:  roundToIDR(discount.DiscountAmount), // 实际折扣金额
			Description:     discount.Description,
			ApplicableItems: discount.ApplicableItems,
			Metadata:        discount.Metadata,
		}
	}

	return promotions
}

// buildDiscountedItems 构建商品折扣详情，按比例分配总折扣
func (h *PromotionCalcHandler) buildDiscountedItems(order Order, totalDiscount float64) []DiscountItemResponse {
	items := make([]DiscountItemResponse, len(order.Items))

	for i, item := range order.Items {
		// 计算该商品的原始总价
		itemTotalPrice := item.Price * float64(item.Quantity)

		// 按比例分配折扣
		itemDiscount := 0.0
		if order.OrderAmount > 0 {
			itemDiscount = totalDiscount * (itemTotalPrice / order.OrderAmount)
		}

		// 计算折扣后的单价（确保不为负数）
		discountPerUnit := itemDiscount / float64(item.Quantity)
		discountedPrice := item.Price - discountPerUnit
		if discountedPrice < 0 {
			discountedPrice = 0
		}

		items[i] = DiscountItemResponse{
			ItemID:          item.ItemID,
			Name:            item.Name,
			OriginalPrice:   roundToIDR(item.Price),
			DiscountedPrice: roundToIDR(discountedPrice),
			Quantity:        item.Quantity, // 使用float64类型
			CategoryIDs:     processCategoryIDs(item, order.VehicleType),
			Attributes:      processAttributes(item), // 使用函数生成属性
		}
	}

	return items
}

// calculateTotalDiscountAmount 计算总折扣金额（使用四舍五入后的实际金额）
func calculateTotalDiscountAmount(discounts []types.CalculatedDiscount) float64 {
	total := 0.0
	for _, discount := range discounts {
		total += discount.DiscountAmount // 使用已经四舍五入的实际金额
	}
	return roundToIDR(total)
}

// roundToIDR 四舍五入到印尼盾（整数）
func roundToIDR(amount float64) float64 {
	return math.Round(amount)
}

// calculateTotalDiscount 保持向后兼容性的函数（已废弃，使用 calculateTotalDiscountAmount）
func calculateTotalDiscount(discounts []types.CalculatedDiscount) float64 {
	return calculateTotalDiscountAmount(discounts)
}

// calculateTotalQuantity 计算订单中所有商品的总数量
func calculateTotalQuantity(items []OrderItem) float64 {
	totalQuantity := 0.0
	for _, item := range items {
		totalQuantity += item.Quantity
	}
	return roundToIDR(totalQuantity)
}

// ====== Mock模式控制API ======

// ToggleMockMode 切换Mock模式
// @Summary 切换Mock模式
// @Description 在真实SDK和Mock模式之间切换
// @Tags 促销计算
// @Accept json
// @Produce json
// @Param mode body map[string]interface{} true "模式设置"
// @Success 200 {object} map[string]interface{} "切换结果"
// @Router /calculator/mock/toggle [post]
func (h *PromotionCalcHandler) ToggleMockMode(c echo.Context) error {
	var request struct {
		UseMock bool `json:"useMock"`
	}

	if err := c.Bind(&request); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"message": "请求数据格式错误",
		})
	}

	h.UseMockMode = request.UseMock

	mode := "真实SDK"
	if h.UseMockMode {
		mode = "Mock模式"
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": fmt.Sprintf("已切换到%s", mode),
		"mode":    mode,
		"useMock": h.UseMockMode,
	})
}

// GetMockStatus 获取当前Mock状态
// @Summary 获取当前Mock状态
// @Description 返回当前是否使用Mock模式及相关配置
// @Tags 促销计算
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "Mock状态信息"
// @Router /calculator/mock/status [get]
func (h *PromotionCalcHandler) GetMockStatus(c echo.Context) error {
	mode := "真实SDK"
	if h.UseMockMode {
		mode = "Mock模式"
	}

	// 记录状态查询日志
	log.Printf("📊 [状态查询] 当前模式: %s, UseMockMode: %t, RuleSet: %s",
		mode, h.UseMockMode, h.MockService.CurrentRuleSet)

	return c.JSON(http.StatusOK, map[string]interface{}{
		"useMock":        h.UseMockMode,
		"mode":           mode,
		"currentRuleSet": h.MockService.CurrentRuleSet,
		"availableRules": h.MockService.GetAvailableRuleSets(),
		"debug": map[string]interface{}{
			"handlerUseMockMode": h.UseMockMode,
			"mockServiceExists":  h.MockService != nil,
			"timestamp":          time.Now().Format("2006-01-02 15:04:05"),
		},
	})
}

// SetMockRuleSet 设置Mock规则集
// @Summary 设置Mock规则集
// @Description 设置Mock模式使用的规则集
// @Tags 促销计算
// @Accept json
// @Produce json
// @Param ruleSet body map[string]interface{} true "规则集设置"
// @Success 200 {object} map[string]interface{} "设置结果"
// @Router /calculator/mock/ruleset [post]
func (h *PromotionCalcHandler) SetMockRuleSet(c echo.Context) error {
	var request struct {
		RuleSet string `json:"ruleSet"`
	}

	if err := c.Bind(&request); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"message": "请求数据格式错误",
		})
	}

	// 验证规则集是否有效
	validRuleSets := []string{"volume_discount", "gradient_discount"}
	isValid := false
	for _, valid := range validRuleSets {
		if request.RuleSet == valid {
			isValid = true
			break
		}
	}

	if !isValid {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success":       false,
			"message":       "无效的规则集",
			"validRuleSets": validRuleSets,
		})
	}

	h.MockService.SetRuleSet(request.RuleSet)

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success":        true,
		"message":        fmt.Sprintf("已设置规则集为: %s", request.RuleSet),
		"currentRuleSet": h.MockService.CurrentRuleSet,
	})
}

// QuickSwitchToMock 快速切换到Mock模式并设置规则集
// @Summary 快速切换到Mock模式
// @Description 一键切换到Mock模式并设置指定的规则集
// @Tags 促销计算
// @Accept json
// @Produce json
// @Param config body map[string]interface{} true "快速切换配置"
// @Success 200 {object} map[string]interface{} "切换结果"
// @Router /calculator/mock/quick-switch [post]
func (h *PromotionCalcHandler) QuickSwitchToMock(c echo.Context) error {
	var request struct {
		RuleSet string `json:"ruleSet"` // "volume_discount" 或 "gradient_discount"
	}

	if err := c.Bind(&request); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"message": "请求数据格式错误",
		})
	}

	// 如果没有指定规则集，使用默认的
	if request.RuleSet == "" {
		request.RuleSet = "volume_discount"
	}

	// 切换到Mock模式
	h.UseMockMode = true
	h.MockService.SetRuleSet(request.RuleSet)

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success":        true,
		"message":        fmt.Sprintf("已快速切换到Mock模式，规则集: %s", request.RuleSet),
		"mode":           "Mock模式",
		"useMock":        true,
		"currentRuleSet": h.MockService.CurrentRuleSet,
		"availableRules": h.MockService.GetAvailableRuleSets(),
	})
}

// QuickSwitchToReal 快速切换到真实SDK模式
// @Summary 快速切换到真实SDK模式
// @Description 一键切换到真实SDK模式
// @Tags 促销计算
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "切换结果"
// @Router /calculator/mock/quick-real [post]
func (h *PromotionCalcHandler) QuickSwitchToReal(c echo.Context) error {
	h.UseMockMode = false

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "已快速切换到真实SDK模式",
		"mode":    "真实SDK",
		"useMock": false,
	})
}

// DebugCurrentMode 调试当前模式 - 用于验证实际使用的计算方式
// @Summary 调试当前模式
// @Description 执行一个简单的测试计算以验证当前真正使用的是Mock还是真实SDK
// @Tags 促销计算
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "调试结果"
// @Router /calculator/mock/debug [post]
func (h *PromotionCalcHandler) DebugCurrentMode(c echo.Context) error {
	log.Printf("🔍 [调试模式] 开始调试当前模式，UseMockMode=%t", h.UseMockMode)

	// 创建一个测试订单
	testOrder := types.OrderData{
		OrderID:     "DEBUG-" + time.Now().Format("20060102150405"),
		UserID:      "debug_user",
		TotalAmount: 420000, // 28升 * 15000
		Items: []types.OrderItemData{
			{
				ItemID:      "bp-92-fuel",
				ProductID:   "bp-92-fuel",
				Quantity:    28,
				UnitPrice:   15000,
				CategoryIDs: []string{"fuel"},
			},
		},
	}

	ctx := c.Request().Context()

	// 执行计算
	var discounts []types.CalculatedDiscount
	var err error
	var calculationMode string

	if h.UseMockMode {
		calculationMode = "Mock模式"
		log.Printf("🔍 [调试模式] 执行Mock计算")
		discounts, err = h.MockService.CalculateDiscounts(ctx, testOrder)
	} else {
		calculationMode = "真实SDK"
		log.Printf("🔍 [调试模式] 执行真实SDK计算")
		discounts, err = h.PromotionSDK.CalculateDiscounts(ctx, testOrder)
	}

	if err != nil {
		log.Printf("🔍 [调试模式] 计算失败: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"message": "调试计算失败: " + err.Error(),
			"mode":    calculationMode,
		})
	}

	// 构建调试响应
	result := map[string]interface{}{
		"success":        true,
		"actualMode":     calculationMode,
		"handlerUseMock": h.UseMockMode,
		"currentRuleSet": h.MockService.CurrentRuleSet,
		"testOrder":      testOrder,
		"discountCount":  len(discounts),
		"discounts":      discounts,
		"totalDiscount":  calculateTotalDiscount(discounts),
		"timestamp":      time.Now().Format("2006-01-02 15:04:05"),
	}

	// 根据结果判断是否真的在使用预期的模式
	if h.UseMockMode && len(discounts) > 0 {
		// 检查是否是Mock特有的响应
		if len(discounts) > 0 && strings.Contains(discounts[0].PromotionID, "FUEL_") {
			result["verification"] = "✅ 确认使用Mock模式"
		} else {
			result["verification"] = "⚠️ 可能未正确使用Mock模式"
		}
	} else if !h.UseMockMode {
		result["verification"] = "✅ 确认使用真实SDK模式"
	}

	log.Printf("🔍 [调试模式] 调试完成: %s, 折扣数量: %d", calculationMode, len(discounts))

	return c.JSON(http.StatusOK, result)
}
