package handlers

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/xeipuuv/gojsonschema"
	"gitlab4.weicheche.cn/indo-bp/promotion-service/pkg/types"
	"gitlab4.weicheche.cn/indo-bp/promotion-service/sdk"
)

// PromotionManagerHandler 处理与促销活动相关的请求
type PromotionManagerHandler struct {
	SDK sdk.PromotionSDK
}

// NewPromotionManagerHandler 创建一个新的PromotionManagerHandler
func NewPromotionManagerHandler(sdk sdk.PromotionSDK) *PromotionManagerHandler {
	return &PromotionManagerHandler{
		SDK: sdk,
	}
}

// ListPromotions 处理显示促销活动列表的请求
// @Summary 获取促销活动列表
// @Description 获取所有促销活动，支持分页、搜索和筛选
// @Tags 促销管理
// @Accept json
// @Produce json
// @Param status query string false "促销活动状态筛选（如'ACTIVE'，'DRAFT'等）"
// @Param type query string false "促销活动类型筛选（如'PERCENTAGE'，'FIXED_AMOUNT'等）"
// @Param search query string false "搜索关键词（按名称搜索）"
// @Param page query int false "页码，默认为1"
// @Param pageSize query int false "每页记录数，默认为10，最大100"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /promotions [get]
func (h *PromotionManagerHandler) ListPromotions(c echo.Context) error {
	// 获取查询参数
	status := c.QueryParam("status")
	promotionType := c.QueryParam("type")
	search := c.QueryParam("search")
	page, _ := strconv.Atoi(c.QueryParam("page"))
	pageSize, _ := strconv.Atoi(c.QueryParam("pageSize"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 创建查询过滤器DTO
	filterDTO := types.PromotionFilterDTO{
		Status: status,
		Type:   promotionType,
		Search: search,
		Offset: (page - 1) * pageSize,
		Limit:  pageSize,
	}

	ctx := c.Request().Context()

	// 使用DTO调用SDK获取促销活动列表
	promotions, err := h.SDK.GetPromotions(ctx, filterDTO)
	if err != nil {
		log.Printf("获取促销活动列表失败: %v", err)
		return c.HTML(http.StatusInternalServerError, fmt.Sprintf("<html><body><h1>错误</h1><p>获取促销活动列表失败: %v</p></body></html>", err))
	}

	// 获取符合条件的促销活动总数
	total, err := h.SDK.CountPromotions(ctx, filterDTO)
	if err != nil {
		log.Printf("获取促销活动总数失败: %v", err)
		return c.HTML(http.StatusInternalServerError, fmt.Sprintf("<html><body><h1>错误</h1><p>获取促销活动总数失败: %v</p></body></html>", err))
	}

	// 添加额外显示字段
	promotionsDisplay := make([]map[string]interface{}, len(promotions))
	for i, p := range promotions {
		promotionsDisplay[i] = map[string]interface{}{
			"ID":            p.ID,
			"Name":          p.Name,
			"Description":   p.Description,
			"Type":          p.Type,
			"TypeDisplay":   getPromotionTypeDisplay(p.Type),
			"Scope":         p.Scope,
			"ScopeDisplay":  getPromotionScopeDisplay(p.Scope),
			"Value":         p.Value,
			"ValueDisplay":  getValueDisplay(p.Type, p.Value),
			"StartTime":     p.StartTime,
			"EndTime":       p.EndTime,
			"Status":        p.Status,
			"StatusDisplay": getPromotionStatusDisplay(p.Status),
			"Priority":      p.Priority,
			"CreatedAt":     p.CreatedAt,
			"UpdatedAt":     p.UpdatedAt,
		}
	}

	// 计算分页信息
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	hasPrev := page > 1
	hasNext := page < totalPages

	// 在Echo中，我们需要渲染HTML模板
	// 这里简化处理，返回JSON数据代替HTML
	return c.JSON(http.StatusOK, map[string]interface{}{
		"title":       "促销活动管理",
		"promotions":  promotionsDisplay,
		"currentPage": page,
		"pageSize":    pageSize,
		"total":       total,
		"totalPages":  totalPages,
		"hasPrev":     hasPrev,
		"hasNext":     hasNext,
		"status":      status,
		"type":        promotionType,
		"search":      search,
	})
}

// ViewPromotion 处理查看单个促销活动详情的请求
// @Summary 查看促销活动详情
// @Description 根据ID获取单个促销活动的详细信息
// @Tags 促销管理
// @Accept json
// @Produce json
// @Param id path string true "促销活动ID"
// @Success 200 {object} map[string]interface{}
// @Failure 404 {string} string "活动不存在"
// @Failure 500 {string} string "服务器错误"
// @Router /promotions/{id} [get]
func (h *PromotionManagerHandler) ViewPromotion(c echo.Context) error {
	// 获取促销活动ID
	idStr := c.Param("id")

	ctx := c.Request().Context()

	// 通过SDK获取促销活动完整详情，包含折扣、规则、时间周期等
	promotionDetails, err := h.SDK.GetPromotionDetails(ctx, idStr)
	if err != nil {
		log.Printf("获取促销活动详情失败: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"error": fmt.Sprintf("获取促销活动详情失败: %v", err),
		})
	}

	if promotionDetails == nil {
		return c.JSON(http.StatusNotFound, map[string]interface{}{
			"error": "促销活动不存在",
		})
	}

	promotionDTO := &promotionDetails.Promotion

	// 创建用于显示的数据，包含完整的促销详情
	promotionDisplay := map[string]interface{}{
		"ID":                promotionDTO.ID,
		"Name":              promotionDTO.Name,
		"Description":       promotionDTO.Description,
		"Type":              promotionDTO.Type,
		"TypeDisplay":       getPromotionTypeDisplay(promotionDTO.Type),
		"Scope":             promotionDTO.Scope,
		"ScopeDisplay":      getPromotionScopeDisplay(promotionDTO.Scope),
		"Value":             promotionDTO.Value,
		"ValueDisplay":      getValueDisplay(promotionDTO.Type, promotionDTO.Value),
		"MinOrderAmount":    promotionDTO.MinOrderAmount,
		"MaxDiscountAmount": promotionDTO.MaxDiscountAmount,
		"StartTime":         promotionDTO.StartTime,
		"EndTime":           promotionDTO.EndTime,
		"Status":            promotionDTO.Status,
		"StatusDisplay":     getPromotionStatusDisplay(promotionDTO.Status),
		"Priority":          promotionDTO.Priority,
		"Stackable":         promotionDTO.Stackable,
		"RequiresCoupon":    promotionDTO.RequiresCoupon,
		"MaxUseCount":       promotionDTO.MaxUseCount,
		"UsedCount":         promotionDTO.UsedCount,
		"MaxPerUser":        promotionDTO.MaxPerUser,
		"TargetIDs":         promotionDTO.TargetIDs,
		"ForMembers":        promotionDTO.ForMembers,
		"ForNonMembers":     promotionDTO.ForNonMembers,
		"MemberLevels":      promotionDTO.MemberLevels,
		"MemberTags":        promotionDTO.MemberTags,
		"Rules":             convertDTORulesToMaps(promotionDetails.Rules),
		"TimeCycles":        convertDTOTimeCyclesToMaps(promotionDetails.TimeCycles),
		"Discounts":         convertDTODiscountsToMaps(promotionDetails.Discounts),
		"GiftItems":         promotionDTO.GiftItems,
		// 站点控制字段（v2.1+ 新增）
		"SiteIDs":           promotionDTO.SiteIDs,
		"SiteExcludeIDs":    promotionDTO.SiteExcludeIDs,
		"AllSites":          promotionDTO.AllSites,
		"CreatedAt":         promotionDTO.CreatedAt,
		"UpdatedAt":         promotionDTO.UpdatedAt,
	}

	// 返回JSON数据代替HTML
	return c.JSON(http.StatusOK, map[string]interface{}{
		"title":     "促销活动详情",
		"promotion": promotionDisplay,
	})
}

// ShowPromotionForm 显示添加或编辑促销活动的表单
// @Summary 获取促销活动表单数据
// @Description 获取新增或编辑促销活动所需的表单数据
// @Tags 促销管理
// @Accept json
// @Produce json
// @Param id path string false "促销活动ID（编辑时提供）"
// @Success 200 {object} map[string]interface{}
// @Failure 404 {string} string "活动不存在"
// @Failure 500 {string} string "服务器错误"
// @Router /promotions/new [get]
// @Router /promotions/edit/{id} [get]
func (h *PromotionManagerHandler) ShowPromotionForm(c echo.Context) error {
	// 检查是否是编辑现有促销活动
	idStr := c.Param("id")
	isEdit := idStr != ""

	ctx := c.Request().Context()

	// 创建默认的表单数据
	// 为业务需求使用当前时间作为默认值
	now := time.Now()
	oneMonthLater := now.AddDate(0, 1, 0)

	promotionDisplay := map[string]interface{}{
		"ID":                nil, // 默认为nil，确保新建时不会有空字符串ID
		"Name":              "",
		"Description":       "",
		"Type":              "",
		"Scope":             "",
		"Value":             0.0,
		"MinOrderAmount":    0.0,
		"MaxDiscountAmount": 0.0,
		"StartTime":         now,
		"EndTime":           oneMonthLater,
		"Status":            "DRAFT",
		"Priority":          10,
		"Stackable":         true,
		"RequiresCoupon":    false,
		"MaxUseCount":       0,
		"UsedCount":         0,
		"MaxPerUser":        0,
		"TargetIDs":         []string{},
		"ForMembers":        true,
		"ForNonMembers":     false,
		"MemberLevels":      []string{},
		"MemberTags":        []string{},
		"Rules":             []interface{}{},
		"TimeCycles":        []interface{}{},
		"GiftItems":         []interface{}{},
		// 站点控制字段默认值
		"SiteIDs":           []string{},
		"SiteExcludeIDs":    []string{},
		"AllSites":          nil,
	}

	// 如果是编辑模式，从SDK获取促销活动数据
	if isEdit {
		promotionDTO, err := h.SDK.GetPromotionByID(ctx, idStr)
		if err != nil {
			log.Printf("获取促销活动详情失败: %v", err)
			return c.HTML(http.StatusInternalServerError, fmt.Sprintf("<html><body><h1>错误</h1><p>获取促销活动详情失败: %v</p></body></html>", err))
		}

		if promotionDTO == nil {
			return c.HTML(http.StatusNotFound, "<html><body><h1>错误</h1><p>促销活动不存在</p></body></html>")
		}

		// 用真实数据填充表单
		promotionDisplay = map[string]interface{}{
			"ID":                promotionDTO.ID,
			"Name":              promotionDTO.Name,
			"Description":       promotionDTO.Description,
			"Type":              promotionDTO.Type,
			"Scope":             promotionDTO.Scope,
			"Value":             promotionDTO.Value,
			"MinOrderAmount":    promotionDTO.MinOrderAmount,
			"MaxDiscountAmount": promotionDTO.MaxDiscountAmount,
			"StartTime":         promotionDTO.StartTime,
			"EndTime":           promotionDTO.EndTime,
			"Status":            promotionDTO.Status,
			"Priority":          promotionDTO.Priority,
			"Stackable":         promotionDTO.Stackable,
			"RequiresCoupon":    promotionDTO.RequiresCoupon,
			"MaxUseCount":       promotionDTO.MaxUseCount,
			"UsedCount":         promotionDTO.UsedCount,
			"MaxPerUser":        promotionDTO.MaxPerUser,
			"TargetIDs":         promotionDTO.TargetIDs,
			"ForMembers":        promotionDTO.ForMembers,
			"ForNonMembers":     promotionDTO.ForNonMembers,
			"MemberLevels":      promotionDTO.MemberLevels,
			"MemberTags":        promotionDTO.MemberTags,
			"Rules":             promotionDTO.Rules,
			"TimeCycles":        convertDTOTimeCyclesToMaps(promotionDTO.TimeCycles),
			"GiftItems":         promotionDTO.GiftItems,
			// 站点控制字段
			"SiteIDs":           promotionDTO.SiteIDs,
			"SiteExcludeIDs":    promotionDTO.SiteExcludeIDs,
			"AllSites":          promotionDTO.AllSites,
		}
	}

	title := "新增促销活动"
	if isEdit {
		title = "编辑促销活动"
	}

	// 返回JSON数据代替HTML
	return c.JSON(http.StatusOK, map[string]interface{}{
		"title":     title,
		"isEdit":    isEdit,
		"promotion": promotionDisplay,
	})
}

// SavePromotion 处理保存促销活动的JSON请求 (V2版本)
// @Summary 保存促销活动 (V2)
// @Description 通过一个完整的JSON对象创建或更新促销活动
// @Tags 促销管理
// @Accept json
// @Produce json
// @Param promotion body types.PromotionPackageDTO true "完整的促销包"
// @Param id path string false "促销ID (仅用于更新)"
// @Success 200 {object} types.PromotionPackageDTO
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /promotions/save [post]
// @Router /promotions/save/{id} [post]
func (h *PromotionManagerHandler) SavePromotion(c echo.Context) error {
	// 1. 加载 Schema
	schemaLoader, err := GetPromotionSchema()
	if err != nil {
		log.Printf("无法加载JSON Schema: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "服务器内部错误：无法加载schema"})
	}

	// 2. 读取请求体 (因为需要多次使用，所以不能直接用c.Bind())
	bodyBytes, err := ioutil.ReadAll(c.Request().Body)
	if err != nil {
		log.Printf("读取请求体失败: %v", err)
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "无法读取请求体"})
	}
	// 恢复请求体，以便后续可能需要
	c.Request().Body = ioutil.NopCloser(bytes.NewBuffer(bodyBytes))

	// 3. 使用Schema进行验证
	bodyLoader := gojsonschema.NewBytesLoader(bodyBytes)
	result, err := gojsonschema.Validate(schemaLoader, bodyLoader)
	if err != nil {
		log.Printf("验证请求体时发生错误: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "服务器内部错误：验证请求失败"})
	}

	if !result.Valid() {
		var errors []string
		for _, desc := range result.Errors() {
			errors = append(errors, desc.String())
		}
		log.Printf("请求体验证失败: %v", errors)
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error":   "请求数据无效",
			"details": errors,
		})
	}

	// 4. 绑定到DTO
	var dto types.PromotionPackageDTO
	if err := c.Bind(&dto); err != nil {
		log.Printf("绑定请求体到DTO失败: %v", err)
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "请求数据格式错误"})
	}

	ctx := c.Request().Context()

	// 5. 确定是创建还是更新
	idStr := c.Param("id")
	if idStr != "" {
		if dto.Promotion.ID.String() != "" && idStr != dto.Promotion.ID.String() {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "URL中的ID与请求体中的ID不匹配"})
		}
		// 确保使用URL中的ID进行更新
		if parsedID, err := uuid.Parse(idStr); err == nil {
			dto.Promotion.ID = parsedID
		} else {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "无效的促销ID格式"})
		}
	}

	// 6. 调用SDK方法
	var savedPromotion *types.PromotionDTO
	if idStr != "" {
		// 更新操作 - 使用UpdatePromotionPackage处理完整数据包
		savedPromotion, err = h.SDK.UpdatePromotionPackage(ctx, idStr, &dto)
	} else {
		// 创建操作 - 使用CreatePromotionPackage
		savedPromotion, err = h.SDK.CreatePromotionPackage(ctx, &dto)
	}

	if err != nil {
		log.Printf("保存促销活动失败: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "保存促销活动失败"})
	}

	// 7. 返回保存后的促销数据
	return c.JSON(http.StatusOK, savedPromotion)
}

// DeletePromotion 处理删除促销活动的请求
// @Summary 删除促销活动
// @Description 根据ID删除指定的促销活动
// @Tags 促销管理
// @Accept json
// @Produce json
// @Param id path string true "促销活动ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 500 {object} map[string]interface{} "错误响应"
// @Router /promotions/{id} [delete]
func (h *PromotionManagerHandler) DeletePromotion(c echo.Context) error {
	// 获取促销活动ID
	idStr := c.Param("id")

	// 打印请求信息
	log.Printf("删除促销活动: ID=%s", idStr)

	// 删除促销活动
	ctx := c.Request().Context()
	err := h.SDK.DeletePromotion(ctx, idStr)

	if err != nil {
		log.Printf("删除促销活动失败: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   fmt.Sprintf("删除促销活动失败: %v", err),
		})
	}

	// 成功删除
	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
	})
}

// UpdateStatus 处理更新促销活动状态的请求
// @Summary 更新促销活动状态
// @Description 更新指定促销活动的状态（如激活、暂停等）
// @Tags 促销管理
// @Accept json
// @Produce json
// @Param id path string true "促销活动ID"
// @Param status formData string true "新状态"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 500 {object} map[string]interface{} "错误响应"
// @Router /promotions/{id}/status [post]
func (h *PromotionManagerHandler) UpdateStatus(c echo.Context) error {
	// 获取促销活动ID
	idStr := c.Param("id")

	// 定义请求体结构
	type StatusUpdateRequest struct {
		Status  string `json:"status"`
		Remarks string `json:"remarks"`
	}

	// 尝试从JSON请求体中获取数据
	var req StatusUpdateRequest
	if err := c.Bind(&req); err != nil {
		// 如果JSON绑定失败，尝试从表单中获取
		req.Status = c.FormValue("status")
		req.Remarks = c.FormValue("remarks")
	}

	statusStr := req.Status

	// 打印请求信息
	log.Printf("更新促销活动状态: ID=%s, 状态=%s, 备注=%s", idStr, statusStr, req.Remarks)

	// 使用SDK获取促销活动DTO
	ctx := c.Request().Context()
	promotionDTO, err := h.SDK.GetPromotionByID(ctx, idStr)
	if err != nil {
		log.Printf("获取促销活动失败: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   fmt.Sprintf("获取促销活动失败: %v", err),
		})
	}

	// 更新状态
	promotionDTO.Status = statusStr

	// 使用SDK更新促销活动
	_, err = h.SDK.UpdatePromotion(ctx, idStr, promotionDTO)

	if err != nil {
		log.Printf("更新促销活动状态失败: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   fmt.Sprintf("更新促销活动状态失败: %v", err),
		})
	}

	// 成功更新
	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
	})
}

// 辅助函数

// getPromotionTypeDisplay 获取促销活动类型的显示文本
func getPromotionTypeDisplay(promotionType string) string {
	typeMap := map[string]string{
		"PERCENTAGE":    "百分比折扣",
		"FIXED_AMOUNT":  "固定金额折扣",
		"FREE_SHIPPING": "免运费",
		"BUY_X_GET_Y":   "买X送Y",
		"BUNDLE":        "套餐优惠",
	}

	if display, ok := typeMap[promotionType]; ok {
		return display
	}
	return promotionType
}

// getPromotionScopeDisplay gets the display text for promotion scope
func getPromotionScopeDisplay(scope string) string {
	scopeMap := map[string]string{
		"ORDER":    "Order Level",
		"PRODUCT":  "Product Level",
		"CATEGORY": "Category Level",
		"SHIPPING": "Shipping Level",
	}

	if display, ok := scopeMap[scope]; ok {
		return display
	}
	return scope
}

// getPromotionStatusDisplay 获取促销活动状态的显示文本
func getPromotionStatusDisplay(status string) string {
	statusMap := map[string]string{
		"DRAFT":     "草稿",
		"PENDING":   "待审核",
		"APPROVED":  "已审核",
		"ACTIVE":    "进行中",
		"PAUSED":    "已暂停",
		"EXPIRED":   "已过期",
		"CANCELLED": "已取消",
	}

	if display, ok := statusMap[status]; ok {
		return display
	}
	return status
}

// getValueDisplay 根据促销类型返回格式化的折扣值
func getValueDisplay(promotionType string, value float64) string {
	switch promotionType {
	case "PERCENTAGE":
		return fmt.Sprintf("%.1f%%", value)
	case "FIXED_AMOUNT":
		return fmt.Sprintf("¥%.2f", value)
	default:
		return fmt.Sprintf("%.2f", value)
	}
}

// convertDTOTimeCyclesToMaps 将DTO格式的TimeCycle转换为map以便在模板中使用
func convertDTOTimeCyclesToMaps(cycles []types.TimeCycleDTO) []map[string]interface{} {
	result := make([]map[string]interface{}, len(cycles))
	for i, cycle := range cycles {
		cycleType := cycle.Type
		typeDisplay := "未知"
		switch cycleType {
		case "daily":
			typeDisplay = "每天"
		case "weekly":
			typeDisplay = "每周"
		case "monthly":
			typeDisplay = "每月"
		}

		result[i] = map[string]interface{}{
			"Type":        cycleType,
			"TypeDisplay": typeDisplay,
			"StartHour":   cycle.StartHour,
			"EndHour":     cycle.EndHour,
			"WeekDays":    cycle.WeekDays,
			"MonthDays":   nil, // DTO中可能没有此字段，或者需要从其他字段获取
			"Priority":    cycle.Priority,
		}
	}
	return result
}

// CheckPromotionSiteApplicability 检查促销活动在特定站点的适用性
// @Summary 检查促销站点适用性
// @Description 检查指定促销是否适用于特定站点
// @Tags 促销管理
// @Param id path string true "促销活动ID"
// @Param site_code path string true "站点编码"
// @Produce json
// @Success 200 {object} map[string]interface{} "适用性检查结果"
// @Failure 400 {object} map[string]interface{} "请求参数错误"
// @Failure 404 {object} map[string]interface{} "促销活动或站点不存在"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /api/v1/promotions/{id}/applicable/{site_code} [get]
func (h *PromotionManagerHandler) CheckPromotionSiteApplicability(c echo.Context) error {
	ctx := c.Request().Context()

	// 获取路径参数
	promotionID := c.Param("id")
	siteCode := c.Param("site_code")

	if promotionID == "" || siteCode == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success":   false,
			"message":   "促销活动ID和站点编码不能为空",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	}

	// 获取促销活动信息
	promotionDTO, err := h.SDK.GetPromotionByID(ctx, promotionID)
	if err != nil {
		log.Printf("获取促销活动失败: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success":   false,
			"message":   "获取促销活动失败",
			"timestamp": time.Now().Format(time.RFC3339),
			"error":     err.Error(),
		})
	}

	if promotionDTO == nil {
		return c.JSON(http.StatusNotFound, map[string]interface{}{
			"success":   false,
			"message":   "促销活动不存在",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	}

	// 检查站点适用性
	isApplicable, reason := h.checkSiteApplicability(promotionDTO, siteCode)

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success":   true,
		"message":   "Success",
		"timestamp": time.Now().Format(time.RFC3339),
		"data": map[string]interface{}{
			"promotion_id":   promotionDTO.ID,
			"promotion_name": promotionDTO.Name,
			"site_code":      siteCode,
			"is_applicable":  isApplicable,
			"reason":         reason,
			"checked_at":     time.Now().Format(time.RFC3339),
		},
	})
}

// BatchCheckPromotionSiteApplicability 批量检查促销活动站点适用性
// @Summary 批量检查促销站点适用性
// @Description 批量检查多个站点对指定促销的适用性
// @Tags 促销管理
// @Param id path string true "促销活动ID"
// @Param request body map[string]interface{} true "站点编码列表"
// @Produce json
// @Success 200 {object} map[string]interface{} "批量适用性检查结果"
// @Failure 400 {object} map[string]interface{} "请求参数错误"
// @Failure 404 {object} map[string]interface{} "促销活动不存在"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /api/v1/promotions/{id}/applicable/batch [post]
func (h *PromotionManagerHandler) BatchCheckPromotionSiteApplicability(c echo.Context) error {
	ctx := c.Request().Context()

	// 获取路径参数
	promotionID := c.Param("id")
	if promotionID == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success":   false,
			"message":   "促销活动ID不能为空",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	}

	// 解析请求体
	var requestBody struct {
		SiteCodes []string `json:"site_codes"`
	}

	if err := c.Bind(&requestBody); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success":   false,
			"message":   "请求参数格式错误",
			"timestamp": time.Now().Format(time.RFC3339),
			"error":     err.Error(),
		})
	}

	if len(requestBody.SiteCodes) == 0 {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success":   false,
			"message":   "站点编码列表不能为空",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	}

	// 获取促销活动信息
	promotionDTO, err := h.SDK.GetPromotionByID(ctx, promotionID)
	if err != nil {
		log.Printf("获取促销活动失败: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success":   false,
			"message":   "获取促销活动失败",
			"timestamp": time.Now().Format(time.RFC3339),
			"error":     err.Error(),
		})
	}

	if promotionDTO == nil {
		return c.JSON(http.StatusNotFound, map[string]interface{}{
			"success":   false,
			"message":   "促销活动不存在",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	}

	// 批量检查站点适用性
	results := make([]map[string]interface{}, 0, len(requestBody.SiteCodes))
	applicableCount := 0
	notApplicableCount := 0

	for _, siteCode := range requestBody.SiteCodes {
		isApplicable, reason := h.checkSiteApplicability(promotionDTO, siteCode)

		results = append(results, map[string]interface{}{
			"site_code":     siteCode,
			"is_applicable": isApplicable,
			"reason":        reason,
		})

		if isApplicable {
			applicableCount++
		} else {
			notApplicableCount++
		}
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success":   true,
		"message":   "Success",
		"timestamp": time.Now().Format(time.RFC3339),
		"data": map[string]interface{}{
			"promotion_id": promotionDTO.ID,
			"results":      results,
			"summary": map[string]interface{}{
				"total_checked":         len(requestBody.SiteCodes),
				"applicable_count":      applicableCount,
				"not_applicable_count":  notApplicableCount,
			},
		},
	})
}

// checkSiteApplicability 检查站点适用性的核心逻辑
func (h *PromotionManagerHandler) checkSiteApplicability(promotionDTO *types.PromotionDTO, siteCode string) (bool, string) {
	// 1. 优先检查排除列表
	if promotionDTO.SiteExcludeIDs != nil {
		for _, excludeID := range promotionDTO.SiteExcludeIDs {
			if excludeID == siteCode {
				return false, "站点在排除列表中"
			}
		}
	}

	// 2. 检查全局开关
	if promotionDTO.AllSites != nil && *promotionDTO.AllSites {
		return true, "适用于所有站点"
	}

	// 3. 检查包含列表
	if promotionDTO.SiteIDs != nil && len(promotionDTO.SiteIDs) > 0 {
		for _, siteID := range promotionDTO.SiteIDs {
			if siteID == siteCode {
				return true, "站点在适用列表中"
			}
		}
		return false, "站点不在适用列表中"
	}

	// 4. 向后兼容：如果AllSites为nil且SiteIDs为空，默认适用所有站点
	if promotionDTO.AllSites == nil && (promotionDTO.SiteIDs == nil || len(promotionDTO.SiteIDs) == 0) {
		return true, "默认适用所有站点（向后兼容）"
	}

	// 5. 其他情况不适用
	return false, "站点控制配置不匹配"
}

// convertDTORulesToMaps 将DTO格式的Rule转换为map以便在模板中使用
func convertDTORulesToMaps(rules []types.RuleDTO) []map[string]interface{} {
	result := make([]map[string]interface{}, len(rules))
	for i, rule := range rules {
		ruleType := rule.Type
		typeDisplay := getRuleTypeDisplay(ruleType)

		operatorDisplay := getRuleOperatorDisplay(rule.Operator)

		result[i] = map[string]interface{}{
			"ID":           rule.ID,
			"Type":         ruleType,
			"TypeDisplay":  typeDisplay,
			"Operator":     rule.Operator,
			"OperatorDisplay": operatorDisplay,
			"Value":        rule.Value,
			"Priority":     rule.Priority,
			"Description":  rule.Description,
			"CreatedAt":    rule.CreatedAt,
			"UpdatedAt":    rule.UpdatedAt,
		}
	}
	return result
}

// convertDTODiscountsToMaps 将DTO格式的Discount转换为map以便在模板中使用
func convertDTODiscountsToMaps(discounts []types.DiscountDTO) []map[string]interface{} {
	result := make([]map[string]interface{}, len(discounts))
	for i, discount := range discounts {
		discountType := discount.Type
		typeDisplay := getDiscountTypeDisplay(discountType)

		// 处理 FREE_ITEM 类型的特殊逻辑
		var actualValue interface{}
		var freeQuantity interface{}
		var freeProductID interface{}

		if discountType == "FREE_ITEM" && discount.Metadata != nil {
			if qty, ok := discount.Metadata["free_quantity"]; ok {
				freeQuantity = qty
				actualValue = qty // FREE_ITEM 的 value 应该等于 free_quantity
			}
			if productID, ok := discount.Metadata["free_product_id"]; ok {
				freeProductID = productID
			}
		}

		// 如果没有设置 actualValue，使用原始的 DiscountValue
		if actualValue == nil {
			actualValue = discount.DiscountValue
		}

		valueDisplay := getDiscountValueDisplayWithMetadata(discountType, discount.DiscountValue, discount.Metadata)

		result[i] = map[string]interface{}{
			"ID":            discount.ID,
			"PromotionID":   discount.PromotionID,
			"CouponID":      discount.CouponID,
			"Code":          discount.Code,
			"Type":          discountType,
			"TypeDisplay":   typeDisplay,
			"DiscountValue": actualValue, // 使用处理后的值
			"ValueDisplay":  valueDisplay,
			"FreeQuantity":  freeQuantity,
			"FreeProductID": freeProductID,
			"Metadata":      discount.Metadata,
			"CreatedAt":     discount.CreatedAt,
			"UpdatedAt":     discount.UpdatedAt,
		}
	}
	return result
}

// getRuleTypeDisplay gets the display text for rule types
func getRuleTypeDisplay(ruleType string) string {
	typeMap := map[string]string{
		"FUEL_TYPE":     "Fuel Type",
		"FUEL_VOLUME":   "Fuel Volume",
		"MIN_PURCHASE":  "Min Purchase Amount",
		"MAX_PURCHASE":  "Max Purchase Amount",
		"USER_CATEGORY": "User Category",
		"TIME_RANGE":    "Time Range",
		"LOCATION":      "Location",
		"PAYMENT_METHOD": "Payment Method",
	}

	if display, ok := typeMap[ruleType]; ok {
		return display
	}
	return ruleType
}

// getRuleOperatorDisplay gets the display text for rule operators
func getRuleOperatorDisplay(operator string) string {
	operatorMap := map[string]string{
		"eq":           "equals",
		"ne":           "not equals",
		"gt":           "greater than",
		"gte":          "greater than or equal",
		"lt":           "less than",
		"lte":          "less than or equal",
		"contains":     "contains",
		"not_contains": "does not contain",
		"in":           "in list",
		"not_in":       "not in list",
	}

	if display, ok := operatorMap[operator]; ok {
		return display
	}
	return operator
}

// getDiscountTypeDisplay gets the display text for discount types
func getDiscountTypeDisplay(discountType string) string {
	typeMap := map[string]string{
		"FIXED_AMOUNT":  "Fixed Amount",
		"PERCENTAGE":    "Percentage",
		"FREE_ITEM":     "Free Volume",
		"FREE_SHIPPING": "Free Shipping",
		"BUY_X_GET_Y":   "Buy X Get Y",
	}

	if display, ok := typeMap[discountType]; ok {
		return display
	}
	return discountType
}

// getDiscountValueDisplay gets the display text for discount values
func getDiscountValueDisplay(discountType string, value float64) string {
	switch discountType {
	case "FIXED_AMOUNT":
		return fmt.Sprintf("IDR %.0f", value)
	case "PERCENTAGE":
		return fmt.Sprintf("%.1f%%", value)
	case "FREE_ITEM":
		return fmt.Sprintf("%.1f L", value)
	case "FREE_SHIPPING":
		return "Free Shipping"
	case "BUY_X_GET_Y":
		return fmt.Sprintf("Buy %.0f Get Y", value)
	default:
		return fmt.Sprintf("%.2f", value)
	}
}

// getDiscountValueDisplayWithMetadata gets the display text for discount values with metadata context
func getDiscountValueDisplayWithMetadata(discountType string, value float64, metadata map[string]interface{}) string {
	switch discountType {
	case "FIXED_AMOUNT":
		return fmt.Sprintf("IDR %.0f", value)
	case "PERCENTAGE":
		return fmt.Sprintf("%.1f%%", value)
	case "FREE_ITEM":
		// For FREE_ITEM, use free_quantity from metadata if available
		if metadata != nil {
			if qty, ok := metadata["free_quantity"]; ok {
				switch v := qty.(type) {
				case float64:
					return fmt.Sprintf("%.1f L", v)
				case int:
					return fmt.Sprintf("%.1f L", float64(v))
				case string:
					// Try to parse string as number
					if parsed, err := strconv.ParseFloat(v, 64); err == nil {
						return fmt.Sprintf("%.1f L", parsed)
					}
				}
			}
		}
		// Fallback to discount_value
		return fmt.Sprintf("%.1f L", value)
	case "FREE_SHIPPING":
		return "Free Shipping"
	case "BUY_X_GET_Y":
		return fmt.Sprintf("Buy %.0f Get Y", value)
	default:
		return fmt.Sprintf("%.2f", value)
	}
}
