{"$schema": "http://json-schema.org/draft-07/schema#", "title": "促销服务SDK Schema", "description": "促销服务SDK的完整接口规范和数据模型定义", "version": "2.1.0", "definitions": {"UUID": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "标准UUID格式"}, "NonEmptyString": {"type": "string", "minLength": 1, "description": "非空字符串"}, "PositiveNumber": {"type": "number", "minimum": 0, "description": "非负数值"}, "StrictPositiveNumber": {"type": "number", "minimum": 0.01, "description": "严格正数"}, "Percentage": {"type": "number", "minimum": 0, "maximum": 100, "description": "百分比值 (0-100)"}, "Timestamp": {"type": "string", "format": "date-time", "description": "ISO 8601时间戳格式"}, "PromotionType": {"type": "string", "enum": ["PERCENTAGE", "FIXED_AMOUNT", "GIFT", "FUEL_DISCOUNT", "STORE_WIDE", "BUNDLE", "DISCOUNT", "COUPON", "CUMULATIVE_COUNT"], "description": "促销活动类型"}, "PromotionStatus": {"type": "string", "enum": ["DRAFT", "PENDING", "APPROVED", "REJECTED", "ACTIVE", "PAUSED", "EXPIRED", "CANCELLED", "DELETED"], "description": "促销活动状态"}, "PromotionScope": {"type": "string", "enum": ["ORDER", "PRODUCT", "CATEGORY", "STORE"], "description": "促销活动作用范围"}, "DiscountType": {"type": "string", "enum": ["PERCENTAGE", "FIXED_AMOUNT", "FREE_SHIPPING", "FREE_ITEM", "BUY_X_GET_Y"], "description": "折扣类型"}, "RuleType": {"type": "string", "enum": ["MIN_PURCHASE_AMOUNT", "MAX_PURCHASE_AMOUNT", "PRODUCT_INCLUDE", "PRODUCT_EXCLUDE", "CATEGORY_INCLUDE", "CATEGORY_EXCLUDE", "USER_TAG", "USER_LEVEL", "FUEL_TYPE", "FUEL_VOLUME", "CUMULATIVE_COUNT", "TIME_WINDOW"], "description": "规则类型"}, "RuleOperator": {"type": "string", "enum": ["eq", "neq", "gt", "gte", "lt", "lte", "in", "not_in", "contains", "not_contains"], "description": "规则操作符"}, "UserTag": {"type": "string", "enum": ["MEMBER", "NON_MEMBER", "B2B", "VIP", "NEW_USER"], "description": "用户标签"}, "FuelType": {"type": "string", "enum": ["BP92", "BP95", "Ultimate", "Ultimate_Diesel"], "description": "燃油类型"}, "Metadata": {"type": "object", "additionalProperties": true, "description": "扩展元数据，支持任意键值对"}, "PromotionDTO": {"type": "object", "title": "促销活动", "description": "完整的促销活动数据结构", "properties": {"id": {"$ref": "#/definitions/UUID", "description": "促销活动唯一标识"}, "name": {"$ref": "#/definitions/NonEmptyString", "maxLength": 255, "description": "促销活动名称"}, "description": {"type": "string", "maxLength": 1000, "description": "促销活动描述"}, "type": {"$ref": "#/definitions/PromotionType", "description": "促销活动类型"}, "status": {"$ref": "#/definitions/PromotionStatus", "description": "促销活动状态"}, "scope": {"$ref": "#/definitions/PromotionScope", "description": "促销活动作用范围"}, "value": {"$ref": "#/definitions/PositiveNumber", "description": "促销活动基础值"}, "min_order_amount": {"$ref": "#/definitions/PositiveNumber", "description": "最低订单金额要求"}, "max_discount_amount": {"$ref": "#/definitions/PositiveNumber", "description": "最大折扣金额限制"}, "start_time": {"$ref": "#/definitions/Timestamp", "description": "促销活动开始时间"}, "end_time": {"$ref": "#/definitions/Timestamp", "description": "促销活动结束时间"}, "max_use_count": {"type": "integer", "minimum": 0, "description": "最大使用次数，0表示无限制"}, "used_count": {"type": "integer", "minimum": 0, "description": "已使用次数"}, "max_per_user": {"type": "integer", "minimum": 0, "description": "每用户最大使用次数，0表示无限制"}, "target_ids": {"type": "array", "items": {"type": "string"}, "description": "目标ID列表，如商品ID、分类ID等"}, "requires_coupon": {"type": "boolean", "description": "是否需要优惠券"}, "priority": {"type": "integer", "minimum": 0, "maximum": 100, "description": "优先级，数值越大优先级越高"}, "stackable": {"type": "boolean", "description": "是否可与其他促销叠加"}, "for_members": {"type": "boolean", "description": "是否适用于会员"}, "for_non_members": {"type": "boolean", "description": "是否适用于非会员"}, "member_levels": {"type": "array", "items": {"type": "string"}, "description": "适用的会员等级列表"}, "member_tags": {"type": "array", "items": {"$ref": "#/definitions/UserTag"}, "description": "适用的会员标签列表"}, "metadata": {"$ref": "#/definitions/Metadata", "description": "扩展元数据"}, "site_ids": {"type": "array", "items": {"type": "string"}, "description": "适用站点ID列表，空则不限制"}, "site_exclude_ids": {"type": "array", "items": {"type": "string"}, "description": "排除站点ID列表"}, "all_sites": {"type": "boolean", "description": "是否适用所有站点"}, "rules": {"type": "array", "items": {"$ref": "#/definitions/RuleDTO"}, "description": "关联的规则列表"}, "time_cycles": {"type": "array", "items": {"$ref": "#/definitions/TimeCycleDTO"}, "description": "关联的时间周期列表"}, "gift_items": {"type": "array", "items": {"$ref": "#/definitions/GiftItemDTO"}, "description": "关联的赠品列表"}, "reviewer_id": {"type": "string", "description": "审核人ID"}, "review_time": {"$ref": "#/definitions/Timestamp", "description": "审核时间"}, "review_comments": {"type": "string", "description": "审核意见"}, "last_status_change": {"$ref": "#/definitions/Timestamp", "description": "最后状态变更时间"}, "status_change_reason": {"type": "string", "description": "状态变更原因"}, "last_changed_by": {"type": "string", "description": "最后修改人"}, "created_by": {"$ref": "#/definitions/NonEmptyString", "description": "创建人"}, "version": {"type": "integer", "minimum": 1, "description": "版本号"}, "created_at": {"$ref": "#/definitions/Timestamp", "description": "创建时间"}, "updated_at": {"$ref": "#/definitions/Timestamp", "description": "更新时间"}, "deleted_at": {"$ref": "#/definitions/Timestamp", "description": "删除时间"}}, "required": ["id", "name", "type", "status", "scope", "value", "start_time", "end_time", "created_by", "version", "created_at", "updated_at"], "additionalProperties": false}, "GiftItemDTO": {"type": "object", "title": "赠品", "description": "促销活动关联的赠品信息", "properties": {"id": {"$ref": "#/definitions/UUID", "description": "赠品唯一标识"}, "promotion_id": {"$ref": "#/definitions/UUID", "description": "关联的促销活动ID"}, "product_id": {"$ref": "#/definitions/NonEmptyString", "description": "赠品商品ID"}, "quantity": {"type": "integer", "minimum": 1, "description": "赠品数量"}, "source": {"type": "string", "enum": ["PROMOTION", "MANUAL", "SYSTEM"], "description": "赠品来源"}, "created_at": {"$ref": "#/definitions/Timestamp", "description": "创建时间"}, "updated_at": {"$ref": "#/definitions/Timestamp", "description": "更新时间"}}, "required": ["id", "promotion_id", "product_id", "quantity", "source"], "additionalProperties": false}, "DiscountDTO": {"type": "object", "title": "折扣配置", "description": "折扣的具体配置信息", "properties": {"id": {"$ref": "#/definitions/UUID", "description": "折扣唯一标识"}, "promotion_id": {"$ref": "#/definitions/UUID", "description": "关联的促销活动ID"}, "coupon_id": {"$ref": "#/definitions/UUID", "description": "关联的优惠券ID，可选"}, "code": {"type": "string", "description": "折扣代码"}, "type": {"$ref": "#/definitions/DiscountType", "description": "折扣类型"}, "discount_value": {"$ref": "#/definitions/PositiveNumber", "description": "折扣值，可以是百分比或固定金额"}, "used_count": {"type": "integer", "minimum": 0, "description": "已使用次数"}, "metadata": {"$ref": "#/definitions/Metadata", "description": "折扣相关的元数据配置"}, "created_at": {"$ref": "#/definitions/Timestamp", "description": "创建时间"}, "updated_at": {"$ref": "#/definitions/Timestamp", "description": "更新时间"}, "gradient_level": {"type": "integer", "minimum": 1, "description": "梯度级别，用于梯度折扣"}, "threshold_type": {"type": "string", "enum": ["QUANTITY", "AMOUNT", "VOLUME"], "description": "门槛类型：数量、金额或体积"}, "threshold_value": {"$ref": "#/definitions/PositiveNumber", "description": "门槛数值"}, "max_discount_amount": {"$ref": "#/definitions/PositiveNumber", "description": "最大折扣金额限制"}}, "required": ["id", "promotion_id", "type", "discount_value"], "additionalProperties": false}, "GradientDiscountDTO": {"type": "object", "title": "梯度折扣", "description": "支持多层级的梯度折扣配置", "properties": {"id": {"$ref": "#/definitions/UUID", "description": "梯度折扣唯一标识"}, "promotion_id": {"$ref": "#/definitions/UUID", "description": "关联的促销活动ID"}, "name": {"$ref": "#/definitions/NonEmptyString", "description": "梯度折扣名称"}, "description": {"type": "string", "description": "梯度折扣描述"}, "threshold_type": {"type": "string", "enum": ["QUANTITY", "AMOUNT", "VOLUME"], "description": "门槛类型：数量、金额或体积"}, "levels": {"type": "array", "items": {"$ref": "#/definitions/GradientLevelDTO"}, "minItems": 1, "description": "梯度级别列表，按门槛值升序排列"}, "created_at": {"$ref": "#/definitions/Timestamp", "description": "创建时间"}, "updated_at": {"$ref": "#/definitions/Timestamp", "description": "更新时间"}}, "required": ["id", "promotion_id", "name", "threshold_type", "levels"], "additionalProperties": false}, "GradientLevelDTO": {"type": "object", "title": "梯度级别", "description": "梯度折扣中的单个级别配置", "properties": {"level": {"type": "integer", "minimum": 1, "description": "级别序号，从1开始"}, "threshold_value": {"$ref": "#/definitions/PositiveNumber", "description": "门槛值，达到此值触发该级别折扣"}, "discount_type": {"$ref": "#/definitions/DiscountType", "description": "折扣类型"}, "discount_value": {"$ref": "#/definitions/PositiveNumber", "description": "折扣值"}, "max_discount_amount": {"$ref": "#/definitions/PositiveNumber", "description": "该级别的最大折扣金额"}, "description": {"type": "string", "description": "级别描述"}}, "required": ["level", "threshold_value", "discount_type", "discount_value"], "additionalProperties": false}, "RuleDTO": {"type": "object", "title": "促销规则", "description": "促销活动的业务规则配置", "properties": {"id": {"$ref": "#/definitions/UUID", "description": "规则唯一标识"}, "promotion_id": {"$ref": "#/definitions/UUID", "description": "关联的促销活动ID"}, "parent_id": {"$ref": "#/definitions/UUID", "description": "父规则ID，支持嵌套规则"}, "type": {"$ref": "#/definitions/RuleType", "description": "规则类型"}, "path": {"type": "string", "description": "JSONPath或类似的上下文数据访问路径"}, "operator": {"$ref": "#/definitions/RuleOperator", "description": "规则操作符"}, "value": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "array"}, {"type": "object"}], "description": "规则比较值，支持多种数据类型"}, "priority": {"type": "integer", "minimum": 0, "description": "执行优先级"}, "description": {"type": "string", "description": "规则描述"}, "metadata": {"$ref": "#/definitions/Metadata", "description": "规则元数据配置"}, "min_purchase_amount": {"$ref": "#/definitions/PositiveNumber", "description": "最低购买金额门槛"}, "max_purchase_amount": {"$ref": "#/definitions/PositiveNumber", "description": "最高购买金额门槛"}, "product_ids": {"type": "array", "items": {"type": "string"}, "description": "适用商品ID列表"}, "category_ids": {"type": "array", "items": {"type": "string"}, "description": "适用商品类别ID列表"}, "gradient_level": {"type": "integer", "minimum": 1, "description": "梯度级别"}, "threshold_type": {"type": "string", "enum": ["QUANTITY", "AMOUNT", "VOLUME"], "description": "门槛类型：数量、金额或体积"}, "threshold_value": {"$ref": "#/definitions/PositiveNumber", "description": "门槛数值"}, "created_at": {"$ref": "#/definitions/Timestamp", "description": "创建时间"}, "updated_at": {"$ref": "#/definitions/Timestamp", "description": "更新时间"}}, "required": ["id", "promotion_id", "type", "operator"], "additionalProperties": false}, "FuelTypeRuleDTO": {"type": "object", "title": "燃油类型规则", "description": "专门用于燃油类型判断的规则", "properties": {"id": {"$ref": "#/definitions/UUID", "description": "规则唯一标识"}, "promotion_id": {"$ref": "#/definitions/UUID", "description": "关联的促销活动ID"}, "fuel_types": {"type": "array", "items": {"$ref": "#/definitions/FuelType"}, "minItems": 1, "description": "适用的燃油类型列表"}, "operator": {"type": "string", "enum": ["in", "not_in", "eq", "neq"], "description": "燃油类型匹配操作符"}, "description": {"type": "string", "description": "规则描述"}, "created_at": {"$ref": "#/definitions/Timestamp", "description": "创建时间"}, "updated_at": {"$ref": "#/definitions/Timestamp", "description": "更新时间"}}, "required": ["id", "promotion_id", "fuel_types", "operator"], "additionalProperties": false}, "UserTagRuleDTO": {"type": "object", "title": "用户标签规则", "description": "基于用户标签的规则判断", "properties": {"id": {"$ref": "#/definitions/UUID", "description": "规则唯一标识"}, "promotion_id": {"$ref": "#/definitions/UUID", "description": "关联的促销活动ID"}, "user_tags": {"type": "array", "items": {"$ref": "#/definitions/UserTag"}, "minItems": 1, "description": "用户标签列表"}, "operator": {"type": "string", "enum": ["in", "not_in", "contains", "not_contains"], "description": "用户标签匹配操作符"}, "match_all": {"type": "boolean", "default": false, "description": "是否需要匹配所有标签（AND逻辑），否则为OR逻辑"}, "description": {"type": "string", "description": "规则描述"}, "created_at": {"$ref": "#/definitions/Timestamp", "description": "创建时间"}, "updated_at": {"$ref": "#/definitions/Timestamp", "description": "更新时间"}}, "required": ["id", "promotion_id", "user_tags", "operator"], "additionalProperties": false}, "TimeCycleDTO": {"type": "object", "title": "时间周期", "description": "促销活动的时间周期配置", "properties": {"id": {"$ref": "#/definitions/UUID", "description": "时间周期唯一标识"}, "promotion_id": {"$ref": "#/definitions/UUID", "description": "关联的促销活动ID"}, "type": {"type": "string", "enum": ["DAILY", "WEEKLY", "MONTHLY", "SPECIFIC_DATE", "CUSTOM"], "description": "周期类型"}, "start_time": {"$ref": "#/definitions/Timestamp", "description": "周期整体开始时间"}, "end_time": {"$ref": "#/definitions/Timestamp", "description": "周期整体结束时间"}, "start_hour": {"type": "integer", "minimum": 0, "maximum": 23, "description": "每日开始小时 (0-23)"}, "end_hour": {"type": "integer", "minimum": 1, "maximum": 24, "description": "每日结束小时 (1-24)"}, "week_days": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 7}, "uniqueItems": true, "description": "适用的星期几 (1=周一, 7=周日)"}, "month_days": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 31}, "uniqueItems": true, "description": "适用的月份中的日期"}, "months": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 12}, "uniqueItems": true, "description": "适用的月份 (1-12)"}, "priority": {"type": "integer", "minimum": 0, "description": "时间周期优先级"}, "timezone": {"type": "string", "description": "时区，如 'Asia/Jakarta', 'UTC' 等"}, "exclude_dates": {"type": "array", "items": {"type": "string", "format": "date"}, "description": "排除的具体日期列表 (YYYY-MM-DD格式)"}, "include_dates": {"type": "array", "items": {"type": "string", "format": "date"}, "description": "特定包含的日期列表 (YYYY-MM-DD格式)"}, "created_at": {"$ref": "#/definitions/Timestamp", "description": "创建时间"}, "updated_at": {"$ref": "#/definitions/Timestamp", "description": "更新时间"}}, "required": ["id", "promotion_id", "type", "start_time", "end_time"], "additionalProperties": false}, "DailyTimeCycleDTO": {"type": "object", "title": "每日时间周期", "description": "每日重复的时间周期配置", "allOf": [{"$ref": "#/definitions/TimeCycleDTO"}, {"properties": {"type": {"const": "DAILY"}, "start_hour": {"type": "integer", "minimum": 0, "maximum": 23}, "end_hour": {"type": "integer", "minimum": 1, "maximum": 24}}, "required": ["start_hour", "end_hour"]}]}, "WeeklyTimeCycleDTO": {"type": "object", "title": "每周时间周期", "description": "每周重复的时间周期配置", "allOf": [{"$ref": "#/definitions/TimeCycleDTO"}, {"properties": {"type": {"const": "WEEKLY"}, "week_days": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 7}, "minItems": 1, "uniqueItems": true}}, "required": ["week_days"]}]}, "MonthlyTimeCycleDTO": {"type": "object", "title": "每月时间周期", "description": "每月重复的时间周期配置", "allOf": [{"$ref": "#/definitions/TimeCycleDTO"}, {"properties": {"type": {"const": "MONTHLY"}, "month_days": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 31}, "minItems": 1, "uniqueItems": true}}, "required": ["month_days"]}]}, "PromotionPackageDTO": {"type": "object", "title": "促销套件", "description": "包含促销活动及其关联组件的完整套件", "properties": {"promotion": {"$ref": "#/definitions/PromotionDTO", "description": "促销活动基本信息"}, "discount": {"$ref": "#/definitions/DiscountDTO", "description": "单个折扣配置（向后兼容）"}, "discounts": {"type": "array", "items": {"$ref": "#/definitions/DiscountDTO"}, "description": "多个折扣配置（支持梯度促销）"}, "time_cycles": {"type": "array", "items": {"$ref": "#/definitions/TimeCycleDTO"}, "description": "时间周期配置"}, "rules": {"type": "array", "items": {"$ref": "#/definitions/RuleDTO"}, "description": "规则配置"}}, "required": ["promotion"], "additionalProperties": false}}}