package handlers

import (
	"context"
	"fmt"
	"net/http"
	"time"

	bosModels "gitlab4.weicheche.cn/indo-bp/bos/internal/models"

	"gitlab4.weicheche.cn/indo-bp/pts2-go/pkg/client/domain"
	ptsModels "gitlab4.weicheche.cn/indo-bp/pts2-go/pkg/client/models"
	"gitlab4.weicheche.cn/indo-bp/pts2-go/pkg/ptsmanager"
	"gitlab4.weicheche.cn/indo-bp/pts2-go/pkg/utils"

	"github.com/labstack/echo/v4"
)

type ErrorResponse bosModels.ErrorResponse

// DeviceListResponse 设备列表响应
type DeviceListResponse []*ptsmanager.DeviceConfig

// DeviceDetailResponse 设备详情响应
type DeviceDetailResponse ptsmanager.DeviceConfig

// DeviceStatusResponse 设备状态响应
type DeviceStatusResponse ptsmanager.DeviceState

// DeviceInfoResponse 设备信息响应
type DeviceInfoResponse ptsModels.DeviceInfo

// StatusResponse 状态响应
type StatusResponse struct {
	Success bool   `json:"success" example:"true" description:"操作是否成功"`
	Message string `json:"message" example:"操作成功" description:"状态消息"`
}

// PTS2DeviceHandler 处理PTS2设备相关API
type PTS2DeviceHandler struct {
	Manager     *ptsmanager.Manager
	Logger      ptsmanager.Logger
	DeviceStore ptsmanager.DeviceStore
}

// NewPTS2DeviceHandler 创建新的PTS2设备处理器
func NewPTS2DeviceHandler(manager *ptsmanager.Manager, logger ptsmanager.Logger, store ptsmanager.DeviceStore) *PTS2DeviceHandler {
	return &PTS2DeviceHandler{
		Manager:     manager,
		Logger:      logger,
		DeviceStore: store,
	}
}

// Register 注册路由
func (h *PTS2DeviceHandler) Register(e *echo.Echo) {
	// 创建PTS2设备API组
	pts2 := e.Group("/api/v1/pts2")
	devices := pts2.Group("/devices")

	// 设备列表
	devices.GET("", h.GetDeviceList)

	// 设备详情
	devices.GET("/:deviceId", h.GetDeviceDetail)

	// 设备状态
	devices.GET("/:deviceId/status", h.GetDeviceStatus)

	// 连接设备
	devices.POST("/:deviceId/connect", h.ConnectDevice)

	// 断开设备
	devices.POST("/:deviceId/disconnect", h.DisconnectDevice)

	// 获取设备信息
	devices.GET("/:deviceId/info", h.GetDeviceInfo)
}

// GetDeviceList 获取设备列表
// @Summary 获取设备列表
// @Description 获取系统中所有已配置的PTS2设备列表
// @Tags PTS2设备管理
// @Accept json
// @Produce json
// @Success 200 {array} ptsmanager.DeviceConfig "设备列表"
// @Failure 500 {object} bosModels.ErrorResponse "服务器内部错误"
// @Router /pts2/devices [get]
func (h *PTS2DeviceHandler) GetDeviceList(c echo.Context) error {
	devices, err := h.DeviceStore.ListDevices(c.Request().Context())
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("获取设备列表失败: %v", err))
	}

	return c.JSON(http.StatusOK, devices)
}

// GetDeviceDetail 获取设备详情
// @Summary 获取设备详情
// @Description 获取指定PTS2设备的详细配置信息
// @Tags PTS2设备管理
// @Accept json
// @Produce json
// @Param deviceId path string true "设备ID"
// @Success 200 {object} ptsmanager.DeviceConfig "设备详情"
// @Failure 404 {object} bosModels.ErrorResponse "设备不存在"
// @Router /pts2/devices/{deviceId} [get]
func (h *PTS2DeviceHandler) GetDeviceDetail(c echo.Context) error {
	deviceID := c.Param("deviceId")

	device, err := h.DeviceStore.GetDevice(deviceID)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusNotFound, fmt.Sprintf("获取设备失败: %v", err))
	}

	return c.JSON(http.StatusOK, device)
}

// GetDeviceStatus 获取设备状态
// @Summary 获取设备状态
// @Description 获取指定PTS2设备的连接状态和错误信息
// @Tags PTS2设备管理
// @Accept json
// @Produce json
// @Param deviceId path string true "设备ID"
// @Success 200 {object} ptsmanager.DeviceState "设备状态"
// @Failure 500 {object} bosModels.ErrorResponse "服务器内部错误"
// @Router /pts2/devices/{deviceId}/status [get]
func (h *PTS2DeviceHandler) GetDeviceStatus(c echo.Context) error {
	deviceID := c.Param("deviceId")

	state, err := h.Manager.GetDeviceStatus(deviceID)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("获取设备状态失败: %v", err))
	}

	return c.JSON(http.StatusOK, state)
}

// ConnectDevice 连接设备
// @Summary 连接设备
// @Description 建立与指定PTS2设备的连接
// @Tags PTS2设备管理
// @Accept json
// @Produce json
// @Param deviceId path string true "设备ID"
// @Success 200 {object} StatusResponse "连接结果"
// @Failure 500 {object} bosModels.ErrorResponse "服务器内部错误"
// @Router /pts2/devices/{deviceId}/connect [post]
func (h *PTS2DeviceHandler) ConnectDevice(c echo.Context) error {
	deviceID := c.Param("deviceId")

	if err := h.Manager.ConnectDevice(deviceID); err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("连接失败: %v", err))
	}

	response := StatusResponse{
		Success: true,
		Message: "设备已连接",
	}
	return c.JSON(http.StatusOK, response)
}

// DisconnectDevice 断开设备连接
// @Summary 断开设备连接
// @Description 断开与指定PTS2设备的连接
// @Tags PTS2设备管理
// @Accept json
// @Produce json
// @Param deviceId path string true "设备ID"
// @Success 200 {object} StatusResponse "断开连接结果"
// @Failure 500 {object} bosModels.ErrorResponse "服务器内部错误"
// @Router /pts2/devices/{deviceId}/disconnect [post]
func (h *PTS2DeviceHandler) DisconnectDevice(c echo.Context) error {
	deviceID := c.Param("deviceId")

	if err := h.Manager.DisconnectDevice(deviceID); err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("断开连接失败: %v", err))
	}

	response := StatusResponse{
		Success: true,
		Message: "设备已断开连接",
	}
	return c.JSON(http.StatusOK, response)
}

// GetDeviceInfo 获取设备信息
// @Summary 获取设备信息
// @Description 获取指定PTS2设备的详细信息，包括设备类型、序列号、版本等
// @Tags PTS2设备管理
// @Accept json
// @Produce json
// @Param deviceId path string true "设备ID"
// @Success 200 {object} DeviceInfoResponse "设备信息"
// @Failure 500 {object} bosModels.ErrorResponse "服务器内部错误"
// @Router /pts2/devices/{deviceId}/info [get]
func (h *PTS2DeviceHandler) GetDeviceInfo(c echo.Context) error {
	deviceID := c.Param("deviceId")

	client, err := h.GetDeviceClient(deviceID)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("获取客户端连接失败: %v", err))
	}

	ctx, cancel := context.WithTimeout(c.Request().Context(), 10*time.Second)
	defer cancel()

	deviceInfo, err := client.GetDeviceInfo(ctx)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("获取设备信息失败: %v", err))
	}

	return c.JSON(http.StatusOK, deviceInfo)
}

// GetDeviceClient 获取设备客户端
func (h *PTS2DeviceHandler) GetDeviceClient(deviceID string) (domain.Client, error) {
	return h.Manager.GetDeviceClient(deviceID)
}
