package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	ptsModels "gitlab4.weicheche.cn/indo-bp/pts2-go/pkg/client/models"
	"gitlab4.weicheche.cn/indo-bp/pts2-go/pkg/ptsmanager"
	"gitlab4.weicheche.cn/indo-bp/pts2-go/pkg/utils"

	"github.com/labstack/echo/v4"
)

type PumpStatusResponse ptsModels.PumpStatusResponse

// AuthorizePumpRequest 授权油泵请求
type AuthorizePumpRequest struct {
	NozzleID int     `json:"nozzle_id" example:"1" description:"油枪ID"`
	Tag      *string `json:"tag,omitempty" example:"TAG123456" description:"可选的标签信息"`
}

// AuthorizePumpResponse 授权油泵响应
type AuthorizePumpResponse struct {
	Success       bool   `json:"success" example:"true" description:"是否成功"`
	TransactionID int    `json:"transaction_id" example:"12345" description:"交易ID"`
	Message       string `json:"message" example:"油泵授权成功" description:"响应消息"`
	NozzleID      int    `json:"nozzle_id" example:"1" description:"油枪ID"`
}

// PresetPumpRequest 预设油泵请求
type PresetPumpRequest struct {
	NozzleID   int     `json:"nozzle_id" example:"1" description:"油枪ID"`
	PresetType string  `json:"preset_type" example:"amount" description:"预设类型，'amount'表示金额，'volume'表示容量"`
	Value      float64 `json:"value" example:"100.0" description:"预设值，根据预设类型表示金额或容量"`
	Tag        *string `json:"tag,omitempty" example:"TAG123456" description:"可选的标签信息"`
}

// PresetPumpResponse 预设油泵响应
type PresetPumpResponse struct {
	Success       bool    `json:"success" example:"true" description:"是否成功"`
	TransactionID int     `json:"transaction_id" example:"12345" description:"交易ID"`
	Message       string  `json:"message" example:"预设金额成功" description:"响应消息"`
	Price         float64 `json:"price" example:"7.25" description:"油品价格"`
	Value         float64 `json:"value" example:"100.0" description:"预设值"`
	PresetType    string  `json:"preset_type" example:"amount" description:"预设类型"`
}

// SetPumpPricesRequest 设置油泵价格请求
type SetPumpPricesRequest struct {
	Prices map[int]float64 `json:"prices"  description:"油枪价格映射，key为油枪ID，value为价格"`
}

// SetPumpPricesResponse 设置油泵价格响应
type SetPumpPricesResponse struct {
	Success bool            `json:"success" example:"true" description:"是否成功"`
	Message string          `json:"message" example:"油泵价格调整成功" description:"响应消息"`
	Prices  map[int]float64 `json:"prices"  description:"设置的油枪价格"`
}

// SetNozzlePriceRequest 设置单个油枪价格请求
type SetNozzlePriceRequest struct {
	Price float64 `json:"price" example:"7.25" description:"油品价格"`
}

// SetNozzlePriceResponse 设置单个油枪价格响应
type SetNozzlePriceResponse struct {
	Success  bool    `json:"success" example:"true" description:"是否成功"`
	Message  string  `json:"message" example:"Nozzle 1 价格调整成功" description:"响应消息"`
	NozzleID int     `json:"nozzle_id" example:"1" description:"油枪ID"`
	Price    float64 `json:"price" example:"7.25" description:"设置的价格"`
	PumpID   int     `json:"pump_id" example:"1" description:"油泵ID"`
}

// PTS2PumpHandler 处理PTS2油泵相关API
type PTS2PumpHandler struct {
	Manager *ptsmanager.Manager
	Logger  ptsmanager.Logger
}

// NewPTS2PumpHandler 创建PTS2油泵处理器
func NewPTS2PumpHandler(manager *ptsmanager.Manager, logger ptsmanager.Logger) *PTS2PumpHandler {
	return &PTS2PumpHandler{
		Manager: manager,
		Logger:  logger,
	}
}

// Register 注册路由
func (h *PTS2PumpHandler) Register(e *echo.Echo) {
	// 创建PTS2设备API组
	pts2 := e.Group("/api/v1/pts2")

	// 油泵状态API
	pts2.GET("/devices/:deviceId/pumps/:pumpId", h.GetPumpStatus)

	// 油泵授权API
	pts2.POST("/devices/:deviceId/pumps/:pumpId/authorize", h.AuthorizePump)

	// 油泵预设API
	pts2.POST("/devices/:deviceId/pumps/:pumpId/preset", h.PresetPump)

	// 油泵调价API
	pts2.POST("/devices/:deviceId/pumps/:pumpId/prices", h.SetPumpPrices)

	// Nozzle调价API
	pts2.POST("/devices/:deviceId/pumps/:pumpId/nozzles/:nozzleId/price", h.SetNozzlePrice)
}

// GetPumpStatus 获取油泵状态
// @Summary 获取油泵状态
// @Description 获取指定PTS2设备上指定油泵的当前状态信息
// @Tags PTS2油泵管理
// @Accept json
// @Produce json
// @Param deviceId path string true "设备ID"
// @Param pumpId path int true "油泵ID"
// @Success 200 {object} PumpStatusResponse "油泵状态"
// @Failure 400 {object} ErrorResponse "参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /pts2/devices/{deviceId}/pumps/{pumpId} [get]
func (h *PTS2PumpHandler) GetPumpStatus(c echo.Context) error {
	deviceID := c.Param("deviceId")
	pumpIDStr := c.Param("pumpId")

	pumpID, err := strconv.Atoi(pumpIDStr)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, "无效的油泵ID")
	}

	client, err := h.Manager.GetDeviceClient(deviceID)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("获取客户端连接失败: %v", err))
	}

	ctx, cancel := context.WithTimeout(c.Request().Context(), 10*time.Second)
	defer cancel()

	status, err := client.GetPumpStatus(ctx, pumpID)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("获取油泵状态失败: %v", err))
	}

	return c.JSON(http.StatusOK, status)
}

// AuthorizePump 授权油泵
// @Summary 授权油泵
// @Description 对指定PTS2设备上的油泵进行授权操作
// @Tags PTS2油泵管理
// @Accept json
// @Produce json
// @Param deviceId path string true "设备ID"
// @Param pumpId path int true "油泵ID"
// @Param request body AuthorizePumpRequest true "授权参数"
// @Success 200 {object} AuthorizePumpResponse "授权结果"
// @Failure 400 {object} ErrorResponse "参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /pts2/devices/{deviceId}/pumps/{pumpId}/authorize [post]
func (h *PTS2PumpHandler) AuthorizePump(c echo.Context) error {
	deviceID := c.Param("deviceId")
	pumpIDStr := c.Param("pumpId")

	pumpID, err := strconv.Atoi(pumpIDStr)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, "无效的油泵ID")
	}

	// 解析请求体
	var params AuthorizePumpRequest

	if err := c.Bind(&params); err != nil {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, fmt.Sprintf("解析请求参数失败: %v", err))
	}

	client, err := h.Manager.GetDeviceClient(deviceID)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("获取客户端连接失败: %v", err))
	}

	ctx, cancel := context.WithTimeout(c.Request().Context(), 10*time.Second)
	defer cancel()

	// 生成交易ID（使用当前时间戳的低16位作为交易ID，保证在1-65535范围内）
	transactionID := int(time.Now().Unix()%65535) + 1
	h.Logger.Debugf("生成交易ID: %d", transactionID)

	// 准备Tag参数，如果未提供则使用空字符串
	tag := ""
	if params.Tag != nil {
		tag = *params.Tag
	}

	// 调用客户端的授权方法
	confirmation, err := client.AuthorizePump(
		ctx,
		pumpID,          // 油泵ID
		params.NozzleID, // NozzleID
		"FullTank",      //在某些情况下，预设油箱加满也意味着预设剂量将从泵预设键盘输入
		0,               // 预设值
		0,               // 价格
		transactionID,   // 交易编号
		false,           // 不自动结束交易
		tag,             // 标签参数，使用传入的值
		false,           // 不请求标签信息
		tag == "",       // 不带标签进行授权（如果tag为空则为true）
		true,            // 不验证标签
		true,            // 不应用用户
		1,               // 支付方式ID
	)

	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("授权油泵失败: %v", err))
	}

	// 返回授权结果
	response := AuthorizePumpResponse{
		Success:       true,
		TransactionID: confirmation.TransactionID,
		Message:       "油泵授权成功",
		NozzleID:      params.NozzleID,
	}
	return c.JSON(http.StatusOK, response)
}

// PresetPump 预设油泵
// @Summary 预设油泵
// @Description 对指定PTS2设备上的油泵进行预设操作，可设置加油金额或容量
// @Tags PTS2油泵管理
// @Accept json
// @Produce json
// @Param deviceId path string true "设备ID"
// @Param pumpId path int true "油泵ID"
// @Param request body PresetPumpRequest true "预设参数"
// @Success 200 {object} PresetPumpResponse "预设结果"
// @Failure 400 {object} ErrorResponse "参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /pts2/devices/{deviceId}/pumps/{pumpId}/preset [post]
func (h *PTS2PumpHandler) PresetPump(c echo.Context) error {
	deviceID := c.Param("deviceId")
	pumpIDStr := c.Param("pumpId")

	pumpID, err := strconv.Atoi(pumpIDStr)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, "无效的油泵ID")
	}

	// 解析请求体
	var params PresetPumpRequest

	if err := c.Bind(&params); err != nil {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, fmt.Sprintf("解析请求参数失败: %v", err))
	}

	client, err := h.Manager.GetDeviceClient(deviceID)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("获取客户端连接失败: %v", err))
	}

	ctx, cancel := context.WithTimeout(c.Request().Context(), 10*time.Second)
	defer cancel()

	// 根据预设类型确定PTS-2的预设类型
	var presetType string
	if params.PresetType == "amount" {
		presetType = "Amount"
	} else if params.PresetType == "volume" {
		presetType = "Volume"
	} else {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, "无效的预设类型，必须是 'amount' 或 'volume'")
	}

	// 获取油泵状态以获取当前价格
	status, err := client.GetPumpStatus(ctx, pumpID)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("获取油泵状态失败: %v", err))
	}

	// 提取价格
	var price float64 = 0
	// 尝试从状态中获取价格信息
	if status != nil {
		// 如果状态包含价格信息
		if status.Prices != nil && len(status.Prices) > 0 {
			// 尝试直接从价格映射中获取
			if nozzlePrice, ok := status.Prices[params.NozzleID]; ok {
				price = nozzlePrice
				h.Logger.Debugf("从价格映射中获取到Nozzle %d 的价格: %.2f", params.NozzleID, price)
			}
		}

		// 如果没有找到价格，尝试从IdleStatus中获取
		if price == 0 && status.IdleStatus != nil && status.IdleStatus.NozzlePrices != nil {
			// 检查NozzleID是否在有效范围内
			if params.NozzleID > 0 && params.NozzleID <= len(status.IdleStatus.NozzlePrices) {
				price = status.IdleStatus.NozzlePrices[params.NozzleID-1] // 数组索引从0开始，NozzleID从1开始
				h.Logger.Debugf("从IdleStatus.NozzlePrices中获取到Nozzle %d 的价格: %.2f", params.NozzleID, price)
			}
		}

		// 记录价格提取结果
		if price > 0 {
			h.Logger.Infof("为油泵 %d Nozzle %d 提取到价格: %.2f", pumpID, params.NozzleID, price)
		} else {
			h.Logger.Warnf("无法为油泵 %d Nozzle %d 提取价格，使用默认值0", pumpID, params.NozzleID)
		}
	}

	// 准备Tag参数，如果未提供则使用空字符串
	tag := ""
	if params.Tag != nil {
		tag = *params.Tag
	}

	// 调用客户端的授权方法，添加预设
	confirmation, err := client.AuthorizePump(
		ctx,
		pumpID,          // 油泵ID
		params.NozzleID, // NozzleID
		presetType,      // 预设类型
		params.Value,    // 预设值
		price,           // 价格
		0,               // 交易编号
		false,           // 不自动结束交易
		tag,             // 标签参数，使用传入的值
		false,           // 不请求标签信息
		tag == "",       // 不带标签进行授权（如果tag为空则为true）
		true,            // 不验证标签
		true,            // 不应用用户
		1,               // 支付方式ID
	)

	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("预设加油参数失败: %v", err))
	}

	// 构建响应消息
	var messageType string
	if params.PresetType == "amount" {
		messageType = "金额"
	} else {
		messageType = "容量"
	}

	// 返回预设结果
	response := PresetPumpResponse{
		Success:       true,
		TransactionID: confirmation.TransactionID,
		Message:       fmt.Sprintf("预设%s成功", messageType),
		Price:         price,
		Value:         params.Value,
		PresetType:    params.PresetType,
	}
	return c.JSON(http.StatusOK, response)
}

// SetPumpPrices 设置油泵价格
// @Summary 设置油泵价格
// @Description 设置指定PTS2设备上油泵的所有喷枪价格
// @Tags PTS2油泵管理
// @Accept json
// @Produce json
// @Param deviceId path string true "设备ID"
// @Param pumpId path int true "油泵ID"
// @Param request body SetPumpPricesRequest true "价格参数"
// @Success 200 {object} SetPumpPricesResponse "调价结果"
// @Failure 400 {object} ErrorResponse"参数错误"
// @Failure 500 {object} ErrorResponse"服务器内部错误"
// @Router /pts2/devices/{deviceId}/pumps/{pumpId}/prices [post]
func (h *PTS2PumpHandler) SetPumpPrices(c echo.Context) error {
	deviceID := c.Param("deviceId")
	pumpIDStr := c.Param("pumpId")

	pumpID, err := strconv.Atoi(pumpIDStr)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, "无效的油泵ID")
	}

	// 解析请求体
	var params SetPumpPricesRequest

	if err := c.Bind(&params); err != nil {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, fmt.Sprintf("解析请求参数失败: %v", err))
	}

	// 检查是否提供了价格信息
	if params.Prices == nil || len(params.Prices) == 0 {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, "未提供价格信息")
	}

	client, err := h.Manager.GetDeviceClient(deviceID)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("获取客户端连接失败: %v", err))
	}

	ctx, cancel := context.WithTimeout(c.Request().Context(), 10*time.Second)
	defer cancel()

	// 将映射转换为数组，确保数组长度至少为6（支持最多6个Nozzle）
	pricesArray := make([]float64, 6)
	for nozzleID, price := range params.Prices {
		// 检查NozzleID是否在有效范围内
		if nozzleID > 0 && nozzleID <= len(pricesArray) {
			pricesArray[nozzleID-1] = price // 数组索引从0开始，NozzleID从1开始
			h.Logger.Debugf("设置Nozzle %d 的价格为: %.2f", nozzleID, price)
		} else {
			h.Logger.Warnf("忽略无效的NozzleID: %d", nozzleID)
		}
	}

	// 调用客户端的设置价格方法
	err = client.SetPumpPrices(ctx, pumpID, pricesArray)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("调整油泵价格失败: %v", err))
	}

	// 返回调价结果
	response := SetPumpPricesResponse{
		Success: true,
		Message: "油泵价格调整成功",
		Prices:  params.Prices,
	}
	return c.JSON(http.StatusOK, response)
}

// SetNozzlePrice 设置单个Nozzle价格
// @Summary 设置喷枪价格
// @Description 设置指定PTS2设备上油泵的单个喷枪价格
// @Tags PTS2油泵管理
// @Accept json
// @Produce json
// @Param deviceId path string true "设备ID"
// @Param pumpId path int true "油泵ID"
// @Param nozzleId path int true "喷枪ID"
// @Param request body SetNozzlePriceRequest true "价格参数"
// @Success 200 {object} SetNozzlePriceResponse "调价结果"
// @Failure 400 {object} ErrorResponse"参数错误"
// @Failure 500 {object} ErrorResponse"服务器内部错误"
// @Router /pts2/devices/{deviceId}/pumps/{pumpId}/nozzles/{nozzleId}/price [post]
func (h *PTS2PumpHandler) SetNozzlePrice(c echo.Context) error {
	deviceID := c.Param("deviceId")
	pumpIDStr := c.Param("pumpId")
	nozzleIDStr := c.Param("nozzleId")

	pumpID, err := strconv.Atoi(pumpIDStr)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, "无效的油泵ID")
	}

	nozzleID, err := strconv.Atoi(nozzleIDStr)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, "无效的NozzleID")
	}

	// 检查NozzleID是否在有效范围内
	if nozzleID <= 0 || nozzleID > 6 {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, "NozzleID必须在1-6范围内")
	}

	// 解析请求体
	var params SetNozzlePriceRequest

	if err := c.Bind(&params); err != nil {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, fmt.Sprintf("解析请求参数失败: %v", err))
	}

	// 检查价格是否有效
	if params.Price <= 0 {
		return utils.ErrorResponseEcho(c, http.StatusBadRequest, "价格必须大于0")
	}

	client, err := h.Manager.GetDeviceClient(deviceID)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("获取客户端连接失败: %v", err))
	}

	ctx, cancel := context.WithTimeout(c.Request().Context(), 10*time.Second)
	defer cancel()

	// 获取当前油泵状态以获取现有的价格
	status, err := client.GetPumpStatus(ctx, pumpID)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("获取油泵状态失败: %v", err))
	}

	// 创建价格数组，保留其他Nozzle的价格不变
	pricesArray := make([]float64, 6)

	// 尝试从现有状态获取所有Nozzle价格
	if status != nil && status.IdleStatus != nil && status.IdleStatus.NozzlePrices != nil {
		// 复制现有价格
		copy(pricesArray, status.IdleStatus.NozzlePrices)
	}

	// 设置指定Nozzle的新价格
	pricesArray[nozzleID-1] = params.Price
	h.Logger.Debugf("设置Nozzle %d 的价格为: %.2f", nozzleID, params.Price)

	// 调用客户端的设置价格方法
	err = client.SetPumpPrices(ctx, pumpID, pricesArray)
	if err != nil {
		return utils.ErrorResponseEcho(c, http.StatusInternalServerError, fmt.Sprintf("调整Nozzle价格失败: %v", err))
	}

	// 返回调价结果
	response := SetNozzlePriceResponse{
		Success:  true,
		Message:  fmt.Sprintf("Nozzle %d 价格调整成功", nozzleID),
		NozzleID: nozzleID,
		Price:    params.Price,
		PumpID:   pumpID,
	}
	return c.JSON(http.StatusOK, response)
}
