{"$schema": "http://json-schema.org/draft-07/schema#", "title": "促销规则Schema", "description": "促销规则的JSON Schema验证，支持复杂的业务逻辑", "type": "object", "properties": {"id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "规则唯一标识"}, "promotion_id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "关联的促销活动ID"}, "parent_id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "父规则ID，支持嵌套规则"}, "type": {"type": "string", "enum": ["MIN_PURCHASE_AMOUNT", "MAX_PURCHASE_AMOUNT", "PRODUCT_INCLUDE", "PRODUCT_EXCLUDE", "CATEGORY_INCLUDE", "CATEGORY_EXCLUDE", "USER_TAG", "USER_LEVEL", "FUEL_TYPE", "FUEL_VOLUME", "CUMULATIVE_COUNT", "TIME_WINDOW"], "description": "规则类型"}, "path": {"type": "string", "description": "JSONPath或类似的上下文数据访问路径"}, "operator": {"type": "string", "enum": ["eq", "neq", "gt", "gte", "lt", "lte", "in", "not_in", "contains", "not_contains"], "description": "规则操作符"}, "value": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "array"}, {"type": "object"}], "description": "规则比较值，支持多种数据类型"}, "priority": {"type": "integer", "minimum": 0, "description": "执行优先级"}, "description": {"type": "string", "description": "规则描述"}, "metadata": {"type": "object", "additionalProperties": true, "description": "规则元数据配置"}, "min_purchase_amount": {"type": "number", "minimum": 0, "description": "最低购买金额门槛"}, "max_purchase_amount": {"type": "number", "minimum": 0, "description": "最高购买金额门槛"}, "product_ids": {"type": "array", "items": {"type": "string"}, "description": "适用商品ID列表"}, "category_ids": {"type": "array", "items": {"type": "string"}, "description": "适用商品类别ID列表"}, "gradient_level": {"type": "integer", "minimum": 1, "description": "梯度级别"}, "threshold_type": {"type": "string", "enum": ["QUANTITY", "AMOUNT", "VOLUME"], "description": "门槛类型：数量、金额或体积"}, "threshold_value": {"type": "number", "minimum": 0, "description": "门槛数值"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}, "required": ["id", "promotion_id", "type", "operator"], "additionalProperties": false, "definitions": {"FuelTypeRuleDTO": {"type": "object", "title": "燃油类型规则", "description": "专门用于燃油类型判断的规则", "properties": {"id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "规则唯一标识"}, "promotion_id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "关联的促销活动ID"}, "fuel_types": {"type": "array", "items": {"type": "string", "enum": ["BP92", "BP95", "Ultimate", "Ultimate_Diesel"]}, "minItems": 1, "description": "适用的燃油类型列表"}, "operator": {"type": "string", "enum": ["in", "not_in", "eq", "neq"], "description": "燃油类型匹配操作符"}, "description": {"type": "string", "description": "规则描述"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}, "required": ["id", "promotion_id", "fuel_types", "operator"], "additionalProperties": false}, "FuelVolumeRuleDTO": {"type": "object", "title": "燃油体积规则", "description": "专门用于燃油体积判断的规则", "properties": {"id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "规则唯一标识"}, "promotion_id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "关联的促销活动ID"}, "min_volume": {"type": "number", "minimum": 0, "description": "最小燃油体积"}, "max_volume": {"type": "number", "minimum": 0, "description": "最大燃油体积"}, "operator": {"type": "string", "enum": ["gte", "gt", "lte", "lt", "eq", "between"], "description": "燃油体积比较操作符"}, "threshold_value": {"type": "number", "minimum": 0, "description": "门槛体积值"}, "description": {"type": "string", "description": "规则描述"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}, "required": ["id", "promotion_id", "operator"], "additionalProperties": false}, "UserTagRuleDTO": {"type": "object", "title": "用户标签规则", "description": "基于用户标签的规则判断", "properties": {"id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "规则唯一标识"}, "promotion_id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "关联的促销活动ID"}, "user_tags": {"type": "array", "items": {"type": "string", "enum": ["MEMBER", "NON_MEMBER", "B2B", "VIP", "NEW_USER"]}, "minItems": 1, "description": "用户标签列表"}, "operator": {"type": "string", "enum": ["in", "not_in", "contains", "not_contains"], "description": "用户标签匹配操作符"}, "match_all": {"type": "boolean", "default": false, "description": "是否需要匹配所有标签（AND逻辑），否则为OR逻辑"}, "description": {"type": "string", "description": "规则描述"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}, "required": ["id", "promotion_id", "user_tags", "operator"], "additionalProperties": false}, "CumulativeCountRuleDTO": {"type": "object", "title": "累计计数规则", "description": "基于用户历史行为的累计计数规则", "properties": {"id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "规则唯一标识"}, "promotion_id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "关联的促销活动ID"}, "count_type": {"type": "string", "enum": ["ORDER_COUNT", "PURCHASE_AMOUNT", "PRODUCT_COUNT", "VISIT_COUNT"], "description": "计数类型"}, "time_window": {"type": "integer", "minimum": 1, "description": "时间窗口（天数）"}, "threshold_value": {"type": "number", "minimum": 0, "description": "门槛值"}, "operator": {"type": "string", "enum": ["gte", "gt", "lte", "lt", "eq"], "description": "计数比较操作符"}, "reset_cycle": {"type": "string", "enum": ["DAILY", "WEEKLY", "MONTHLY", "YEARLY", "NEVER"], "description": "重置周期"}, "product_ids": {"type": "array", "items": {"type": "string"}, "description": "特定商品ID列表，用于商品级别的计数"}, "category_ids": {"type": "array", "items": {"type": "string"}, "description": "特定类别ID列表，用于类别级别的计数"}, "description": {"type": "string", "description": "规则描述"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}, "required": ["id", "promotion_id", "count_type", "time_window", "threshold_value", "operator"], "additionalProperties": false}}}