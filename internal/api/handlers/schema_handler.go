package handlers

import (
	"net/http"
	"path/filepath"
	"strings"

	"github.com/labstack/echo/v4"
)

// SchemaHandler 处理Schema相关的请求
type SchemaHandler struct {
	schemaLoader *SchemaLoader
}

// NewSchemaHandler 创建一个新的SchemaHandler
func NewSchemaHandler() *SchemaHandler {
	return &SchemaHandler{
		schemaLoader: NewSchemaLoader(),
	}
}

// GetSchema 获取指定的Schema文件
// @Summary 获取Schema文件
// @Description 获取促销表单的JSON Schema文件
// @Tags schema
// @Accept json
// @Produce json
// @Param schemaName path string true "Schema文件名"
// @Success 200 {object} map[string]interface{} "Schema内容"
// @Failure 400 {object} map[string]interface{} "请求参数错误"
// @Failure 404 {object} map[string]interface{} "Schema文件不存在"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /api/schemas/{schemaName} [get]
func (h *SchemaHandler) GetSchema(c echo.Context) error {
	schemaName := c.Param("schemaName")

	// 验证schema文件名，防止路径遍历攻击
	if !isValidSchemaName(schemaName) {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error": "Invalid schema name",
			"code":  "INVALID_SCHEMA_NAME",
		})
	}

	// 加载Schema
	schema, err := h.schemaLoader.LoadSchema(schemaName)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(http.StatusNotFound, map[string]interface{}{
				"error":  "Schema not found",
				"code":   "SCHEMA_NOT_FOUND",
				"schema": schemaName,
			})
		}

		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"error":   "Failed to load schema",
			"code":    "SCHEMA_LOAD_ERROR",
			"details": err.Error(),
		})
	}

	// 设置适当的缓存头
	c.Response().Header().Set("Cache-Control", "public, max-age=3600") // 缓存1小时
	c.Response().Header().Set("Content-Type", "application/json")

	return c.JSON(http.StatusOK, schema)
}

// GetAllSchemas 获取所有可用的Schema列表
// @Summary 获取所有Schema列表
// @Description 获取所有可用的促销表单Schema文件列表
// @Tags schema
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "Schema列表"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /api/schemas [get]
func (h *SchemaHandler) GetAllSchemas(c echo.Context) error {
	schemas, err := h.schemaLoader.LoadAllSchemas()
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"error":   "Failed to load schemas",
			"code":    "SCHEMAS_LOAD_ERROR",
			"details": err.Error(),
		})
	}

	// 设置缓存头
	c.Response().Header().Set("Cache-Control", "public, max-age=1800") // 缓存30分钟
	c.Response().Header().Set("Content-Type", "application/json")

	return c.JSON(http.StatusOK, map[string]interface{}{
		"schemas": schemas,
		"count":   len(schemas),
	})
}

// GetSchemaMetadata 获取Schema的元数据信息
// @Summary 获取Schema元数据
// @Description 获取指定Schema的元数据信息（标题、描述、版本等）
// @Tags schema
// @Accept json
// @Produce json
// @Param schemaName path string true "Schema文件名"
// @Success 200 {object} map[string]interface{} "Schema元数据"
// @Failure 400 {object} map[string]interface{} "请求参数错误"
// @Failure 404 {object} map[string]interface{} "Schema文件不存在"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /api/schemas/{schemaName}/metadata [get]
func (h *SchemaHandler) GetSchemaMetadata(c echo.Context) error {
	schemaName := c.Param("schemaName")

	if !isValidSchemaName(schemaName) {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"error": "Invalid schema name",
			"code":  "INVALID_SCHEMA_NAME",
		})
	}

	metadata, err := h.schemaLoader.GetSchemaMetadata(schemaName)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(http.StatusNotFound, map[string]interface{}{
				"error":  "Schema not found",
				"code":   "SCHEMA_NOT_FOUND",
				"schema": schemaName,
			})
		}

		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"error":   "Failed to get schema metadata",
			"code":    "SCHEMA_METADATA_ERROR",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, metadata)
}

// isValidSchemaName 验证schema文件名是否有效
func isValidSchemaName(name string) bool {
	// 只允许特定的schema文件
	validSchemas := map[string]bool{
		"basic_info_schema.json":    true,
		"discount_type_schema.json": true,
		"rules_schema.json":         true,
		"time_cycles_schema.json":   true,
	}

	// 检查文件名是否在允许列表中
	if !validSchemas[name] {
		return false
	}

	// 检查路径是否包含危险字符
	if strings.Contains(name, "..") || strings.Contains(name, "/") || strings.Contains(name, "\\") {
		return false
	}

	// 检查文件扩展名
	if filepath.Ext(name) != ".json" {
		return false
	}

	return true
}
