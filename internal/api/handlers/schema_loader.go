package handlers

import (
	_ "embed"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/xeipuuv/gojsonschema"
)

// 嵌入所有的schema文件
//
//go:embed promotion_package_schema.json
var promotionSchemaBytes []byte

//go:embed basic_info_schema.json
var basicInfoSchemaBytes []byte

//go:embed discount_type_schema.json
var discountTypeSchemaBytes []byte

//go:embed rules_schema.json
var rulesSchemaBytes []byte

//go:embed time_cycles_schema.json
var timeCyclesSchemaBytes []byte

// SchemaLoader 处理schema文件的加载
type SchemaLoader struct {
	schemas map[string][]byte
}

// NewSchemaLoader 创建新的schema加载器
func NewSchemaLoader() *SchemaLoader {
	return &SchemaLoader{
		schemas: map[string][]byte{
			"promotion_package_schema.json": promotionSchemaBytes,
			"basic_info_schema.json":        basicInfoSchemaBytes,
			"discount_type_schema.json":     discountTypeSchemaBytes,
			"rules_schema.json":             rulesSchemaBytes,
			"time_cycles_schema.json":       timeCyclesSchemaBytes,
		},
	}
}

// LoadSchema 加载指定的schema文件
func (sl *SchemaLoader) LoadSchema(filename string) (map[string]interface{}, error) {
	schemaBytes, exists := sl.schemas[filename]
	if !exists {
		return nil, fmt.Errorf("schema file not found: %s", filename)
	}

	var schema map[string]interface{}
	if err := json.Unmarshal(schemaBytes, &schema); err != nil {
		return nil, fmt.Errorf("failed to parse schema %s: %w", filename, err)
	}

	return schema, nil
}

// LoadAllSchemas 加载所有可用的schema
func (sl *SchemaLoader) LoadAllSchemas() (map[string]interface{}, error) {
	schemas := make(map[string]interface{})

	// 加载所有schema文件
	schemaFiles := []string{
		"basic_info_schema.json",
		"discount_type_schema.json",
		"rules_schema.json",
		"time_cycles_schema.json",
	}

	for _, filename := range schemaFiles {
		schema, err := sl.LoadSchema(filename)
		if err != nil {
			return nil, fmt.Errorf("failed to load schema %s: %w", filename, err)
		}

		// 使用不带扩展名的文件名作为key
		key := strings.TrimSuffix(filename, ".json")
		schemas[key] = schema
	}

	return schemas, nil
}

// GetSchemaMetadata 获取schema的元数据信息
func (sl *SchemaLoader) GetSchemaMetadata(filename string) (map[string]interface{}, error) {
	schema, err := sl.LoadSchema(filename)
	if err != nil {
		return nil, err
	}

	// 提取元数据
	metadata := make(map[string]interface{})

	if title, ok := schema["title"]; ok {
		metadata["title"] = title
	}

	if description, ok := schema["description"]; ok {
		metadata["description"] = description
	}

	if schemaVersion, ok := schema["$schema"]; ok {
		metadata["schema_version"] = schemaVersion
	}

	// 添加文件信息
	metadata["filename"] = filename
	metadata["key"] = strings.TrimSuffix(filename, ".json")

	// 统计属性数量
	if properties, ok := schema["properties"].(map[string]interface{}); ok {
		metadata["property_count"] = len(properties)

		// 提取顶级属性名称
		var propertyNames []string
		for name := range properties {
			propertyNames = append(propertyNames, name)
		}
		metadata["properties"] = propertyNames
	}

	// 检查是否有required字段
	if required, ok := schema["required"].([]interface{}); ok {
		metadata["required_fields"] = required
		metadata["required_count"] = len(required)
	}

	return metadata, nil
}

// GetPromotionSchema 获取促销活动的JSON Schema加载器（保持向后兼容）
func GetPromotionSchema() (gojsonschema.JSONLoader, error) {
	return gojsonschema.NewBytesLoader(promotionSchemaBytes), nil
}

// GetSchemaLoader 获取指定schema的JSONLoader
func (sl *SchemaLoader) GetSchemaLoader(filename string) (gojsonschema.JSONLoader, error) {
	schemaBytes, exists := sl.schemas[filename]
	if !exists {
		return nil, fmt.Errorf("schema file not found: %s", filename)
	}

	return gojsonschema.NewBytesLoader(schemaBytes), nil
}
