package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"

	"gitlab4.weicheche.cn/indo-bp/bos/internal/models"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/utils"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"
)

// 错误类型检查函数
func isShiftAlreadyActiveError(err error) bool {
	return strings.Contains(err.Error(), "当前站点已有活跃班次")
}

func isNoActiveShiftError(err error) bool {
	return strings.Contains(err.Error(), "当前站点没有活跃班次")
}

func isCannotEndShiftWithPendingTransactionsError(err error) bool {
	return strings.Contains(err.Error(), "班次中有未处理的交易")
}

func isShiftNotFoundError(err error) bool {
	return strings.Contains(err.Error(), "找不到指定班次") ||
		strings.Contains(err.Error(), "SHIFT_NOT_FOUND") ||
		strings.Contains(err.Error(), "no rows in result set")
}

func isCannotDeleteActiveShiftError(err error) bool {
	return strings.Contains(err.Error(), "无法删除活跃班次")
}

// ShiftHandler 处理与班次相关的请求
type ShiftHandler struct {
	shiftService          service.ShiftService
	shiftAttendantService service.ShiftAttendantService
	shiftSummaryService   service.ShiftSummaryService
}

// NewShiftHandler 创建新的班次处理器
func NewShiftHandler(shiftService service.ShiftService, shiftAttendantService service.ShiftAttendantService, shiftSummaryService service.ShiftSummaryService) *ShiftHandler {
	return &ShiftHandler{
		shiftService:          shiftService,
		shiftAttendantService: shiftAttendantService,
		shiftSummaryService:   shiftSummaryService,
	}
}

// StartShiftRequest 定义开始班次的请求结构
type StartShiftRequest struct {
	StationID int64                  `json:"station_id" validate:"required"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// StartShift 处理开始新班次的请求
// @Summary 开始新班次
// @Description 在指定站点开始一个新的班次
// @Tags 班次
// @Accept json
// @Produce json
// @Param request body StartShiftRequest true "班次信息"
// @Success 201 {object} repository.Shift
// @Failure 400 {object} models.ErrorResponse
// @Failure 409 {object} models.ErrorResponse "当前站点已有活跃班次"
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts/start [post]
func (h *ShiftHandler) StartShift(c echo.Context) error {
	ctx := context.Background()

	var req StartShiftRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求数据无效",
			Detail:  err.Error(),
		})
	}

	// 条件验证 - 只有在验证器存在时才进行验证
	if c.Echo().Validator != nil {
		if err := c.Validate(&req); err != nil {
			return c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Code:    "VALIDATION_ERROR",
				Message: "输入验证失败",
				Detail:  err.Error(),
			})
		}
	}

	// 调用服务层方法
	shift, err := h.shiftService.StartShift(ctx, req.StationID, req.Metadata)
	if err != nil {
		// 检查是否为"已有活跃班次"错误
		if isShiftAlreadyActiveError(err) {
			return c.JSON(http.StatusConflict, models.ErrorResponse{
				Code:    "SHIFT_ALREADY_ACTIVE",
				Message: "该站点已有活跃班次",
				Detail:  err.Error(),
			})
		}

		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "开始班次失败",
			Detail:  err.Error(),
		})
	}

	return c.JSON(http.StatusCreated, shift)
}

// EndShift 处理结束班次的请求
// @Summary 结束当前班次
// @Description 结束指定站点的当前活跃班次
// @Tags 班次
// @Accept json
// @Produce json
// @Param station_id path int true "站点ID"
// @Success 200 {object} repository.Shift
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse "没有活跃班次"
// @Failure 409 {object} models.ErrorResponse "班次中有未处理的交易"
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts/{station_id}/end [post]
func (h *ShiftHandler) EndShift(c echo.Context) error {
	ctx := context.Background()

	// 解析站点ID
	stationIDStr := c.Param("station_id")
	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_STATION_ID",
			Message: "站点ID格式无效",
			Detail:  err.Error(),
		})
	}

	// 调用服务层方法
	shift, err := h.shiftService.EndShift(ctx, stationID)
	if err != nil {
		// 检查是否为"没有活跃班次"错误
		if isNoActiveShiftError(err) {
			return c.JSON(http.StatusNotFound, models.ErrorResponse{
				Code:    "NO_ACTIVE_SHIFT",
				Message: "该站点没有活跃班次",
				Detail:  err.Error(),
			})
		}

		// 检查是否为"有未处理交易"错误
		if isCannotEndShiftWithPendingTransactionsError(err) {
			return c.JSON(http.StatusConflict, models.ErrorResponse{
				Code:    "PENDING_TRANSACTIONS",
				Message: "班次中有未处理的交易，无法结束",
				Detail:  err.Error(),
			})
		}

		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "结束班次失败",
			Detail:  err.Error(),
		})
	}

	return c.JSON(http.StatusOK, shift)
}

// GetCurrentShift 处理获取当前活跃班次的请求
// @Summary 获取当前活跃班次
// @Description 获取指定站点的当前活跃班次状态，如果没有活跃班次则返回相应状态
// @Tags 班次
// @Accept json
// @Produce json
// @Param station_id path int true "站点ID"
// @Success 200 {object} models.CurrentShiftResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts/current/{station_id} [get]
func (h *ShiftHandler) GetCurrentShift(c echo.Context) error {
	ctx := context.Background()

	// 解析站点ID
	stationIDStr := c.Param("station_id")
	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_STATION_ID",
			Message: "站点ID格式无效",
			Detail:  err.Error(),
		})
	}

	// 调用服务层方法
	shift, err := h.shiftService.GetCurrentShift(ctx, stationID)
	if err != nil {
		// 检查是否为"没有活跃班次"错误
		if isNoActiveShiftError(err) {
			// 返回没有活跃班次的状态，而不是404错误
			response := models.CurrentShiftResponse{
				HasActiveShift: false,
				Shift:          nil,
				Message:        "该站点当前没有活跃班次",
			}
			return c.JSON(http.StatusOK, response)
		}

		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取当前班次失败",
			Detail:  err.Error(),
		})
	}

	// 返回有活跃班次的状态
	response := models.CurrentShiftResponse{
		HasActiveShift: true,
		Shift:          shift,
		Message:        "获取当前活跃班次成功",
	}
	return c.JSON(http.StatusOK, response)
}

// EnsureShiftStarted 处理确保班次已开始的请求
// @Summary 确保班次已开始
// @Description 确保指定站点有活跃班次，如果没有则自动创建
// @Tags 班次
// @Accept json
// @Produce json
// @Param station_id path int true "站点ID"
// @Success 200 {object} repository.Shift
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts/{station_id}/ensure [post]
func (h *ShiftHandler) EnsureShiftStarted(c echo.Context) error {
	ctx := context.Background()

	// 解析站点ID
	stationIDStr := c.Param("station_id")
	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_STATION_ID",
			Message: "站点ID格式无效",
			Detail:  err.Error(),
		})
	}

	// 调用服务层方法
	shift, created, err := h.shiftService.EnsureShiftStarted(ctx, stationID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "确保班次已开始失败",
			Detail:  err.Error(),
		})
	}

	status := http.StatusOK
	if created {
		status = http.StatusCreated
	}

	return c.JSON(status, shift)
}

// ListShiftsResponse 定义列表响应结构
type ListShiftsResponse struct {
	Items     []repository.Shift `json:"items"`
	Total     int                `json:"total"`
	Page      int                `json:"page"`
	PageSize  int                `json:"page_size"`
	TotalPage int                `json:"total_page"`
}

// ListShifts 处理获取班次列表的请求
// @Summary 列出班次
// @Description 获取班次列表，支持分页和过滤
// @Tags 班次
// @Accept json
// @Produce json
// @Param station_id query int false "站点ID"
// @Param status query string false "班次状态 (active, closed)"
// @Param shift_number query string false "班次编号"
// @Param date_from query string false "开始日期 (格式: 2006-01-02)"
// @Param date_to query string false "结束日期 (格式: 2006-01-02)"
// @Param include_deleted query bool false "是否包含已删除班次"
// @Param page query int false "页码，默认为1"
// @Param limit query int false "每页数量，默认为10"
// @Param sort_by query string false "排序字段，默认为created_at"
// @Param sort_dir query string false "排序方向 (asc, desc)，默认为desc"
// @Success 200 {object} ListShiftsResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts [get]
func (h *ShiftHandler) ListShifts(c echo.Context) error {
	ctx := context.Background()

	// 解析分页参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page <= 0 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit <= 0 {
		limit = 10
	}

	pagination := repository.Pagination{
		Page:  page,
		Limit: limit,
	}

	// 解析排序参数
	sortField := c.QueryParam("sort_by")
	if sortField == "" {
		sortField = "created_at"
	}

	sortDir := c.QueryParam("sort_dir")
	if sortDir != "asc" {
		sortDir = "desc" // 默认降序
	}

	sortOrder := repository.SortOrder{
		Field:     sortField,
		Direction: sortDir,
	}

	// 构建过滤条件
	filter := repository.ShiftFilter{}

	// 解析并设置过滤参数
	if c.QueryParam("station_id") != "" {
		stationID, err := strconv.ParseInt(c.QueryParam("station_id"), 10, 64)
		if err == nil && stationID > 0 {
			filter.StationID = &stationID // 直接使用 int64 类型
		}
	}

	if c.QueryParam("status") != "" {
		status := repository.ShiftStatus(c.QueryParam("status"))
		filter.Status = &status
	}

	if c.QueryParam("shift_number") != "" {
		shiftNumber := c.QueryParam("shift_number")
		filter.ShiftNumber = &shiftNumber
	}

	// 解析日期参数 - 使用雅加达时区
	if c.QueryParam("date_from") != "" {
		dateFromStr := c.QueryParam("date_from")
		dateFrom, err := time.Parse("2006-01-02", dateFromStr)
		if err == nil {
			// 在雅加达时区中创建日期的开始时间
			dateFrom = time.Date(dateFrom.Year(), dateFrom.Month(), dateFrom.Day(), 0, 0, 0, 0, utils.JakartaLocation)
			filter.DateFrom = &dateFrom
			// 添加调试日志
			fmt.Printf("DEBUG: date_from param: %s, parsed to Jakarta time: %s\n", dateFromStr, dateFrom.Format("2006-01-02 15:04:05 MST"))
		}
	}

	if c.QueryParam("date_to") != "" {
		dateToStr := c.QueryParam("date_to")
		dateTo, err := time.Parse("2006-01-02", dateToStr)
		if err == nil {
			// 在雅加达时区中创建日期的结束时间
			dateTo = time.Date(dateTo.Year(), dateTo.Month(), dateTo.Day(), 23, 59, 59, 999999999, utils.JakartaLocation)
			filter.DateTo = &dateTo
			// 添加调试日志
			fmt.Printf("DEBUG: date_to param: %s, parsed to Jakarta time: %s\n", dateToStr, dateTo.Format("2006-01-02 15:04:05 MST"))
		}
	}

	if c.QueryParam("include_deleted") != "" {
		includeDeleted, _ := strconv.ParseBool(c.QueryParam("include_deleted"))
		filter.IncludeDeleted = &includeDeleted
	}

	// 调用服务获取数据
	shifts, total, err := h.shiftService.ListShifts(ctx, filter, pagination, sortOrder)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取班次列表失败",
			Detail:  err.Error(),
		})
	}

	// 计算总页数
	totalPage := total / limit
	if total%limit > 0 {
		totalPage++
	}

	// 返回响应
	return c.JSON(http.StatusOK, ListShiftsResponse{
		Items:     shifts,
		Total:     total,
		Page:      page,
		PageSize:  limit,
		TotalPage: totalPage,
	})
}

// GetShift 处理获取班次详情的请求
// @Summary 获取班次详情
// @Description 根据ID获取班次详情
// @Tags 班次
// @Accept json
// @Produce json
// @Param id path string true "班次ID"
// @Success 200 {object} repository.Shift
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse "班次不存在"
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts/{id} [get]
func (h *ShiftHandler) GetShift(c echo.Context) error {
	ctx := context.Background()

	// 解析ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_ID",
			Message: "ID格式无效",
			Detail:  err.Error(),
		})
	}

	// 调用服务层方法
	shift, err := h.shiftService.GetShift(ctx, repository.IDFromInt64(id))
	if err != nil {
		// 检查是否为"班次不存在"错误
		if isShiftNotFoundError(err) {
			return c.JSON(http.StatusNotFound, models.ErrorResponse{
				Code:    "SHIFT_NOT_FOUND",
				Message: "班次不存在",
				Detail:  err.Error(),
			})
		}

		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取班次失败",
			Detail:  err.Error(),
		})
	}

	return c.JSON(http.StatusOK, shift)
}

// GetShiftByNumber 处理根据编号获取班次的请求
// @Summary 根据编号获取班次
// @Description 根据班次编号获取班次详情
// @Tags 班次
// @Accept json
// @Produce json
// @Param number path string true "班次编号"
// @Success 200 {object} repository.Shift
// @Failure 404 {object} models.ErrorResponse "班次不存在"
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts/number/{number} [get]
func (h *ShiftHandler) GetShiftByNumber(c echo.Context) error {
	ctx := context.Background()

	// 获取班次编号
	shiftNumber := c.Param("number")

	// 调用服务层方法
	shift, err := h.shiftService.GetShiftByNumber(ctx, shiftNumber)
	if err != nil {
		// 检查是否为"班次不存在"错误
		if isShiftNotFoundError(err) {
			return c.JSON(http.StatusNotFound, models.ErrorResponse{
				Code:    "SHIFT_NOT_FOUND",
				Message: "班次不存在",
				Detail:  err.Error(),
			})
		}

		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取班次失败",
			Detail:  err.Error(),
		})
	}

	return c.JSON(http.StatusOK, shift)
}

// SoftDeleteShift 处理软删除班次的请求
// @Summary 软删除班次
// @Description 软删除指定班次
// @Tags 班次
// @Accept json
// @Produce json
// @Param id path string true "班次ID"
// @Success 204 {object} nil
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse "班次不存在"
// @Failure 409 {object} models.ErrorResponse "无法删除活跃班次"
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts/{id} [delete]
func (h *ShiftHandler) SoftDeleteShift(c echo.Context) error {
	ctx := context.Background()

	// 解析ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_ID",
			Message: "ID格式无效",
			Detail:  err.Error(),
		})
	}

	// 调用服务层方法
	err = h.shiftService.SoftDeleteShift(ctx, repository.IDFromInt64(id))
	if err != nil {
		// 检查是否为"班次不存在"错误
		if isShiftNotFoundError(err) {
			return c.JSON(http.StatusNotFound, models.ErrorResponse{
				Code:    "SHIFT_NOT_FOUND",
				Message: "班次不存在",
				Detail:  err.Error(),
			})
		}

		// 检查是否为"无法删除活跃班次"错误
		if isCannotDeleteActiveShiftError(err) {
			return c.JSON(http.StatusConflict, models.ErrorResponse{
				Code:    "CANNOT_DELETE_ACTIVE_SHIFT",
				Message: "无法删除活跃班次",
				Detail:  err.Error(),
			})
		}

		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "删除班次失败",
			Detail:  err.Error(),
		})
	}

	return c.NoContent(http.StatusNoContent)
}

// RestoreShift 处理恢复已删除班次的请求
// @Summary 恢复已删除班次
// @Description 恢复已被软删除的班次
// @Tags 班次
// @Accept json
// @Produce json
// @Param id path string true "班次ID"
// @Success 204 {object} nil
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse "班次不存在或未被删除"
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts/{id}/restore [post]
func (h *ShiftHandler) RestoreShift(c echo.Context) error {
	ctx := context.Background()

	// 解析ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_ID",
			Message: "ID格式无效",
			Detail:  err.Error(),
		})
	}

	// 调用服务层方法
	err = h.shiftService.RestoreShift(ctx, repository.IDFromInt64(id))
	if err != nil {
		// 检查是否为"班次不存在"错误
		if isShiftNotFoundError(err) {
			return c.JSON(http.StatusNotFound, models.ErrorResponse{
				Code:    "SHIFT_NOT_FOUND",
				Message: "班次不存在或未被删除",
				Detail:  err.Error(),
			})
		}

		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "恢复班次失败",
			Detail:  err.Error(),
		})
	}

	return c.NoContent(http.StatusNoContent)
}

// GetShiftReport 处理获取班次报表的请求
// @Summary 获取班次报表
// @Description 根据ID获取班次报表数据，支持JSON格式和小票格式。默认返回JSON格式的结构化数据，当format=receipt时返回小票格式数据。响应结构：当format=json时data字段包含ShiftReportDTO结构，当format=receipt时data字段包含ShiftReceiptDTO结构
// @Tags 班次
// @Accept json
// @Produce json
// @Param id path string true "班次ID"
// @Param format query string false "响应格式 (json|receipt)" default(json) Enums(json, receipt)
// @Param currency query string false "货币类型 (IDR|USD)" default(IDR) Enums(IDR, USD)
// @Param timezone query string false "时区" default(Asia/Jakarta)
// @Param include_details query bool false "是否包含详细数据" default(true)
// @Success 200 {object} repository.ShiftReportJSONResponse "成功响应，data字段根据format参数包含不同结构的数据"
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse "班次不存在"
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts/report/{id} [get]
func (h *ShiftHandler) GetShiftReport(c echo.Context) error {
	ctx := context.Background()

	// 解析班次ID
	idStr := c.Param("id")
	shiftID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_ID",
			Message: "班次ID格式无效",
			Detail:  err.Error(),
		})
	}

	// 解析查询参数
	format := c.QueryParam("format")
	if format == "" {
		format = "json"
	}

	currency := c.QueryParam("currency")
	if currency == "" {
		currency = "IDR"
	}

	timezone := c.QueryParam("timezone")
	if timezone == "" {
		timezone = "Asia/Jakarta"
	}

	includeDetails := true
	if includeDetailsStr := c.QueryParam("include_details"); includeDetailsStr != "" {
		includeDetails, _ = strconv.ParseBool(includeDetailsStr)
	}

	// 构建查询选项
	options := repository.ShiftReportOptions{
		Format:         format,
		Currency:       currency,
		Timezone:       timezone,
		IncludeDetails: includeDetails,
	}

	// 调用服务层方法获取报表数据
	reportData, err := h.shiftService.GetShiftReport(ctx, int64ToUUID(shiftID), options)
	if err != nil {
		// 检查是否为"班次不存在"错误
		if isShiftNotFoundError(err) {
			return c.JSON(http.StatusNotFound, models.ErrorResponse{
				Code:    "SHIFT_NOT_FOUND",
				Message: "班次不存在",
				Detail:  err.Error(),
			})
		}

		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取班次报表失败",
			Detail:  err.Error(),
		})
	}

	// 根据格式返回不同的响应
	if format == "receipt" {
		// 获取小票格式数据
		receiptData, err := h.shiftService.GetShiftReceipt(ctx, int64ToUUID(shiftID), options)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Code:    "INTERNAL_ERROR",
				Message: "生成班次小票失败",
				Detail:  err.Error(),
			})
		}

		return c.JSON(http.StatusOK, repository.ShiftReportResponse{
			Success: true,
			Message: "班次小票数据获取成功",
			Data:    receiptData,
			Meta: repository.ShiftReportMetaDTO{
				GeneratedAt:      utils.GetNowInJakarta(),
				ProcessingTimeMs: 0,          // TODO: 计算实际处理时间
				DataSource:       "realtime", // TODO: 根据实际数据源设置
				Version:          "1.0",
			},
		})
	}

	// 默认返回JSON格式
	return c.JSON(http.StatusOK, repository.ShiftReportResponse{
		Success: true,
		Message: "班次报表数据获取成功",
		Data:    reportData,
		Meta: repository.ShiftReportMetaDTO{
			GeneratedAt:      utils.GetNowInJakarta(),
			ProcessingTimeMs: 0,          // TODO: 计算实际处理时间
			DataSource:       "realtime", // TODO: 根据实际数据源设置
			Version:          "1.0",
		},
	})
}

// GetShiftAttendants 处理获取班次员工详细信息的请求
// @Summary 获取班次员工详细信息
// @Description 根据班次ID获取该班次中所有员工的加油情况，包括油品销售、支付方式等详细数据。支持按员工姓名、油品等级、支付方式筛选
// @Tags 班次
// @Accept json
// @Produce json
// @Param id path string true "班次ID"
// @Param attendant_name query string false "员工姓名筛选，支持模糊匹配"
// @Param fuel_grade query string false "油品等级筛选 (92, 95, 98, diesel)"
// @Param payment_method query string false "支付方式筛选 (cash, card, pvs, cimb, bca, mandiri, voucher)"
// @Success 200 {object} repository.ShiftAttendantResponse "成功响应"
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse "班次不存在"
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts/{id}/attendants [get]
func (h *ShiftHandler) GetShiftAttendants(c echo.Context) error {
	ctx := context.Background()
	startTime := time.Now()

	// 解析班次ID - 支持UUID格式
	idStr := c.Param("id")
	var shiftID repository.ID

	// 首先尝试解析为UUID
	if parsedUUID, err := uuid.Parse(idStr); err == nil {
		shiftID = repository.ID(parsedUUID)
	} else {
		// 如果UUID解析失败，尝试解析为int64（向后兼容）
		if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
			shiftID = repository.IDFromInt64(id)
		} else {
			return c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Code:    "INVALID_ID",
				Message: "班次ID格式无效，应为UUID或数字格式",
				Detail:  fmt.Sprintf("无法解析ID: %s", idStr),
			})
		}
	}

	// 解析查询参数
	var attendantName, fuelGrade, paymentMethod *string

	if name := c.QueryParam("attendant_name"); name != "" {
		attendantName = &name
	}

	if grade := c.QueryParam("fuel_grade"); grade != "" {
		fuelGrade = &grade
	}

	if method := c.QueryParam("payment_method"); method != "" {
		paymentMethod = &method
	}

	// 构建请求参数
	request := repository.ShiftAttendantRequest{
		ShiftID:       shiftID,
		AttendantName: attendantName,
		FuelGrade:     fuelGrade,
		PaymentMethod: paymentMethod,
	}

	// 调用服务层方法
	data, err := h.shiftAttendantService.GetShiftAttendants(ctx, request)
	if err != nil {
		// 检查是否为"班次不存在"错误
		if isShiftNotFoundError(err) {
			return c.JSON(http.StatusNotFound, models.ErrorResponse{
				Code:    "SHIFT_NOT_FOUND",
				Message: "班次不存在",
				Detail:  err.Error(),
			})
		}

		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取班次员工信息失败",
			Detail:  err.Error(),
		})
	}

	// 计算处理时间
	processingTime := time.Since(startTime).Milliseconds()

	// 转换响应中的时间字段为雅加达时间
	h.convertShiftAttendantTimesToJakarta(&data)

	// 构建响应
	response := repository.ShiftAttendantResponse{
		Success: true,
		Message: "班次员工加油情况获取成功",
		Data:    data,
		Meta: repository.ShiftAttendantMeta{
			GeneratedAt:      utils.GetNowInJakarta(),
			ProcessingTimeMs: processingTime,
			DataSource:       "realtime", // TODO: 根据实际数据源设置
			Version:          "1.0",
		},
	}

	return c.JSON(http.StatusOK, response)
}

// GetShiftAttendantsDispenser 处理获取按加油机分类的班次员工详细信息的请求
// @Summary 获取按加油机分类的班次员工详细信息
// @Description 根据班次ID获取该班次中按加油机分类的所有员工的加油情况，包括油品销售、支付方式等详细数据。支持按员工姓名、油品等级、支付方式、加油机ID筛选
// @Tags 班次
// @Accept json
// @Produce json
// @Param id path string true "班次ID"
// @Param attendant_name query string false "员工姓名筛选，支持模糊匹配"
// @Param fuel_grade query string false "油品等级筛选 (92, 95, 98, diesel)"
// @Param payment_method query string false "支付方式筛选 (cash, card, pvs, cimb, bca, mandiri, voucher)"
// @Param dispenser_id query integer false "加油机ID筛选"
// @Success 200 {object} repository.ShiftAttendantDispenserResponse "成功响应"
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse "班次不存在"
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts/{id}/attendants-dispenser [get]
func (h *ShiftHandler) GetShiftAttendantsDispenser(c echo.Context) error {
	ctx := context.Background()
	startTime := time.Now()

	// 解析班次ID - 支持UUID格式
	idStr := c.Param("id")
	var shiftID repository.ID

	// 首先尝试解析为UUID
	if parsedUUID, err := uuid.Parse(idStr); err == nil {
		shiftID = repository.ID(parsedUUID)
	} else {
		// 如果UUID解析失败，尝试解析为int64（向后兼容）
		if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
			shiftID = repository.IDFromInt64(id)
		} else {
			return c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Code:    "INVALID_ID",
				Message: "班次ID格式无效，应为UUID或数字格式",
				Detail:  fmt.Sprintf("无法解析ID: %s", idStr),
			})
		}
	}

	// 解析查询参数
	var attendantName, fuelGrade, paymentMethod *string
	var dispenserID *int64

	if name := c.QueryParam("attendant_name"); name != "" {
		attendantName = &name
	}

	if grade := c.QueryParam("fuel_grade"); grade != "" {
		fuelGrade = &grade
	}

	if method := c.QueryParam("payment_method"); method != "" {
		paymentMethod = &method
	}

	if dispenserIDStr := c.QueryParam("dispenser_id"); dispenserIDStr != "" {
		if id, err := strconv.ParseInt(dispenserIDStr, 10, 64); err == nil {
			dispenserID = &id
		} else {
			return c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Code:    "INVALID_DISPENSER_ID",
				Message: "加油机ID格式无效",
				Detail:  fmt.Sprintf("无法解析加油机ID: %s", dispenserIDStr),
			})
		}
	}

	// 构建请求参数
	request := repository.ShiftAttendantDispenserRequest{
		ShiftID:       shiftID,
		AttendantName: attendantName,
		FuelGrade:     fuelGrade,
		PaymentMethod: paymentMethod,
		DispenserID:   dispenserID,
	}

	// 调用服务层方法
	data, err := h.shiftAttendantService.GetShiftAttendantsDispenser(ctx, request)
	if err != nil {
		// 检查是否为"班次不存在"错误
		if isShiftNotFoundError(err) {
			return c.JSON(http.StatusNotFound, models.ErrorResponse{
				Code:    "SHIFT_NOT_FOUND",
				Message: "班次不存在",
				Detail:  err.Error(),
			})
		}

		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取按加油机分类的班次员工信息失败",
			Detail:  err.Error(),
		})
	}

	// 计算处理时间
	processingTime := time.Since(startTime).Milliseconds()

	// 转换响应中的时间字段为雅加达时间
	h.convertShiftAttendantDispenserTimesToJakarta(&data)

	// 构建响应
	response := repository.ShiftAttendantDispenserResponse{
		Success: true,
		Message: "按加油机分类的班次员工加油情况获取成功",
		Data:    data,
		Meta: repository.ShiftAttendantMeta{
			GeneratedAt:      utils.GetNowInJakarta(),
			ProcessingTimeMs: processingTime,
			DataSource:       "realtime", // TODO: 根据实际数据源设置
			Version:          "1.0",
		},
	}

	return c.JSON(http.StatusOK, response)
}

// convertShiftAttendantDispenserTimesToJakarta 将按加油机分类的班次员工数据中的时间字段转换为雅加达时间
func (h *ShiftHandler) convertShiftAttendantDispenserTimesToJakarta(data *repository.ShiftAttendantDispenserData) {
	// 转换班次信息中的时间字段
	data.ShiftInfo.StartTime = utils.ConvertToJakartaTime(data.ShiftInfo.StartTime)
	if data.ShiftInfo.EndTime != nil {
		jakartaEndTime := utils.ConvertToJakartaTime(*data.ShiftInfo.EndTime)
		data.ShiftInfo.EndTime = &jakartaEndTime
	}
}

// convertShiftAttendantTimesToJakarta 将班次员工数据中的时间字段转换为雅加达时间
func (h *ShiftHandler) convertShiftAttendantTimesToJakarta(data *repository.ShiftAttendantData) {
	// 转换班次信息中的时间字段
	data.ShiftInfo.StartTime = utils.ConvertToJakartaTime(data.ShiftInfo.StartTime)
	if data.ShiftInfo.EndTime != nil {
		jakartaEndTime := utils.ConvertToJakartaTime(*data.ShiftInfo.EndTime)
		data.ShiftInfo.EndTime = &jakartaEndTime
	}
}

// GenerateShiftSummary 处理生成班次汇总的请求
// @Summary 生成班次汇总
// @Description 为指定班次生成汇总数据，包括油品明细、支付方式明细等
// @Tags 班次
// @Accept json
// @Produce json
// @Param id path string true "班次ID"
// @Success 200 {object} map[string]interface{} "生成成功"
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse "班次不存在"
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts/{id}/generate-summary [post]
func (h *ShiftHandler) GenerateShiftSummary(c echo.Context) error {
	ctx := context.Background()

	// 解析班次ID
	shiftIDStr := c.Param("id")
	shiftID, err := strconv.ParseInt(shiftIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_SHIFT_ID",
			Message: "班次ID格式无效",
			Detail:  err.Error(),
		})
	}

	// 调用服务生成汇总
	summary, err := h.shiftSummaryService.GenerateShiftSummary(ctx, int64ToUUID(shiftID))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "生成班次汇总失败",
			Detail:  err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "班次汇总生成成功",
		"data":    summary,
	})
}

// EndShiftValidation 处理班结预检查的请求
// @Summary 班结预检查
// @Description 检查指定站点的班次是否可以结束，返回班次信息和总量器连续性检查结果
// @Tags 班次
// @Accept json
// @Produce json
// @Param station_id path int true "站点ID"
// @Success 200 {object} repository.EndShiftValidationResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse "没有活跃班次"
// @Failure 500 {object} models.ErrorResponse
// @Router /shifts/{station_id}/end-shift-validation [get]
func (h *ShiftHandler) EndShiftValidation(c echo.Context) error {
	ctx := context.Background()

	// 解析站点ID
	stationIDStr := c.Param("station_id")
	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    "INVALID_STATION_ID",
			Message: "站点ID格式无效",
			Detail:  err.Error(),
		})
	}

	// 调用服务层方法
	validationResult, err := h.shiftService.ValidateEndShift(ctx, stationID)
	if err != nil {
		// 检查是否为"没有活跃班次"错误
		if isNoActiveShiftError(err) {
			return c.JSON(http.StatusNotFound, models.ErrorResponse{
				Code:    "NO_ACTIVE_SHIFT",
				Message: "该站点没有活跃班次",
				Detail:  err.Error(),
			})
		}

		return c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "班结预检查失败",
			Detail:  err.Error(),
		})
	}

	return c.JSON(http.StatusOK, validationResult)
}
