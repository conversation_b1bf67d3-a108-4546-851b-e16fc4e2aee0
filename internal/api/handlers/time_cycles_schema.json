{"$schema": "http://json-schema.org/draft-07/schema#", "title": "时间周期Schema", "description": "时间周期的JSON Schema验证，支持复杂的时间控制", "type": "object", "properties": {"id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "时间周期唯一标识"}, "promotion_id": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "description": "关联的促销活动ID"}, "type": {"type": "string", "enum": ["DAILY", "WEEKLY", "MONTHLY", "SPECIFIC_DATE", "CUSTOM"], "description": "周期类型"}, "start_time": {"type": "string", "format": "date-time", "description": "周期整体开始时间"}, "end_time": {"type": "string", "format": "date-time", "description": "周期整体结束时间"}, "start_hour": {"type": "integer", "minimum": 0, "maximum": 23, "description": "每日开始小时 (0-23)"}, "end_hour": {"type": "integer", "minimum": 1, "maximum": 24, "description": "每日结束小时 (1-24)"}, "week_days": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 7}, "uniqueItems": true, "description": "适用的星期几 (1=周一, 7=周日)"}, "month_days": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 31}, "uniqueItems": true, "description": "适用的月份中的日期"}, "months": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 12}, "uniqueItems": true, "description": "适用的月份 (1-12)"}, "priority": {"type": "integer", "minimum": 0, "description": "时间周期优先级"}, "timezone": {"type": "string", "description": "时区，如 'Asia/Jakarta', 'UTC' 等"}, "exclude_dates": {"type": "array", "items": {"type": "string", "format": "date"}, "description": "排除的具体日期列表 (YYYY-MM-DD格式)"}, "include_dates": {"type": "array", "items": {"type": "string", "format": "date"}, "description": "特定包含的日期列表 (YYYY-MM-DD格式)"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}, "required": ["id", "promotion_id", "type", "start_time", "end_time"], "additionalProperties": false, "definitions": {"DailyTimeCycleDTO": {"type": "object", "title": "每日时间周期", "description": "每日重复的时间周期配置", "allOf": [{"$ref": "#"}, {"properties": {"type": {"const": "DAILY"}, "start_hour": {"type": "integer", "minimum": 0, "maximum": 23}, "end_hour": {"type": "integer", "minimum": 1, "maximum": 24}}, "required": ["start_hour", "end_hour"]}]}, "WeeklyTimeCycleDTO": {"type": "object", "title": "每周时间周期", "description": "每周重复的时间周期配置", "allOf": [{"$ref": "#"}, {"properties": {"type": {"const": "WEEKLY"}, "week_days": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 7}, "minItems": 1, "uniqueItems": true}}, "required": ["week_days"]}]}, "MonthlyTimeCycleDTO": {"type": "object", "title": "每月时间周期", "description": "每月重复的时间周期配置", "allOf": [{"$ref": "#"}, {"properties": {"type": {"const": "MONTHLY"}, "month_days": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 31}, "minItems": 1, "uniqueItems": true}}, "required": ["month_days"]}]}, "SpecificDateTimeCycleDTO": {"type": "object", "title": "特定日期时间周期", "description": "指定具体日期的时间周期配置", "allOf": [{"$ref": "#"}, {"properties": {"type": {"const": "SPECIFIC_DATE"}, "include_dates": {"type": "array", "items": {"type": "string", "format": "date"}, "minItems": 1}}, "required": ["include_dates"]}]}, "TimeWindowDTO": {"type": "object", "title": "时间窗口", "description": "用于规则评估的时间窗口", "properties": {"start_time": {"type": "string", "format": "date-time", "description": "窗口开始时间"}, "end_time": {"type": "string", "format": "date-time", "description": "窗口结束时间"}, "duration_minutes": {"type": "integer", "minimum": 1, "description": "持续时间（分钟）"}, "repeat_interval": {"type": "string", "enum": ["NONE", "HOURLY", "DAILY", "WEEKLY", "MONTHLY"], "description": "重复间隔"}, "timezone": {"type": "string", "description": "时区设置"}}, "required": ["start_time", "end_time"], "additionalProperties": false}}}