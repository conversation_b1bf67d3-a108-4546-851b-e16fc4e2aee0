package handlers

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/websocket"
)

// WebSocketHandler WebSocket API处理器
type WebSocketHandler struct {
	wsService *websocket.Service
	wsRouter  *websocket.WebSocketRouter
}

// NewWebSocketHandler 创建WebSocket处理器
func NewWebSocketHandler(wsService *websocket.Service) *WebSocketHandler {
	return &WebSocketHandler{
		wsService: wsService,
		wsRouter:  websocket.NewWebSocketRouter(wsService),
	}
}

// HandleWebSocketConnection 处理WebSocket连接
// @Summary WebSocket连接端点
// @Description 建立WebSocket连接，支持实时数据推送
// @Tags WebSocket
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Success 101 {string} string "Switching Protocols"
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /ws [get]
func (wsh *WebSocketHandler) HandleWebSocketConnection(c echo.Context) error {
	return wsh.wsRouter.GetWebSocketHandler().HandleWebSocket(c)
}

// GetWebSocketStats 获取WebSocket统计信息
// @Summary 获取WebSocket统计信息
// @Description 获取WebSocket连接数、消息统计等信息
// @Tags WebSocket
// @Accept json
// @Produce json
// @Success 200 {object} websocket.ServiceStats
// @Failure 500 {object} models.ErrorResponse
// @Router /ws/stats [get]
func (wsh *WebSocketHandler) GetWebSocketStats(c echo.Context) error {
	stats := wsh.wsRouter.GetWebSocketStats()
	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    stats,
	})
}

// GetConnectedClients 获取连接的客户端列表
// @Summary 获取连接的客户端列表
// @Description 获取当前连接的WebSocket客户端信息
// @Tags WebSocket
// @Accept json
// @Produce json
// @Param station_id query int false "站点ID过滤"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} models.ErrorResponse
// @Router /ws/clients [get]
func (wsh *WebSocketHandler) GetConnectedClients(c echo.Context) error {
	hub := wsh.wsService.GetHub()
	
	// 获取所有客户端
	allClients := hub.GetAllClients()
	
	// 过滤条件
	stationIDStr := c.QueryParam("station_id")
	var stationIDFilter *int64
	if stationIDStr != "" {
		if stationID, err := strconv.ParseInt(stationIDStr, 10, 64); err == nil {
			stationIDFilter = &stationID
		}
	}
	
	// 构造响应数据
	clients := make([]map[string]interface{}, 0)
	for _, client := range allClients {
		// 应用过滤器
		if stationIDFilter != nil && client.StationID != *stationIDFilter {
			continue
		}
		
		clientInfo := map[string]interface{}{
			"id":            client.ID,
			"user_id":       client.UserID,
			"station_id":    client.StationID,
			"created_at":    client.CreatedAt,
			"last_ping":     client.LastPing,
			"is_active":     client.IsActive,
			"subscriptions": client.Subscriptions,
		}
		
		if client.Filters != nil {
			clientInfo["filters"] = client.Filters
		}
		
		clients = append(clients, clientInfo)
	}
	
	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"clients": clients,
			"total":   len(clients),
		},
	})
}

// BroadcastMessage 广播消息
// @Summary 广播消息到所有客户端
// @Description 向所有连接的WebSocket客户端广播消息
// @Tags WebSocket
// @Accept json
// @Produce json
// @Param message body BroadcastMessageRequest true "广播消息"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /ws/broadcast [post]
func (wsh *WebSocketHandler) BroadcastMessage(c echo.Context) error {
	var req BroadcastMessageRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid request format",
		})
	}
	
	// 构造消息
	message := &websocket.Message{
		Type:    websocket.MessageType(req.Type),
		Payload: req.Payload,
		Source:  "admin",
	}
	
	// 广播消息
	if err := wsh.wsService.BroadcastMessage(message); err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
	}
	
	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "Message broadcasted successfully",
	})
}

// BroadcastToStation 向指定站点广播消息
// @Summary 向指定站点广播消息
// @Description 向指定站点的所有WebSocket客户端广播消息
// @Tags WebSocket
// @Accept json
// @Produce json
// @Param station_id path int true "站点ID"
// @Param message body BroadcastMessageRequest true "广播消息"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /ws/stations/{station_id}/broadcast [post]
func (wsh *WebSocketHandler) BroadcastToStation(c echo.Context) error {
	// 解析站点ID
	stationIDStr := c.Param("station_id")
	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid station ID",
		})
	}
	
	var req BroadcastMessageRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid request format",
		})
	}
	
	// 构造消息
	message := &websocket.Message{
		Type:    websocket.MessageType(req.Type),
		Payload: req.Payload,
		Source:  "admin",
	}
	
	// 向站点广播消息
	if err := wsh.wsService.BroadcastToStation(stationID, message); err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
	}
	
	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "Message broadcasted to station successfully",
	})
}

// DisconnectClient 断开客户端连接
// @Summary 断开指定客户端连接
// @Description 强制断开指定的WebSocket客户端连接
// @Tags WebSocket
// @Accept json
// @Produce json
// @Param client_id path string true "客户端ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /ws/clients/{client_id}/disconnect [post]
func (wsh *WebSocketHandler) DisconnectClient(c echo.Context) error {
	clientID := c.Param("client_id")
	if clientID == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Client ID is required",
		})
	}
	
	hub := wsh.wsService.GetHub()
	
	// 检查客户端是否存在
	if _, exists := hub.GetClient(clientID); !exists {
		return c.JSON(http.StatusNotFound, map[string]interface{}{
			"success": false,
			"error":   "Client not found",
		})
	}
	
	// 断开客户端连接
	if err := hub.RemoveClient(clientID); err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
	}
	
	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "Client disconnected successfully",
	})
}

// GetEventSources 获取事件源列表
// @Summary 获取事件源列表
// @Description 获取当前配置的所有事件源信息
// @Tags WebSocket
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} models.ErrorResponse
// @Router /ws/event-sources [get]
func (wsh *WebSocketHandler) GetEventSources(c echo.Context) error {
	eventManager := wsh.wsService.GetEventManager()
	sources := eventManager.GetEventSources()
	
	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"event_sources": sources,
			"total":         len(sources),
		},
	})
}

// BroadcastMessageRequest 广播消息请求
type BroadcastMessageRequest struct {
	Type    string      `json:"type" validate:"required"`
	Payload interface{} `json:"payload"`
}

// TestWebSocketConnection 测试WebSocket连接
// @Summary 测试WebSocket连接
// @Description 测试WebSocket服务是否正常运行
// @Tags WebSocket
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} models.ErrorResponse
// @Router /ws/test [get]
func (wsh *WebSocketHandler) TestWebSocketConnection(c echo.Context) error {
	stats := wsh.wsService.GetStats()
	
	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "WebSocket service is running",
		"data": map[string]interface{}{
			"is_running":         stats.IsRunning,
			"active_connections": stats.HubStats.ActiveConnections,
			"total_connections":  stats.HubStats.TotalConnections,
			"event_sources":      stats.EventSources,
		},
	})
}
