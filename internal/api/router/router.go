package router

import (
	"gitlab4.weicheche.cn/indo-bp/bos/internal/api/handlers"

	"github.com/labstack/echo/v4"
)

// Router 路由管理
type Router struct {
	fuelTransactionHandler   *handlers.FuelTransactionHandler
	orderHandler             *handlers.OrderHandler
	reportHandler            *handlers.ReportHandler
	enhancedReportHandler    *handlers.EnhancedReportHandler
	endOfDayReportHandler    *handlers.EndOfDayReportHandler
	employeeHandler          *handlers.EmployeeHandler
	shiftHandler             *handlers.ShiftHandler
	promotionManagerHandler *handlers.PromotionManagerHandler
	promotionCalcHandler     *handlers.PromotionCalcHandler
	schemaHandler            *handlers.SchemaHandler
	oilHandler               *handlers.OilHandler
	oilPriceHandler          *handlers.OilPriceHandler
	pts2DeviceHandler        *handlers.PTS2DeviceHandler
	pts2PumpHandler          *handlers.PTS2PumpHandler
	customerHandler          *handlers.CustomerHandler
	inventoryHandler         *handlers.InventoryHandler
	paymentHandler           *handlers.PaymentHandler
	staffCardHandler         *handlers.StaffCardHandler
	dashboardHandler         *handlers.DashboardHandler
}

// NewRouter 创建新的路由管理
func NewRouter(
	fuelTransactionHandler *handlers.FuelTransactionHandler,
	orderHandler *handlers.OrderHandler,
	reportHandler *handlers.ReportHandler,
	enhancedReportHandler *handlers.EnhancedReportHandler,
	endOfDayReportHandler *handlers.EndOfDayReportHandler,
	employeeHandler *handlers.EmployeeHandler,
	shiftHandler *handlers.ShiftHandler,
	promotionManagerHandler *handlers.PromotionManagerHandler,
	promotionCalcHandler *handlers.PromotionCalcHandler,
	schemaHandler *handlers.SchemaHandler,
	oilHandler *handlers.OilHandler,
	oilPriceHandler *handlers.OilPriceHandler,
	pts2DeviceHandler *handlers.PTS2DeviceHandler,
	pts2PumpHandler *handlers.PTS2PumpHandler,
	customerHandler *handlers.CustomerHandler,
	inventoryHandler *handlers.InventoryHandler,
	paymentHandler *handlers.PaymentHandler,
	staffCardHandler *handlers.StaffCardHandler,
	dashboardHandler *handlers.DashboardHandler,
) *Router {
	return &Router{
		fuelTransactionHandler:   fuelTransactionHandler,
		orderHandler:             orderHandler,
		reportHandler:            reportHandler,
		enhancedReportHandler:    enhancedReportHandler,
		endOfDayReportHandler:    endOfDayReportHandler,
		employeeHandler:          employeeHandler,
		shiftHandler:             shiftHandler,
		promotionManagerHandler: promotionManagerHandler,
		promotionCalcHandler:     promotionCalcHandler,
		schemaHandler:            schemaHandler,
		oilHandler:               oilHandler,
		oilPriceHandler:          oilPriceHandler,
		pts2DeviceHandler:        pts2DeviceHandler,
		pts2PumpHandler:          pts2PumpHandler,
		customerHandler:          customerHandler,
		inventoryHandler:         inventoryHandler,
		paymentHandler:           paymentHandler,
		staffCardHandler:         staffCardHandler,
		dashboardHandler:         dashboardHandler,
	}
}

// RegisterRoutes 注册API路由
func (r *Router) RegisterRoutes(e *echo.Echo) {
	// API组
	api := e.Group("/api/v1")

	// 燃油交易相关路由
	fuelTransaction := api.Group("/fuel-transactions")
	fuelTransaction.GET("", r.fuelTransactionHandler.ListFuelTransactions)
	fuelTransaction.POST("", r.fuelTransactionHandler.CreateFuelTransaction)
	fuelTransaction.GET("/:id", r.fuelTransactionHandler.GetFuelTransaction)
	fuelTransaction.POST("/:id/link", r.fuelTransactionHandler.LinkFuelTransactionToOrder)
	fuelTransaction.DELETE("/:id/unlink/:order_id", r.fuelTransactionHandler.UnlinkFuelTransactionFromOrder)
	fuelTransaction.GET("/:id/orders", r.fuelTransactionHandler.GetFuelTransactionOrders)
	fuelTransaction.PUT("/:id/link/:order_id", r.fuelTransactionHandler.UpdateLinkAllocatedAmount)
	fuelTransaction.POST("/:id/confirm/:order_id", r.fuelTransactionHandler.ConfirmFuelTransactionLink)
	fuelTransaction.POST("/:id/cancel/:order_id", r.fuelTransactionHandler.CancelFuelTransactionLink)
	fuelTransaction.POST("/:id/cleanup", r.fuelTransactionHandler.CleanupFuelTransactionLinks)
	fuelTransaction.POST("/:id/process", r.fuelTransactionHandler.ProcessFuelTransactionStatus)
	fuelTransaction.PATCH("/:id/pump-readings", r.fuelTransactionHandler.UpdatePumpReadings)

	// 缓存管理路由
	fuelTransaction.GET("/cache/stats", r.fuelTransactionHandler.GetCacheStats)
	fuelTransaction.POST("/cache/clear", r.fuelTransactionHandler.ClearCache)

	// 完整燃油交易信息路由（使用新的优化接口）
	api.GET("/fuel-transactions-full", r.fuelTransactionHandler.GetFuelTransactionsFullOptimized)

	// 完整燃油交易信息路由（原始版本，保留用于对比测试）
	api.GET("/fuel-transactions-full-original", r.fuelTransactionHandler.ListFuelTransactionsFullOptimized)

	// 性能统计路由
	fuelTransaction.GET("/performance-stats", r.fuelTransactionHandler.GetPerformanceStats)

	// 燃油交易导出路由
	api.GET("/fuel-transactions-full/export", r.fuelTransactionHandler.ExportFuelTransactionsToExcel)

	// 订单关联的燃油交易路由
	order := api.Group("/orders")
	order.GET("", r.orderHandler.ListOrders)
	order.POST("", r.orderHandler.CreateOrder)
	order.GET("/:id", r.orderHandler.GetOrder)
	order.POST("/:id/items", r.orderHandler.AddOrderItem)
	order.DELETE("/:id/items/:item_id", r.orderHandler.RemoveOrderItem)
	order.POST("/:id/promotions", r.orderHandler.ApplyPromotion)
	order.POST("/:id/complete", r.orderHandler.CompleteOrder)
	order.POST("/:id/cancel", r.orderHandler.CancelOrder)
	order.GET("/:id/payment-status", r.orderHandler.GetOrderPaymentStatus)
	order.GET("/:order_id/fuel-transactions", r.fuelTransactionHandler.GetOrderFuelTransactions)

	// 报表相关路由
	report := api.Group("/reports")
	report.GET("/payment-methods", r.reportHandler.GetPaymentMethodSummary)
	report.GET("/revenue", r.reportHandler.GetAggregatedRevenue)
	report.GET("/receivable", r.reportHandler.GetAggregatedReceivable)
	report.GET("/nozzle-sales", r.reportHandler.GetNozzleSalesSummary)
	report.GET("/transactions", r.reportHandler.GetTransactionsForReport)
	report.GET("/sales-by-category", r.reportHandler.GetAggregatedSalesByProductCategory)
	report.GET("/shift-eod", r.reportHandler.GetShiftEODReport)
	report.GET("/nozzle-pump-readings", r.reportHandler.GetNozzlePumpReport) // 新增油枪泵码报表
	report.GET("/end-of-day", r.endOfDayReportHandler.GetEndOfDayReport)
	// 增强报表相关路由
	enhancedReport := api.Group("/enhanced-reports")
	enhancedReport.GET("/total-revenue", r.enhancedReportHandler.GetTotalRevenue)
	enhancedReport.GET("/fuel-receivable", r.enhancedReportHandler.GetFuelReceivableSummary)
	enhancedReport.GET("/sales-by-payment-method", r.enhancedReportHandler.GetSalesDetailByPaymentMethod)
	enhancedReport.GET("/sales-by-product", r.enhancedReportHandler.GetSalesDetailByProduct)
	enhancedReport.GET("/nozzle-sales", r.enhancedReportHandler.GetNozzleDetailedSales)
	enhancedReport.GET("/query-performance", r.enhancedReportHandler.GetQueryPerformance)

	// 员工相关路由
	employee := api.Group("/employee")
	employee.POST("/login", r.employeeHandler.Login)
	employee.POST("", r.employeeHandler.CreateEmployee)
	employee.GET("/:id", r.employeeHandler.GetEmployee)
	employee.PUT("/:id", r.employeeHandler.UpdateEmployee)
	employee.DELETE("/:id", r.employeeHandler.DeleteEmployee)

	// 员工列表路由
	api.GET("/employees", r.employeeHandler.ListEmployees)

	// 班次相关路由
	shift := api.Group("/shifts")
	shift.GET("", r.shiftHandler.ListShifts)
	shift.POST("/start", r.shiftHandler.StartShift)
	shift.POST("/:station_id/end", r.shiftHandler.EndShift)
	shift.GET("/current/:station_id", r.shiftHandler.GetCurrentShift)
	shift.POST("/:station_id/ensure", r.shiftHandler.EnsureShiftStarted)
	shift.GET("/:station_id/end-shift-validation", r.shiftHandler.EndShiftValidation)
	shift.GET("/:id", r.shiftHandler.GetShift)
	shift.GET("/number/:number", r.shiftHandler.GetShiftByNumber)
	shift.GET("/report/:id", r.shiftHandler.GetShiftReport)
	shift.GET("/:id/attendants", r.shiftHandler.GetShiftAttendants)
	shift.GET("/:id/attendants-dispenser", r.shiftHandler.GetShiftAttendantsDispenser)
	shift.POST("/:id/generate-summary", r.shiftHandler.GenerateShiftSummary)
	shift.DELETE("/:id", r.shiftHandler.SoftDeleteShift)
	shift.POST("/:id/restore", r.shiftHandler.RestoreShift)

	// 促销管理相关路由
	promotion := api.Group("/promotions")
	promotion.GET("", r.promotionManagerHandler.ListPromotions)
	promotion.GET("/:id", r.promotionManagerHandler.ViewPromotion)
	promotion.GET("/new", r.promotionManagerHandler.ShowPromotionForm)
	promotion.GET("/edit/:id", r.promotionManagerHandler.ShowPromotionForm)
	promotion.POST("/save", r.promotionManagerHandler.SavePromotion)
	promotion.POST("/save/:id", r.promotionManagerHandler.SavePromotion)
	promotion.DELETE("/:id", r.promotionManagerHandler.DeletePromotion)
	promotion.POST("/:id/status", r.promotionManagerHandler.UpdateStatus)

	// 促销套件创建接口 (统一接口)
	promotion.POST("/packages", r.promotionManagerHandler.SavePromotion)

	// 站点适用性检查路由
	promotion.GET("/:id/applicable/:site_code", r.promotionManagerHandler.CheckPromotionSiteApplicability)
	promotion.POST("/:id/applicable/batch", r.promotionManagerHandler.BatchCheckPromotionSiteApplicability)

	// 促销计算相关路由
	calc := api.Group("/calculator")
	calc.GET("", r.promotionCalcHandler.IndexPromotionCalcHandler)
	calc.GET("/status", r.promotionCalcHandler.GetStatus)
	calc.POST("/process", r.promotionCalcHandler.ProcessOrder)
	calc.POST("/calculate", r.promotionCalcHandler.CalculateHandler)

	// Mock模式控制路由
	mockGroup := calc.Group("/mock")
	mockGroup.POST("/toggle", r.promotionCalcHandler.ToggleMockMode)
	mockGroup.GET("/status", r.promotionCalcHandler.GetMockStatus)
	mockGroup.POST("/ruleset", r.promotionCalcHandler.SetMockRuleSet)
	mockGroup.POST("/quick-switch", r.promotionCalcHandler.QuickSwitchToMock)
	mockGroup.POST("/quick-real", r.promotionCalcHandler.QuickSwitchToReal)
	mockGroup.POST("/debug", r.promotionCalcHandler.DebugCurrentMode)

	// Schema相关路由
	schema := api.Group("/schemas")
	schema.GET("", r.schemaHandler.GetAllSchemas)
	schema.GET("/:schemaName", r.schemaHandler.GetSchema)
	schema.GET("/:schemaName/metadata", r.schemaHandler.GetSchemaMetadata)

	// 油品管理相关路由
	oil := api.Group("/oil")
	// 油品基础信息管理（简化版 - 移除价格相关字段）
	oil.GET("/products", r.oilHandler.ListOilProducts)
	oil.POST("/products", r.oilHandler.CreateOilProduct)
	oil.GET("/products/:id", r.oilHandler.GetOilProduct)
	oil.PUT("/products/:id", r.oilHandler.UpdateOilProduct)
	oil.DELETE("/products/:id", r.oilHandler.DeleteOilProduct) // 新增删除接口（含依赖检查）

	// 站点级价格管理路由
	prices := oil.Group("/prices")
	// 站点价格管理
	prices.GET("/stations/:station_id", r.oilPriceHandler.GetStationPrices)
	prices.POST("/stations/:station_id", r.oilPriceHandler.SetStationPrice)
	prices.POST("/stations/:station_id/batch", r.oilPriceHandler.BatchSetStationPrices) // 事务处理
	prices.GET("/stations/:station_id/products/:product_id", r.oilPriceHandler.GetStationProductPrice) // 获取站点特定产品价格

	// 价格历史查询（分页支持）
	prices.GET("/history", r.oilPriceHandler.GetPriceHistory)
	prices.GET("/hierarchy", r.oilPriceHandler.GetPriceHierarchy)

	// 未生效价格管理
	prices.DELETE("/:id", r.oilPriceHandler.DeleteFuturePrice)  // 删除未生效价格
	prices.PUT("/:id", r.oilPriceHandler.UpdateFuturePrice)     // 更新未生效价格

	// 报表和分析
	reports := oil.Group("/reports")
	reports.GET("/price-analysis", r.oilPriceHandler.GetPriceAnalysis)

	// PTS2设备管理路由
	if r.pts2DeviceHandler != nil && r.pts2PumpHandler != nil {
		pts2 := api.Group("/pts2")

		// 设备相关路由
		devices := pts2.Group("/devices")
		devices.GET("", r.pts2DeviceHandler.GetDeviceList)
		devices.GET("/:deviceId", r.pts2DeviceHandler.GetDeviceDetail)
		devices.GET("/:deviceId/status", r.pts2DeviceHandler.GetDeviceStatus)
		devices.POST("/:deviceId/connect", r.pts2DeviceHandler.ConnectDevice)
		devices.POST("/:deviceId/disconnect", r.pts2DeviceHandler.DisconnectDevice)
		devices.GET("/:deviceId/info", r.pts2DeviceHandler.GetDeviceInfo)

		// 油泵相关路由
		devices.GET("/:deviceId/pumps/:pumpId", r.pts2PumpHandler.GetPumpStatus)
		devices.POST("/:deviceId/pumps/:pumpId/authorize", r.pts2PumpHandler.AuthorizePump)
		devices.POST("/:deviceId/pumps/:pumpId/preset", r.pts2PumpHandler.PresetPump)
		devices.POST("/:deviceId/pumps/:pumpId/prices", r.pts2PumpHandler.SetPumpPrices)
		devices.POST("/:deviceId/pumps/:pumpId/nozzles/:nozzleId/price", r.pts2PumpHandler.SetNozzlePrice)
	}

	// 客户管理相关路由
	if r.customerHandler != nil {
		// 客户基础管理路由
		customer := api.Group("/customers")
		customer.POST("", r.customerHandler.CreateCustomer)
		customer.GET("", r.customerHandler.ListCustomers)
		customer.GET("/count", r.customerHandler.CountCustomers)
		customer.GET("/:id", r.customerHandler.GetCustomer)
		customer.PUT("/:id", r.customerHandler.UpdateCustomer)
		customer.GET("/phone/:phone", r.customerHandler.GetCustomerByPhone)

		// 客户车辆管理路由
		customer.GET("/:customer_id/vehicles", r.customerHandler.ListVehiclesByCustomer)
		customer.GET("/:customer_id/labels", r.customerHandler.GetCustomerLabels)

		// 车辆管理路由
		vehicle := api.Group("/vehicles")
		vehicle.POST("", r.customerHandler.CreateVehicle)
		vehicle.GET("/:id", r.customerHandler.GetVehicle)
		vehicle.PUT("/:id", r.customerHandler.UpdateVehicle)
		vehicle.GET("/number/:number", r.customerHandler.GetVehicleByNumber)

		// 标签管理路由
		label := api.Group("/labels")
		label.POST("", r.customerHandler.CreateLabel)
		label.GET("", r.customerHandler.ListLabels)

		// 客户标签关联路由
		customerLabel := api.Group("/customer-labels")
		customerLabel.POST("", r.customerHandler.AssignLabelToCustomer)
	}

	// 库存管理相关路由
	if r.inventoryHandler != nil {
		inventory := api.Group("/inventory")

		// 商品管理路由
		products := inventory.Group("/products")
		products.POST("", r.inventoryHandler.CreateProduct)
		products.GET("/:id", r.inventoryHandler.GetProduct)
		products.GET("", r.inventoryHandler.ListProducts)
		products.DELETE("/:id", r.inventoryHandler.DeleteProduct)

		// 库存查询路由
		stocks := inventory.Group("/stocks")
		stocks.GET("", r.inventoryHandler.ListInventory)
		stocks.GET("/:id", r.inventoryHandler.GetInventory)

		// 库存操作路由
		inventory.POST("/inbound", r.inventoryHandler.InboundInventory)
		inventory.POST("/outbound", r.inventoryHandler.OutboundInventory)

		// 库存预留路由
		reservations := inventory.Group("/reservations")
		reservations.POST("", r.inventoryHandler.CreateReservation)
		reservations.POST("/:id/release", r.inventoryHandler.ReleaseReservation)

		// 盘点管理路由
		stocktakes := inventory.Group("/stocktakes")
		stocktakes.GET("", r.inventoryHandler.ListStocktake)
		stocktakes.GET("/:id", r.inventoryHandler.GetStocktake)
		stocktakes.POST("", r.inventoryHandler.StartStocktake)
		stocktakes.POST("/items/record", r.inventoryHandler.RecordStocktakeItem)
		stocktakes.POST("/:id/complete", r.inventoryHandler.CompleteStocktake)

		// 预警管理路由
		inventory.GET("/warnings", r.inventoryHandler.GetWarnings)

		// 分类管理路由
		category := inventory.Group("/categories")
		category.POST("", r.inventoryHandler.CreateCategory)
		category.GET("", r.inventoryHandler.ListCategories)
		category.GET("/:id", r.inventoryHandler.GetCategory)
	}

	// 支付管理相关路由
	if r.paymentHandler != nil {
		payment := api.Group("/payments")

		// 支付处理路由
		payment.POST("", r.paymentHandler.ProcessPayment)
		payment.GET("/:id", r.paymentHandler.GetPayment)
		payment.GET("/:id/status", r.paymentHandler.GetPaymentStatus)
		payment.POST("/:id/cancel", r.paymentHandler.CancelPayment)
		payment.GET("", r.paymentHandler.ListPayments)

		// 退款处理路由
		payment.POST("/refunds", r.paymentHandler.ProcessRefund)
		payment.GET("/refunds/:id/status", r.paymentHandler.GetRefundStatus)

		// 支付方式管理路由
		paymentMethods := api.Group("/payment-methods")
		paymentMethods.GET("", r.paymentHandler.ListPaymentMethods)
		paymentMethods.POST("", r.paymentHandler.CreatePaymentMethod)
		paymentMethods.PUT("/:id", r.paymentHandler.UpdatePaymentMethod)
		paymentMethods.POST("/:id/enable", r.paymentHandler.EnablePaymentMethod)
		paymentMethods.POST("/:id/disable", r.paymentHandler.DisablePaymentMethod)
	}

	// 员工卡管理相关路由
	if r.staffCardHandler != nil {
		staffCard := api.Group("/staff-cards")

		// 员工卡基础管理路由
		staffCard.POST("", r.staffCardHandler.CreateStaffCard)
		staffCard.GET("", r.staffCardHandler.ListStaffCards)
		staffCard.GET("/:id", r.staffCardHandler.GetStaffCard)
		staffCard.PUT("/:id", r.staffCardHandler.UpdateStaffCard)
		staffCard.DELETE("/:id", r.staffCardHandler.DeleteStaffCard)

		// 根据卡号获取员工卡
		staffCard.GET("/number/:cardNumber", r.staffCardHandler.GetStaffCardByNumber)

		// 员工卡操作路由
		staffCard.POST("/number/:cardNumber/activate", r.staffCardHandler.ActivateCard)
		staffCard.POST("/number/:cardNumber/suspend", r.staffCardHandler.SuspendCard)
		staffCard.POST("/number/:cardNumber/validate", r.staffCardHandler.ValidateCard)
		staffCard.POST("/number/:cardNumber/extend", r.staffCardHandler.ExtendCardValidity)

		// 用户员工卡管理路由
		users := api.Group("/users")
		users.GET("/:userId/staff-cards", r.staffCardHandler.GetUserStaffCards)
		users.POST("/:userId/staff-cards", r.staffCardHandler.CreateStaffCardForUser)

		// 站点员工卡管理路由
		stations := api.Group("/stations")
		stations.GET("/:stationId/staff-cards", r.staffCardHandler.GetActiveCardsByStation)

		// 批量操作路由
		staffCard.POST("/bulk-create", r.staffCardHandler.BulkCreateStaffCards)
	}

	// Dashboard相关路由
	if r.dashboardHandler != nil {
		dashboard := api.Group("/dashboard")

		// Dashboard核心接口
		dashboard.GET("/summary", r.dashboardHandler.GetDashboardSummary)
		dashboard.GET("/sales-trend", r.dashboardHandler.GetSalesTrend)
		dashboard.GET("/fuel-sales-mix", r.dashboardHandler.GetFuelSalesMix)
	}
}
