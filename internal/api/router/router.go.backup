package router

import (
	"bytes"
	"io"
	"io/ioutil"
	"strings"

	"gitlab4.weicheche.cn/indo-bp/bos/internal/api/handlers"

	"github.com/labstack/echo/v4"
)

// 请求体日志中间件
func requestLoggerMiddleware(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		// 如果是会员相关API或返回500错误的请求，记录请求体
		if strings.Contains(c.Request().URL.Path, "/members") {
			reqBody, err := io.ReadAll(c.Request().Body)
			if err != nil {
				c.Logger().Errorf("读取请求体失败: %v", err)
				return next(c)
			}
			// 恢复请求体以便后续处理
			c.Request().Body = ioutil.NopCloser(bytes.NewBuffer(reqBody))

			// 记录请求信息
			c.Logger().Infof("请求路径: %s, 方法: %s, 请求体: %s",
				c.Request().URL.Path, c.Request().Method, string(reqBody))
		}
		return next(c)
	}
}

// Router 路由管理
type Router struct {
	fuelTransactionHandler   *handlers.FuelTransactionHandler
	orderHandler             *handlers.OrderHandler
	reportHandler            *handlers.ReportHandler
	enhancedReportHandler    *handlers.EnhancedReportHandler
	employeeHandler          *handlers.EmployeeHandler
	shiftHandler             *handlers.ShiftHandler
	promotionManangerHandler *handlers.PromotionManangerHandler
	promotionCalcHandler     *handlers.PromotionCalcHandler
	memberHandler            *handlers.MemberHandler
	oilHandler               *handlers.OilHandler
	pts2DeviceHandler        *handlers.PTS2DeviceHandler
	pts2PumpHandler          *handlers.PTS2PumpHandler
}

// NewRouter 创建新的路由管理
func NewRouter(
	fuelTransactionHandler *handlers.FuelTransactionHandler,
	orderHandler *handlers.OrderHandler,
	reportHandler *handlers.ReportHandler,
	enhancedReportHandler *handlers.EnhancedReportHandler,
	employeeHandler *handlers.EmployeeHandler,
	shiftHandler *handlers.ShiftHandler,
	promotionManangerHandler *handlers.PromotionManangerHandler,
	promotionCalcHandler *handlers.PromotionCalcHandler,
	memberHandler *handlers.MemberHandler,
	oilHandler *handlers.OilHandler,
	pts2DeviceHandler *handlers.PTS2DeviceHandler,
	pts2PumpHandler *handlers.PTS2PumpHandler,
) *Router {
	return &Router{
		fuelTransactionHandler:   fuelTransactionHandler,
		orderHandler:             orderHandler,
		reportHandler:            reportHandler,
		enhancedReportHandler:    enhancedReportHandler,
		employeeHandler:          employeeHandler,
		shiftHandler:             shiftHandler,
		promotionManangerHandler: promotionManangerHandler,
		promotionCalcHandler:     promotionCalcHandler,
		memberHandler:            memberHandler,
		oilHandler:               oilHandler,
		pts2DeviceHandler:        pts2DeviceHandler,
		pts2PumpHandler:          pts2PumpHandler,
	}
}

// RegisterRoutes 注册API路由
func (r *Router) RegisterRoutes(e *echo.Echo) {
	// API组
	api := e.Group("/api/v1")

	// 燃油交易相关路由
	fuelTransaction := api.Group("/fuel-transactions")
	fuelTransaction.GET("", r.fuelTransactionHandler.ListFuelTransactions)
	fuelTransaction.POST("", r.fuelTransactionHandler.CreateFuelTransaction)
	fuelTransaction.GET("/:id", r.fuelTransactionHandler.GetFuelTransaction)
	fuelTransaction.POST("/:id/link", r.fuelTransactionHandler.LinkFuelTransactionToOrder)
	fuelTransaction.DELETE("/:id/unlink/:order_id", r.fuelTransactionHandler.UnlinkFuelTransactionFromOrder)
	fuelTransaction.GET("/:id/orders", r.fuelTransactionHandler.GetFuelTransactionOrders)
	fuelTransaction.PUT("/:id/link/:order_id", r.fuelTransactionHandler.UpdateLinkAllocatedAmount)

	// 订单关联的燃油交易路由
	order := api.Group("/orders")
	order.GET("", r.orderHandler.ListOrders)
	order.POST("", r.orderHandler.CreateOrder)
	order.GET("/:id", r.orderHandler.GetOrder)
	order.POST("/:id/items", r.orderHandler.AddOrderItem)
	order.DELETE("/:id/items/:item_id", r.orderHandler.RemoveOrderItem)
	order.POST("/:id/promotions", r.orderHandler.ApplyPromotion)
	order.POST("/:id/complete", r.orderHandler.CompleteOrder)
	order.POST("/:id/cancel", r.orderHandler.CancelOrder)
	order.GET("/:order_id/fuel-transactions", r.fuelTransactionHandler.GetOrderFuelTransactions)

	// 报表相关路由
	report := api.Group("/reports")
	report.GET("/payment-methods", r.reportHandler.GetPaymentMethodSummary)
	report.GET("/revenue", r.reportHandler.GetAggregatedRevenue)
	report.GET("/receivable", r.reportHandler.GetAggregatedReceivable)
	report.GET("/nozzle-sales", r.reportHandler.GetNozzleSalesSummary)
	report.GET("/transactions", r.reportHandler.GetTransactionsForReport)
	report.GET("/sales-by-category", r.reportHandler.GetAggregatedSalesByProductCategory)

	// 增强报表相关路由
	enhancedReport := api.Group("/enhanced-reports")
	enhancedReport.GET("/total-revenue", r.enhancedReportHandler.GetTotalRevenue)
	enhancedReport.GET("/fuel-receivable", r.enhancedReportHandler.GetFuelReceivableSummary)
	enhancedReport.GET("/sales-by-payment-method", r.enhancedReportHandler.GetSalesDetailByPaymentMethod)
	enhancedReport.GET("/sales-by-product", r.enhancedReportHandler.GetSalesDetailByProduct)
	enhancedReport.GET("/nozzle-sales", r.enhancedReportHandler.GetNozzleDetailedSales)
	enhancedReport.GET("/query-performance", r.enhancedReportHandler.GetQueryPerformance)

	// 员工相关路由
	employee := api.Group("/employee")
	employee.POST("/login", r.employeeHandler.Login)
	employee.POST("", r.employeeHandler.CreateEmployee)
	employee.GET("/:id", r.employeeHandler.GetEmployee)
	employee.PUT("/:id", r.employeeHandler.UpdateEmployee)
	employee.DELETE("/:id", r.employeeHandler.DeleteEmployee)

	// 员工列表路由
	api.GET("/employees", r.employeeHandler.ListEmployees)

	// 班次相关路由
	shift := api.Group("/shifts")
	shift.GET("", r.shiftHandler.ListShifts)
	shift.POST("/start", r.shiftHandler.StartShift)
	shift.POST("/:station_id/end", r.shiftHandler.EndShift)
	shift.GET("/current/:station_id", r.shiftHandler.GetCurrentShift)
	shift.POST("/:station_id/ensure", r.shiftHandler.EnsureShiftStarted)
	shift.GET("/:id", r.shiftHandler.GetShift)
	shift.GET("/number/:number", r.shiftHandler.GetShiftByNumber)
	shift.GET("/report/:id", r.shiftHandler.GetShiftReport)
	shift.DELETE("/:id", r.shiftHandler.SoftDeleteShift)
	shift.POST("/:id/restore", r.shiftHandler.RestoreShift)

	// 促销管理相关路由
	promotion := api.Group("/promotions")
	promotion.GET("", r.promotionManangerHandler.ListPromotions)
	promotion.GET("/:id", r.promotionManangerHandler.ViewPromotion)
	promotion.GET("/new", r.promotionManangerHandler.ShowPromotionForm)
	promotion.GET("/edit/:id", r.promotionManangerHandler.ShowPromotionForm)
	promotion.POST("/save", r.promotionManangerHandler.SavePromotion)
	promotion.POST("/save/:id", r.promotionManangerHandler.SavePromotion)
	promotion.DELETE("/:id", r.promotionManangerHandler.DeletePromotion)
	promotion.POST("/:id/status", r.promotionManangerHandler.UpdateStatus)

	// 促销计算相关路由
	calc := api.Group("/calculator")
	calc.GET("", r.promotionCalcHandler.IndexPromotionCalcHandler)
	calc.GET("/status", r.promotionCalcHandler.GetStatus)
	calc.POST("/process", r.promotionCalcHandler.ProcessOrder)
	calc.POST("/calculate", r.promotionCalcHandler.CalculateHandler)

	// 会员相关路由
	member := api.Group("/members")
	// 添加会员请求日志中间件
	member.Use(requestLoggerMiddleware)
	member.POST("", r.memberHandler.CreateMember)
	member.GET("", r.memberHandler.ListMembers)
	member.GET("/:id", r.memberHandler.GetMember)
	member.GET("/phone/:phone", r.memberHandler.GetMemberByPhone)
	member.POST("/verify", r.memberHandler.VerifyMember)
	member.PUT("/:id", r.memberHandler.UpdateMember)
	member.PATCH("/:id/status", r.memberHandler.UpdateMemberStatus)
	member.GET("/statistics", r.memberHandler.GetMemberStatistics)

	// 油品管理相关路由
	oil := api.Group("/oil")
	// 油品基础信息管理
	oil.GET("/products", r.oilHandler.ListOilProducts)
	oil.POST("/products", r.oilHandler.CreateOilProduct)
	oil.GET("/products/:id", r.oilHandler.GetOilProduct)
	oil.PUT("/products/:id", r.oilHandler.UpdateOilProduct)
	oil.POST("/products/:id/price", r.oilHandler.SetBasePrice)
	oil.GET("/sales-price", r.oilHandler.GetSalesPrice)

	// 定价策略管理
	oil.GET("/strategies", r.oilHandler.ListStrategies)
	oil.POST("/strategies", r.oilHandler.CreatePricingStrategy)
	oil.POST("/strategies/:id/activate", r.oilHandler.ActivateStrategy)
	oil.POST("/strategies/:id/deactivate", r.oilHandler.DeactivateStrategy)

	// 价格调整流程管理
	oil.GET("/adjustments", r.oilHandler.ListAdjustmentProcesses)
	oil.POST("/adjustments", r.oilHandler.InitiatePriceAdjustment)
	oil.GET("/adjustments/:id", r.oilHandler.GetAdjustmentProcess)
	oil.POST("/adjustments/:id/approve", r.oilHandler.ApproveAdjustment)
	oil.POST("/adjustments/:id/submit", r.oilHandler.SubmitAdjustmentForApproval)

	// PTS2设备管理路由
	if r.pts2DeviceHandler != nil && r.pts2PumpHandler != nil {
		pts2 := api.Group("/pts2")

		// 设备相关路由
		devices := pts2.Group("/devices")
		devices.GET("", r.pts2DeviceHandler.GetDeviceList)
		devices.GET("/:deviceId", r.pts2DeviceHandler.GetDeviceDetail)
		devices.GET("/:deviceId/status", r.pts2DeviceHandler.GetDeviceStatus)
		devices.POST("/:deviceId/connect", r.pts2DeviceHandler.ConnectDevice)
		devices.POST("/:deviceId/disconnect", r.pts2DeviceHandler.DisconnectDevice)
		devices.GET("/:deviceId/info", r.pts2DeviceHandler.GetDeviceInfo)

		// 油泵相关路由
		devices.GET("/:deviceId/pumps/:pumpId", r.pts2PumpHandler.GetPumpStatus)
		devices.POST("/:deviceId/pumps/:pumpId/authorize", r.pts2PumpHandler.AuthorizePump)
		devices.POST("/:deviceId/pumps/:pumpId/preset", r.pts2PumpHandler.PresetPump)
		devices.POST("/:deviceId/pumps/:pumpId/prices", r.pts2PumpHandler.SetPumpPrices)
		devices.POST("/:deviceId/pumps/:pumpId/nozzles/:nozzleId/price", r.pts2PumpHandler.SetNozzlePrice)
	}
}
