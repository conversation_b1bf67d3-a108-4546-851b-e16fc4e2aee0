package cache

import (
	"context"
	"sync"
	"time"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// FuelTransactionCache 燃油交易缓存接口
type FuelTransactionCache interface {
	// 员工姓名缓存
	GetEmployeeName(ctx context.Context, employeeID repository.ID) (string, bool)
	SetEmployeeName(ctx context.Context, employeeID repository.ID, name string)
	
	// 站点信息缓存
	GetStationName(ctx context.Context, stationID repository.ID) (string, bool)
	SetStationName(ctx context.Context, stationID repository.ID, name string)
	
	// 批量操作
	GetEmployeeNames(ctx context.Context, employeeIDs []repository.ID) map[repository.ID]string
	SetEmployeeNames(ctx context.Context, employeeNames map[repository.ID]string)
	
	GetStationNames(ctx context.Context, stationIDs []repository.ID) map[repository.ID]string
	SetStationNames(ctx context.Context, stationNames map[repository.ID]string)
	
	// 缓存管理
	Clear(ctx context.Context) error
	GetStats(ctx context.Context) CacheStats
}

// CacheStats 缓存统计信息
type CacheStats struct {
	EmployeeHits    int64 `json:"employee_hits"`
	EmployeeMisses  int64 `json:"employee_misses"`
	StationHits     int64 `json:"station_hits"`
	StationMisses   int64 `json:"station_misses"`
	EmployeeCount   int   `json:"employee_count"`
	StationCount    int   `json:"station_count"`
	LastRefresh     time.Time `json:"last_refresh"`
}

// cacheItem 缓存项
type cacheItem struct {
	Value     string
	ExpiresAt time.Time
}

// isExpired 检查缓存项是否过期
func (item *cacheItem) isExpired() bool {
	return time.Now().After(item.ExpiresAt)
}

// MemoryFuelTransactionCache 内存燃油交易缓存实现
type MemoryFuelTransactionCache struct {
	employeeCache map[repository.ID]*cacheItem
	stationCache  map[repository.ID]*cacheItem
	mu            sync.RWMutex
	
	// 缓存配置
	ttl time.Duration
	
	// 统计信息
	stats CacheStats
}

// NewMemoryFuelTransactionCache 创建新的内存燃油交易缓存
func NewMemoryFuelTransactionCache(ttl time.Duration) *MemoryFuelTransactionCache {
	if ttl <= 0 {
		ttl = 5 * time.Minute // 默认5分钟过期
	}
	
	return &MemoryFuelTransactionCache{
		employeeCache: make(map[repository.ID]*cacheItem),
		stationCache:  make(map[repository.ID]*cacheItem),
		ttl:           ttl,
		stats:         CacheStats{LastRefresh: time.Now()},
	}
}

// GetEmployeeName 获取员工姓名
func (c *MemoryFuelTransactionCache) GetEmployeeName(ctx context.Context, employeeID repository.ID) (string, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	item, exists := c.employeeCache[employeeID]
	if !exists || item.isExpired() {
		c.stats.EmployeeMisses++
		return "", false
	}
	
	c.stats.EmployeeHits++
	return item.Value, true
}

// SetEmployeeName 设置员工姓名
func (c *MemoryFuelTransactionCache) SetEmployeeName(ctx context.Context, employeeID repository.ID, name string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.employeeCache[employeeID] = &cacheItem{
		Value:     name,
		ExpiresAt: time.Now().Add(c.ttl),
	}
}

// GetStationName 获取站点名称
func (c *MemoryFuelTransactionCache) GetStationName(ctx context.Context, stationID repository.ID) (string, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	item, exists := c.stationCache[stationID]
	if !exists || item.isExpired() {
		c.stats.StationMisses++
		return "", false
	}
	
	c.stats.StationHits++
	return item.Value, true
}

// SetStationName 设置站点名称
func (c *MemoryFuelTransactionCache) SetStationName(ctx context.Context, stationID repository.ID, name string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.stationCache[stationID] = &cacheItem{
		Value:     name,
		ExpiresAt: time.Now().Add(c.ttl),
	}
}

// GetEmployeeNames 批量获取员工姓名
func (c *MemoryFuelTransactionCache) GetEmployeeNames(ctx context.Context, employeeIDs []repository.ID) map[repository.ID]string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	result := make(map[repository.ID]string)
	for _, employeeID := range employeeIDs {
		item, exists := c.employeeCache[employeeID]
		if exists && !item.isExpired() {
			result[employeeID] = item.Value
			c.stats.EmployeeHits++
		} else {
			c.stats.EmployeeMisses++
		}
	}
	
	return result
}

// SetEmployeeNames 批量设置员工姓名
func (c *MemoryFuelTransactionCache) SetEmployeeNames(ctx context.Context, employeeNames map[repository.ID]string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	expiresAt := time.Now().Add(c.ttl)
	for employeeID, name := range employeeNames {
		c.employeeCache[employeeID] = &cacheItem{
			Value:     name,
			ExpiresAt: expiresAt,
		}
	}
}

// GetStationNames 批量获取站点名称
func (c *MemoryFuelTransactionCache) GetStationNames(ctx context.Context, stationIDs []repository.ID) map[repository.ID]string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	result := make(map[repository.ID]string)
	for _, stationID := range stationIDs {
		item, exists := c.stationCache[stationID]
		if exists && !item.isExpired() {
			result[stationID] = item.Value
			c.stats.StationHits++
		} else {
			c.stats.StationMisses++
		}
	}
	
	return result
}

// SetStationNames 批量设置站点名称
func (c *MemoryFuelTransactionCache) SetStationNames(ctx context.Context, stationNames map[repository.ID]string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	expiresAt := time.Now().Add(c.ttl)
	for stationID, name := range stationNames {
		c.stationCache[stationID] = &cacheItem{
			Value:     name,
			ExpiresAt: expiresAt,
		}
	}
}

// Clear 清空缓存
func (c *MemoryFuelTransactionCache) Clear(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.employeeCache = make(map[repository.ID]*cacheItem)
	c.stationCache = make(map[repository.ID]*cacheItem)
	c.stats = CacheStats{LastRefresh: time.Now()}
	
	return nil
}

// GetStats 获取缓存统计信息
func (c *MemoryFuelTransactionCache) GetStats(ctx context.Context) CacheStats {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	// 清理过期项并更新统计
	c.cleanupExpiredItems()
	
	stats := c.stats
	stats.EmployeeCount = len(c.employeeCache)
	stats.StationCount = len(c.stationCache)
	
	return stats
}

// cleanupExpiredItems 清理过期项（需要在锁内调用）
func (c *MemoryFuelTransactionCache) cleanupExpiredItems() {
	now := time.Now()
	
	// 清理过期的员工缓存
	for employeeID, item := range c.employeeCache {
		if now.After(item.ExpiresAt) {
			delete(c.employeeCache, employeeID)
		}
	}
	
	// 清理过期的站点缓存
	for stationID, item := range c.stationCache {
		if now.After(item.ExpiresAt) {
			delete(c.stationCache, stationID)
		}
	}
}

// StartCleanupRoutine 启动定期清理协程
func (c *MemoryFuelTransactionCache) StartCleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(c.ttl / 2) // 每半个TTL清理一次
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			c.mu.Lock()
			c.cleanupExpiredItems()
			c.mu.Unlock()
		}
	}
} 