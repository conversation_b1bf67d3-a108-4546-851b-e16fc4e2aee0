package di

import (
	"gitlab4.weicheche.cn/indo-bp/bos/internal/handler"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/infrastructure/database"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/repository"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/service"
)

// FuelTransactionContainer 燃油交易依赖注入容器
type FuelTransactionContainer struct {
	db                     *database.Database
	fuelTransactionRepo    repository.FuelTransactionRepository
	fuelTransactionService *service.FuelTransactionServiceOptimized
	fuelTransactionHandler *handler.FuelTransactionHandler
}

// NewFuelTransactionContainer 创建燃油交易依赖注入容器
func NewFuelTransactionContainer(db *database.Database) *FuelTransactionContainer {
	container := &FuelTransactionContainer{
		db: db,
	}

	// 初始化依赖
	container.initRepository()
	container.initService()
	container.initHandler()

	return container
}

// initRepository 初始化存储库
func (c *FuelTransactionContainer) initRepository() {
	// TODO: 实现BOS项目的FuelTransactionRepository
	// c.fuelTransactionRepo = postgres.NewFuelTransactionRepository(c.db)
	panic("FuelTransactionRepository not implemented for BOS project")
}

// initService 初始化服务
func (c *FuelTransactionContainer) initService() {
	// 启用优化和性能日志
	useOptimization := true
	performanceLog := true

	c.fuelTransactionService = service.NewFuelTransactionServiceOptimized(
		c.fuelTransactionRepo,
		useOptimization,
		performanceLog,
	)
}

// initHandler 初始化处理器
func (c *FuelTransactionContainer) initHandler() {
	c.fuelTransactionHandler = handler.NewFuelTransactionHandler(c.fuelTransactionService)
}

// GetFuelTransactionRepository 获取燃油交易存储库
func (c *FuelTransactionContainer) GetFuelTransactionRepository() repository.FuelTransactionRepository {
	return c.fuelTransactionRepo
}

// GetFuelTransactionService 获取燃油交易服务
func (c *FuelTransactionContainer) GetFuelTransactionService() *service.FuelTransactionServiceOptimized {
	return c.fuelTransactionService
}

// GetFuelTransactionHandler 获取燃油交易处理器
func (c *FuelTransactionContainer) GetFuelTransactionHandler() *handler.FuelTransactionHandler {
	return c.fuelTransactionHandler
}
