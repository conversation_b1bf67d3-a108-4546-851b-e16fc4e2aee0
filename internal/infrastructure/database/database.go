package database

import (
	"github.com/jackc/pgx/v4/pgxpool"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/infrastructure/database"
)

// Database BOS数据库连接适配器
type Database struct {
	pool *pgxpool.Pool
}

// NewDatabase 创建数据库连接适配器
func NewDatabase(multiDB *database.MultiDatabase) *Database {
	return &Database{
		pool: multiDB.GetOrderDB().GetPool(), // 使用主数据库连接
	}
}

// GetPool 获取数据库连接池
func (d *Database) GetPool() *pgxpool.Pool {
	return d.pool
}
