package middleware

import (
	"fmt"
	"log"
	"time"

	"github.com/gin-gonic/gin"
)

// PerformanceMonitor 性能监控中间件
func PerformanceMonitor() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录开始时间
		startTime := time.Now()

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(startTime)

		// 获取请求信息
		method := c.Request.Method
		path := c.Request.URL.Path
		statusCode := c.Writer.Status()
		clientIP := c.ClientIP()
		userAgent := c.Request.UserAgent()

		// 记录性能日志
		logPerformance(method, path, statusCode, duration, clientIP, userAgent)

		// 如果是燃油交易相关接口，记录详细信息
		if isFuelTransactionAPI(path) {
			logFuelTransactionPerformance(path, duration, c)
		}
	}
}

// logPerformance 记录性能日志
func logPerformance(method, path string, statusCode int, duration time.Duration, clientIP, userAgent string) {
	log.Printf("[PERF] %s %s - %d - %v - %s - %s",
		method, path, statusCode, duration, clientIP, userAgent)
}

// logFuelTransactionPerformance 记录燃油交易接口性能
func logFuelTransactionPerformance(path string, duration time.Duration, c *gin.Context) {
	// 获取查询参数
	stationID := c.Query("station_id")
	page := c.Query("page")
	pageSize := c.Query("page_size")
	
	// 判断性能等级
	performanceLevel := getPerformanceLevel(duration)
	
	log.Printf("[FUEL_TRANSACTION_PERF] Path: %s, Duration: %v, Level: %s, StationID: %s, Page: %s, PageSize: %s",
		path, duration, performanceLevel, stationID, page, pageSize)
	
	// 如果响应时间过长，记录警告
	if duration > 1*time.Second {
		log.Printf("[PERF_WARNING] Slow fuel transaction query: %s took %v", path, duration)
	}
}

// isFuelTransactionAPI 判断是否为燃油交易相关API
func isFuelTransactionAPI(path string) bool {
	fuelTransactionPaths := []string{
		"/api/v1/fuel-transactions-full",
		"/api/v1/fuel-transactions-full-optimized",
		"/api/v1/fuel-transactions/full",
		"/api/v1/fuel-transactions/full-optimized",
	}
	
	for _, fuelPath := range fuelTransactionPaths {
		if path == fuelPath {
			return true
		}
	}
	
	return false
}

// getPerformanceLevel 获取性能等级
func getPerformanceLevel(duration time.Duration) string {
	if duration < 50*time.Millisecond {
		return "EXCELLENT"
	} else if duration < 100*time.Millisecond {
		return "GOOD"
	} else if duration < 500*time.Millisecond {
		return "FAIR"
	} else if duration < 1*time.Second {
		return "POOR"
	} else {
		return "CRITICAL"
	}
}

// PerformanceStats 性能统计结构
type PerformanceStats struct {
	TotalRequests    int64         `json:"total_requests"`
	AverageResponse  time.Duration `json:"average_response"`
	MinResponse      time.Duration `json:"min_response"`
	MaxResponse      time.Duration `json:"max_response"`
	ExcellentCount   int64         `json:"excellent_count"`
	GoodCount        int64         `json:"good_count"`
	FairCount        int64         `json:"fair_count"`
	PoorCount        int64         `json:"poor_count"`
	CriticalCount    int64         `json:"critical_count"`
	LastUpdated      time.Time     `json:"last_updated"`
}

// 全局性能统计（简单实现，生产环境建议使用Redis或数据库）
var globalStats = &PerformanceStats{
	LastUpdated: time.Now(),
}

// UpdatePerformanceStats 更新性能统计
func UpdatePerformanceStats(duration time.Duration) {
	globalStats.TotalRequests++
	
	// 更新最小和最大响应时间
	if globalStats.MinResponse == 0 || duration < globalStats.MinResponse {
		globalStats.MinResponse = duration
	}
	if duration > globalStats.MaxResponse {
		globalStats.MaxResponse = duration
	}
	
	// 计算平均响应时间（简单实现）
	if globalStats.TotalRequests == 1 {
		globalStats.AverageResponse = duration
	} else {
		// 使用移动平均
		globalStats.AverageResponse = (globalStats.AverageResponse + duration) / 2
	}
	
	// 更新性能等级计数
	level := getPerformanceLevel(duration)
	switch level {
	case "EXCELLENT":
		globalStats.ExcellentCount++
	case "GOOD":
		globalStats.GoodCount++
	case "FAIR":
		globalStats.FairCount++
	case "POOR":
		globalStats.PoorCount++
	case "CRITICAL":
		globalStats.CriticalCount++
	}
	
	globalStats.LastUpdated = time.Now()
}

// GetPerformanceStats 获取性能统计
func GetPerformanceStats() *PerformanceStats {
	return globalStats
}

// ResetPerformanceStats 重置性能统计
func ResetPerformanceStats() {
	globalStats = &PerformanceStats{
		LastUpdated: time.Now(),
	}
}

// PerformanceAlert 性能告警
func PerformanceAlert(path string, duration time.Duration, threshold time.Duration) {
	if duration > threshold {
		alertMessage := fmt.Sprintf("PERFORMANCE ALERT: %s took %v (threshold: %v)", path, duration, threshold)
		log.Printf("[ALERT] %s", alertMessage)
		
		// 这里可以集成告警系统，如发送邮件、短信、Slack通知等
		// sendAlert(alertMessage)
	}
}
