package models

import (
	"crypto/rand"
	"fmt"
	"time"
)

// 定义业务错误码常量
const (
	// 成功状态码
	CodeSuccess = 0

	// 客户端错误 (4xx)
	CodeBadRequest       = 400 // 请求参数错误
	CodeUnauthorized     = 401 // 未授权
	CodeForbidden        = 403 // 禁止访问
	CodeNotFound         = 404 // 资源未找到
	CodeMethodNotAllowed = 405 // 方法不允许
	CodeConflict         = 409 // 资源冲突
	CodeValidationError  = 422 // 参数验证失败

	// 服务器错误 (5xx)
	CodeInternalServerError = 500 // 内部服务器错误
	CodeBadGateway          = 502 // 网关错误
	CodeServiceUnavailable  = 503 // 服务不可用
	CodeGatewayTimeout      = 504 // 网关超时

	// 业务错误码 (1xxx)
	CodeBusinessError = 1000 // 通用业务错误
	CodePaymentFailed = 1001 // 支付失败
	CodeSystemBusy    = -1   // 系统繁忙
)

// ErrorResponse 定义API错误响应结构
type ErrorResponse struct {
	Code    string `json:"code"`             // 错误代码
	Message string `json:"message"`          // 错误消息
	Detail  string `json:"detail,omitempty"` // 详细信息
}

// APIResponse 定义统一的API响应结构
type APIResponse struct {
	Code      int         `json:"code"`      // 业务状态码 (0表示成功，-1表示系统繁忙，正数表示业务错误)
	Message   string      `json:"message"`   // 响应消息
	Data      interface{} `json:"data"`      // 响应数据
	Timestamp string      `json:"timestamp"` // 响应时间戳 (ISO 8601)
	RequestID string      `json:"requestId"` // 请求唯一标识
}

// generateRequestID 生成请求唯一标识
func generateRequestID() string {
	bytes := make([]byte, 8)
	rand.Read(bytes)
	return fmt.Sprintf("req_%x", bytes)
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(data interface{}, message string) *APIResponse {
	return &APIResponse{
		Code:      CodeSuccess,
		Message:   message,
		Data:      data,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: generateRequestID(),
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(code int, message string) *APIResponse {
	return &APIResponse{
		Code:      code,
		Message:   message,
		Data:      nil,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: generateRequestID(),
	}
}

// CurrentShiftResponse 当前班次响应结构
type CurrentShiftResponse struct {
	HasActiveShift bool        `json:"has_active_shift"` // 是否有活跃班次
	Shift          interface{} `json:"shift"`            // 班次数据，有活跃班次时为Shift对象，否则为null
	Message        string      `json:"message"`          // 状态说明
}
