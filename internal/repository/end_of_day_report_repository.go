package repository

import (
	"context"
	"time"
)

/**
 * End of Day Report Repository Interface
 *
 * 定义日终报表数据访问层接口
 */

// EndOfDayReportRepository 日终报表数据访问接口
type EndOfDayReportRepository interface {
	// GetShiftsForDate 获取指定日期的班次信息
	GetShiftsForDate(ctx context.Context, stationID int64, date string) ([]ShiftInfo, error)

	// GetTransactionsForDate 获取指定日期的交易数据
	GetTransactionsForDate(ctx context.Context, stationID int64, date string) ([]TransactionInfo, error)

	// GetOtherIncomeForDate 获取指定日期的其他收入数据
	GetOtherIncomeForDate(ctx context.Context, stationID int64, date string) ([]OtherIncomeInfo, error)

	// GetStationInfo 获取站点信息
	GetStationInfo(ctx context.Context, stationID int64) (*StationInfo, error)

	// GetUserInfo 获取用户信息
	GetUserInfo(ctx context.Context, userID int64) (*UserInfo, error)

	// GetPaymentMethodName 获取支付方式显示名称
	GetPaymentMethodName(ctx context.Context, paymentMethod string) (string, error)

	// GetPaymentMethods 获取所有支付方式配置（新增）
	GetPaymentMethods(ctx context.Context) ([]PaymentMethodInfo, error)

	// GetPaymentsByStationAndTimeRange 获取指定站点和时间范围的支付数据（新增）
	GetPaymentsByStationAndTimeRange(ctx context.Context, stationID int64, startTime, endTime time.Time) ([]PaymentInfo, error)

	// GetOrderIDByTransactionID 根据交易ID获取订单ID（新增，用于Tera支付过滤）
	GetOrderIDByTransactionID(ctx context.Context, transactionID int64) (string, error)
}

// ShiftInfo 班次信息
type ShiftInfo struct {
	ID          int64      `db:"id"`
	ShiftNumber string     `db:"shift_number"`
	StationID   int64      `db:"station_id"`
	StartTime   time.Time  `db:"start_time"`
	EndTime     *time.Time `db:"end_time"`
	Status      string     `db:"status"`
}

// TransactionInfo 交易信息
type TransactionInfo struct {
	ID                string    `db:"id"` // 修改为string类型以支持UUID
	TransactionNumber string    `db:"transaction_number"`
	StationID         int64     `db:"station_id"`
	Volume            float64   `db:"volume"`
	Amount            float64   `db:"amount"`
	UnitPrice         float64   `db:"unit_price"`
	FuelGrade         string    `db:"fuel_grade"`
	FuelType          string    `db:"fuel_type"`
	EmployeeID        *string   `db:"employee_id"`   // 修改为string类型以支持UUID
	StaffCardID       *string   `db:"staff_card_id"` // 修改为string类型以支持UUID
	PaymentID         string    `db:"payment_id"`    // 修改为string类型以支持UUID
	PaymentMethod     string    `db:"payment_method"`
	PaymentAmount     float64   `db:"payment_amount"`
	PaymentTime       time.Time `db:"payment_time"`
	PaymentStatus     string    `db:"payment_status"`
}

// OtherIncomeInfo 其他收入信息
type OtherIncomeInfo struct {
	ID            int64     `db:"id"`
	OrderID       int64     `db:"order_id"`
	ProductType   string    `db:"product_type"`
	ProductName   string    `db:"product_name"`
	Quantity      int       `db:"quantity"`
	UnitPrice     float64   `db:"unit_price"`
	TotalAmount   float64   `db:"total_amount"`
	EmployeeID    *int64    `db:"employee_id"`
	StaffCardID   *int64    `db:"staff_card_id"`
	PaymentTime   time.Time `db:"payment_time"`
	PaymentMethod string    `db:"payment_method"`
}

// StationInfo 站点信息
type StationInfo struct {
	ID       int64  `db:"id"`
	SiteCode string `db:"site_code"`
	SiteName string `db:"site_name"`
}

// UserInfo 用户信息
type UserInfo struct {
	ID       int64  `db:"id"`
	Username string `db:"username"`
	FullName string `db:"full_name"`
}

// PaymentMethodInfo 支付方式信息（新增）
type PaymentMethodInfo struct {
	ID          int64  `db:"id"`
	Name        string `db:"name"`
	DisplayName string `db:"display_name"`
	Type        string `db:"type"`
	IsActive    bool   `db:"is_active"`
}

// PaymentInfo 支付信息（新增）
type PaymentInfo struct {
	ID            int64     `db:"id"`
	OrderID       string    `db:"order_id"`
	PaymentMethod *int64    `db:"payment_method"`
	PaymentType   string    `db:"payment_type"`
	Amount        float64   `db:"amount"`
	CompletedAt   time.Time `db:"completed_at"`
	Status        string    `db:"status"`
}
