package repository

import (
	"time"
)

// ID 定义ID类型
type ID int64

// Pagination 分页参数
type Pagination struct {
	Page     int `json:"page"`      // 页码
	PageSize int `json:"page_size"` // 每页数量
}

// SortOrder 排序参数
type SortOrder struct {
	Field string `json:"field"` // 排序字段
	Order string `json:"order"` // 排序方向 (asc/desc)
}

// Order 订单结构
type Order struct {
	ID           ID                     `json:"id"`
	OrderNumber  string                 `json:"order_number"`
	StationID    ID                     `json:"station_id"`
	CustomerID   *ID                    `json:"customer_id"`
	CustomerName *string                `json:"customer_name"`
	EmployeeNo   *string                `json:"employee_no"`
	TerminalID   *string                `json:"terminal_id"`
	TotalAmount  float64                `json:"total_amount"`
	Status       string                 `json:"status"`
	Metadata     map[string]interface{} `json:"metadata"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
}

// FuelTransactionStatus 燃油交易状态枚举
type FuelTransactionStatus string

const (
	FuelTransactionStatusPending   FuelTransactionStatus = "pending"
	FuelTransactionStatusProcessed FuelTransactionStatus = "processed"
	FuelTransactionStatusCancelled FuelTransactionStatus = "cancelled"
)

// FuelTransaction 燃油交易结构
type FuelTransaction struct {
	ID                        ID                     `json:"id"`
	TransactionNumber         string                 `json:"transaction_number"`
	StationID                 ID                     `json:"station_id"`
	PumpID                    string                 `json:"pump_id"`
	NozzleID                  string                 `json:"nozzle_id"`
	FuelType                  string                 `json:"fuel_type"`
	FuelGrade                 string                 `json:"fuel_grade"`
	Tank                      *int                   `json:"tank"`
	UnitPrice                 float64                `json:"unit_price"`
	Volume                    float64                `json:"volume"`
	Amount                    float64                `json:"amount"`
	TotalVolume               float64                `json:"total_volume"`
	TotalAmount               float64                `json:"total_amount"`
	Status                    FuelTransactionStatus  `json:"status"`
	MemberCardID              *string                `json:"member_card_id"`
	MemberID                  *ID                    `json:"member_id"`
	EmployeeID                *ID                    `json:"employee_id"`
	StaffCardID               *ID                    `json:"staff_card_id"`
	FCCTransactionID          *string                `json:"fcc_transaction_id"`
	POSTerminalID             *string                `json:"pos_terminal_id"`
	StartTotalizer            *float64               `json:"start_totalizer"`
	EndTotalizer              *float64               `json:"end_totalizer"`
	NozzleStartTime           *time.Time             `json:"nozzle_start_time"`
	NozzleEndTime             *time.Time             `json:"nozzle_end_time"`
	TotalizerContinuityStatus *string                `json:"totalizer_continuity_status"` // 泵码连续性状态：normal/abnormal/unknown
	Metadata                  map[string]interface{} `json:"metadata"`
	CreatedAt                 time.Time              `json:"created_at"`
	UpdatedAt                 time.Time              `json:"updated_at"`
	ProcessedAt               *time.Time             `json:"processed_at"`
	CancelledAt               *time.Time             `json:"cancelled_at"`
}

// FuelTransactionFull 完整的燃油交易信息结构（用于连表查询优化）
type FuelTransactionFull struct {
	// 基础燃油交易信息
	ID                ID                     `json:"id"`
	TransactionNumber string                 `json:"transaction_number"`
	StationID         ID                     `json:"station_id"`
	PumpID            string                 `json:"pump_id"`
	NozzleID          string                 `json:"nozzle_id"`
	FuelType          string                 `json:"fuel_type"`
	FuelGrade         string                 `json:"fuel_grade"`
	Tank              *int                   `json:"tank"`
	UnitPrice         float64                `json:"unit_price"`
	Volume            float64                `json:"volume"`
	Amount            float64                `json:"amount"`
	TotalVolume       float64                `json:"total_volume"`
	TotalAmount       float64                `json:"total_amount"`
	Status            FuelTransactionStatus  `json:"status"`
	MemberCardID      *string                `json:"member_card_id"`
	MemberID          *ID                    `json:"member_id"`
	EmployeeID        *ID                    `json:"employee_id"`
	StaffCardID       *ID                    `json:"staff_card_id"`
	ShiftID           *ID                    `json:"shift_id"`
	FCCTransactionID  *string                `json:"fcc_transaction_id"`
	POSTerminalID     *string                `json:"pos_terminal_id"`
	StartTotalizer    *float64               `json:"start_totalizer"`
	EndTotalizer      *float64               `json:"end_totalizer"`
	NozzleStartTime   *time.Time             `json:"nozzle_start_time"`
	NozzleEndTime     *time.Time             `json:"nozzle_end_time"`
	Metadata          map[string]interface{} `json:"metadata"`
	CreatedAt         time.Time              `json:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at"`
	ProcessedAt       *time.Time             `json:"processed_at"`
	CancelledAt       *time.Time             `json:"cancelled_at"`

	// 扩展信息（通过连表查询获取）
	StationName      string     `json:"station_name"`
	SiteCode         string     `json:"site_code"`
	EmployeeName     string     `json:"employee_name"`
	ShiftName        string     `json:"shift_name"`
	CustomerName     string     `json:"customer_name"`
	CustomerPhone    string     `json:"customer_phone"`
	VehicleType      string     `json:"vehicle_type"`
	LicensePlate     string     `json:"license_plate"`
	PromotionName    string     `json:"promotion_name"`
	OrderNumbers     string     `json:"order_numbers"`
	PaymentMethods   string     `json:"payment_methods"`
	FirstPaymentTime *time.Time `json:"first_payment_time"`

	// 设备信息（新增）
	DispenserName    string     `json:"dispenser_name"`    // 加油机名称
	TankName         string     `json:"tank_name"`         // 油罐名称
	DeviceID         *string    `json:"device_id"`         // 设备ID（对应pos_terminal_id）

	// 折扣信息（新增）
	TotalDiscountAmount     float64 `json:"total_discount_amount"`     // 总折扣金额
	ItemDiscountAmount      float64 `json:"item_discount_amount"`      // 订单项级别折扣
	PromotionDiscountAmount float64 `json:"promotion_discount_amount"` // 促销活动折扣
	OrderDiscountAmount     float64 `json:"order_discount_amount"`     // 订单级别折扣

	// 免费升数信息（新增）
	FreeLiter       float64 `json:"free_liter"`        // 免费升数 (L)
	FreeLiterAmount float64 `json:"free_liter_amount"` // 免费升金额 (Rp)
}

// FuelTransactionFilter 燃油交易过滤器
type FuelTransactionFilter struct {
	StationID         *ID                    `json:"station_id"`
	PumpID            *string                `json:"pump_id"`
	NozzleID          *string                `json:"nozzle_id"`
	FuelType          *string                `json:"fuel_type"`
	FuelGrade         *string                `json:"fuel_grade"`
	Status            *FuelTransactionStatus `json:"status"`
	TransactionNumber *string                `json:"transaction_number"`
	MemberID          *ID                    `json:"member_id"`
	EmployeeID        *ID                    `json:"employee_id"`
	StaffCardID       *ID                    `json:"staff_card_id"`
	ShiftID           *ID                    `json:"shift_id"` // 班次ID筛选
	Tank              *int                   `json:"tank"`
	DateFrom          *time.Time             `json:"date_from"`
	DateTo            *time.Time             `json:"date_to"`
	// 新增筛选条件
	VehicleType               *string `json:"vehicle_type"`                // 车型筛选
	PumpNozzle                *string `json:"pump_nozzle"`                 // 泵枪组合筛选
	TotalizerContinuityStatus *string `json:"totalizer_continuity_status"` // 泵码连续性状态筛选
	ShiftName                 *string `json:"shift_name"`                  // 班次名称筛选
	OrderSerialNo             *string `json:"order_serial_no"`             // 订单序列号筛选
	// 兼容字段
	FuelTypeCompat *string `json:"fuel_type_compat"` // 兼容前端的fuel_type参数
}

// FuelTransactionOrderLinkStatus 燃油交易订单关联状态
type FuelTransactionOrderLinkStatus string

const (
	LinkStatusActive   FuelTransactionOrderLinkStatus = "active"
	LinkStatusInactive FuelTransactionOrderLinkStatus = "inactive"
)

// FuelTransactionOrderLink 燃油交易订单关联
type FuelTransactionOrderLink struct {
	ID                ID                             `json:"id"`
	FuelTransactionID ID                             `json:"fuel_transaction_id"`
	OrderID           ID                             `json:"order_id"`
	AllocatedAmount   float64                        `json:"allocated_amount"`
	Status            FuelTransactionOrderLinkStatus `json:"status"`
	Metadata          map[string]interface{}         `json:"metadata"`
	CreatedAt         time.Time                      `json:"created_at"`
	UpdatedAt         time.Time                      `json:"updated_at"`
	DeactivatedAt     *time.Time                     `json:"deactivated_at"`
}
