package repository

import (
	"context"
	"time"
)

// FuelTransactionRepository 燃油交易存储库接口
type FuelTransactionRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, transaction FuelTransaction) (FuelTransaction, error)
	Update(ctx context.Context, transaction FuelTransaction) (FuelTransaction, error)
	Get(ctx context.Context, id ID) (FuelTransaction, error)
	List(ctx context.Context, filter FuelTransactionFilter, pagination Pagination, sort SortOrder) ([]FuelTransaction, int, error)
	GetByTransactionNumber(ctx context.Context, transactionNumber string) (FuelTransaction, error)

	// 完整信息查询（连表查询优化）
	GetFuelTransactionsFullWithJoins(ctx context.Context, filter FuelTransactionFilter, pagination Pagination, sort SortOrder) ([]FuelTransactionFull, int, error)

	// 执行自定义查询（用于汇总表查询）
	ExecuteFullTransactionQuery(ctx context.Context, query string, countQuery string, params []interface{}) ([]FuelTransactionFull, int, error)

	// 订单关联操作
	LinkFuelTransactionToOrder(ctx context.Context, fuelTransactionID ID, orderID ID, allocatedAmount float64) (FuelTransactionOrderLink, error)
	UnlinkFuelTransactionFromOrder(ctx context.Context, fuelTransactionID ID, orderID ID) error
	GetFuelTransactionOrders(ctx context.Context, fuelTransactionID ID) ([]Order, error)
	UpdateLinkAllocatedAmount(ctx context.Context, fuelTransactionID ID, orderID ID, allocatedAmount float64) error
	ConfirmFuelTransactionLink(ctx context.Context, fuelTransactionID ID, orderID ID) error
	CancelFuelTransactionLink(ctx context.Context, fuelTransactionID ID, orderID ID) error
	CleanupFuelTransactionLinks(ctx context.Context, fuelTransactionID ID) error

	// 状态处理
	ProcessFuelTransactionStatus(ctx context.Context, fuelTransactionID ID, force bool) error

	// 查询操作
	GetOrderFuelTransactions(ctx context.Context, orderID ID) ([]FuelTransaction, error)
	GetFuelTransactionStats(ctx context.Context, filter FuelTransactionFilter) (FuelTransactionStats, error)
	GetFuelTransactionsByDateRange(ctx context.Context, stationID ID, startDate, endDate time.Time) ([]FuelTransaction, error)
	GetFuelTransactionsByShift(ctx context.Context, shiftID ID) ([]FuelTransaction, error)
	GetFuelTransactionsByEmployee(ctx context.Context, employeeID ID) ([]FuelTransaction, error)
	GetFuelTransactionsByMember(ctx context.Context, memberID ID) ([]FuelTransaction, error)
}

// FuelTransactionStats 燃油交易统计信息
type FuelTransactionStats struct {
	TotalTransactions int     `json:"total_transactions"`
	TotalVolume       float64 `json:"total_volume"`
	TotalAmount       float64 `json:"total_amount"`
	AverageVolume     float64 `json:"average_volume"`
	AverageAmount     float64 `json:"average_amount"`
}
