package postgres

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v5"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/infrastructure/database"
)

/**
 * End of Day Report Repository PostgreSQL Implementation
 *
 * 基于PGX的日终报表数据访问层实现
 */
type EndOfDayReportRepository struct {
	orderDB   *database.Database // order_schema 数据库连接
	coreDB    *database.Database // core_schema 数据库连接
	paymentDB *database.Database // payment_schema 数据库连接
}

// NewEndOfDayReportRepository 创建新的日终报表数据访问实例
func NewEndOfDayReportRepository(multiDB *database.MultiDatabase) repository.EndOfDayReportRepository {
	return &EndOfDayReportRepository{
		orderDB:   multiDB.GetOrderDB(),
		coreDB:    multiDB.GetAuthDB(), // 在现有架构中，core_schema通过AuthDB访问
		paymentDB: multiDB.GetPaymentDB(),
	}
}

// GetShiftsForDate 获取指定日期的班次信息
func (r *EndOfDayReportRepository) GetShiftsForDate(ctx context.Context, stationID int64, date string) ([]repository.ShiftInfo, error) {
	query := `
		SELECT
			id,
			shift_number,
			station_id,
			start_time,
			end_time,
			CASE
				WHEN end_time IS NULL THEN 'active'
				ELSE 'closed'
			END as status
		FROM shifts
		WHERE station_id = $1
		  AND DATE(start_time AT TIME ZONE 'Asia/Jakarta') <= $2
		  AND (end_time IS NULL OR DATE(end_time AT TIME ZONE 'Asia/Jakarta') >= $2)
		  AND deleted_at IS NULL
		ORDER BY start_time
	`

	rows, err := r.orderDB.GetPool().Query(ctx, query, stationID, date)
	if err != nil {
		return nil, fmt.Errorf("failed to query shifts: %w", err)
	}
	defer rows.Close()

	var shifts []repository.ShiftInfo
	for rows.Next() {
		var shift repository.ShiftInfo
		err := rows.Scan(
			&shift.ID,
			&shift.ShiftNumber,
			&shift.StationID,
			&shift.StartTime,
			&shift.EndTime,
			&shift.Status,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan shift: %w", err)
		}
		shifts = append(shifts, shift)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating shifts: %w", err)
	}

	return shifts, nil
}

// GetTransactionsForDate 获取指定日期的交易数据
func (r *EndOfDayReportRepository) GetTransactionsForDate(ctx context.Context, stationID int64, date string) ([]repository.TransactionInfo, error) {
	// 🚨 关键修复：简化查询路径，直接查询 fuel_transactions 表
	// 避免复杂的 JOIN 链路，提高性能和数据一致性
	query := `
		WITH date_range AS (
			SELECT
				($2::date AT TIME ZONE 'Asia/Jakarta')::timestamp AT TIME ZONE 'Asia/Jakarta' AS start_time,
				(($2::date + INTERVAL '1 day') AT TIME ZONE 'Asia/Jakarta')::timestamp AT TIME ZONE 'Asia/Jakarta' AS end_time
		)
		SELECT
			ft.id,
			ft.transaction_number,
			ft.station_id,
			ft.volume,
			ft.amount,
			ft.unit_price,
			ft.fuel_grade,
			ft.fuel_type,
			ft.employee_id,
			ft.staff_card_id,
			ft.created_at as payment_time,
			ft.status as transaction_status,
			op_latest.id as payment_id,
			op_latest.payment_method,
			op_latest.amount as payment_amount,
			op_latest.status as payment_status
		FROM fuel_transactions ft
		LEFT JOIN fuel_transaction_order_links ftol
		  ON ft.id = ftol.fuel_transaction_id AND ftol.status = 'active'
		LEFT JOIN orders o ON ftol.order_id = o.id
		LEFT JOIN LATERAL (
			SELECT op.id, op.payment_method, op.amount, op.status, op.completed_at
			FROM order_payments op
			WHERE op.order_id = o.id AND op.status = 'completed'
			ORDER BY op.completed_at DESC
			LIMIT 1
		) AS op_latest ON TRUE
		CROSS JOIN date_range dr
		WHERE ft.station_id = $1
		  AND ft.status = 'processed'
		  AND ft.created_at >= dr.start_time
		  AND ft.created_at < dr.end_time
	`

	rows, err := r.orderDB.GetPool().Query(ctx, query, stationID, date)
	if err != nil {
		return nil, fmt.Errorf("failed to query transactions: %w", err)
	}
	defer rows.Close()

	var transactions []repository.TransactionInfo
	for rows.Next() {
		var tx repository.TransactionInfo
		var transactionStatus string
		var paymentID *string
		var paymentMethod *string
		var paymentAmount *float64
		var paymentStatus *string

		err := rows.Scan(
			&tx.ID,
			&tx.TransactionNumber,
			&tx.StationID,
			&tx.Volume,
			&tx.Amount,
			&tx.UnitPrice,
			&tx.FuelGrade,
			&tx.FuelType,
			&tx.EmployeeID,
			&tx.StaffCardID,
			&tx.PaymentTime,
			&transactionStatus,
			&paymentID,
			&paymentMethod,
			&paymentAmount,
			&paymentStatus,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan transaction row: %w", err)
		}

		// 使用联表/横向查询一次性带出支付信息，避免 N+1
		if paymentID != nil {
			tx.PaymentID = *paymentID
		}
		if paymentMethod != nil {
			tx.PaymentMethod = *paymentMethod
		} else {
			tx.PaymentMethod = "unknown"
		}
		if paymentAmount != nil {
			tx.PaymentAmount = *paymentAmount
		} else {
			// 无支付信息时，回退到交易金额
			tx.PaymentAmount = tx.Amount
		}
		if paymentStatus != nil {
			tx.PaymentStatus = *paymentStatus
		} else {
			tx.PaymentStatus = "unknown"
		}

		transactions = append(transactions, tx)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error reading transaction rows: %w", err)
	}

	return transactions, nil
}

// GetOtherIncomeForDate 获取指定日期的其他收入数据
func (r *EndOfDayReportRepository) GetOtherIncomeForDate(ctx context.Context, stationID int64, date string) ([]repository.OtherIncomeInfo, error) {
	// 🚨 关键修复：统一时间基准，使用 o.created_at 替代 op.completed_at
	// 确保与交易查询和 shifts/{id}/attendants 接口使用相同的时间逻辑
	query := `
		WITH date_range AS (
			SELECT 
				($2::date AT TIME ZONE 'Asia/Jakarta')::timestamp AT TIME ZONE 'Asia/Jakarta' AS start_time,
				(($2::date + INTERVAL '1 day') AT TIME ZONE 'Asia/Jakarta')::timestamp AT TIME ZONE 'Asia/Jakarta' AS end_time
		)
		SELECT 
			oi.id,
			oi.order_id,
			oi.product_type,
			oi.product_name,
			oi.quantity,
			oi.unit_price,
			oi.total_price as total_amount,
			o.employee_no as employee_no,
			o.staff_card_id,
			o.created_at as payment_time,    -- 🔧 修复：使用 o.created_at 作为时间基准
			op.payment_method
		FROM order_items oi
		INNER JOIN orders o ON oi.order_id = o.id
		INNER JOIN order_payments op ON o.id = op.order_id
		CROSS JOIN date_range dr
		WHERE o.station_id = $1
		  AND op.status = 'completed'
		  AND o.created_at >= dr.start_time      -- 🔧 修复：使用 o.created_at 筛选
		  AND o.created_at < dr.end_time         -- 🔧 修复：使用 o.created_at 筛选
		  AND oi.product_type IN ('nitrogen', 'other')
	`

	rows, err := r.orderDB.GetPool().Query(ctx, query, stationID, date)
	if err != nil {
		return nil, fmt.Errorf("failed to query other income: %w", err)
	}
	defer rows.Close()

	var otherIncomes []repository.OtherIncomeInfo
	for rows.Next() {
		var income repository.OtherIncomeInfo
		var employeeNoStr *string

		err := rows.Scan(
			&income.ID,
			&income.OrderID,
			&income.ProductType,
			&income.ProductName,
			&income.Quantity,
			&income.UnitPrice,
			&income.TotalAmount,
			&employeeNoStr,
			&income.StaffCardID,
			&income.PaymentTime,
			&income.PaymentMethod,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan other income: %w", err)
		}

		// 已去除逐行 employee_no→employee_id 查询，改为在上游进行批量或联表方式处理（Phase 2 可继续优化）

		otherIncomes = append(otherIncomes, income)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating other income: %w", err)
	}

	return otherIncomes, nil
}

// GetStationInfo 获取站点信息
func (r *EndOfDayReportRepository) GetStationInfo(ctx context.Context, stationID int64) (*repository.StationInfo, error) {
	query := `
		SELECT 
			id,
			site_code,
			site_name
		FROM stations 
		WHERE id = $1 AND deleted_at IS NULL
	`

	var station repository.StationInfo
	err := r.coreDB.GetPool().QueryRow(ctx, query, stationID).Scan(
		&station.ID,
		&station.SiteCode,
		&station.SiteName,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get station info: %w", err)
	}

	return &station, nil
}

// GetUserInfo 获取用户信息 - 完整的员工姓名获取逻辑
func (r *EndOfDayReportRepository) GetUserInfo(ctx context.Context, userID int64) (*repository.UserInfo, error) {
	// 方法1：直接通过staff_cards表关联users表获取真实姓名（优先级最高）
	staffCardQuery := `
		SELECT 
			sc.id,
			u.username,
			u.full_name
		FROM staff_cards sc
		INNER JOIN users u ON sc.user_id = u.id
		WHERE sc.id = $1 AND sc.deleted_at IS NULL AND u.deleted_at IS NULL
	`

	var user repository.UserInfo
	err := r.coreDB.GetPool().QueryRow(ctx, staffCardQuery, userID).Scan(
		&user.ID,
		&user.Username,
		&user.FullName,
	)
	if err == nil && user.FullName != "" {
		return &user, nil
	}

	// 方法2：查找从employees表迁移过来的记录（通过metadata中的migrated_from_employee_id）
	migrationQuery := `
		SELECT 
			sc.id,
			u.username,
			u.full_name
		FROM staff_cards sc
		INNER JOIN users u ON sc.user_id = u.id
		WHERE sc.metadata->>'migrated_from_employee_id' = $1 
		  AND sc.deleted_at IS NULL 
		  AND u.deleted_at IS NULL
		LIMIT 1
	`

	err = r.coreDB.GetPool().QueryRow(ctx, migrationQuery, fmt.Sprintf("%d", userID)).Scan(
		&user.ID,
		&user.Username,
		&user.FullName,
	)
	if err == nil && user.FullName != "" {
		return &user, nil
	}

	// 方法3：通过员工编号匹配（处理历史数据兼容性）
	// 首先从employees表获取员工编号
	var employeeNo string
	employeeQuery := `
		SELECT employee_no
		FROM employees
		WHERE id = $1 AND deleted_at IS NULL
	`

	err = r.orderDB.GetPool().QueryRow(ctx, employeeQuery, userID).Scan(&employeeNo)
	if err == nil && employeeNo != "" {
		// 通过员工编号在staff_cards的metadata中查找对应记录
		employeeNoQuery := `
			SELECT 
				sc.id,
				u.username,
				u.full_name
			FROM staff_cards sc
			INNER JOIN users u ON sc.user_id = u.id
			WHERE sc.metadata->>'original_employee_no' = $1 
			  AND sc.deleted_at IS NULL 
			  AND u.deleted_at IS NULL
			LIMIT 1
		`

		err = r.coreDB.GetPool().QueryRow(ctx, employeeNoQuery, employeeNo).Scan(
			&user.ID,
			&user.Username,
			&user.FullName,
		)
		if err == nil && user.FullName != "" {
			return &user, nil
		}
	}

	// 方法4：尝试从staff_cards表的metadata中获取原始员工姓名
	originalNameQuery := `
		SELECT 
			sc.id,
			COALESCE(sc.metadata->>'original_employee_name', '') as original_name,
			COALESCE(u.username, '') as username
		FROM staff_cards sc
		LEFT JOIN users u ON sc.user_id = u.id
		WHERE sc.id = $1 AND sc.deleted_at IS NULL
	`

	var originalName string
	err = r.coreDB.GetPool().QueryRow(ctx, originalNameQuery, userID).Scan(
		&user.ID,
		&originalName,
		&user.Username,
	)
	if err == nil && originalName != "" {
		user.FullName = originalName
		return &user, nil
	}

	// 方法5：最后尝试直接从users表查询（兼容性处理）
	directQuery := `
		SELECT 
			id,
			username,
			full_name
		FROM users 
		WHERE id = $1 AND deleted_at IS NULL
	`

	err = r.coreDB.GetPool().QueryRow(ctx, directQuery, userID).Scan(
		&user.ID,
		&user.Username,
		&user.FullName,
	)
	if err == nil && user.FullName != "" {
		return &user, nil
	}

	// 如果所有方法都失败，返回错误而不是空数据
	return nil, fmt.Errorf("failed to get user info for ID %d: user not found in any lookup method", userID)
}

// GetPaymentMethodName 获取支付方式显示名称
func (r *EndOfDayReportRepository) GetPaymentMethodName(ctx context.Context, paymentMethod string) (string, error) {
	query := `
		SELECT 
			display_name
		FROM payment_methods 
		WHERE type = $1 AND deleted_at IS NULL
	`

	var displayName string
	err := r.paymentDB.GetPool().QueryRow(ctx, query, paymentMethod).Scan(&displayName)
	if err != nil {
		// 如果没有找到，返回默认的显示名称
		return r.getDefaultPaymentMethodName(paymentMethod), nil
	}

	return displayName, nil
}

// getDefaultPaymentMethodName 获取默认的支付方式显示名称
func (r *EndOfDayReportRepository) getDefaultPaymentMethodName(paymentMethod string) string {
	nameMap := map[string]string{
		"cash":    "CASH",
		"pvs":     "PVS",
		"cimb":    "CIMB",
		"bca":     "BCA",
		"mandiri": "Mandiri",
		"bri":     "BRI",
		"bni":     "BNI",
		"voucher": "Voucher",
		"b2b":     "B2B",
		"tera":    "TERA",
	}

	if name, exists := nameMap[strings.ToLower(paymentMethod)]; exists {
		return name
	}

	return strings.ToUpper(paymentMethod)
}

// GetPaymentMethods 获取所有支付方式配置
func (r *EndOfDayReportRepository) GetPaymentMethods(ctx context.Context) ([]repository.PaymentMethodInfo, error) {
	query := `
		SELECT 
			id,
			name,
			display_name,
			type,
			is_active
		FROM payment_methods 
		WHERE deleted_at IS NULL
		ORDER BY id
	`

	rows, err := r.paymentDB.GetPool().Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query payment methods: %w", err)
	}
	defer rows.Close()

	var methods []repository.PaymentMethodInfo
	for rows.Next() {
		var method repository.PaymentMethodInfo
		err := rows.Scan(
			&method.ID,
			&method.Name,
			&method.DisplayName,
			&method.Type,
			&method.IsActive,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan payment method: %w", err)
		}
		methods = append(methods, method)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating payment methods: %w", err)
	}

	return methods, nil
}

// GetPaymentsByStationAndTimeRange 获取指定站点和时间范围的支付数据
func (r *EndOfDayReportRepository) GetPaymentsByStationAndTimeRange(ctx context.Context, stationID int64, startTime, endTime time.Time) ([]repository.PaymentInfo, error) {
	query := `
		SELECT 
			op.id,
			o.order_number as order_id,
			op.payment_method,
			op.payment_type,
			op.amount,
			op.completed_at,
			op.status
		FROM order_payments op
		INNER JOIN orders o ON op.order_id = o.id
		WHERE o.station_id = $1
		  AND op.completed_at >= $2
		  AND op.completed_at < $3
		  AND op.status = 'completed'
		  AND o.status = 'completed'
		ORDER BY op.completed_at
	`

	rows, err := r.orderDB.GetPool().Query(ctx, query, stationID, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("failed to query payments: %w", err)
	}
	defer rows.Close()

	var payments []repository.PaymentInfo
	for rows.Next() {
		var payment repository.PaymentInfo
		err := rows.Scan(
			&payment.ID,
			&payment.OrderID,
			&payment.PaymentMethod,
			&payment.PaymentType,
			&payment.Amount,
			&payment.CompletedAt,
			&payment.Status,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan payment: %w", err)
		}
		payments = append(payments, payment)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating payments: %w", err)
	}

	return payments, nil
}

// GetOrderIDByTransactionID 根据交易ID获取订单ID
func (r *EndOfDayReportRepository) GetOrderIDByTransactionID(ctx context.Context, transactionID int64) (string, error) {
	query := `
		SELECT DISTINCT o.id::text
		FROM fuel_transactions ft
		INNER JOIN fuel_transaction_order_links ftol ON ft.id = ftol.fuel_transaction_id
		INNER JOIN orders o ON ftol.order_id = o.id
		WHERE ft.id = $1
		  AND ftol.status = 'active'
		  AND ft.deleted_at IS NULL
		  AND o.deleted_at IS NULL
		LIMIT 1
	`

	var orderID string
	err := r.orderDB.GetPool().QueryRow(ctx, query, transactionID).Scan(&orderID)
	if err != nil {
		if err == pgx.ErrNoRows {
			return "", fmt.Errorf("未找到交易ID %d 对应的订单", transactionID)
		}
		return "", fmt.Errorf("查询交易对应订单失败: %w", err)
	}

	return orderID, nil
}

// PaymentInfoForTransaction 交易支付信息结构
type PaymentInfoForTransaction struct {
	PaymentID     string // 修改为string类型以支持UUID
	PaymentMethod string
	PaymentAmount float64
	PaymentStatus string
}

// getPaymentInfoForTransaction 获取交易的支付信息（单独查询）
func (r *EndOfDayReportRepository) getPaymentInfoForTransaction(ctx context.Context, transactionID string) (*PaymentInfoForTransaction, error) {
	query := `
		SELECT 
			op.id as payment_id,
			op.payment_method,
			op.amount as payment_amount,
			op.status as payment_status
		FROM fuel_transactions ft
		INNER JOIN fuel_transaction_order_links ftol ON ft.id = ftol.fuel_transaction_id
		INNER JOIN orders o ON ftol.order_id = o.id
		INNER JOIN order_payments op ON o.id = op.order_id
		WHERE ft.id = $1
		  AND ftol.status = 'active'
		  AND op.status = 'completed'
		ORDER BY op.completed_at DESC
		LIMIT 1
	`

	var paymentInfo PaymentInfoForTransaction
	err := r.orderDB.GetPool().QueryRow(ctx, query, transactionID).Scan(
		&paymentInfo.PaymentID,
		&paymentInfo.PaymentMethod,
		&paymentInfo.PaymentAmount,
		&paymentInfo.PaymentStatus,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("未找到交易 %s 的支付信息", transactionID)
		}
		return nil, fmt.Errorf("查询交易支付信息失败: %w", err)
	}

	return &paymentInfo, nil
}
