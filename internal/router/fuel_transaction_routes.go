package router

import (
	"github.com/gin-gonic/gin"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/handler"
)

// SetupFuelTransactionRoutes 设置燃油交易路由
func SetupFuelTransactionRoutes(router *gin.RouterGroup, fuelTransactionHandler *handler.FuelTransactionHandler) {
	// 燃油交易相关路由
	fuelTransactionGroup := router.Group("/fuel-transactions")
	{
		// 获取完整燃油交易信息（优化版）
		fuelTransactionGroup.GET("/full-optimized", fuelTransactionHandler.GetFuelTransactionsFullOptimized)
		
		// 获取完整燃油交易信息（兼容原接口）
		fuelTransactionGroup.GET("/full", fuelTransactionHandler.GetFuelTransactionsFull)
		
		// 性能统计
		fuelTransactionGroup.GET("/performance-stats", fuelTransactionHandler.GetPerformanceStats)
	}

	// 兼容旧路由
	router.GET("/fuel-transactions-full", fuelTransactionHandler.GetFuelTransactionsFull)
	router.GET("/fuel-transactions-full-optimized", fuelTransactionHandler.GetFuelTransactionsFullOptimized)
}
