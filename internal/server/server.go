package server

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"os"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	echoLog "github.com/labstack/gommon/log"
	"github.com/sirupsen/logrus"
	echoSwagger "github.com/swaggo/echo-swagger"
	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"gitlab4.weicheche.cn/indo-bp/bos/config"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/api/handlers"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/api/router"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/cache"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/repository"
	bosPostgres "gitlab4.weicheche.cn/indo-bp/bos/internal/repository/postgres"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/service"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/websocket"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/infrastructure/database"
	orderPostgres "gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository/postgres"
	orderService "gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"

	promotionDB "gitlab4.weicheche.cn/indo-bp/promotion-service/pkg/database"
	"gitlab4.weicheche.cn/indo-bp/promotion-service/pkg/types"
	"gitlab4.weicheche.cn/indo-bp/promotion-service/sdk"

	// 油品服务相关引入
	oilCache "gitlab4.weicheche.cn/indo-bp/oil-service/pkg/oil/cache"
	"gitlab4.weicheche.cn/indo-bp/oil-service/pkg/oil/pricing"
	"gitlab4.weicheche.cn/indo-bp/oil-service/pkg/oil/product"
	oilPgRepo "gitlab4.weicheche.cn/indo-bp/oil-service/pkg/oil/repository/postgres"
	"gitlab4.weicheche.cn/indo-bp/oil-service/pkg/oil/strategy"
	oilSDK "gitlab4.weicheche.cn/indo-bp/oil-service/sdk/oil"

	// 支付服务SDK引入
	paymentsdk "gitlab4.weicheche.cn/indo-bp/payment-service/sdk"

	"gitlab4.weicheche.cn/indo-bp/pts2-go/pkg/ptsmanager"

	// TODO: 等待 bos-core 模块提供公开的路由注册接口
	// 目前该模块只提供服务层接口，没有公开的路由注册功能

	authConfig "gitlab4.weicheche.cn/indo-bp/bos-core/config"
	authRouter "gitlab4.weicheche.cn/indo-bp/bos-core/router"

	"time"

	_ "github.com/jackc/pgx/v4/stdlib" // PGX驱动
)

// Server 封装Echo服务器实例
type Server struct {
	echo         *echo.Echo
	cleanupFuncs []func() // 所有服务的清理函数列表
}

// SimpleAuthService 简单的认证服务实现
type SimpleAuthService struct{}

// ValidateToken 验证Token
func (s *SimpleAuthService) ValidateToken(token string) (*websocket.UserInfo, error) {
	// 简单实现：接受任何非空token
	if token == "" {
		return nil, fmt.Errorf("token不能为空")
	}

	return &websocket.UserInfo{
		UserID:    "user_" + token,
		Username:  "user_" + token,
		StationID: 1, // 默认站点ID
		Role:      "operator",
	}, nil
}

// New 创建新的服务器实例
func New(cfg *config.Config) (*Server, error) {
	e := echo.New()

	// 创建服务器实例，用于收集清理函数
	server := &Server{
		echo:         e,
		cleanupFuncs: make([]func(), 0),
	}

	// 配置详细日志级别
	e.Logger.SetLevel(echoLog.DEBUG)
	e.Logger.SetHeader(`${time_rfc3339} ${level} ${short_file}:${line} ${message}`)

	// 配置全局中间件
	setupMiddleware(e)

	// 配置验证器
	setupValidator(e)

	// 配置全局错误处理
	setupErrorHandler(e)

	// 配置Swagger文档
	setupSwagger(e)

	// 初始化数据库配置 - 使用统一的MultiDatabase管理
	ctx := context.Background()

	// 创建order数据库配置
	dbConfig := database.Config{
		Host:            cfg.BosDB.Host,
		Port:            cfg.BosDB.Port,
		User:            cfg.BosDB.User,
		Password:        cfg.BosDB.Password,
		Database:        cfg.BosDB.DBName,
		Schema:          cfg.Schemas.OrderSchema,
		SSLMode:         cfg.BosDB.SSLMode,
		MaxConns:        5,
		MinConns:        1,
		MaxConnLifetime: 30 * time.Minute,
		MaxConnIdleTime: 10 * time.Minute,
	}

	// 创建auth数据库配置 (core_service数据库，包含stations表)
	authDBConfig := database.Config{
		Host:            cfg.BosDB.Host,
		Port:            cfg.BosDB.Port,
		User:            cfg.BosDB.User,
		Password:        cfg.BosDB.Password,
		Database:        cfg.BosDB.DBName,
		Schema:          cfg.Schemas.AuthSchema,
		SSLMode:         cfg.BosDB.SSLMode,
		MaxConns:        5,
		MinConns:        1,
		MaxConnLifetime: 30 * time.Minute,
		MaxConnIdleTime: 10 * time.Minute,
	}

	// 创建payment数据库配置
	paymentDBConfig := database.Config{
		Host:            cfg.BosDB.Host,
		Port:            cfg.BosDB.Port,
		User:            cfg.BosDB.User,
		Password:        cfg.BosDB.Password,
		Database:        cfg.BosDB.DBName,
		Schema:          cfg.Schemas.PaymentSchema,
		SSLMode:         cfg.BosDB.SSLMode,
		MaxConns:        5,
		MinConns:        1,
		MaxConnLifetime: 30 * time.Minute,
		MaxConnIdleTime: 10 * time.Minute,
	}

	// 创建MultiDatabase实例
	multiDB := database.NewMultiDatabase(dbConfig, authDBConfig, paymentDBConfig)

	// 连接所有数据库
	if err := multiDB.Connect(ctx); err != nil {
		return nil, err
	}

	// 验证order数据库连接的schema设置
	e.Logger.Infof("验证order数据库连接schema设置...")
	orderDB := multiDB.GetOrderDB()
	if orderDB != nil {
		var currentSearchPath string
		err := orderDB.GetPool().QueryRow(ctx, "SELECT current_setting('search_path')").Scan(&currentSearchPath)
		if err != nil {
			e.Logger.Errorf("无法获取order数据库search_path: %v", err)
		} else {
			e.Logger.Infof("order数据库当前search_path: %s", currentSearchPath)
		}

		// 测试staff_cards表是否可访问
		var staffCardCount int
		err = orderDB.GetPool().QueryRow(ctx, "SELECT COUNT(*) FROM staff_cards").Scan(&staffCardCount)
		if err != nil {
			e.Logger.Errorf("无法访问staff_cards表: %v", err)
		} else {
			e.Logger.Infof("staff_cards表访问成功，共有 %d 条记录", staffCardCount)
		}
	}

	// 使用order数据库连接创建存储库
	// orderDB已经在上面定义过了

	// 创建存储库
	fuelTransactionRepo := orderPostgres.NewFuelTransactionRepository(orderDB)
	orderRepo := orderPostgres.NewOrderRepository(orderDB)
	fuelTransactionLinkRepo := orderPostgres.NewFuelTransactionOrderLinkRepository(orderDB)
	reportRepo := orderPostgres.NewReportRepository(orderDB)
	employeeRepo := orderPostgres.NewEmployeeRepository(orderDB)
	shiftRepo := orderPostgres.NewShiftRepository(orderDB)
	shiftSummaryRepo := orderPostgres.NewShiftSummaryRepository(orderDB)
	// 注意：employee shift和shift template repository需要sqlx.DB，暂时跳过初始化
	// employeeShiftRepo := orderPostgres.NewEmployeeShiftRepository(db)
	// shiftTemplateRepo := orderPostgres.NewShiftTemplateRepository(db)

	// 创建增强报表相关组件
	queryBuilder := orderPostgres.NewReportQueryBuilder()
	dataConverter := orderPostgres.NewReportDataConverterImpl()
	// 创建增强报表存储库
	enhancedReportRepo := orderPostgres.NewEnhancedReportRepository(
		orderDB,
		queryBuilder,
		dataConverter,
		reportRepo,
	)

	// 首先创建员工服务（在使用之前定义）
	employeeService := orderService.NewEmployeeService(employeeRepo)

	// 创建服务（暂时使用order-service的实现，稍后会替换为优化版本）
	fuelTransactionService := orderService.NewFuelTransactionService(
		fuelTransactionRepo,
		orderRepo,
		fuelTransactionLinkRepo,
	)

	// 添加服务初始化验证日志
	if fuelTransactionService == nil {
		e.Logger.Fatal("燃油交易服务初始化失败: 服务为nil")
	} else {
		e.Logger.Info("燃油交易服务初始化成功")
	}

	orderServiceInstance := orderService.NewOrderService(
		orderRepo,
		fuelTransactionRepo,
		fuelTransactionLinkRepo,
		shiftRepo,
	)

	// 创建班次日终报表仓库
	shiftEODRepo := orderPostgres.NewShiftEODRepository(orderDB)

	reportService := orderService.NewReportService(reportRepo, shiftEODRepo)

	// 创建 logrus.Logger 实例
	reportLogger := logrus.New()
	reportLogger.SetLevel(logrus.InfoLevel)

	// 创建增强报表服务
	enhancedReportService := orderService.NewEnhancedReportService(
		reportRepo,
		enhancedReportRepo,
		dataConverter,
		reportLogger,
	)

	// 初始化Staff Card服务
	e.Logger.Info("初始化Staff Card服务...")

	// 创建站点服务 (使用core_schema数据库连接)
	stationRepo := orderPostgres.NewStationRepository(multiDB.GetAuthDB())
	stationService := orderService.NewStationService(stationRepo)

	// 创建日终报表相关组件
	endOfDayReportRepo := bosPostgres.NewEndOfDayReportRepository(multiDB)

	shiftService := orderService.NewShiftService(
		shiftRepo,
		fuelTransactionRepo,
		shiftSummaryRepo,
		stationService,
	)

	// 注意：暂时跳过员工班次服务初始化，因为需要sqlx.DB
	// employeeShiftService := orderService.NewEmployeeShiftService(
	//	employeeShiftRepo,
	//	shiftRepo,
	//	shiftTemplateRepo,
	//	employeeRepo,
	//	stationService,
	// )

	// 创建班次员工服务
	shiftAttendantService := orderService.NewShiftAttendantService(
		shiftRepo,
		shiftEODRepo,
		fuelTransactionRepo,
		orderRepo,
		fuelTransactionLinkRepo,
		shiftService,
		employeeService,
		nil, // staffCardService将在下面初始化后更新
		stationService,
		nil, // paymentService将在下面初始化后更新
	)

	// 创建Staff Card存储库
	staffCardRepo := orderPostgres.NewStaffCardRepository(multiDB)

	// 创建Staff Card服务
	staffCardService := orderService.NewStaffCardService(staffCardRepo)

	// 注意：endOfDayReportService 将在 shiftAttendantService 重新创建后初始化

	// 创建我们的支付服务
	paymentServiceImpl := orderService.NewPaymentService(multiDB.GetPaymentDB())

	// 重新创建燃油交易服务，现在有了所有必要的依赖
	fuelTransactionService = orderService.NewFuelTransactionService(
		fuelTransactionRepo,
		orderRepo,
		fuelTransactionLinkRepo,
	)

	// 重新创建班次员工服务，现在有了所有必要的依赖
	shiftAttendantService = orderService.NewShiftAttendantService(
		shiftRepo,
		shiftEODRepo,
		fuelTransactionRepo,
		orderRepo,
		fuelTransactionLinkRepo,
		shiftService,
		employeeService,
		staffCardService,
		stationService,
		paymentServiceImpl,
	)

	// 现在创建日终报表服务 (重构版本) - 使用完整的 shiftAttendantService
	endOfDayReportService := service.NewEndOfDayReportService(
		shiftRepo,
		shiftAttendantService,
		stationService,
		endOfDayReportRepo,
	)

	// 创建BOS报表服务
	bosReportService := service.NewReportService(
		fuelTransactionRepo,
		stationService,
		shiftService,
	)

	// 创建Staff Card处理器
	staffCardHandler := handlers.NewStaffCardHandler(staffCardService)

	// 初始化促销服务SDK的数据库连接
	promotionDBConfig := promotionDB.GormConfig{
		Host:         cfg.BosDB.Host,
		Port:         cfg.BosDB.Port,
		User:         cfg.BosDB.User,
		Password:     cfg.BosDB.Password,
		DBName:       cfg.BosDB.DBName,
		SSLMode:      cfg.BosDB.SSLMode,
		Schema:       cfg.Schemas.PromotionSchema, // 设置促销服务的schema
		MaxIdleConns: 2,
		MaxOpenConns: 5,
	}

	promotionDBConn, err := promotionDB.NewDatabase(promotionDBConfig)
	if err != nil {
		return nil, err
	}

	// 修复promotion-service的search_path问题
	// 使用连接池回调确保每个新连接都正确设置search_path
	e.Logger.Info("配置promotion-service连接池回调以确保search_path正确性...")
	gormDB := promotionDBConn.GetDB()

	// 获取底层的 sql.DB 实例以配置连接池
	sqlDB, err := gormDB.DB()
	if err != nil {
		e.Logger.Errorf("获取promotion-service底层数据库实例失败: %v", err)
		return nil, fmt.Errorf("获取promotion-service底层数据库实例失败: %w", err)
	}

	// 配置连接池参数，确保连接定期更新
	sqlDB.SetMaxOpenConns(5)
	sqlDB.SetMaxIdleConns(2)
	sqlDB.SetConnMaxLifetime(20 * time.Minute) // 20分钟后强制重新创建连接
	sqlDB.SetConnMaxIdleTime(5 * time.Minute)  // 5分钟空闲后回收连接

	// 创建一个初始化函数，确保每次使用GORM时都设置正确的search_path
	setupSearchPath := func(db *gorm.DB) *gorm.DB {
		return db.Exec(fmt.Sprintf("SET search_path TO %s, pg_catalog", cfg.Schemas.PromotionSchema))
	}

	// 使用GORM的回调机制确保每次查询前都设置search_path
	gormDB.Callback().Query().Before("gorm:query").Register("set_search_path", func(db *gorm.DB) {
		db.Exec(fmt.Sprintf("SET search_path TO %s, pg_catalog", cfg.Schemas.PromotionSchema))
	})

	gormDB.Callback().Create().Before("gorm:create").Register("set_search_path", func(db *gorm.DB) {
		db.Exec(fmt.Sprintf("SET search_path TO %s, pg_catalog", cfg.Schemas.PromotionSchema))
	})

	gormDB.Callback().Update().Before("gorm:update").Register("set_search_path", func(db *gorm.DB) {
		db.Exec(fmt.Sprintf("SET search_path TO %s, pg_catalog", cfg.Schemas.PromotionSchema))
	})

	gormDB.Callback().Delete().Before("gorm:delete").Register("set_search_path", func(db *gorm.DB) {
		db.Exec(fmt.Sprintf("SET search_path TO %s, pg_catalog", cfg.Schemas.PromotionSchema))
	})

	// 初始设置和验证
	if err := setupSearchPath(gormDB).Error; err != nil {
		e.Logger.Errorf("设置promotion-service search_path失败: %v", err)
		return nil, fmt.Errorf("设置promotion-service search_path失败: %w", err)
	}

	// 验证search_path设置
	var currentSearchPath string
	if err := gormDB.Raw("SELECT current_setting('search_path')").Scan(&currentSearchPath).Error; err != nil {
		e.Logger.Errorf("无法验证promotion-service search_path: %v", err)
	} else {
		e.Logger.Infof("promotion-service search_path设置成功: %s", currentSearchPath)
	}

	// 测试promotion表是否可访问
	var promotionCount int64
	if err := gormDB.Raw("SELECT COUNT(*) FROM promotion").Scan(&promotionCount).Error; err != nil {
		e.Logger.Errorf("无法访问promotion表: %v", err)
		return nil, fmt.Errorf("无法访问promotion表: %w", err)
	}
	e.Logger.Infof("promotion表访问成功，共有 %d 条记录", promotionCount)
	e.Logger.Info("promotion-service连接池配置完成，包含自动search_path设置机制")

	// 初始化SDK
	sdkConfig := types.Config{
		DBConnection: promotionDBConn, // 共享数据库连接
	}

	promotionSDK, err := sdk.New(sdkConfig)
	if err != nil {
		return nil, err
	}

	// 初始化油品服务
	e.Logger.Info("初始化油品服务...")

	// 配置油品服务的数据库连接
	oilDBConfig := promotionDB.GormConfig{
		Host:         cfg.BosDB.Host,
		Port:         cfg.BosDB.Port,
		User:         cfg.BosDB.User,
		Password:     cfg.BosDB.Password,
		DBName:       cfg.BosDB.DBName,
		SSLMode:      cfg.BosDB.SSLMode,
		Schema:       cfg.Schemas.OilSchema, // 设置油品服务的schema
		MaxIdleConns: 5,
		MaxOpenConns: 10,
	}

	oilDB, err := promotionDB.NewDatabase(oilDBConfig)
	if err != nil {
		return nil, err
	}

	// 创建油品服务组件
	oilProductRepo := oilPgRepo.NewPostgresOilProductRepository(oilDB.GetDB())
	oilPriceRepo := oilPgRepo.NewPostgresOilPriceRepository(oilDB.GetDB())

	// 创建策略仓库
	oilStrategyRepo := oilPgRepo.NewPostgresPricingStrategyRepository(oilDB.GetDB())

	// 创建价格缓存
	oilPriceCache := oilCache.NewMemoryOilPriceCache()

	// 创建油品服务
	productService := product.NewOilProductService(oilProductRepo, oilPriceRepo)
	priceService := pricing.NewOilPriceService(oilPriceRepo, oilPriceCache)

	// 创建价格计算器
	priceCalculator := strategy.NewPriceCalculator()

	// 创建策略服务
	strategyService := strategy.NewPricingStrategyService(oilStrategyRepo, oilPriceRepo, priceCalculator)

	// 创建油品SDK
	oilService := oilSDK.NewOilSDK(productService, priceService, strategyService)

	// 创建油品处理器
	oilHandler := handlers.NewOilHandler(oilService)

	// 创建油品价格处理器
	oilPriceHandler := handlers.NewOilPriceHandler(gormDB)

	// 初始化PTS2设备相关服务
	e.Logger.Info("初始化PTS2设备相关服务...")

	// 创建PTS2日志记录器
	pts2Logger := ptsmanager.NewLogger(ptsmanager.LogLevelDebug, os.Stdout, "[PTS2] ")

	// 连接PTS2设备数据库
	pts2DB, err := sql.Open("pgx", cfg.GetFCCDSN())
	if err != nil {
		return nil, err
	}

	// 测试数据库连接
	if err := pts2DB.Ping(); err != nil {
		return nil, err
	}
	e.Logger.Info("PTS2设备数据库连接成功")

	// 创建PTS2设备存储
	pts2DeviceStore := ptsmanager.NewDBDeviceStore(pts2DB, pts2Logger)

	// 创建PTS2事件分发器
	pts2Dispatcher := ptsmanager.NewDefaultEventDispatcher(pts2Logger)

	// 创建PTS2处理器工厂
	pts2ProcessorFactory := ptsmanager.NewAPIProcessorFactory(pts2Logger, "http://localhost:8080/api/v1/fuel-transactions")

	// 创建PTS2设备管理器
	pts2Manager := ptsmanager.NewManager(pts2DeviceStore, pts2Dispatcher, pts2ProcessorFactory, pts2Logger)

	// 创建PTS2设备处理器
	pts2DeviceHandler := handlers.NewPTS2DeviceHandler(pts2Manager, pts2Logger, pts2DeviceStore)
	pts2PumpHandler := handlers.NewPTS2PumpHandler(pts2Manager, pts2Logger)

	// 创建客户服务处理器
	e.Logger.Info("初始化客户服务处理器...")
	customerHandler, customerCleanup, err := handlers.NewCustomerHandler(
		context.Background(),
		cfg.GetCustomerDSN(),
	)
	if err != nil {
		e.Logger.Errorf("创建客户服务处理器失败: %v", err)
		return nil, err
	}
	// 添加到清理函数列表
	if customerCleanup != nil {
		server.cleanupFuncs = append(server.cleanupFuncs, customerCleanup)
	}

	// 创建库存服务处理器
	e.Logger.Info("初始化库存服务处理器...")
	inventoryHandler, inventoryCleanup, err := handlers.NewInventoryHandler(
		context.Background(),
		cfg.GetInventoryDSN(),
	)
	if err != nil {
		e.Logger.Errorf("创建库存服务处理器失败: %v", err)
		return nil, err
	}
	// 添加到清理函数列表
	if inventoryCleanup != nil {
		server.cleanupFuncs = append(server.cleanupFuncs, inventoryCleanup)
	}

	// 创建支付服务
	e.Logger.Info("初始化支付服务...")

	// 创建支付服务数据库配置（与其他服务配置格式一致）
	paymentSDKDBConfig := paymentsdk.GormConfig{
		Host:         cfg.BosDB.Host,
		Port:         cfg.BosDB.Port,
		User:         cfg.BosDB.User,
		Password:     cfg.BosDB.Password,
		DBName:       cfg.BosDB.DBName,
		SSLMode:      cfg.BosDB.SSLMode,
		Schema:       cfg.Schemas.PaymentSchema, // 添加schema配置
		MaxIdleConns: 2,
		MaxOpenConns: 5,
	}

	// 创建支付服务配置
	paymentConfig := paymentsdk.NewConfigFromGormConfig(paymentSDKDBConfig,
		paymentsdk.WithLogLevel("info"),
		paymentsdk.WithEvents(true),
	)

	// 初始化支付服务
	paymentService, paymentCleanup, err := paymentsdk.NewPaymentService(ctx, paymentConfig)
	if err != nil {
		e.Logger.Errorf("初始化支付服务失败: %v", err)
		return nil, err
	}
	// 添加到清理函数列表
	if paymentCleanup != nil {
		server.cleanupFuncs = append(server.cleanupFuncs, paymentCleanup)
	}

	// 创建支付处理器
	paymentHandler := handlers.NewPaymentHandler(paymentService)

	// 创建WebSocket服务
	e.Logger.Info("初始化WebSocket服务...")

	// 创建WebSocket服务配置
	wsConfig := &websocket.ServiceConfig{
		Hub:          websocket.DefaultHubConfig(),
		EventManager: websocket.DefaultEventManagerConfig(),
	}

	// 创建简单的认证服务（暂时使用空实现）
	authService := &SimpleAuthService{}

	wsService := websocket.NewService(authService, wsConfig)

	// 添加FCC事件源
	e.Logger.Info("添加FCC事件源...")
	fccClient := websocket.NewHTTPFCCClient()
	fccConfigs := []websocket.FCCConfig{
		{
			ID:         "fcc1",
			PrimaryURL: "http://localhost:8081/api",
			HealthURL:  "http://localhost:8081/health",
			DeviceIDs:  []string{"device1", "device2"},
		},
	}
	fccSource := websocket.NewFCCEventSource("fcc-main", fccConfigs, fccClient)
	if err := wsService.AddEventSource(fccSource); err != nil {
		e.Logger.Errorf("添加FCC事件源失败: %v", err)
		return nil, err
	}
	e.Logger.Info("FCC事件源添加成功")

	// 添加BOS事件源
	e.Logger.Info("添加BOS事件源...")
	bosSource := websocket.NewBOSEventSource("bos-main")
	if err := wsService.AddEventSource(bosSource); err != nil {
		e.Logger.Errorf("添加BOS事件源失败: %v", err)
		return nil, err
	}
	e.Logger.Info("BOS事件源添加成功")

	if err := wsService.Start(); err != nil {
		e.Logger.Errorf("启动WebSocket服务失败: %v", err)
		return nil, err
	}
	e.Logger.Info("WebSocket服务启动成功")

	// 添加WebSocket服务清理函数
	server.cleanupFuncs = append(server.cleanupFuncs, func() {
		if err := wsService.Stop(); err != nil {
			e.Logger.Errorf("停止WebSocket服务失败: %v", err)
		}
	})

	// 创建Dashboard相关组件 (提前创建用于WebSocket事件触发器)
	dashboardRepo := repository.NewDashboardRepository(orderDB)
	dashboardService := service.NewDashboardService(dashboardRepo)

	// 创建WebSocket处理器
	wsHandler := handlers.NewWebSocketHandler(wsService)

	// 创建WebSocket事件触发器
	wsEventTrigger := websocket.NewWebSocketEventTrigger(wsService, dashboardService)

	// 创建处理器
	// 创建燃油交易缓存
	fuelTransactionCache := cache.NewMemoryFuelTransactionCache(5 * time.Minute)

	// 启动缓存清理协程
	go fuelTransactionCache.StartCleanupRoutine(ctx)

	fuelTransactionHandler := handlers.NewFuelTransactionHandler(fuelTransactionService, stationService, staffCardService, orderRepo, fuelTransactionCache, cfg)
	// 设置WebSocket事件触发器
	fuelTransactionHandler.SetWebSocketEventTrigger(wsEventTrigger)

	// 启动仪表板更新调度器
	dashboardScheduler := websocket.NewDashboardUpdateScheduler(wsEventTrigger, []int64{1}, 30*time.Second) // 每30秒更新一次
	dashboardScheduler.Start()

	// 添加调度器清理函数
	server.cleanupFuncs = append(server.cleanupFuncs, func() {
		dashboardScheduler.Stop()
	})
	orderHandler := handlers.NewOrderHandlerWithDB(orderServiceInstance, fuelTransactionService, paymentService, shiftService, orderDB.GetPool(), cfg)
	reportHandler := handlers.NewReportHandlerWithBOS(reportService, bosReportService)
	enhancedReportHandler := handlers.NewEnhancedReportHandler(enhancedReportService)
	// 创建日终报表处理器
	endOfDayReportHandler := handlers.NewEndOfDayReportHandler(endOfDayReportService)
	employeeHandler := handlers.NewEmployeeHandler(employeeService)
	// 创建班次汇总服务
	shiftSummaryService := orderService.NewShiftSummaryService(shiftSummaryRepo, shiftRepo)

	shiftHandler := handlers.NewShiftHandler(shiftService, shiftAttendantService, shiftSummaryService)
	promotionManagerHandler := handlers.NewPromotionManagerHandler(promotionSDK)
	promotionCalcHandler := handlers.NewPromotionCalcHandler(promotionSDK)
	schemaHandler := handlers.NewSchemaHandler()

	// 创建Dashboard处理器 (使用已创建的dashboardService)
	dashboardHandler := handlers.NewDashboardHandler(dashboardService)

	// 创建路由管理并注册路由
	r := router.NewRouter(
		fuelTransactionHandler,
		orderHandler,
		reportHandler,
		enhancedReportHandler,
		endOfDayReportHandler,
		employeeHandler,
		shiftHandler,
		promotionManagerHandler,
		promotionCalcHandler,
		schemaHandler,
		oilHandler,
		oilPriceHandler,
		pts2DeviceHandler,
		pts2PumpHandler,
		customerHandler,
		inventoryHandler,
		paymentHandler,
		staffCardHandler,
		dashboardHandler,
	)

	// 注册API路由并启动调度器
	e.Logger.Info("注册API路由...")
	r.RegisterRoutes(e)

	// 注册WebSocket路由
	e.Logger.Info("注册WebSocket路由...")
	e.GET("/ws", wsHandler.HandleWebSocketConnection)

	// 注册WebSocket管理API路由
	wsAPI := e.Group("/api/v1/ws")
	wsAPI.GET("/stats", wsHandler.GetWebSocketStats)
	wsAPI.GET("/clients", wsHandler.GetConnectedClients)
	wsAPI.POST("/broadcast", wsHandler.BroadcastMessage)
	wsAPI.POST("/broadcast/station/:stationId", wsHandler.BroadcastToStation)
	wsAPI.GET("/test", wsHandler.TestWebSocketConnection)

	// 价格调整调度器已移除 - 简化架构

	// 集成认证模块
	e.Logger.Info("初始化认证模块...")

	// 创建认证模块的数据库连接（使用GORM）
	authDBDSN := cfg.GetAuthDSN() // 需要在config中添加此方法
	authGormDB, err := gorm.Open(postgres.Open(authDBDSN), &gorm.Config{})
	if err != nil {
		e.Logger.Errorf("认证模块数据库连接失败: %v", err)
		return nil, err
	}
	e.Logger.Info("认证模块数据库连接成功")

	// 创建zap日志记录器（认证模块需要）
	zapLogger, err := zap.NewProduction()
	if err != nil {
		e.Logger.Errorf("创建zap日志记录器失败: %v", err)
		return nil, err
	}

	authConfig := &authConfig.Config{
		JWT: authConfig.JWTConfig{
			SecretKey:        cfg.Auth.JWTSecret,
			ExpiresIn:        time.Duration(cfg.Auth.JWTExpirationHours) * time.Hour,
			RefreshExpiresIn: time.Duration(cfg.Auth.RefreshExpirationDays) * 24 * time.Hour,
			Issuer:           "bos-core",
		},
	}

	// 注册认证模块路由
	authExports := authRouter.RegisterRoutes(e, authRouter.ModuleOptions{
		DB:     authGormDB,
		Logger: zapLogger,
		Config: authConfig,
	})

	e.Logger.Info("认证模块集成完成")

	// 设置认证中间件
	setupAuthMiddleware(e, authExports, zapLogger)

	return server, nil
}

// setupMiddleware 配置全局中间件
func setupMiddleware(e *echo.Echo) {
	// 日志中间件
	e.Use(middleware.LoggerWithConfig(middleware.LoggerConfig{
		Format: `{"time":"${time_rfc3339_nano}","id":"${id}","remote_ip":"${remote_ip}",` +
			`"host":"${host}","method":"${method}","uri":"${uri}","user_agent":"${user_agent}",` +
			`"status":${status},"error":"${error}","latency":${latency},"latency_human":"${latency_human}"` +
			`,"bytes_in":${bytes_in},"bytes_out":${bytes_out},"request":"${query}","body":"${body}"}` + "\n",
		CustomTimeFormat: "2006-01-02 15:04:05.00000",
		Output:           os.Stdout,
	}))

	// 恢复中间件
	e.Use(middleware.Recover())

	// CORS中间件
	e.Use(middleware.CORS())
}

// CustomValidator 自定义验证器
type CustomValidator struct {
	validator *validator.Validate
}

// Validate 验证方法
func (cv *CustomValidator) Validate(i interface{}) error {
	if err := cv.validator.Struct(i); err != nil {
		// 提供更详细的验证错误信息
		if validationErrors, ok := err.(validator.ValidationErrors); ok {
			var errorMessages []string
			for _, validationError := range validationErrors {
				errorMessages = append(errorMessages, fmt.Sprintf("Field '%s' failed validation: %s", validationError.Field(), validationError.Tag()))
			}
			return fmt.Errorf("validation failed: %s", strings.Join(errorMessages, ", "))
		}
		return err
	}
	return nil
}

// setupValidator 配置验证器
func setupValidator(e *echo.Echo) {
	e.Validator = &CustomValidator{validator: validator.New()}
}

// setupErrorHandler 配置全局错误处理
func setupErrorHandler(e *echo.Echo) {
	e.HTTPErrorHandler = func(err error, c echo.Context) {
		code := http.StatusInternalServerError
		if he, ok := err.(*echo.HTTPError); ok {
			code = he.Code
		}

		// 记录详细错误信息
		e.Logger.Errorf("HTTP错误: %v, 路径: %s, 方法: %s", err, c.Request().URL.Path, c.Request().Method)

		c.JSON(code, map[string]string{
			"error": err.Error(),
		})
	}
}

// setupSwagger 配置Swagger文档
func setupSwagger(e *echo.Echo) {
	// 提供swagger-ui界面
	e.GET("/swagger/*", echoSwagger.EchoWrapHandler(
		echoSwagger.URL("/swagger/doc.json"),
		echoSwagger.DocExpansion("none"),
		echoSwagger.DomID("swagger-ui"),
	))

	// 添加静态文件服务，提供swagger文件
	e.Static("/docs", "./docs")

	// 添加WebSocket演示页面静态文件服务
	e.Static("/static", "./web")

	// 添加特定文件的路由以确保正确的MIME类型
	e.GET("/static/websocket-demo.html", func(c echo.Context) error {
		c.Response().Header().Set("Content-Type", "text/html; charset=utf-8")
		return c.File("./web/websocket-demo.html")
	})

	// 添加测试客户端页面路由
	e.GET("/static/test-websocket-client.html", func(c echo.Context) error {
		c.Response().Header().Set("Content-Type", "text/html; charset=utf-8")
		return c.File("./web/test-websocket-client.html")
	})

	// 添加访问指南页面路由
	e.GET("/static/websocket-access-guide.html", func(c echo.Context) error {
		c.Response().Header().Set("Content-Type", "text/html; charset=utf-8")
		return c.File("./web/websocket-access-guide.html")
	})

	// 添加根路径重定向到访问指南
	e.GET("/", func(c echo.Context) error {
		return c.Redirect(302, "/static/websocket-access-guide.html")
	})

	e.GET("/static/websocket-client.js", func(c echo.Context) error {
		c.Response().Header().Set("Content-Type", "application/javascript; charset=utf-8")
		return c.File("./web/websocket-client.js")
	})

	e.GET("/static/websocket-integration.js", func(c echo.Context) error {
		c.Response().Header().Set("Content-Type", "application/javascript; charset=utf-8")
		return c.File("./web/websocket-integration.js")
	})

	e.GET("/static/websocket-integration-v2.js", func(c echo.Context) error {
		c.Response().Header().Set("Content-Type", "application/javascript; charset=utf-8")
		return c.File("./web/websocket-integration-v2.js")
	})

	e.GET("/static/debug-websocket.html", func(c echo.Context) error {
		c.Response().Header().Set("Content-Type", "text/html; charset=utf-8")
		return c.File("./web/debug-websocket.html")
	})

	e.GET("/static/test-websocket-client.html", func(c echo.Context) error {
		c.Response().Header().Set("Content-Type", "text/html; charset=utf-8")
		return c.File("./web/test-websocket-client.html")
	})

	// 添加API路由来服务swagger JSON
	e.GET("/swagger/doc.json", func(c echo.Context) error {
		return c.File("./docs/swagger.json")
	})
}

// Start 启动服务器
func (s *Server) Start(address string) error {
	s.echo.Logger.Infof("启动服务器，监听地址: %s", address)
	return s.echo.Start(address)
}

// Shutdown 优雅关闭服务器
func (s *Server) Shutdown(ctx context.Context) error {
	// 在关闭Echo服务器之前，先清理各服务资源
	// 反向执行清理函数，确保后初始化的服务先清理
	for i := len(s.cleanupFuncs) - 1; i >= 0; i-- {
		cleanupFunc := s.cleanupFuncs[i]
		if cleanupFunc != nil {
			cleanupFunc()
		}
	}

	return s.echo.Shutdown(ctx)
}

// GetEcho 获取Echo实例（用于需要直接访问Echo实例的场景）
func (s *Server) GetEcho() *echo.Echo {
	return s.echo
}

// setupAuthMiddleware 设置认证中间件
func setupAuthMiddleware(e *echo.Echo, authExports interface{}, logger *zap.Logger) {
	// 注意：BOS Core的认证中间件已经在RegisterRoutes中应用到了BOS Core的路由上
	// 这里我们不需要额外设置，因为：
	// 1. BOS Core的API路由（如/users, /roles等）已经有认证保护
	// 2. BOS项目的业务API路由（如dashboard, reports等）目前不需要额外的认证
	// 3. 前端通过localStorage中的accessToken进行认证

	logger.Info("认证中间件设置完成 - BOS Core路由已受保护")
}
