package dto

import (
	"time"
)

// BPFuelConfig BP燃油促销配置
type BPFuelConfig struct {
	Name          string        `json:"name" validate:"required,min=1,max=100" example:"BP燃油周一促销"`
	Description   string        `json:"description,omitempty" validate:"max=500" example:"BP加油站燃油促销活动"`
	FuelTypes     []string      `json:"fuel_types" validate:"required,min=1,dive,oneof=fuel_BP_92 fuel_ULTIMATE fuel_ULTIMATE_DIESEL" example:"fuel_BP_92,fuel_ULTIMATE"`
	ExcludeB2B    bool          `json:"exclude_b2b" example:"true"`
	BaseDiscount  float64       `json:"base_discount" validate:"min=0" example:"1000.0"`
	GradientTiers []GradientTier `json:"gradient_tiers,omitempty" validate:"dive"`
	TimeSlots     []TimeSlot    `json:"time_slots,omitempty" validate:"dive"`
	Limits        *PromotionLimits `json:"limits,omitempty"`
	StartTime     time.Time     `json:"start_time" validate:"required" example:"2024-01-01T09:00:00Z"`
	EndTime       time.Time     `json:"end_time" validate:"required,gtfield=StartTime" example:"2024-12-31T10:00:00Z"`
	Priority      int           `json:"priority,omitempty" validate:"min=0,max=100" example:"10"`
}

// GradientTier 梯度档次配置
type GradientTier struct {
	Level          int      `json:"level" validate:"required,min=1" example:"1"`
	MinVolume      float64  `json:"min_volume" validate:"required,gt=0" example:"3.0"`
	MaxVolume      *float64 `json:"max_volume,omitempty" validate:"omitempty,gtfield=MinVolume" example:"27.9"`
	DiscountAmount float64  `json:"discount_amount" validate:"required,gt=0" example:"1000.0"`
	FuelTypes      []string `json:"fuel_types" validate:"required,min=1,dive,oneof=fuel_BP_92 fuel_ULTIMATE fuel_ULTIMATE_DIESEL" example:"fuel_BP_92"`
	Description    string   `json:"description,omitempty" validate:"max=200" example:"消费3.0升以上享受1000印尼盾折扣"`
}

// TimeSlot 时间段配置
type TimeSlot struct {
	Type      string `json:"type" validate:"required,oneof=DAILY WEEKLY MONTHLY" example:"WEEKLY"`
	StartHour int    `json:"start_hour" validate:"min=0,max=23" example:"9"`
	EndHour   int    `json:"end_hour" validate:"min=1,max=24,gtfield=StartHour" example:"10"`
	WeekDays  []int  `json:"week_days,omitempty" validate:"dive,min=1,max=7" example:"1"`
	MonthDays []int  `json:"month_days,omitempty" validate:"dive,min=1,max=31"`
}

// PromotionLimits 促销限制配置
type PromotionLimits struct {
	MaxDiscountPerOrder float64 `json:"max_discount_per_order,omitempty" validate:"omitempty,gt=0" example:"50000.0"`
	MaxDiscountPerUser  float64 `json:"max_discount_per_user,omitempty" validate:"omitempty,gt=0" example:"100000.0"`
	MaxUseCount         int     `json:"max_use_count,omitempty" validate:"omitempty,gt=0" example:"1000"`
	MaxPerUser          int     `json:"max_per_user,omitempty" validate:"omitempty,gt=0" example:"5"`
	ValidFrom           *time.Time `json:"valid_from,omitempty"`
	ValidTo             *time.Time `json:"valid_to,omitempty"`
}

// B2BExclusionConfig B2B排斥促销配置
type B2BExclusionConfig struct {
	Name           string          `json:"name" validate:"required,min=1,max=100" example:"非B2B用户专享促销"`
	Description    string          `json:"description,omitempty" validate:"max=500" example:"排斥B2B用户的促销活动"`
	DiscountAmount float64         `json:"discount_amount" validate:"required,gt=0" example:"3000.0"`
	DiscountType   string          `json:"discount_type" validate:"required,oneof=FIXED_AMOUNT PERCENTAGE" example:"FIXED_AMOUNT"`
	ExcludedUserTypes []string     `json:"excluded_user_types" validate:"required,min=1" example:"b2b,enterprise,B2B"`
	TargetUserTypes   []string     `json:"target_user_types,omitempty" example:"general,member"`
	MinOrderAmount    *float64     `json:"min_order_amount,omitempty" validate:"omitempty,gt=0" example:"50000.0"`
	Limits            *PromotionLimits `json:"limits,omitempty"`
	StartTime         time.Time    `json:"start_time" validate:"required"`
	EndTime           time.Time    `json:"end_time" validate:"required,gtfield=StartTime"`
	Priority          int          `json:"priority,omitempty" validate:"min=0,max=100" example:"20"`
}

// GradientConfig 梯度折扣配置
type GradientConfig struct {
	Name         string        `json:"name" validate:"required,min=1,max=100" example:"燃油梯度折扣促销"`
	Description  string        `json:"description,omitempty" validate:"max=500" example:"不同消费档次享受不同折扣"`
	BaseDiscount float64       `json:"base_discount" validate:"min=0" example:"500.0"`
	Tiers        []GradientTier `json:"tiers" validate:"required,min=1,dive"`
	ThresholdType string       `json:"threshold_type" validate:"required,oneof=QUANTITY AMOUNT" example:"QUANTITY"`
	ApplicableProducts []string `json:"applicable_products,omitempty" validate:"dive"`
	Limits       *PromotionLimits `json:"limits,omitempty"`
	StartTime    time.Time     `json:"start_time" validate:"required"`
	EndTime      time.Time     `json:"end_time" validate:"required,gtfield=StartTime"`
	Priority     int           `json:"priority,omitempty" validate:"min=0,max=100" example:"15"`
}

// MemberOnlyConfig 会员专属促销配置
type MemberOnlyConfig struct {
	Name           string          `json:"name" validate:"required,min=1,max=100" example:"会员专享折扣"`
	Description    string          `json:"description,omitempty" validate:"max=500"`
	DiscountAmount float64         `json:"discount_amount" validate:"required,gt=0" example:"5000.0"`
	DiscountType   string          `json:"discount_type" validate:"required,oneof=FIXED_AMOUNT PERCENTAGE" example:"FIXED_AMOUNT"`
	MemberLevels   []string        `json:"member_levels,omitempty" example:"VIP,GOLD,SILVER"`
	MemberTags     []string        `json:"member_tags,omitempty" example:"loyal_customer,premium"`
	MinOrderAmount *float64        `json:"min_order_amount,omitempty" validate:"omitempty,gt=0"`
	Limits         *PromotionLimits `json:"limits,omitempty"`
	StartTime      time.Time       `json:"start_time" validate:"required"`
	EndTime        time.Time       `json:"end_time" validate:"required,gtfield=StartTime"`
	Priority       int             `json:"priority,omitempty" validate:"min=0,max=100" example:"25"`
}

// VolumeDiscountConfig 量级折扣配置
type VolumeDiscountConfig struct {
	Name          string          `json:"name" validate:"required,min=1,max=100" example:"大量购买折扣"`
	Description   string          `json:"description,omitempty" validate:"max=500"`
	MinVolume     float64         `json:"min_volume" validate:"required,gt=0" example:"25.0"`
	DiscountType  string          `json:"discount_type" validate:"required,oneof=FIXED_AMOUNT PERCENTAGE FREE_VOLUME" example:"FREE_VOLUME"`
	DiscountValue float64         `json:"discount_value" validate:"required,gt=0" example:"1.0"`
	ProductTypes  []string        `json:"product_types" validate:"required,min=1" example:"fuel"`
	Limits        *PromotionLimits `json:"limits,omitempty"`
	StartTime     time.Time       `json:"start_time" validate:"required"`
	EndTime       time.Time       `json:"end_time" validate:"required,gtfield=StartTime"`
	Priority      int             `json:"priority,omitempty" validate:"min=0,max=100" example:"30"`
}

// DefaultConfigValues 默认配置值
var DefaultConfigValues = struct {
	Priority      int
	BaseDiscount  float64
	TimeSlotType  string
	DiscountType  string
	ThresholdType string
}{
	Priority:      10,
	BaseDiscount:  1000.0,
	TimeSlotType:  "WEEKLY",
	DiscountType:  "FIXED_AMOUNT", 
	ThresholdType: "QUANTITY",
} 