package dto

import (
	"time"
)

// CreatePromotionRequest 通用促销创建请求
type CreatePromotionRequest struct {
	Name        string    `json:"name" validate:"required,min=1,max=100" example:"BP燃油促销"`
	Description string    `json:"description,omitempty" validate:"max=500" example:"BP加油站燃油促销活动"`
	Type        string    `json:"type" validate:"required,oneof=FUEL_PROMOTION B2B_EXCLUSION GRADIENT_DISCOUNT" example:"FUEL_PROMOTION"`
	StartTime   time.Time `json:"start_time" validate:"required" example:"2024-01-01T09:00:00Z"`
	EndTime     time.Time `json:"end_time" validate:"required,gtfield=StartTime" example:"2024-12-31T10:00:00Z"`
	Priority    int       `json:"priority,omitempty" validate:"min=0,max=100" example:"10"`
}

// UpdatePromotionRequest 促销更新请求
type UpdatePromotionRequest struct {
	Name        *string    `json:"name,omitempty" validate:"omitempty,min=1,max=100" example:"BP燃油促销-更新"`
	Description *string    `json:"description,omitempty" validate:"omitempty,max=500"`
	StartTime   *time.Time `json:"start_time,omitempty"`
	EndTime     *time.Time `json:"end_time,omitempty"`
	Priority    *int       `json:"priority,omitempty" validate:"omitempty,min=0,max=100"`
	Status      *string    `json:"status,omitempty" validate:"omitempty,oneof=ACTIVE INACTIVE DRAFT"`
}

// CalculateRequest 折扣计算请求
type CalculateRequest struct {
	OrderID     string      `json:"order_id" validate:"required" example:"order_001"`
	UserID      string      `json:"user_id" validate:"required" example:"user_001"`
	TotalAmount float64     `json:"total_amount" validate:"required,gt=0" example:"200000.0"`
	Items       []OrderItem `json:"items" validate:"required,min=1,dive"`
	CouponCode  string      `json:"coupon_code,omitempty" example:"FUEL2024"`
}

// OrderItem 订单项结构
type OrderItem struct {
	ProductID   string   `json:"product_id" validate:"required" example:"fuel_BP_92"`
	Quantity    float64  `json:"quantity" validate:"required,gt=0" example:"30.125"`
	UnitPrice   float64  `json:"unit_price" validate:"required,gt=0" example:"15000.0"`
	CategoryIDs []string `json:"category_ids,omitempty" validate:"dive,required" example:"fuel,BP92"`
	SKU         string   `json:"sku,omitempty" example:"BP92-FUEL-001"`
}

// PromotionFilter 促销筛选条件
type PromotionFilter struct {
	Status    string     `json:"status,omitempty" validate:"omitempty,oneof=ACTIVE INACTIVE DRAFT" example:"ACTIVE"`
	Type      string     `json:"type,omitempty" validate:"omitempty,oneof=FUEL_PROMOTION B2B_EXCLUSION GRADIENT_DISCOUNT"`
	Search    string     `json:"search,omitempty" validate:"omitempty,max=100" example:"BP燃油"`
	Page      int        `json:"page,omitempty" validate:"omitempty,min=1" example:"1"`
	Size      int        `json:"size,omitempty" validate:"omitempty,min=1,max=100" example:"10"`
	StartFrom *time.Time `json:"start_from,omitempty"`
	StartTo   *time.Time `json:"start_to,omitempty"`
}

// DeletePromotionRequest 删除促销请求
type DeletePromotionRequest struct {
	ID string `json:"id" validate:"required,uuid" example:"123e4567-e89b-12d3-a456-************"`
}

// GetPromotionRequest 获取促销详情请求
type GetPromotionRequest struct {
	ID string `json:"id" validate:"required,uuid" example:"123e4567-e89b-12d3-a456-************"`
}
