package dto

import (
	"time"

	"github.com/google/uuid"
)

// BaseResponse 基础响应结构
type BaseResponse struct {
	Success   bool      `json:"success" example:"true"`
	Message   string    `json:"message" example:"操作成功"`
	Code      string    `json:"code,omitempty" example:"SUCCESS"`
	Timestamp time.Time `json:"timestamp" example:"2024-01-01T10:00:00Z"`
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	BaseResponse
	Error   string      `json:"error" example:"参数验证失败"`
	Details interface{} `json:"details,omitempty"`
}

// PaginationInfo 分页信息
type PaginationInfo struct {
	Page       int   `json:"page" example:"1"`
	Size       int   `json:"size" example:"10"`
	Total      int64 `json:"total" example:"100"`
	TotalPages int   `json:"total_pages" example:"10"`
	HasNext    bool  `json:"has_next" example:"true"`
	HasPrev    bool  `json:"has_prev" example:"false"`
}

// PromotionResponse 促销操作响应
type PromotionResponse struct {
	BaseResponse
	Data PromotionData `json:"data"`
}

// PromotionData 促销数据
type PromotionData struct {
	ID          uuid.UUID `json:"id" example:"123e4567-e89b-12d3-a456-************"`
	Name        string    `json:"name" example:"BP燃油促销"`
	Description string    `json:"description" example:"BP加油站燃油促销活动"`
	Type        string    `json:"type" example:"FUEL_PROMOTION"`
	Status      string    `json:"status" example:"ACTIVE"`
	StartTime   time.Time `json:"start_time" example:"2024-01-01T09:00:00Z"`
	EndTime     time.Time `json:"end_time" example:"2024-12-31T10:00:00Z"`
	Priority    int       `json:"priority" example:"10"`
	CreatedAt   time.Time `json:"created_at" example:"2024-01-01T08:00:00Z"`
	UpdatedAt   time.Time `json:"updated_at" example:"2024-01-01T08:00:00Z"`
}

// PromotionDetailResponse 促销详情响应
type PromotionDetailResponse struct {
	BaseResponse
	Data PromotionDetailData `json:"data"`
}

// PromotionDetailData 促销详情数据
type PromotionDetailData struct {
	PromotionData
	Rules      []RuleData      `json:"rules,omitempty"`
	Discounts  []DiscountData  `json:"discounts,omitempty"`
	TimeCycles []TimeCycleData `json:"time_cycles,omitempty"`
}

// RuleData 规则数据
type RuleData struct {
	ID          string      `json:"id" example:"rule_001"`
	Type        string      `json:"type" example:"USER_CATEGORY_NOT_CONTAINS"`
	Operator    string      `json:"operator" example:"not_contains"`
	Value       interface{} `json:"value" example:"b2b,enterprise"`
	Description string      `json:"description" example:"排斥B2B用户"`
}

// DiscountData 折扣数据
type DiscountData struct {
	ID            string  `json:"id" example:"discount_001"`
	Type          string  `json:"type" example:"FIXED_AMOUNT"`
	DiscountValue float64 `json:"discount_value" example:"3000.0"`
	Code          string  `json:"code" example:"BP_FUEL_12345678"`
}

// TimeCycleData 时间周期数据
type TimeCycleData struct {
	ID        string    `json:"id" example:"cycle_001"`
	Type      string    `json:"type" example:"WEEKLY"`
	StartHour int       `json:"start_hour" example:"9"`
	EndHour   int       `json:"end_hour" example:"10"`
	WeekDays  []int     `json:"week_days" example:"1"`
	StartTime time.Time `json:"start_time" example:"2024-01-01T09:00:00Z"`
	EndTime   time.Time `json:"end_time" example:"2024-12-31T10:00:00Z"`
}

// PromotionListResponse 促销列表响应
type PromotionListResponse struct {
	BaseResponse
	Data       []PromotionData `json:"data"`
	Pagination PaginationInfo  `json:"pagination"`
}

// CalculateResponse 折扣计算响应
type CalculateResponse struct {
	BaseResponse
	Data CalculateData `json:"data"`
}

// CalculateData 折扣计算数据
type CalculateData struct {
	OrderID          string             `json:"order_id" example:"order_001"`
	OriginalAmount   float64            `json:"original_amount" example:"200000.0"`
	DiscountedAmount float64            `json:"discounted_amount" example:"185000.0"`
	TotalSavings     float64            `json:"total_savings" example:"15000.0"`
	Discounts        []AppliedDiscount  `json:"discounts"`
}

// AppliedDiscount 应用的折扣
type AppliedDiscount struct {
	PromotionID     string   `json:"promotion_id" example:"123e4567-e89b-12d3-a456-************"`
	PromotionName   string   `json:"promotion_name" example:"BP燃油促销"`
	DiscountType    string   `json:"discount_type" example:"FIXED_AMOUNT"`
	DiscountValue   float64  `json:"discount_value" example:"15000.0"`
	Description     string   `json:"description" example:"BP92燃油消费28升以上享受15000印尼盾折扣"`
	ApplicableItems []string `json:"applicable_items,omitempty" example:"fuel_BP_92"`
}

// ValidationResponse 验证结果响应
type ValidationResponse struct {
	BaseResponse
	Data ValidationData `json:"data"`
}

// ValidationData 验证数据
type ValidationData struct {
	Valid  bool              `json:"valid" example:"true"`
	Issues []ValidationIssue `json:"issues,omitempty"`
}

// ValidationIssue 验证问题
type ValidationIssue struct {
	Field   string `json:"field" example:"rules.operator"`
	Message string `json:"message" example:"B2B排斥规则必须使用not_contains操作符"`
	Code    string `json:"code,omitempty" example:"INVALID_OPERATOR"`
} 