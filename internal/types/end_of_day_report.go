package types

import (
	"time"
)

/**
 * End of Day Report 数据类型定义
 *
 * 定义日终报表相关的请求和响应数据结构
 */

// EndOfDayReportRequest 日终报表请求参数
type EndOfDayReportRequest struct {
	StationID      int64   `json:"station_id" validate:"required,min=1"` // 站点ID
	Date           string  `json:"date" validate:"required"`             // 报表日期 (YYYY-MM-DD)
	AttendantName  *string `json:"attendant_name,omitempty"`             // 员工姓名筛选（支持模糊匹配）
	FuelGrade      *string `json:"fuel_grade,omitempty"`                 // 油品等级筛选
	PaymentMethod  *string `json:"payment_method,omitempty"`             // 支付方式筛选
	IncludeSummary *bool   `json:"include_summary,omitempty"`            // 是否包含汇总统计
}

// EndOfDayReportResponse 日终报表响应数据
type EndOfDayReportResponse struct {
	Success bool               `json:"success"` // 请求是否成功
	Message string             `json:"message"` // 响应消息
	Data    EndOfDayReportData `json:"data"`    // 报表数据
	Meta    EndOfDayReportMeta `json:"meta"`    // 元数据信息
}

// EndOfDayReportData 日终报表数据
type EndOfDayReportData struct {
	ReportHeader ReportHeader `json:"report_header"` // 报表头部信息
	Shifts       []ShiftData  `json:"shifts"`        // 班次数据
	DailySummary DailySummary `json:"daily_summary"` // 日汇总统计
}

// ReportHeader 报表头部信息
type ReportHeader struct {
	CompanyName string    `json:"company_name"` // 公司名称
	ReportType  string    `json:"report_type"`  // 报表类型
	StationID   int64     `json:"station_id"`   // 站点ID
	StationName string    `json:"station_name"` // 站点名称
	SiteCode    string    `json:"site_code"`    // 站点编码
	ReportDate  string    `json:"report_date"`  // 报表日期
	GeneratedAt time.Time `json:"generated_at"` // 生成时间
}

// ShiftData 班次数据
type ShiftData struct {
	ShiftInfo    ShiftInfo       `json:"shift_info"`    // 班次基本信息
	Attendants   []AttendantData `json:"attendants"`    // 员工数据
	ShiftSummary ShiftSummary    `json:"shift_summary"` // 班次汇总
}

// ShiftInfo 班次基本信息
type ShiftInfo struct {
	ID          int64     `json:"id"`           // 班次ID
	ShiftNumber string    `json:"shift_number"` // 班次编号
	StationID   int64     `json:"station_id"`   // 站点ID
	StationName string    `json:"station_name"` // 站点名称
	StartTime   time.Time `json:"start_time"`   // 班次开始时间
	EndTime     time.Time `json:"end_time"`     // 班次结束时间
	Status      string    `json:"status"`       // 班次状态
	ShiftName   string    `json:"shift_name"`   // 班次名称
	TimeRange   string    `json:"time_range"`   // 时间范围
}

// AttendantData 员工数据
type AttendantData struct {
	AttendantInfo    AttendantInfo   `json:"attendant_info"`    // 员工基本信息
	TransactionCount int             `json:"transaction_count"` // 交易笔数
	SalesVolumeLtr   float64         `json:"sales_volume_ltr"`  // 销售油量（升）
	SalesAmountIDR   float64         `json:"sales_amount_idr"`  // 销售金额（印尼盾）
	FuelSales        FuelSales       `json:"fuel_sales"`        // 油品销售明细
	PaymentSummary   PaymentSummary  `json:"payment_summary"`   // 支付方式汇总
	OtherIncome      OtherIncome     `json:"other_income"`      // 其他收入明细
	EmployeeSummary  EmployeeSummary `json:"employee_summary"`  // 员工汇总
}

// AttendantInfo 员工基本信息
type AttendantInfo struct {
	AttendantName string `json:"attendant_name"` // 员工真实姓名
	StaffCardID   int64  `json:"staff_card_id"`  // 员工卡ID
	EmployeeID    int64  `json:"employee_id"`    // 员工ID
	EmployeeCode  string `json:"employee_code"`  // 员工编号
}

// FuelSales 油品销售明细
type FuelSales struct {
	ByGrade []FuelGradeData `json:"by_grade"` // 按油品等级分组的销售数据
	Total   FuelSalesTotal  `json:"total"`    // 油品销售汇总
}

// FuelGradeData 油品等级数据
type FuelGradeData struct {
	FuelGrade        string  `json:"fuel_grade"`        // 油品等级
	FuelName         string  `json:"fuel_name"`         // 油品名称
	FuelType         string  `json:"fuel_type"`         // 油品类型
	SalesVolume      float64 `json:"sales_volume"`      // 销售油量（保留3位小数）
	GrossAmount      float64 `json:"gross_amount"`      // 毛销售额
	DiscountAmount   float64 `json:"discount_amount"`   // 折扣金额
	NetAmount        float64 `json:"net_amount"`        // 净销售额
	UnitPrice        float64 `json:"unit_price"`        // 单价
	TransactionCount int     `json:"transaction_count"` // 交易笔数
	FreeLiter        float64 `json:"free_liter"`        // 免费升数 (L)
	FreeLiterAmount  float64 `json:"free_liter_amount"` // 免费升金额 (Rp)
	DiscountFuel     float64 `json:"discount_fuel"`     // 折扣燃油 = discount_amount - free_liter_amount
}

// FuelSalesTotal 油品销售汇总
type FuelSalesTotal struct {
	TotalVolume          float64 `json:"total_volume"`            // 总销售油量
	TotalGrossAmount     float64 `json:"total_gross_amount"`      // 总毛销售额
	TotalDiscountAmount  float64 `json:"total_discount_amount"`   // 总折扣金额
	TotalNetAmount       float64 `json:"total_net_amount"`        // 总净销售额
	TotalTransactions    int     `json:"total_transactions"`      // 总交易笔数
	TotalFreeLiter       float64 `json:"total_free_liter"`        // 总免费升数 (L)
	TotalFreeLiterAmount float64 `json:"total_free_liter_amount"` // 总免费升金额 (Rp)
	TotalDiscountFuel    float64 `json:"total_discount_fuel"`     // 总折扣燃油 = total_discount_amount - total_free_liter_amount
}

// PaymentSummary 支付方式汇总
type PaymentSummary struct {
	Cash         float64             `json:"cash"`           // 现金支付金额
	NonCashTotal float64             `json:"non_cash_total"` // 非现金支付总额
	PVS          float64             `json:"pvs"`            // PVS支付金额
	CIMB         float64             `json:"cimb"`           // CIMB支付金额
	BCA          float64             `json:"bca"`            // BCA支付金额
	Mandiri      float64             `json:"mandiri"`        // Mandiri支付金额
	BRI          float64             `json:"bri"`            // BRI支付金额
	BNI          float64             `json:"bni"`            // BNI支付金额
	Voucher      float64             `json:"voucher"`        // 代金券支付金额
	B2B          float64             `json:"b2b"`            // B2B支付金额
	Tera         float64             `json:"tera"`           // Tera支付金额
	ByMethod     []PaymentMethodData `json:"by_method"`      // 支付方式明细数组
}

// PaymentMethodData 支付方式明细数据
type PaymentMethodData struct {
	PaymentMethod     string  `json:"payment_method"`      // 支付方式代码
	PaymentMethodName string  `json:"payment_method_name"` // 支付方式显示名称
	TotalAmount       float64 `json:"total_amount"`        // 该支付方式总金额
	TransactionCount  int     `json:"transaction_count"`   // 该支付方式交易笔数
	Percentage        float64 `json:"percentage"`          // 占比（%）
}

// OtherIncome 其他收入明细
type OtherIncome struct {
	Items                  []OtherIncomeItem `json:"items"`                    // 其他收入项目
	TotalOtherIncome       float64           `json:"total_other_income"`       // 其他收入总额
	TotalOtherTransactions int               `json:"total_other_transactions"` // 其他收入交易笔数
}

// OtherIncomeItem 其他收入项目
type OtherIncomeItem struct {
	ItemType         string  `json:"item_type"`         // 项目类型
	ItemName         string  `json:"item_name"`         // 项目名称
	Quantity         int     `json:"quantity"`          // 数量
	UnitPrice        float64 `json:"unit_price"`        // 单价
	TotalAmount      float64 `json:"total_amount"`      // 总金额
	TransactionCount int     `json:"transaction_count"` // 交易笔数
}

// EmployeeSummary 员工汇总
type EmployeeSummary struct {
	TotalFuelSales       float64 `json:"total_fuel_sales"`        // 燃油销售总额
	TotalOtherIncome     float64 `json:"total_other_income"`      // 其他收入总额
	GrandTotal           float64 `json:"grand_total"`             // 总计金额
	TotalTransactions    int     `json:"total_transactions"`      // 总交易笔数
	OverCash             float64 `json:"over_cash"`               // 超额现金
	TotalFreeLiter       float64 `json:"total_free_liter"`        // 总免费升数 (L)
	TotalFreeLiterAmount float64 `json:"total_free_liter_amount"` // 总免费升金额 (Rp)
	TotalDiscountFuel    float64 `json:"total_discount_fuel"`     // 总折扣燃油
}

// ShiftSummary 班次汇总信息
type ShiftSummary struct {
	TotalAttendants       int                      `json:"total_attendants"`        // 员工总数
	TotalTransactions     int                      `json:"total_transactions"`      // 交易总笔数
	TotalSalesVolume      float64                  `json:"total_sales_volume"`      // 销售总油量
	TotalFuelSales        float64                  `json:"total_fuel_sales"`        // 燃油销售总额
	TotalOtherIncome      float64                  `json:"total_other_income"`      // 其他收入总额
	TotalCash             float64                  `json:"total_cash"`              // 现金总额
	TotalNonCash          float64                  `json:"total_non_cash"`          // 非现金总额
	TotalPVS              float64                  `json:"total_pvs"`               // PVS总额
	TotalCIMB             float64                  `json:"total_cimb"`              // CIMB总额
	TotalBCA              float64                  `json:"total_bca"`               // BCA总额
	TotalMandiri          float64                  `json:"total_mandiri"`           // Mandiri总额
	TotalBRI              float64                  `json:"total_bri"`               // BRI总额
	TotalBNI              float64                  `json:"total_bni"`               // BNI总额
	TotalVoucher          float64                  `json:"total_voucher"`           // 代金券总额
	TotalB2B              float64                  `json:"total_b2b"`               // B2B总额
	TotalTera             float64                  `json:"total_tera"`              // Tera总额
	GrandTotal            float64                  `json:"grand_total"`             // 班次总计金额
	ControlPoint          string                   `json:"control_point"`           // 控制点状态
	Comments              string                   `json:"comments"`                // 备注信息
	TotalFreeLiter        float64                  `json:"total_free_liter"`        // 班次总免费升数 (L)
	TotalFreeLiterAmount  float64                  `json:"total_free_liter_amount"` // 班次总免费升金额 (Rp)
	TotalDiscountFuel     float64                  `json:"total_discount_fuel"`     // 班次总折扣燃油
	FuelGradesSummary     []FuelGradeSummary       `json:"fuel_grades_summary"`     // 按油品等级汇总
	PaymentMethodsSummary []PaymentMethodSummary   `json:"payment_methods_summary"` // 按支付方式汇总
	OtherIncomeSummary    []OtherIncomeSummaryItem `json:"other_income_summary"`    // 其他收入汇总
}

// DailySummary 日汇总统计
type DailySummary struct {
	TotalShifts           int                      `json:"total_shifts"`            // 总班次数
	TotalAttendants       int                      `json:"total_attendants"`        // 总员工数
	TotalTransactions     int                      `json:"total_transactions"`      // 总交易笔数
	TotalFuelVolume       float64                  `json:"total_fuel_volume"`       // 总燃油销售量
	TotalFuelSales        float64                  `json:"total_fuel_sales"`        // 总燃油销售额
	TotalOtherIncome      float64                  `json:"total_other_income"`      // 总其他收入
	TotalGrossSales       float64                  `json:"total_gross_sales"`       // 总毛销售额
	TotalDiscount         float64                  `json:"total_discount"`          // 总折扣金额
	TotalNetSales         float64                  `json:"total_net_sales"`         // 总净销售额
	TotalFreeLiter        float64                  `json:"total_free_liter"`        // 日总免费升数 (L)
	TotalFreeLiterAmount  float64                  `json:"total_free_liter_amount"` // 日总免费升金额 (Rp)
	TotalDiscountFuel     float64                  `json:"total_discount_fuel"`     // 日总折扣燃油
	PaymentMethodsSummary []PaymentMethodSummary   `json:"payment_methods_summary"` // 支付方式汇总
	FuelGradesSummary     []FuelGradeSummary       `json:"fuel_grades_summary"`     // 油品等级汇总
	OtherIncomeSummary    []OtherIncomeSummaryItem `json:"other_income_summary"`    // 其他收入汇总
}

// PaymentMethodSummary 支付方式汇总
type PaymentMethodSummary struct {
	PaymentMethod     string  `json:"payment_method"`      // 支付方式代码
	PaymentMethodName string  `json:"payment_method_name"` // 支付方式显示名称
	TotalAmount       float64 `json:"total_amount"`        // 总金额
	TransactionCount  int     `json:"transaction_count"`   // 交易笔数
	Percentage        float64 `json:"percentage"`          // 占比（%）
}

// FuelGradeSummary 油品等级汇总
type FuelGradeSummary struct {
	FuelGrade            string  `json:"fuel_grade"`              // 油品等级
	FuelName             string  `json:"fuel_name"`               // 油品名称
	FuelType             string  `json:"fuel_type"`               // 油品类型
	TotalVolume          float64 `json:"total_volume"`            // 总销售油量
	TotalGrossAmount     float64 `json:"total_gross_amount"`      // 总毛销售额
	TotalDiscountAmount  float64 `json:"total_discount_amount"`   // 总折扣金额
	TotalNetAmount       float64 `json:"total_net_amount"`        // 总净销售额
	TransactionCount     int     `json:"transaction_count"`       // 交易笔数
	AveragePrice         float64 `json:"average_price"`           // 平均单价
	TotalFreeLiter       float64 `json:"total_free_liter"`        // 总免费升数 (L)
	TotalFreeLiterAmount float64 `json:"total_free_liter_amount"` // 总免费升金额 (Rp)
	TotalDiscountFuel    float64 `json:"total_discount_fuel"`     // 总折扣燃油 = total_discount_amount - total_free_liter_amount
	// 新增字段用于计算平均单价
	UnitPrices []float64 `json:"-"` // 所有交易的单价列表（不输出到JSON）
}

// OtherIncomeSummaryItem 其他收入汇总项目
type OtherIncomeSummaryItem struct {
	ItemType         string  `json:"item_type"`         // 项目类型
	ItemName         string  `json:"item_name"`         // 项目名称
	TotalAmount      float64 `json:"total_amount"`      // 总金额
	TransactionCount int     `json:"transaction_count"` // 交易笔数
}

// EndOfDayReportMeta 元数据信息
type EndOfDayReportMeta struct {
	GeneratedAt      time.Time   `json:"generated_at"`       // 生成时间
	ProcessingTimeMs int64       `json:"processing_time_ms"` // 处理时间（毫秒）
	DataSource       string      `json:"data_source"`        // 数据源（realtime/aggregated）
	Version          string      `json:"version"`            // API版本
	QueryParams      QueryParams `json:"query_params"`       // 查询参数
}

// QueryParams 查询参数
type QueryParams struct {
	StationID     int64   `json:"station_id"`               // 站点ID
	Date          string  `json:"date"`                     // 查询日期
	AttendantName *string `json:"attendant_name,omitempty"` // 员工姓名筛选
	FuelGrade     *string `json:"fuel_grade,omitempty"`     // 油品等级筛选
	PaymentMethod *string `json:"payment_method,omitempty"` // 支付方式筛选
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Success bool   `json:"success"` // 请求是否成功
	Message string `json:"message"` // 错误消息
	Error   Error  `json:"error"`   // 错误详情
}

// Error 错误详情
type Error struct {
	Code    string `json:"code"`    // 错误代码
	Message string `json:"message"` // 错误消息
	Detail  string `json:"detail"`  // 错误详情
}

// ShiftTimeRange 班次时间范围（内部使用）
type ShiftTimeRange struct {
	ShiftID     int64      `json:"shift_id"`     // 班次ID
	ShiftNumber string     `json:"shift_number"` // 班次编号
	ShiftName   string     `json:"shift_name"`   // 班次名称
	StartTime   time.Time  `json:"start_time"`   // 开始时间
	EndTime     *time.Time `json:"end_time"`     // 结束时间
	Status      string     `json:"status"`       // 班次状态
	IsVirtual   bool       `json:"is_virtual"`   // 是否虚拟班次
	TimeRange   string     `json:"time_range"`   // 时间范围
}
