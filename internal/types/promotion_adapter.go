package types

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/services/promotion/dto"
	"gitlab4.weicheche.cn/indo-bp/promotion-service/pkg/types"
)

// PromotionAdapter 促销适配器
type PromotionAdapter struct{}

// NewPromotionAdapter 创建新的促销适配器
func NewPromotionAdapter() *PromotionAdapter {
	return &PromotionAdapter{}
}

// ToSDKOrderData 转换为SDK订单数据
func (a *PromotionAdapter) ToSDKOrderData(req dto.CalculateRequest) (types.OrderData, error) {
	if req.OrderID == "" {
		return types.OrderData{}, fmt.Errorf("订单ID不能为空")
	}

	// 转换订单项
	items := make([]types.OrderItemData, len(req.Items))
	for i, item := range req.Items {
		items[i] = types.OrderItemData{
			ProductID:   item.ProductID,
			Quantity:    item.Quantity,
			UnitPrice:   item.UnitPrice,
			CategoryIDs: item.CategoryIDs,
			SKU:         item.SKU,
		}
	}

	return types.OrderData{
		OrderID:     req.OrderID,
		UserID:      req.UserID,
		Items:       items,
		TotalAmount: req.TotalAmount,
		CouponCode:  req.CouponCode,
	}, nil
}

// FromSDKPromotionDTO 转换SDK促销DTO为本地响应
func (a *PromotionAdapter) FromSDKPromotionDTO(sdkPromotion *types.PromotionDTO) dto.PromotionData {
	return dto.PromotionData{
		ID:          sdkPromotion.ID,
		Name:        sdkPromotion.Name,
		Description: sdkPromotion.Description,
		Type:        sdkPromotion.Type,
		Status:      sdkPromotion.Status,
		StartTime:   sdkPromotion.StartTime,
		EndTime:     sdkPromotion.EndTime,
		Priority:    sdkPromotion.Priority,
		CreatedAt:   sdkPromotion.CreatedAt,
		UpdatedAt:   sdkPromotion.UpdatedAt,
	}
}

// ToSDKPromotionPackage 转换为SDK促销套件
func (a *PromotionAdapter) ToSDKPromotionPackage(config interface{}) (*types.PromotionPackageDTO, error) {
	switch cfg := config.(type) {
	case dto.BPFuelConfig:
		return a.buildBPFuelPackage(cfg)
	case dto.B2BExclusionConfig:
		return a.buildB2BExclusionPackage(cfg)
	case dto.GradientConfig:
		return a.buildGradientPackage(cfg)
	default:
		return nil, fmt.Errorf("不支持的配置类型: %T", config)
	}
}

// buildBPFuelPackage 构建BP燃油促销套件
func (a *PromotionAdapter) buildBPFuelPackage(config dto.BPFuelConfig) (*types.PromotionPackageDTO, error) {
	// 创建基础促销活动
	promotion := types.PromotionDTO{
		ID:          uuid.New(),
		Name:        config.Name,
		Description: config.Description,
		Type:        "FUEL_PROMOTION",
		Status:      "ACTIVE",
		StartTime:   config.StartTime,
		EndTime:     config.EndTime,
		Priority:    config.Priority,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 构建规则
	var rules []types.RuleDTO

	// 燃油类型规则
	if len(config.FuelTypes) > 0 {
		rules = append(rules, types.RuleDTO{
			ID:          uuid.New().String(),
			Type:        "PRODUCT_INCLUDE",
			Operator:    "in",
			CategoryIDs: config.FuelTypes,
			Description: "适用燃油类型限制",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	// B2B排斥规则
	if config.ExcludeB2B {
		rules = append(rules, types.RuleDTO{
			ID:          uuid.New().String(),
			Type:        "USER_CATEGORY_NOT_CONTAINS",
			Operator:    "not_contains",
			Value:       []string{"b2b", "enterprise", "B2B"},
			Description: "排斥B2B用户",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	// 梯度折扣规则
	for _, tier := range config.GradientTiers {
		rules = append(rules, types.RuleDTO{
			ID:             uuid.New().String(),
			Type:           "VOLUME_GRADIENT",
			ThresholdType:  "QUANTITY",
			ThresholdValue: tier.MinVolume,
			GradientLevel:  tier.Level,
			CategoryIDs:    tier.FuelTypes,
			Description:    tier.Description,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		})
	}

	// 创建折扣配置
	discount := &types.DiscountDTO{
		ID:            uuid.New().String(),
		PromotionID:   promotion.ID.String(),
		Type:          "GRADIENT_FIXED_AMOUNT",
		DiscountValue: config.BaseDiscount,
		Code:          fmt.Sprintf("BP_FUEL_%s", uuid.New().String()[:8]),
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// 创建时间周期
	var timeCycles []types.TimeCycleDTO
	if len(config.TimeSlots) > 0 {
		for _, slot := range config.TimeSlots {
			timeCycles = append(timeCycles, types.TimeCycleDTO{
				ID:          uuid.New().String(),
				PromotionID: promotion.ID.String(),
				Type:        slot.Type,
				StartTime:   config.StartTime,
				EndTime:     config.EndTime,
				StartHour:   slot.StartHour,
				EndHour:     slot.EndHour,
				WeekDays:    slot.WeekDays,
				Priority:    0,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	} else {
		// 默认时间周期：每周一9-10点
		timeCycles = append(timeCycles, types.TimeCycleDTO{
			ID:          uuid.New().String(),
			PromotionID: promotion.ID.String(),
			Type:        "WEEKLY",
			StartTime:   config.StartTime,
			EndTime:     config.EndTime,
			StartHour:   9,
			EndHour:     10,
			WeekDays:    []int{1}, // 周一
			Priority:    0,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	return &types.PromotionPackageDTO{
		Promotion:  promotion,
		Discount:   discount,
		TimeCycles: timeCycles,
		Rules:      rules,
	}, nil
}

// buildB2BExclusionPackage 构建B2B排斥促销套件
func (a *PromotionAdapter) buildB2BExclusionPackage(config dto.B2BExclusionConfig) (*types.PromotionPackageDTO, error) {
	promotion := types.PromotionDTO{
		ID:          uuid.New(),
		Name:        config.Name,
		Description: config.Description,
		Type:        "B2B_EXCLUSION",
		Status:      "ACTIVE",
		StartTime:   config.StartTime,
		EndTime:     config.EndTime,
		Priority:    config.Priority,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// B2B排斥规则
	rules := []types.RuleDTO{
		{
			ID:          uuid.New().String(),
			Type:        "USER_CATEGORY_NOT_CONTAINS",
			Operator:    "not_contains",
			Value:       config.ExcludedUserTypes,
			Description: "排斥B2B用户享受促销",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	// 最低订单金额规则
	if config.MinOrderAmount != nil {
		rules = append(rules, types.RuleDTO{
			ID:                uuid.New().String(),
			Type:              "MIN_AMOUNT",
			Operator:          ">=",
			MinPurchaseAmount: *config.MinOrderAmount,
			Description:       fmt.Sprintf("最低订单金额%.0f", *config.MinOrderAmount),
			CreatedAt:         time.Now(),
			UpdatedAt:         time.Now(),
		})
	}

	// 创建折扣
	discount := &types.DiscountDTO{
		ID:            uuid.New().String(),
		PromotionID:   promotion.ID.String(),
		Type:          config.DiscountType,
		DiscountValue: config.DiscountAmount,
		Code:          fmt.Sprintf("B2B_EXCL_%s", uuid.New().String()[:8]),
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	return &types.PromotionPackageDTO{
		Promotion: promotion,
		Discount:  discount,
		Rules:     rules,
	}, nil
}

// buildGradientPackage 构建梯度折扣促销套件
func (a *PromotionAdapter) buildGradientPackage(config dto.GradientConfig) (*types.PromotionPackageDTO, error) {
	promotion := types.PromotionDTO{
		ID:          uuid.New(),
		Name:        config.Name,
		Description: config.Description,
		Type:        "GRADIENT_DISCOUNT",
		Status:      "ACTIVE",
		StartTime:   config.StartTime,
		EndTime:     config.EndTime,
		Priority:    config.Priority,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	var rules []types.RuleDTO

	// 为每个梯度级别创建规则
	for _, tier := range config.Tiers {
		rules = append(rules, types.RuleDTO{
			ID:             uuid.New().String(),
			Type:           "VOLUME_GRADIENT",
			ThresholdType:  config.ThresholdType,
			ThresholdValue: tier.MinVolume,
			GradientLevel:  tier.Level,
			CategoryIDs:    tier.FuelTypes,
			Description:    tier.Description,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		})
	}

	discount := &types.DiscountDTO{
		ID:            uuid.New().String(),
		PromotionID:   promotion.ID.String(),
		Type:          "GRADIENT_FIXED_AMOUNT",
		DiscountValue: config.BaseDiscount,
		Code:          fmt.Sprintf("GRADIENT_%s", uuid.New().String()[:8]),
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	return &types.PromotionPackageDTO{
		Promotion: promotion,
		Discount:  discount,
		Rules:     rules,
	}, nil
}

// FromSDKCalculatedDiscount 转换SDK计算结果
func (a *PromotionAdapter) FromSDKCalculatedDiscount(sdkDiscounts []types.CalculatedDiscount) []dto.AppliedDiscount {
	discounts := make([]dto.AppliedDiscount, len(sdkDiscounts))
	for i, sd := range sdkDiscounts {
		discounts[i] = dto.AppliedDiscount{
			PromotionID:     sd.PromotionID,
			PromotionName:   sd.PromotionName,
			DiscountType:    sd.DiscountType,
			DiscountValue:   sd.DiscountValue,
			Description:     sd.Description,
			ApplicableItems: sd.ApplicableItems,
		}
	}
	return discounts
}

// ValidateConfig 验证配置
func (a *PromotionAdapter) ValidateConfig(config interface{}) error {
	switch cfg := config.(type) {
	case dto.BPFuelConfig:
		return a.validateBPFuelConfig(cfg)
	case dto.B2BExclusionConfig:
		return a.validateB2BExclusionConfig(cfg)
	case dto.GradientConfig:
		return a.validateGradientConfig(cfg)
	default:
		return fmt.Errorf("不支持的配置类型: %T", config)
	}
}

// validateBPFuelConfig 验证BP燃油配置
func (a *PromotionAdapter) validateBPFuelConfig(config dto.BPFuelConfig) error {
	if config.StartTime.After(config.EndTime) {
		return fmt.Errorf("开始时间不能晚于结束时间")
	}
	if len(config.FuelTypes) == 0 {
		return fmt.Errorf("燃油类型不能为空")
	}
	for _, tier := range config.GradientTiers {
		if tier.MinVolume <= 0 {
			return fmt.Errorf("梯度最小量必须大于0")
		}
		if tier.DiscountAmount <= 0 {
			return fmt.Errorf("梯度折扣金额必须大于0")
		}
	}
	return nil
}

// validateB2BExclusionConfig 验证B2B排斥配置
func (a *PromotionAdapter) validateB2BExclusionConfig(config dto.B2BExclusionConfig) error {
	if config.StartTime.After(config.EndTime) {
		return fmt.Errorf("开始时间不能晚于结束时间")
	}
	if len(config.ExcludedUserTypes) == 0 {
		return fmt.Errorf("排斥用户类型不能为空")
	}
	if config.DiscountAmount <= 0 {
		return fmt.Errorf("折扣金额必须大于0")
	}
	return nil
}

// validateGradientConfig 验证梯度配置
func (a *PromotionAdapter) validateGradientConfig(config dto.GradientConfig) error {
	if config.StartTime.After(config.EndTime) {
		return fmt.Errorf("开始时间不能晚于结束时间")
	}
	if len(config.Tiers) == 0 {
		return fmt.Errorf("梯度档次不能为空")
	}

	// 验证梯度档次顺序
	for i := 1; i < len(config.Tiers); i++ {
		if config.Tiers[i].MinVolume <= config.Tiers[i-1].MinVolume {
			return fmt.Errorf("梯度档次必须按升序排列")
		}
	}
	return nil
}
