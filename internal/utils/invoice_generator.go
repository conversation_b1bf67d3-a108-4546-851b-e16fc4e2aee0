package utils

import (
	"context"
	"crypto/rand"
	"fmt"
	"log"
	"math/big"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
)

// InvoiceNumberGenerator 订单号生成器接口
type InvoiceNumberGenerator interface {
	GenerateInvoiceNumber(ctx context.Context, stationID int64) (string, error)
	GetSiteCode(ctx context.Context, stationID int64) (string, error)
}

// RedisInvoiceGenerator Redis订单号生成器实现
type RedisInvoiceGenerator struct {
	redisClient   *redis.Client
	siteCodeCache map[int64]string
	cacheMutex    sync.RWMutex
	cacheTTL      time.Duration
	cacheExpiry   map[int64]time.Time
	
	// 数据库查询函数，由外部注入
	queryStationFunc func(ctx context.Context, stationID int64) (string, error)
}

// NewRedisInvoiceGenerator 创建新的Redis订单号生成器
func NewRedisInvoiceGenerator(queryStationFunc func(ctx context.Context, stationID int64) (string, error)) *RedisInvoiceGenerator {
	// 创建Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:     "127.0.0.1:6379",
		Password: "", // 无密码
		DB:       0,  // 默认数据库
	})

	return &RedisInvoiceGenerator{
		redisClient:      rdb,
		siteCodeCache:    make(map[int64]string),
		cacheExpiry:      make(map[int64]time.Time),
		cacheTTL:         5 * time.Minute, // 缓存5分钟
		queryStationFunc: queryStationFunc,
	}
}

// GenerateInvoiceNumber 生成新的订单号
// 格式：INV/{SITE_CODE}/{DAY_CODE}{RANDOM_NUMBERS}
func (g *RedisInvoiceGenerator) GenerateInvoiceNumber(ctx context.Context, stationID int64) (string, error) {
	// 1. 获取站点代码
	siteCode, err := g.GetSiteCode(ctx, stationID)
	if err != nil {
		return "", fmt.Errorf("获取站点代码失败: %w", err)
	}

	// 2. 计算Julian日期代码
	dayCode := g.calculateJulianDayCode(time.Now())

	// 3. 生成序列号
	seqNumber, err := g.generateSequenceNumber(ctx, siteCode, dayCode)
	if err != nil {
		log.Printf("Redis生成序列号失败，使用降级策略: %v", err)
		seqNumber = g.fallbackSequenceNumber()
	}

	// 4. 组装订单号
	invoiceNumber := fmt.Sprintf("INV/%s/%s%s", siteCode, dayCode, seqNumber)

	log.Printf("生成订单号: %s (站点ID: %d, 站点代码: %s, 日期代码: %s, 序列号: %s)", 
		invoiceNumber, stationID, siteCode, dayCode, seqNumber)

	return invoiceNumber, nil
}

// GetSiteCode 获取站点代码（带缓存）
func (g *RedisInvoiceGenerator) GetSiteCode(ctx context.Context, stationID int64) (string, error) {
	// 先查缓存
	g.cacheMutex.RLock()
	if siteCode, exists := g.siteCodeCache[stationID]; exists {
		if expiry, hasExpiry := g.cacheExpiry[stationID]; hasExpiry && time.Now().Before(expiry) {
			g.cacheMutex.RUnlock()
			return siteCode, nil
		}
	}
	g.cacheMutex.RUnlock()

	// 缓存未命中或已过期，查询数据库
	siteCode, err := g.queryStationFunc(ctx, stationID)
	if err != nil {
		// 数据库查询失败，使用降级策略
		return g.fallbackSiteCode(stationID), nil
	}

	// 更新缓存
	g.cacheMutex.Lock()
	g.siteCodeCache[stationID] = siteCode
	g.cacheExpiry[stationID] = time.Now().Add(g.cacheTTL)
	g.cacheMutex.Unlock()

	return siteCode, nil
}

// calculateJulianDayCode 计算Julian日期代码
// 格式：年份后两位 + 该年第几天（1-366）
// 例如：2024年12月15日 → 24350
func (g *RedisInvoiceGenerator) calculateJulianDayCode(date time.Time) string {
	year := date.Year() % 100  // 年份后两位
	dayOfYear := date.YearDay() // 该年第几天 (1-366)
	return fmt.Sprintf("%02d%03d", year, dayOfYear)
}

// generateSequenceNumber 使用Redis生成带随机性的序列号
func (g *RedisInvoiceGenerator) generateSequenceNumber(ctx context.Context, siteCode, dayCode string) (string, error) {
	// 生成随机起始偏移量（1-999）
	randomOffset, err := g.generateRandomOffset()
	if err != nil {
		randomOffset = 1 // 降级为固定值
	}

	// 使用带随机偏移的key，增加随机性
	key := fmt.Sprintf("invoice_seq:%s:%s:%03d", siteCode, dayCode, randomOffset)

	// Redis原子递增
	seq, err := g.redisClient.Incr(ctx, key).Result()
	if err != nil {
		return "", fmt.Errorf("Redis INCR失败: %w", err)
	}

	// 设置48小时TTL（172800秒）
	g.redisClient.Expire(ctx, key, 48*time.Hour)

	// 组合随机偏移和序列号，保持6位格式
	// 格式：{randomOffset:3位}{seq:3位}
	finalSeq := randomOffset*1000 + int(seq%1000)
	return fmt.Sprintf("%06d", finalSeq), nil
}

// generateRandomOffset 生成随机偏移量（1-999）
func (g *RedisInvoiceGenerator) generateRandomOffset() (int, error) {
	// 使用加密安全的随机数生成器
	n, err := rand.Int(rand.Reader, big.NewInt(999))
	if err != nil {
		return 0, err
	}
	return int(n.Int64()) + 1, nil // 确保范围是1-999
}

// fallbackSequenceNumber 降级策略：生成基于时间的序列号
func (g *RedisInvoiceGenerator) fallbackSequenceNumber() string {
	timestamp := time.Now().UnixNano() / 1000000 // 毫秒时间戳
	return fmt.Sprintf("%06d", timestamp%1000000)
}

// fallbackSiteCode 降级策略：使用站点ID生成站点代码
func (g *RedisInvoiceGenerator) fallbackSiteCode(stationID int64) string {
	return fmt.Sprintf("ST%03d", stationID%1000)
}

// Close 关闭Redis连接
func (g *RedisInvoiceGenerator) Close() error {
	return g.redisClient.Close()
}

// TestRedisConnection 测试Redis连接
func (g *RedisInvoiceGenerator) TestRedisConnection(ctx context.Context) error {
	return g.redisClient.Ping(ctx).Err()
}
