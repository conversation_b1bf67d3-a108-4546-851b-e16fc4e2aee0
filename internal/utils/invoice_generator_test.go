package utils

import (
	"context"
	"fmt"
	"regexp"
	"testing"
	"time"
)

func TestCalculateJulianDayCode(t *testing.T) {
	generator := &RedisInvoiceGenerator{}
	
	tests := []struct {
		name     string
		date     time.Time
		expected string
	}{
		{
			name:     "2024年12月15日",
			date:     time.Date(2024, 12, 15, 0, 0, 0, 0, time.UTC),
			expected: "24350", // 2024年第350天
		},
		{
			name:     "2024年1月1日",
			date:     time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			expected: "24001", // 2024年第1天
		},
		{
			name:     "2024年12月31日",
			date:     time.Date(2024, 12, 31, 0, 0, 0, 0, time.UTC),
			expected: "24366", // 2024年第366天（闰年）
		},
		{
			name:     "2025年1月1日",
			date:     time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			expected: "25001", // 2025年第1天
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := generator.calculateJulianDayCode(tt.date)
			if result != tt.expected {
				t.<PERSON><PERSON><PERSON>("calculateJulianDayCode() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestFallbackSiteCode(t *testing.T) {
	generator := &RedisInvoiceGenerator{}
	
	tests := []struct {
		name      string
		stationID int64
		expected  string
	}{
		{
			name:      "站点ID 1",
			stationID: 1,
			expected:  "ST001",
		},
		{
			name:      "站点ID 12",
			stationID: 12,
			expected:  "ST012",
		},
		{
			name:      "站点ID 1234",
			stationID: 1234,
			expected:  "ST234", // 1234 % 1000 = 234
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := generator.fallbackSiteCode(tt.stationID)
			if result != tt.expected {
				t.Errorf("fallbackSiteCode() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestFallbackSequenceNumber(t *testing.T) {
	generator := &RedisInvoiceGenerator{}
	
	// 测试生成的序列号格式
	result := generator.fallbackSequenceNumber()
	
	// 应该是6位数字
	if len(result) != 6 {
		t.Errorf("fallbackSequenceNumber() length = %v, want 6", len(result))
	}
	
	// 应该全是数字
	matched, _ := regexp.MatchString(`^\d{6}$`, result)
	if !matched {
		t.Errorf("fallbackSequenceNumber() = %v, should be 6 digits", result)
	}
}

func TestGenerateInvoiceNumberWithMock(t *testing.T) {
	// 使用模拟查询函数
	mockQueryFunc := CreateMockStationQueryFunc()
	generator := NewRedisInvoiceGenerator(mockQueryFunc)
	
	ctx := context.Background()
	
	tests := []struct {
		name      string
		stationID int64
		wantError bool
	}{
		{
			name:      "有效站点ID 1",
			stationID: 1,
			wantError: false,
		},
		{
			name:      "有效站点ID 12",
			stationID: 12,
			wantError: false,
		},
		{
			name:      "无效站点ID 999",
			stationID: 999,
			wantError: false, // 不会报错，会使用降级策略
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := generator.GenerateInvoiceNumber(ctx, tt.stationID)
			
			if tt.wantError && err == nil {
				t.Errorf("GenerateInvoiceNumber() error = nil, wantError %v", tt.wantError)
				return
			}
			
			if !tt.wantError && err != nil {
				t.Errorf("GenerateInvoiceNumber() error = %v, wantError %v", err, tt.wantError)
				return
			}
			
			if !tt.wantError {
				// 验证订单号格式：INV/{SITE_CODE}/{DAY_CODE}{RANDOM_NUMBERS}
				pattern := `^INV/[A-Z0-9]{3,5}/\d{5}\d{6}$`
				matched, _ := regexp.MatchString(pattern, result)
				if !matched {
					t.Errorf("GenerateInvoiceNumber() = %v, doesn't match pattern %v", result, pattern)
				}
				
				// 验证长度（应该在19-23字符之间）
				if len(result) < 19 || len(result) > 23 {
					t.Errorf("GenerateInvoiceNumber() length = %v, should be between 19-23", len(result))
				}
				
				fmt.Printf("生成的订单号: %s (长度: %d)\n", result, len(result))
			}
		})
	}
}

func TestGetSiteCodeWithCache(t *testing.T) {
	mockQueryFunc := CreateMockStationQueryFunc()
	generator := NewRedisInvoiceGenerator(mockQueryFunc)
	
	ctx := context.Background()
	stationID := int64(1)
	
	// 第一次调用，应该查询数据库
	siteCode1, err := generator.GetSiteCode(ctx, stationID)
	if err != nil {
		t.Errorf("GetSiteCode() error = %v", err)
		return
	}
	
	// 第二次调用，应该使用缓存
	siteCode2, err := generator.GetSiteCode(ctx, stationID)
	if err != nil {
		t.Errorf("GetSiteCode() error = %v", err)
		return
	}
	
	// 结果应该相同
	if siteCode1 != siteCode2 {
		t.Errorf("GetSiteCode() cache inconsistent: %v != %v", siteCode1, siteCode2)
	}
	
	// 应该是预期的值
	if siteCode1 != "BP001" {
		t.Errorf("GetSiteCode() = %v, want BP001", siteCode1)
	}
}

func TestInvoiceNumberUniqueness(t *testing.T) {
	mockQueryFunc := CreateMockStationQueryFunc()
	generator := NewRedisInvoiceGenerator(mockQueryFunc)
	
	ctx := context.Background()
	stationID := int64(1)
	
	// 生成多个订单号，检查唯一性
	generated := make(map[string]bool)
	count := 10
	
	for i := 0; i < count; i++ {
		orderNumber, err := generator.GenerateInvoiceNumber(ctx, stationID)
		if err != nil {
			t.Errorf("GenerateInvoiceNumber() error = %v", err)
			continue
		}
		
		if generated[orderNumber] {
			t.Errorf("GenerateInvoiceNumber() generated duplicate: %v", orderNumber)
		}
		
		generated[orderNumber] = true
		fmt.Printf("订单号 %d: %s\n", i+1, orderNumber)
	}
	
	if len(generated) != count {
		t.Errorf("Generated %d unique order numbers, expected %d", len(generated), count)
	}
}
