package utils

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/jackc/pgx/v4/pgxpool"
)

// StationQueryFunc 站点查询函数类型
type StationQueryFunc func(ctx context.Context, stationID int64) (string, error)

// CreateStationQueryFunc 创建站点查询函数
// 这个函数可以接受不同类型的数据库连接
func CreateStationQueryFunc(db interface{}) StationQueryFunc {
	return func(ctx context.Context, stationID int64) (string, error) {
		var siteCode string
		var query string
		var err error

		// 支持不同的数据库连接类型
		switch dbConn := db.(type) {
		case *sql.DB:
			// 标准 database/sql 连接，指定core_schema
			query = `SELECT site_code FROM core_schema.stations WHERE id = $1 AND deleted_at IS NULL AND business_status = 'ACTIVE'`
			err = dbConn.QueryRowContext(ctx, query, stationID).Scan(&siteCode)
		case *pgxpool.Pool:
			// pgxpool 连接池，指定core_schema
			query = `SELECT site_code FROM core_schema.stations WHERE id = $1 AND deleted_at IS NULL AND business_status = 'ACTIVE'`
			err = dbConn.QueryRow(ctx, query, stationID).Scan(&siteCode)
		default:
			return "", fmt.Errorf("不支持的数据库连接类型: %T", db)
		}

		if err != nil {
			if err == sql.ErrNoRows {
				return "", fmt.Errorf("站点ID %d 不存在或状态不可用", stationID)
			}
			return "", fmt.Errorf("查询站点代码失败: %w", err)
		}

		if siteCode == "" {
			return "", fmt.Errorf("站点ID %d 的站点代码为空", stationID)
		}

		return siteCode, nil
	}
}

// CreateMockStationQueryFunc 创建模拟站点查询函数（用于测试）
func CreateMockStationQueryFunc() StationQueryFunc {
	// 模拟数据
	mockData := map[int64]string{
		1: "BP001",
		2: "BP002", 
		3: "BP003",
		12: "BP012", // 对应测试中的站点ID
	}

	return func(ctx context.Context, stationID int64) (string, error) {
		if siteCode, exists := mockData[stationID]; exists {
			return siteCode, nil
		}
		return "", fmt.Errorf("站点ID %d 不存在", stationID)
	}
}
