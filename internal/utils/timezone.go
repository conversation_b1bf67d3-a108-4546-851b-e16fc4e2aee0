package utils

import (
	"fmt"
	"time"
)

// JakartaLocation 印尼雅加达时区（导出变量）
var JakartaLocation *time.Location

// 初始化时区
func init() {
	var err error
	JakartaLocation, err = time.LoadLocation("Asia/Jakarta")
	if err != nil {
		// 如果无法加载时区，使用UTC+7作为备用
		JakartaLocation = time.FixedZone("WIB", 7*60*60)
	}
}

// ConvertToJakartaTime 将时间转换为印尼雅加达时间
func ConvertToJakartaTime(t time.Time) time.Time {
	if t.IsZero() {
		return t
	}
	return t.In(JakartaLocation)
}

// ConvertToJakartaTimePtr 将时间指针转换为印尼雅加达时间
func ConvertToJakartaTimePtr(t *time.Time) *time.Time {
	if t == nil || t.IsZero() {
		return t
	}
	jakartaTime := t.In(JakartaLocation)
	return &jakartaTime
}

// GetNowInJakarta 获取当前雅加达时间
func GetNowInJakarta() time.Time {
	return time.Now().In(JakartaLocation)
}

// GetJakartaLocation 获取雅加达时区
func GetJakartaLocation() *time.Location {
	return JakartaLocation
}

// ForceToJakartaTime 强制将时间转换为雅加达时区，确保正确的时区显示
func ForceToJakartaTime(t time.Time) time.Time {
	if t.IsZero() {
		return t
	}
	
	// 如果时间已经是雅加达时区，直接返回
	if t.Location() == JakartaLocation {
		return t
	}
	
	// 获取时间的年、月、日、时、分、秒
	year, month, day := t.Date()
	hour, min, sec := t.Clock()
	nsec := t.Nanosecond()
	
	// 在雅加达时区中重新创建时间
	return time.Date(year, month, day, hour, min, sec, nsec, JakartaLocation)
}

// ParseTimeParam 解析时间参数，支持多种格式
func ParseTimeParam(timeStr string) (time.Time, error) {
	var parsedTime time.Time
	var err error
	
	// 尝试解析含时区的完整时间格式（RFC3339）
	parsedTime, err = time.Parse(time.RFC3339, timeStr)
	if err != nil {
		// 尝试解析含时区的完整时间格式（+时区）
		parsedTime, err = time.Parse("2006-01-02T15:04:05+07:00", timeStr)
	}
	if err != nil {
		// 尝试解析含时区的完整时间格式（-时区）
		parsedTime, err = time.Parse("2006-01-02T15:04:05-07:00", timeStr)
	}
	if err != nil {
		// 尝试解析不含时区的完整时间格式，默认使用印尼时区
		parsedTime, err = time.Parse("2006-01-02T15:04:05", timeStr)
		if err == nil {
			parsedTime = time.Date(parsedTime.Year(), parsedTime.Month(), parsedTime.Day(), 
				parsedTime.Hour(), parsedTime.Minute(), parsedTime.Second(), parsedTime.Nanosecond(), JakartaLocation)
		}
	}
	if err != nil {
		// 尝试解析日期格式，根据参数类型自动补充时间，使用印尼时区
		dateTime, err := time.Parse("2006-01-02", timeStr)
		if err == nil {
			// 对于time_from，设置为当天00:00:00
			// 对于time_to，设置为当天23:59:59
			// 这里统一设置为00:00:00，在调用处根据参数类型调整
			parsedTime = time.Date(dateTime.Year(), dateTime.Month(), dateTime.Day(), 
				0, 0, 0, 0, JakartaLocation)
		}
	}
	
	if err != nil {
		return time.Time{}, fmt.Errorf("无效的时间格式: %s", timeStr)
	}
	
	return parsedTime, nil
} 