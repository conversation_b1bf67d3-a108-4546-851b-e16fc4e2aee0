package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
)

// BenchmarkConfig 性能测试配置
type BenchmarkConfig struct {
	// 连接配置
	MaxConnections int           `json:"max_connections"`
	ConnectionRate int           `json:"connection_rate"` // 每秒建立连接数
	MessageRate    int           `json:"message_rate"`    // 每秒发送消息数
	TestDuration   time.Duration `json:"test_duration"`   // 测试持续时间

	// 消息配置
	MessageSize  int           `json:"message_size"`  // 消息大小（字节）
	MessageTypes []MessageType `json:"message_types"` // 测试的消息类型

	// 服务器配置
	ServerURL string `json:"server_url"`
	AuthToken string `json:"auth_token"`

	// 测试选项
	EnableHeartbeat bool `json:"enable_heartbeat"`
	EnableReconnect bool `json:"enable_reconnect"`
	CollectMetrics  bool `json:"collect_metrics"`
}

// BenchmarkResult 性能测试结果
type BenchmarkResult struct {
	// 连接统计
	TotalConnections      int64 `json:"total_connections"`
	SuccessfulConnections int64 `json:"successful_connections"`
	FailedConnections     int64 `json:"failed_connections"`
	ActiveConnections     int64 `json:"active_connections"`

	// 消息统计
	MessagesSent     int64 `json:"messages_sent"`
	MessagesReceived int64 `json:"messages_received"`
	MessagesLost     int64 `json:"messages_lost"`

	// 性能指标
	AvgLatency time.Duration `json:"avg_latency"`
	MinLatency time.Duration `json:"min_latency"`
	MaxLatency time.Duration `json:"max_latency"`
	P95Latency time.Duration `json:"p95_latency"`
	P99Latency time.Duration `json:"p99_latency"`

	// 吞吐量
	MessagesPerSecond float64 `json:"messages_per_second"`
	BytesPerSecond    float64 `json:"bytes_per_second"`

	// 错误统计
	ConnectionErrors int64 `json:"connection_errors"`
	MessageErrors    int64 `json:"message_errors"`
	TimeoutErrors    int64 `json:"timeout_errors"`

	// 资源使用
	MemoryUsage int64   `json:"memory_usage"`
	CPUUsage    float64 `json:"cpu_usage"`

	// 测试信息
	TestDuration time.Duration `json:"test_duration"`
	StartTime    time.Time     `json:"start_time"`
	EndTime      time.Time     `json:"end_time"`
}

// BenchmarkClient 性能测试客户端
type BenchmarkClient struct {
	id      string
	conn    *websocket.Conn
	config  *BenchmarkConfig
	metrics *ClientMetrics
	ctx     context.Context
	cancel  context.CancelFunc
	wg      sync.WaitGroup
}

// ClientMetrics 客户端指标
type ClientMetrics struct {
	messagesSent     int64
	messagesReceived int64
	bytesReceived    int64
	latencies        []time.Duration
	errors           int64
	lastPing         time.Time
	connected        bool
	mu               sync.RWMutex
}

// WebSocketBenchmark WebSocket性能测试器
type WebSocketBenchmark struct {
	config    *BenchmarkConfig
	clients   []*BenchmarkClient
	result    *BenchmarkResult
	startTime time.Time
	endTime   time.Time
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
}

// NewWebSocketBenchmark 创建性能测试器
func NewWebSocketBenchmark(config *BenchmarkConfig) *WebSocketBenchmark {
	ctx, cancel := context.WithCancel(context.Background())

	return &WebSocketBenchmark{
		config:  config,
		clients: make([]*BenchmarkClient, 0, config.MaxConnections),
		result:  &BenchmarkResult{},
		ctx:     ctx,
		cancel:  cancel,
	}
}

// Run 运行性能测试
func (wb *WebSocketBenchmark) Run() (*BenchmarkResult, error) {
	log.Printf("开始WebSocket性能测试，最大连接数: %d", wb.config.MaxConnections)

	wb.startTime = time.Now()
	wb.result.StartTime = wb.startTime

	// 启动连接创建器
	wb.wg.Add(1)
	go wb.connectionCreator()

	// 启动消息发送器
	wb.wg.Add(1)
	go wb.messageSender()

	// 启动指标收集器
	if wb.config.CollectMetrics {
		wb.wg.Add(1)
		go wb.metricsCollector()
	}

	// 等待测试完成
	testCtx, testCancel := context.WithTimeout(wb.ctx, wb.config.TestDuration)
	defer testCancel()

	<-testCtx.Done()

	// 停止测试
	wb.cancel()
	wb.wg.Wait()

	wb.endTime = time.Now()
	wb.result.EndTime = wb.endTime
	wb.result.TestDuration = wb.endTime.Sub(wb.startTime)

	// 计算最终结果
	wb.calculateResults()

	log.Printf("性能测试完成，耗时: %v", wb.result.TestDuration)
	return wb.result, nil
}

// connectionCreator 连接创建器
func (wb *WebSocketBenchmark) connectionCreator() {
	defer wb.wg.Done()

	ticker := time.NewTicker(time.Second / time.Duration(wb.config.ConnectionRate))
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if len(wb.clients) >= wb.config.MaxConnections {
				continue
			}

			go wb.createClient()

		case <-wb.ctx.Done():
			return
		}
	}
}

// createClient 创建测试客户端
func (wb *WebSocketBenchmark) createClient() {
	clientID := fmt.Sprintf("client_%d", len(wb.clients))

	dialer := websocket.Dialer{
		HandshakeTimeout: 10 * time.Second,
	}

	conn, _, err := dialer.Dial(wb.config.ServerURL, nil)
	if err != nil {
		atomic.AddInt64(&wb.result.FailedConnections, 1)
		atomic.AddInt64(&wb.result.ConnectionErrors, 1)
		log.Printf("客户端 %s 连接失败: %v", clientID, err)
		return
	}

	ctx, cancel := context.WithCancel(wb.ctx)

	client := &BenchmarkClient{
		id:     clientID,
		conn:   conn,
		config: wb.config,
		metrics: &ClientMetrics{
			latencies: make([]time.Duration, 0, 1000),
			connected: true,
		},
		ctx:    ctx,
		cancel: cancel,
	}

	wb.clients = append(wb.clients, client)
	atomic.AddInt64(&wb.result.TotalConnections, 1)
	atomic.AddInt64(&wb.result.SuccessfulConnections, 1)
	atomic.AddInt64(&wb.result.ActiveConnections, 1)

	// 启动客户端处理协程
	client.wg.Add(2)
	go client.readPump()
	go client.writePump()

	log.Printf("客户端 %s 连接成功", clientID)
}

// messageSender 消息发送器
func (wb *WebSocketBenchmark) messageSender() {
	defer wb.wg.Done()

	ticker := time.NewTicker(time.Second / time.Duration(wb.config.MessageRate))
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			wb.sendTestMessage()

		case <-wb.ctx.Done():
			return
		}
	}
}

// sendTestMessage 发送测试消息
func (wb *WebSocketBenchmark) sendTestMessage() {
	if len(wb.clients) == 0 {
		return
	}

	// 随机选择客户端
	clientIndex := int(atomic.LoadInt64(&wb.result.MessagesSent)) % len(wb.clients)
	client := wb.clients[clientIndex]

	if !client.metrics.connected {
		return
	}

	// 构造测试消息
	messageType := wb.config.MessageTypes[int(atomic.LoadInt64(&wb.result.MessagesSent))%len(wb.config.MessageTypes)]

	message := &Message{
		Type: messageType,
		Payload: map[string]interface{}{
			"test_data": generateTestData(wb.config.MessageSize),
			"timestamp": time.Now().UnixNano(),
			"client_id": client.id,
		},
		Timestamp: time.Now(),
		Source:    "benchmark",
	}

	data, err := json.Marshal(message)
	if err != nil {
		atomic.AddInt64(&wb.result.MessageErrors, 1)
		return
	}

	startTime := time.Now()
	err = client.conn.WriteMessage(websocket.TextMessage, data)
	if err != nil {
		atomic.AddInt64(&wb.result.MessageErrors, 1)
		client.metrics.mu.Lock()
		client.metrics.connected = false
		client.metrics.mu.Unlock()
		return
	}

	atomic.AddInt64(&wb.result.MessagesSent, 1)
	atomic.AddInt64(&client.metrics.messagesSent, 1)

	// 记录延迟（如果收到响应）
	client.metrics.mu.Lock()
	client.metrics.latencies = append(client.metrics.latencies, time.Since(startTime))
	client.metrics.mu.Unlock()
}

// readPump 客户端读取协程
func (bc *BenchmarkClient) readPump() {
	defer bc.wg.Done()
	defer bc.conn.Close()

	bc.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	bc.conn.SetPongHandler(func(string) error {
		bc.metrics.lastPing = time.Now()
		bc.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		select {
		case <-bc.ctx.Done():
			return
		default:
			_, message, err := bc.conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Printf("客户端 %s 读取错误: %v", bc.id, err)
				}
				bc.metrics.mu.Lock()
				bc.metrics.connected = false
				bc.metrics.mu.Unlock()
				return
			}

			atomic.AddInt64(&bc.metrics.messagesReceived, 1)
			atomic.AddInt64(&bc.metrics.bytesReceived, int64(len(message)))
		}
	}
}

// writePump 客户端写入协程
func (bc *BenchmarkClient) writePump() {
	defer bc.wg.Done()

	ticker := time.NewTicker(54 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			bc.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := bc.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}

		case <-bc.ctx.Done():
			bc.conn.SetWriteDeadline(time.Now().Add(time.Second))
			bc.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
			return
		}
	}
}

// metricsCollector 指标收集器
func (wb *WebSocketBenchmark) metricsCollector() {
	defer wb.wg.Done()

	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			wb.collectMetrics()

		case <-wb.ctx.Done():
			return
		}
	}
}

// collectMetrics 收集指标
func (wb *WebSocketBenchmark) collectMetrics() {
	var totalReceived, totalBytes int64
	activeConnections := int64(0)

	for _, client := range wb.clients {
		client.metrics.mu.RLock()
		if client.metrics.connected {
			activeConnections++
		}
		totalReceived += client.metrics.messagesReceived
		totalBytes += client.metrics.bytesReceived
		client.metrics.mu.RUnlock()
	}

	atomic.StoreInt64(&wb.result.ActiveConnections, activeConnections)
	atomic.StoreInt64(&wb.result.MessagesReceived, totalReceived)

	// 计算吞吐量
	elapsed := time.Since(wb.startTime).Seconds()
	if elapsed > 0 {
		wb.result.MessagesPerSecond = float64(totalReceived) / elapsed
		wb.result.BytesPerSecond = float64(totalBytes) / elapsed
	}
}

// calculateResults 计算最终结果
func (wb *WebSocketBenchmark) calculateResults() {
	var allLatencies []time.Duration

	for _, client := range wb.clients {
		client.metrics.mu.RLock()
		allLatencies = append(allLatencies, client.metrics.latencies...)
		client.metrics.mu.RUnlock()
	}

	if len(allLatencies) > 0 {
		wb.result.AvgLatency = calculateAverage(allLatencies)
		wb.result.MinLatency = calculateMin(allLatencies)
		wb.result.MaxLatency = calculateMax(allLatencies)
		wb.result.P95Latency = calculatePercentile(allLatencies, 0.95)
		wb.result.P99Latency = calculatePercentile(allLatencies, 0.99)
	}

	// 计算丢失消息数
	wb.result.MessagesLost = wb.result.MessagesSent - wb.result.MessagesReceived
}

// 工具函数
func generateTestData(size int) string {
	data := make([]byte, size)
	for i := range data {
		data[i] = byte('A' + (i % 26))
	}
	return string(data)
}

func calculateAverage(latencies []time.Duration) time.Duration {
	if len(latencies) == 0 {
		return 0
	}

	var total time.Duration
	for _, latency := range latencies {
		total += latency
	}
	return total / time.Duration(len(latencies))
}

func calculateMin(latencies []time.Duration) time.Duration {
	if len(latencies) == 0 {
		return 0
	}

	min := latencies[0]
	for _, latency := range latencies[1:] {
		if latency < min {
			min = latency
		}
	}
	return min
}

func calculateMax(latencies []time.Duration) time.Duration {
	if len(latencies) == 0 {
		return 0
	}

	max := latencies[0]
	for _, latency := range latencies[1:] {
		if latency > max {
			max = latency
		}
	}
	return max
}

func calculatePercentile(latencies []time.Duration, percentile float64) time.Duration {
	if len(latencies) == 0 {
		return 0
	}

	// 简单的百分位数计算（实际应用中应该排序）
	index := int(float64(len(latencies)) * percentile)
	if index >= len(latencies) {
		index = len(latencies) - 1
	}
	return latencies[index]
}

// DefaultBenchmarkConfig 默认性能测试配置
func DefaultBenchmarkConfig() *BenchmarkConfig {
	return &BenchmarkConfig{
		MaxConnections:  100,
		ConnectionRate:  10,
		MessageRate:     100,
		TestDuration:    60 * time.Second,
		MessageSize:     1024,
		MessageTypes:    []MessageType{MessageTypeNozzleStatus, MessageTypeTransactionUpdate, MessageTypeDashboardUpdate},
		ServerURL:       "ws://localhost:8080/ws",
		AuthToken:       "test-token",
		EnableHeartbeat: true,
		EnableReconnect: false,
		CollectMetrics:  true,
	}
}

// PrintResults 打印测试结果
func (br *BenchmarkResult) PrintResults() {
	fmt.Println("=== WebSocket 性能测试结果 ===")
	fmt.Printf("测试时间: %v\n", br.TestDuration)
	fmt.Printf("开始时间: %v\n", br.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("结束时间: %v\n", br.EndTime.Format("2006-01-02 15:04:05"))
	fmt.Println()

	fmt.Println("连接统计:")
	fmt.Printf("  总连接数: %d\n", br.TotalConnections)
	fmt.Printf("  成功连接: %d\n", br.SuccessfulConnections)
	fmt.Printf("  失败连接: %d\n", br.FailedConnections)
	fmt.Printf("  活跃连接: %d\n", br.ActiveConnections)
	fmt.Printf("  连接成功率: %.2f%%\n", float64(br.SuccessfulConnections)/float64(br.TotalConnections)*100)
	fmt.Println()

	fmt.Println("消息统计:")
	fmt.Printf("  发送消息: %d\n", br.MessagesSent)
	fmt.Printf("  接收消息: %d\n", br.MessagesReceived)
	fmt.Printf("  丢失消息: %d\n", br.MessagesLost)
	fmt.Printf("  消息丢失率: %.2f%%\n", float64(br.MessagesLost)/float64(br.MessagesSent)*100)
	fmt.Println()

	fmt.Println("性能指标:")
	fmt.Printf("  平均延迟: %v\n", br.AvgLatency)
	fmt.Printf("  最小延迟: %v\n", br.MinLatency)
	fmt.Printf("  最大延迟: %v\n", br.MaxLatency)
	fmt.Printf("  P95延迟: %v\n", br.P95Latency)
	fmt.Printf("  P99延迟: %v\n", br.P99Latency)
	fmt.Println()

	fmt.Println("吞吐量:")
	fmt.Printf("  消息/秒: %.2f\n", br.MessagesPerSecond)
	fmt.Printf("  字节/秒: %.2f\n", br.BytesPerSecond)
	fmt.Printf("  MB/秒: %.2f\n", br.BytesPerSecond/1024/1024)
	fmt.Println()

	fmt.Println("错误统计:")
	fmt.Printf("  连接错误: %d\n", br.ConnectionErrors)
	fmt.Printf("  消息错误: %d\n", br.MessageErrors)
	fmt.Printf("  超时错误: %d\n", br.TimeoutErrors)
	fmt.Println()
}
