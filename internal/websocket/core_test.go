package websocket

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestFCCClient_OnlineScenario 测试FCC客户端在线场景
func TestFCCClient_OnlineScenario(t *testing.T) {
	// 创建模拟FCC服务器
	fccServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/api/nozzles/status":
			nozzles := []map[string]interface{}{
				{
					"nozzle_id":      "test-nozzle-1",
					"dispenser_id":   "test-dispenser-1",
					"station_id":     1,
					"status":         "idle",
					"current_volume": 0.0,
					"current_amount": 0.0,
					"fuel_grade":     "RON92",
				},
				{
					"nozzle_id":      "test-nozzle-2",
					"dispenser_id":   "test-dispenser-1",
					"station_id":     1,
					"status":         "filling",
					"current_volume": 10.5,
					"current_amount": 150000.0,
					"fuel_grade":     "RON95",
				},
			}
			w.<PERSON><PERSON>().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(nozzles)
		case "/api/devices/status":
			devices := []map[string]interface{}{
				{
					"device_id":   "test-device-1",
					"station_id":  1,
					"device_name": "Test Dispenser 1",
					"is_enabled":  true,
					"status":      "online",
				},
			}
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(devices)
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
	defer fccServer.Close()

	// 测试FCC客户端
	client := NewHTTPFCCClient()
	ctx := context.Background()

	// 测试获取喷嘴状态
	nozzles, err := client.GetNozzleStatus(ctx, fccServer.URL+"/api")
	require.NoError(t, err, "获取喷嘴状态应该成功")
	assert.Len(t, nozzles, 2, "应该返回2个喷嘴")

	// 验证第一个喷嘴
	nozzle1, ok := nozzles[0].(map[string]interface{})
	require.True(t, ok, "喷嘴数据应该是map类型")
	assert.Equal(t, "test-nozzle-1", nozzle1["nozzle_id"], "喷嘴ID应该匹配")
	assert.Equal(t, "idle", nozzle1["status"], "喷嘴状态应该是idle")

	// 验证第二个喷嘴
	nozzle2, ok := nozzles[1].(map[string]interface{})
	require.True(t, ok, "喷嘴数据应该是map类型")
	assert.Equal(t, "test-nozzle-2", nozzle2["nozzle_id"], "喷嘴ID应该匹配")
	assert.Equal(t, "filling", nozzle2["status"], "喷嘴状态应该是filling")
	assert.Equal(t, 10.5, nozzle2["current_volume"], "当前体积应该匹配")

	// 测试获取设备状态
	devices, err := client.GetDeviceStatus(ctx, fccServer.URL+"/api")
	require.NoError(t, err, "获取设备状态应该成功")
	assert.Len(t, devices, 1, "应该返回1个设备")

	device1, ok := devices[0].(map[string]interface{})
	require.True(t, ok, "设备数据应该是map类型")
	assert.Equal(t, "test-device-1", device1["device_id"], "设备ID应该匹配")
	assert.Equal(t, "online", device1["status"], "设备状态应该是online")

	t.Log("✅ FCC客户端在线场景测试通过")
}

// TestFCCClient_OfflineScenario 测试FCC客户端离线场景
func TestFCCClient_OfflineScenario(t *testing.T) {
	client := NewHTTPFCCClient()
	ctx := context.Background()

	// 测试连接不存在的服务器
	nozzles, err := client.GetNozzleStatus(ctx, "http://localhost:9999/api")
	require.NoError(t, err, "FCC离线时不应该返回错误")
	assert.NotEmpty(t, nozzles, "应该返回离线喷嘴数据")

	// 验证返回的是离线状态
	nozzle1, ok := nozzles[0].(map[string]interface{})
	require.True(t, ok, "喷嘴数据应该是map类型")
	assert.Equal(t, "offline", nozzle1["status"], "喷嘴状态应该是offline")
	assert.Equal(t, "FCC_DISCONNECTED", nozzle1["offline_reason"], "离线原因应该是FCC_DISCONNECTED")

	t.Log("✅ FCC客户端离线场景测试通过")
}

// TestFCCClient_BatchProcessing 测试FCC客户端批量处理
func TestFCCClient_BatchProcessing(t *testing.T) {
	// 创建模拟FCC服务器，返回多个喷嘴
	fccServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		nozzles := make([]map[string]interface{}, 8) // 8个喷嘴
		for i := 0; i < 8; i++ {
			nozzles[i] = map[string]interface{}{
				"nozzle_id":      "batch-nozzle-" + string(rune('1'+i)),
				"dispenser_id":   "batch-dispenser-" + string(rune('1'+i/2)),
				"station_id":     1,
				"status":         "idle",
				"current_volume": 0.0,
				"current_amount": 0.0,
				"fuel_grade":     "RON92",
			}
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(nozzles)
	}))
	defer fccServer.Close()

	client := NewHTTPFCCClient()
	ctx := context.Background()

	nozzles, err := client.GetNozzleStatus(ctx, fccServer.URL+"/api")
	require.NoError(t, err, "批量获取喷嘴状态应该成功")
	assert.Len(t, nozzles, 8, "应该返回8个喷嘴")

	// 验证所有喷嘴都有正确的数据结构
	for i, nozzle := range nozzles {
		nozzleMap, ok := nozzle.(map[string]interface{})
		require.True(t, ok, "喷嘴 %d 数据应该是map类型", i)
		assert.Contains(t, nozzleMap, "nozzle_id", "喷嘴 %d 应该有nozzle_id", i)
		assert.Contains(t, nozzleMap, "status", "喷嘴 %d 应该有status", i)
		assert.Equal(t, "idle", nozzleMap["status"], "喷嘴 %d 状态应该是idle", i)
	}

	t.Log("✅ FCC客户端批量处理测试通过")
}

// TestWebSocketService_CoreFunctionality 测试WebSocket服务核心功能
func TestWebSocketService_CoreFunctionality(t *testing.T) {
	// 创建WebSocket服务
	service := CreateDefaultWebSocketService()
	require.NotNil(t, service, "WebSocket服务应该创建成功")

	// 启动服务
	err := service.Start()
	require.NoError(t, err, "启动WebSocket服务应该成功")
	defer service.Stop()

	// 等待服务完全启动
	time.Sleep(100 * time.Millisecond)

	// 测试交易更新消息
	transactionPayload := &TransactionUpdatePayload{
		TransactionID:     "test-tx-001",
		TransactionNumber: "TXN001",
		NozzleID:          "test-nozzle-1",
		StationID:         1,
		Status:            "completed",
		Amount:            100000.0,
		Volume:            10.0,
		FuelType:          "gasoline",
		FuelGrade:         "RON92",
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	err = service.EmitTransactionUpdate(transactionPayload)
	assert.NoError(t, err, "发送交易更新应该成功")

	// 测试仪表板更新消息
	dashboardPayload := &DashboardUpdatePayload{
		StationID:         1,
		TodayRevenue:      1000000.0,
		TodayTransactions: 100,
		TodayVolume:       500.0,
		RevenueChange:     5.0,
		TransactionChange: 3.0,
		VolumeChange:      2.0,
		LastUpdated:       time.Now(),
		FuelSalesMix:      []FuelSalesMixItem{},
	}

	err = service.EmitDashboardUpdate(dashboardPayload)
	assert.NoError(t, err, "发送仪表板更新应该成功")

	// 测试喷嘴状态更新消息
	nozzlePayload := &NozzleStatusPayload{
		NozzleID:      "test-nozzle-1",
		DispenserID:   "test-dispenser-1",
		StationID:     1,
		Status:        "filling",
		CurrentVolume: 5.5,
		CurrentAmount: 75000.0,
		FuelGrade:     "RON92",
		UpdatedAt:     time.Now(),
	}

	err = service.EmitNozzleStatusUpdate(nozzlePayload)
	assert.NoError(t, err, "发送喷嘴状态更新应该成功")

	t.Log("✅ WebSocket服务核心功能测试通过")
}

// TestWebSocketService_ErrorHandling 测试WebSocket服务错误处理
func TestWebSocketService_ErrorHandling(t *testing.T) {
	service := CreateDefaultWebSocketService()

	// 测试未启动时发送消息
	payload := &DashboardUpdatePayload{
		StationID:         1,
		TodayRevenue:      1000000.0,
		TodayTransactions: 100,
		TodayVolume:       500.0,
		RevenueChange:     5.0,
		TransactionChange: 3.0,
		VolumeChange:      2.0,
		LastUpdated:       time.Now(),
		FuelSalesMix:      []FuelSalesMixItem{},
	}

	err := service.EmitDashboardUpdate(payload)
	assert.Error(t, err, "未启动的服务发送消息应该失败")

	// 测试重复启动
	err = service.Start()
	require.NoError(t, err, "第一次启动应该成功")

	err = service.Start()
	assert.Error(t, err, "重复启动应该失败")

	// 测试正常停止
	err = service.Stop()
	assert.NoError(t, err, "停止服务应该成功")

	// 测试重复停止
	err = service.Stop()
	assert.Error(t, err, "重复停止应该失败")

	t.Log("✅ WebSocket服务错误处理测试通过")
}

// TestMessageTypes 测试消息类型
func TestMessageTypes(t *testing.T) {
	testCases := []struct {
		messageType MessageType
		description string
	}{
		{MessageTypeAuth, "认证消息"},
		{MessageTypeSubscribe, "订阅消息"},
		{MessageTypeNozzleStatus, "喷嘴状态消息"},
		{MessageTypeNozzleStatusBatch, "批量喷嘴状态消息"},
		{MessageTypeTransactionUpdate, "交易更新消息"},
		{MessageTypeDashboardUpdate, "仪表板更新消息"},
		{MessageTypeDeviceStatus, "设备状态消息"},
		{MessageTypeError, "错误消息"},
	}

	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			message := &Message{
				Type:      tc.messageType,
				Payload:   map[string]interface{}{"test": "data"},
				Timestamp: time.Now(),
				Source:    "test",
			}

			assert.Equal(t, tc.messageType, message.Type, "消息类型应该匹配")
			assert.NotNil(t, message.Payload, "消息载荷不应该为空")
			assert.NotZero(t, message.Timestamp, "消息时间戳不应该为零")
			assert.Equal(t, "test", message.Source, "消息来源应该匹配")
		})
	}

	t.Log("✅ 消息类型测试通过")
}
