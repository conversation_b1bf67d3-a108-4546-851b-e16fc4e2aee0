package websocket

import (
	"fmt"
	"log"
)

// ErrorCode 错误代码类型
type ErrorCode string

const (
	// 连接相关错误
	ErrCodeConnectionFailed   ErrorCode = "CONNECTION_FAILED"
	ErrCodeConnectionClosed   ErrorCode = "CONNECTION_CLOSED"
	ErrCodeConnectionTimeout  ErrorCode = "CONNECTION_TIMEOUT"
	ErrCodeMaxConnectionsExceeded ErrorCode = "MAX_CONNECTIONS_EXCEEDED"

	// 认证相关错误
	ErrCodeAuthFailed         ErrorCode = "AUTH_FAILED"
	ErrCodeInvalidToken       ErrorCode = "INVALID_TOKEN"
	ErrCodeTokenExpired       ErrorCode = "TOKEN_EXPIRED"
	ErrCodeUnauthorized       ErrorCode = "UNAUTHORIZED"

	// 消息相关错误
	ErrCodeInvalidMessage     ErrorCode = "INVALID_MESSAGE"
	ErrCodeMessageTooLarge    ErrorCode = "MESSAGE_TOO_LARGE"
	ErrCodeUnsupportedMessageType ErrorCode = "UNSUPPORTED_MESSAGE_TYPE"
	ErrCodeMessageSendFailed  ErrorCode = "MESSAGE_SEND_FAILED"

	// 订阅相关错误
	ErrCodeSubscriptionFailed ErrorCode = "SUBSCRIPTION_FAILED"
	ErrCodeInvalidSubscription ErrorCode = "INVALID_SUBSCRIPTION"

	// 系统相关错误
	ErrCodeServiceUnavailable ErrorCode = "SERVICE_UNAVAILABLE"
	ErrCodeInternalError      ErrorCode = "INTERNAL_ERROR"
	ErrCodeResourceExhausted  ErrorCode = "RESOURCE_EXHAUSTED"
)

// WebSocketError WebSocket错误类型
type WebSocketError struct {
	Code    ErrorCode `json:"code"`
	Message string    `json:"message"`
	Detail  string    `json:"detail,omitempty"`
	Cause   error     `json:"-"` // 原始错误，不序列化
}

// Error 实现error接口
func (e *WebSocketError) Error() string {
	if e.Detail != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Code, e.Message, e.Detail)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// Unwrap 支持错误链
func (e *WebSocketError) Unwrap() error {
	return e.Cause
}

// NewWebSocketError 创建WebSocket错误
func NewWebSocketError(code ErrorCode, message string, detail string, cause error) *WebSocketError {
	return &WebSocketError{
		Code:    code,
		Message: message,
		Detail:  detail,
		Cause:   cause,
	}
}

// ErrorHandler 错误处理器
type ErrorHandler struct {
	// 是否启用详细日志
	EnableVerboseLogging bool
}

// NewErrorHandler 创建错误处理器
func NewErrorHandler(enableVerboseLogging bool) *ErrorHandler {
	return &ErrorHandler{
		EnableVerboseLogging: enableVerboseLogging,
	}
}

// HandleError 处理错误
func (eh *ErrorHandler) HandleError(err error, context string) {
	if err == nil {
		return
	}

	// 根据错误类型决定日志级别
	if wsErr, ok := err.(*WebSocketError); ok {
		eh.handleWebSocketError(wsErr, context)
	} else {
		eh.handleGenericError(err, context)
	}
}

// handleWebSocketError 处理WebSocket特定错误
func (eh *ErrorHandler) handleWebSocketError(err *WebSocketError, context string) {
	logMsg := fmt.Sprintf("[%s] %s", context, err.Error())

	switch err.Code {
	case ErrCodeConnectionClosed, ErrCodeConnectionTimeout:
		// 连接关闭是正常情况，使用INFO级别
		if eh.EnableVerboseLogging {
			log.Printf("INFO: %s", logMsg)
		}
	case ErrCodeAuthFailed, ErrCodeInvalidToken, ErrCodeUnauthorized:
		// 认证错误需要关注，使用WARN级别
		log.Printf("WARN: %s", logMsg)
	case ErrCodeInternalError, ErrCodeServiceUnavailable:
		// 系统错误需要立即关注，使用ERROR级别
		log.Printf("ERROR: %s", logMsg)
		if err.Cause != nil && eh.EnableVerboseLogging {
			log.Printf("ERROR: Caused by: %v", err.Cause)
		}
	default:
		// 其他错误使用WARN级别
		log.Printf("WARN: %s", logMsg)
	}
}

// handleGenericError 处理通用错误
func (eh *ErrorHandler) handleGenericError(err error, context string) {
	log.Printf("ERROR: [%s] %v", context, err)
}

// LogInfo 记录信息日志
func (eh *ErrorHandler) LogInfo(context, message string, args ...interface{}) {
	if eh.EnableVerboseLogging {
		logMsg := fmt.Sprintf(message, args...)
		log.Printf("INFO: [%s] %s", context, logMsg)
	}
}

// LogWarn 记录警告日志
func (eh *ErrorHandler) LogWarn(context, message string, args ...interface{}) {
	logMsg := fmt.Sprintf(message, args...)
	log.Printf("WARN: [%s] %s", context, logMsg)
}

// LogError 记录错误日志
func (eh *ErrorHandler) LogError(context, message string, args ...interface{}) {
	logMsg := fmt.Sprintf(message, args...)
	log.Printf("ERROR: [%s] %s", context, logMsg)
}

// 预定义的常用错误
var (
	ErrConnectionFailed = NewWebSocketError(
		ErrCodeConnectionFailed,
		"WebSocket connection failed",
		"",
		nil,
	)

	ErrMaxConnectionsExceeded = NewWebSocketError(
		ErrCodeMaxConnectionsExceeded,
		"Maximum connections exceeded",
		"",
		nil,
	)

	ErrInvalidToken = NewWebSocketError(
		ErrCodeInvalidToken,
		"Invalid authentication token",
		"",
		nil,
	)

	ErrServiceUnavailable = NewWebSocketError(
		ErrCodeServiceUnavailable,
		"WebSocket service is unavailable",
		"",
		nil,
	)
)

// WrapError 包装错误为WebSocket错误
func WrapError(code ErrorCode, message string, cause error) *WebSocketError {
	detail := ""
	if cause != nil {
		detail = cause.Error()
	}
	return NewWebSocketError(code, message, detail, cause)
}
