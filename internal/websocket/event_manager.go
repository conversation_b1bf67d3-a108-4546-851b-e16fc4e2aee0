package websocket

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// EventManager 事件管理器
type EventManager struct {
	hub        *HubImpl
	sources    map[string]EventSource
	sourcesMux sync.RWMutex
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup

	// 事件处理配置
	config *EventManagerConfig
}

// EventManagerConfig 事件管理器配置
type EventManagerConfig struct {
	BufferSize      int           `json:"buffer_size"`
	ProcessInterval time.Duration `json:"process_interval"`
	BatchSize       int           `json:"batch_size"`
	EnableDedup     bool          `json:"enable_dedup"`
	DedupWindow     time.Duration `json:"dedup_window"`
}

// DefaultEventManagerConfig 默认事件管理器配置
func DefaultEventManagerConfig() *EventManagerConfig {
	return &EventManagerConfig{
		BufferSize:      1000,
		ProcessInterval: 100 * time.Millisecond,
		BatchSize:       50,
		EnableDedup:     true,
		DedupWindow:     5 * time.Second,
	}
}

// NewEventManager 创建新的事件管理器
func NewEventManager(hub *HubImpl, config *EventManagerConfig) *EventManager {
	if config == nil {
		config = DefaultEventManagerConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &EventManager{
		hub:     hub,
		sources: make(map[string]EventSource),
		ctx:     ctx,
		cancel:  cancel,
		config:  config,
	}
}

// Start 启动事件管理器
func (em *EventManager) Start() error {
	em.wg.Add(1)
	go em.run()

	log.Println("Event Manager started")
	return nil
}

// Stop 停止事件管理器
func (em *EventManager) Stop() error {
	em.cancel()
	em.wg.Wait()

	// 停止所有事件源
	em.sourcesMux.RLock()
	for _, source := range em.sources {
		source.Stop()
	}
	em.sourcesMux.RUnlock()

	log.Println("Event Manager stopped")
	return nil
}

// AddEventSource 添加事件源
func (em *EventManager) AddEventSource(source EventSource) error {
	em.sourcesMux.Lock()
	defer em.sourcesMux.Unlock()

	name := source.Name()
	if _, exists := em.sources[name]; exists {
		return fmt.Errorf("事件源已存在: %s", name)
	}

	em.sources[name] = source

	// 启动事件源
	if err := source.Start(em.ctx); err != nil {
		delete(em.sources, name)
		return fmt.Errorf("启动事件源失败: %w", err)
	}

	log.Printf("事件源已添加: %s", name)
	return nil
}

// RemoveEventSource 移除事件源
func (em *EventManager) RemoveEventSource(name string) error {
	em.sourcesMux.Lock()
	defer em.sourcesMux.Unlock()

	source, exists := em.sources[name]
	if !exists {
		return fmt.Errorf("事件源不存在: %s", name)
	}

	source.Stop()
	delete(em.sources, name)

	log.Printf("事件源已移除: %s", name)
	return nil
}

// run 事件管理器主循环
func (em *EventManager) run() {
	defer em.wg.Done()

	ticker := time.NewTicker(em.config.ProcessInterval)
	defer ticker.Stop()

	eventBuffer := make([]*Message, 0, em.config.BatchSize)
	dedupMap := make(map[string]time.Time)

	for {
		select {
		case <-ticker.C:
			// 处理缓冲的事件
			if len(eventBuffer) > 0 {
				em.processEvents(eventBuffer)
				eventBuffer = eventBuffer[:0]
			}

			// 清理去重映射
			if em.config.EnableDedup {
				em.cleanupDedupMap(dedupMap)
			}

		case <-em.ctx.Done():
			// 处理剩余事件
			if len(eventBuffer) > 0 {
				em.processEvents(eventBuffer)
			}
			return
		default:
			// 收集事件
			event := em.collectEvent()
			if event != nil {
				// 去重检查
				if em.config.EnableDedup && em.isDuplicate(event, dedupMap) {
					continue
				}

				eventBuffer = append(eventBuffer, event)

				// 批量处理
				if len(eventBuffer) >= em.config.BatchSize {
					em.processEvents(eventBuffer)
					eventBuffer = eventBuffer[:0]
				}
			}
		}
	}
}

// collectEvent 收集事件
func (em *EventManager) collectEvent() *Message {
	em.sourcesMux.RLock()
	defer em.sourcesMux.RUnlock()

	for _, source := range em.sources {
		select {
		case event := <-source.Events():
			return event
		default:
			continue
		}
	}

	return nil
}

// processEvents 处理事件批次
func (em *EventManager) processEvents(events []*Message) {
	for _, event := range events {
		if err := em.hub.BroadcastMessage(event); err != nil {
			log.Printf("广播事件失败: %v", err)
		}
	}

	log.Printf("处理了 %d 个事件", len(events))
}

// isDuplicate 检查是否为重复事件
func (em *EventManager) isDuplicate(event *Message, dedupMap map[string]time.Time) bool {
	key := em.generateDedupKey(event)
	if key == "" {
		return false
	}

	if lastTime, exists := dedupMap[key]; exists {
		if time.Since(lastTime) < em.config.DedupWindow {
			return true
		}
	}

	dedupMap[key] = time.Now()
	return false
}

// generateDedupKey 生成去重键
func (em *EventManager) generateDedupKey(event *Message) string {
	switch event.Type {
	case MessageTypeNozzleStatus:
		if payload, ok := event.Payload.(*NozzleStatusPayload); ok {
			return fmt.Sprintf("nozzle_%s_%s", payload.NozzleID, payload.Status)
		}
	case MessageTypeTransactionUpdate:
		if payload, ok := event.Payload.(*TransactionUpdatePayload); ok {
			return fmt.Sprintf("transaction_%s_%s", payload.TransactionID, payload.Status)
		}
	case MessageTypeShiftStatus:
		if payload, ok := event.Payload.(*ShiftStatusPayload); ok {
			return fmt.Sprintf("shift_%s_%s", payload.ShiftID, payload.Status)
		}
	}

	return ""
}

// cleanupDedupMap 清理去重映射
func (em *EventManager) cleanupDedupMap(dedupMap map[string]time.Time) {
	now := time.Now()
	for key, timestamp := range dedupMap {
		if now.Sub(timestamp) > em.config.DedupWindow*2 {
			delete(dedupMap, key)
		}
	}
}

// GetEventSources 获取所有事件源
func (em *EventManager) GetEventSources() []string {
	em.sourcesMux.RLock()
	defer em.sourcesMux.RUnlock()

	sources := make([]string, 0, len(em.sources))
	for name := range em.sources {
		sources = append(sources, name)
	}

	return sources
}

// FCCEventSource FCC事件源实现
type FCCEventSource struct {
	name   string
	events chan *Message
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// FCC配置
	fccConfigs []FCCConfig
	client     FCCClient
}

// FCCConfig FCC配置
type FCCConfig struct {
	ID         string   `json:"id"`
	PrimaryURL string   `json:"primary_url"`
	HealthURL  string   `json:"health_url"`
	DeviceIDs  []string `json:"device_ids"`
}

// FCCClient FCC客户端接口
type FCCClient interface {
	GetDeviceStatus(ctx context.Context, url string) ([]interface{}, error)
	GetNozzleStatus(ctx context.Context, url string) ([]interface{}, error)
}

// NewFCCEventSource 创建FCC事件源
func NewFCCEventSource(name string, configs []FCCConfig, client FCCClient) *FCCEventSource {
	return &FCCEventSource{
		name:       name,
		events:     make(chan *Message, 1000),
		fccConfigs: configs,
		client:     client,
	}
}

// Start 启动FCC事件源
func (fes *FCCEventSource) Start(ctx context.Context) error {
	fes.ctx, fes.cancel = context.WithCancel(ctx)

	fes.wg.Add(1)
	go fes.run()

	log.Printf("FCC事件源已启动: %s", fes.name)
	return nil
}

// Stop 停止FCC事件源
func (fes *FCCEventSource) Stop() error {
	fes.cancel()
	fes.wg.Wait()
	close(fes.events)

	log.Printf("FCC事件源已停止: %s", fes.name)
	return nil
}

// Events 获取事件通道
func (fes *FCCEventSource) Events() <-chan *Message {
	return fes.events
}

// Name 获取事件源名称
func (fes *FCCEventSource) Name() string {
	return fes.name
}

// run FCC事件源主循环
func (fes *FCCEventSource) run() {
	defer fes.wg.Done()

	ticker := time.NewTicker(2 * time.Second) // 2秒轮询频率
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			fes.pollFCCServices()
		case <-fes.ctx.Done():
			return
		}
	}
}

// pollFCCServices 轮询FCC服务
func (fes *FCCEventSource) pollFCCServices() {
	for _, config := range fes.fccConfigs {
		// 轮询设备状态
		if devices, err := fes.client.GetDeviceStatus(fes.ctx, config.PrimaryURL); err == nil {
			fes.processDeviceStatus(devices)
		} else {
			log.Printf("轮询FCC设备状态失败 %s: %v", config.ID, err)
		}

		// 轮询喷嘴状态
		if nozzles, err := fes.client.GetNozzleStatus(fes.ctx, config.PrimaryURL); err == nil {
			fes.processNozzleStatus(nozzles)
		} else {
			log.Printf("轮询FCC喷嘴状态失败 %s: %v", config.ID, err)
		}
	}
}

// processDeviceStatus 处理设备状态
func (fes *FCCEventSource) processDeviceStatus(devices []interface{}) {
	for _, device := range devices {
		message := &Message{
			Type:      MessageTypeDeviceStatus,
			Payload:   device,
			Timestamp: time.Now(),
			Source:    "fcc",
		}

		select {
		case fes.events <- message:
		default:
			log.Printf("FCC事件缓冲区已满，丢弃设备状态事件")
		}
	}
}

// processNozzleStatus 处理喷嘴状态 (批量推送)
func (fes *FCCEventSource) processNozzleStatus(nozzles []interface{}) {
	if len(nozzles) == 0 {
		return
	}

	// 批量推送所有喷嘴状态
	batchMessage := &Message{
		Type: MessageTypeNozzleStatusBatch,
		Payload: map[string]interface{}{
			"nozzles":    nozzles,
			"count":      len(nozzles),
			"batch_time": time.Now(),
		},
		Timestamp: time.Now(),
		Source:    "fcc",
	}

	select {
	case fes.events <- batchMessage:
		log.Printf("📦 批量推送喷嘴状态: %d个喷嘴", len(nozzles))
	default:
		log.Printf("❌ FCC事件缓冲区已满，丢弃批量喷嘴状态事件")
	}
}

// BOSEventSource BOS事件源实现
type BOSEventSource struct {
	name   string
	events chan *Message
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// NewBOSEventSource 创建BOS事件源
func NewBOSEventSource(name string) *BOSEventSource {
	return &BOSEventSource{
		name:   name,
		events: make(chan *Message, 1000),
	}
}

// Start 启动BOS事件源
func (bes *BOSEventSource) Start(ctx context.Context) error {
	bes.ctx, bes.cancel = context.WithCancel(ctx)

	log.Printf("BOS事件源已启动: %s", bes.name)
	return nil
}

// Stop 停止BOS事件源
func (bes *BOSEventSource) Stop() error {
	bes.cancel()
	close(bes.events)

	log.Printf("BOS事件源已停止: %s", bes.name)
	return nil
}

// Events 获取事件通道
func (bes *BOSEventSource) Events() <-chan *Message {
	return bes.events
}

// Name 获取事件源名称
func (bes *BOSEventSource) Name() string {
	return bes.name
}

// EmitEvent 发送事件
func (bes *BOSEventSource) EmitEvent(event *Message) {
	select {
	case bes.events <- event:
	default:
		log.Printf("BOS事件缓冲区已满，丢弃事件: %s", event.Type)
	}
}
