package websocket

import (
	"encoding/json"
	"log"
)

// StationFilter 站点过滤器
type StationFilter struct{}

// NewStationFilter 创建站点过滤器
func NewStationFilter() *StationFilter {
	return &StationFilter{}
}

// ShouldSend 检查是否应该发送消息给客户端
func (sf *StationFilter) ShouldSend(client *Client, message *Message) bool {
	// 如果客户端没有指定站点，不发送
	if client.StationID == 0 {
		return false
	}
	
	// 根据消息类型检查站点匹配
	switch message.Type {
	case MessageTypeNozzleStatus:
		return sf.checkNozzleStatus(client, message)
	case MessageTypeTransactionUpdate:
		return sf.checkTransactionUpdate(client, message)
	case MessageTypeShiftStatus:
		return sf.checkShiftStatus(client, message)
	case MessageTypeDashboardUpdate:
		return sf.checkDashboardUpdate(client, message)
	case MessageTypeDeviceConfig, MessageTypeDeviceStatus:
		return sf.checkDeviceMessage(client, message)
	case MessageTypePriceUpdate:
		return sf.checkPriceUpdate(client, message)
	default:
		// 系统消息（心跳、错误等）发送给所有客户端
		return true
	}
}

// checkNozzleStatus 检查喷嘴状态消息
func (sf *StationFilter) checkNozzleStatus(client *Client, message *Message) bool {
	payload, ok := sf.parsePayload(message.Payload, &NozzleStatusPayload{})
	if !ok {
		return false
	}
	
	nozzlePayload := payload.(*NozzleStatusPayload)
	
	// 检查站点匹配
	if nozzlePayload.StationID != client.StationID {
		return false
	}
	
	// 检查喷嘴过滤器
	if client.Filters != nil && len(client.Filters.NozzleIDs) > 0 {
		for _, nozzleID := range client.Filters.NozzleIDs {
			if nozzleID == nozzlePayload.NozzleID {
				return true
			}
		}
		return false
	}
	
	// 检查分配器过滤器
	if client.Filters != nil && len(client.Filters.DispenserIDs) > 0 {
		for _, dispenserID := range client.Filters.DispenserIDs {
			if dispenserID == nozzlePayload.DispenserID {
				return true
			}
		}
		return false
	}
	
	return true
}

// checkTransactionUpdate 检查交易更新消息
func (sf *StationFilter) checkTransactionUpdate(client *Client, message *Message) bool {
	payload, ok := sf.parsePayload(message.Payload, &TransactionUpdatePayload{})
	if !ok {
		return false
	}
	
	transactionPayload := payload.(*TransactionUpdatePayload)
	
	// 检查站点匹配
	if transactionPayload.StationID != client.StationID {
		return false
	}
	
	// 检查喷嘴过滤器
	if client.Filters != nil && len(client.Filters.NozzleIDs) > 0 {
		for _, nozzleID := range client.Filters.NozzleIDs {
			if nozzleID == transactionPayload.NozzleID {
				return true
			}
		}
		return false
	}
	
	return true
}

// checkShiftStatus 检查班次状态消息
func (sf *StationFilter) checkShiftStatus(client *Client, message *Message) bool {
	payload, ok := sf.parsePayload(message.Payload, &ShiftStatusPayload{})
	if !ok {
		return false
	}
	
	shiftPayload := payload.(*ShiftStatusPayload)
	
	// 检查站点匹配
	return shiftPayload.StationID == client.StationID
}

// checkDashboardUpdate 检查仪表板更新消息
func (sf *StationFilter) checkDashboardUpdate(client *Client, message *Message) bool {
	payload, ok := sf.parsePayload(message.Payload, &DashboardUpdatePayload{})
	if !ok {
		return false
	}
	
	dashboardPayload := payload.(*DashboardUpdatePayload)
	
	// 检查站点匹配
	return dashboardPayload.StationID == client.StationID
}

// checkDeviceMessage 检查设备消息
func (sf *StationFilter) checkDeviceMessage(client *Client, message *Message) bool {
	payload, ok := sf.parsePayload(message.Payload, &DeviceConfigPayload{})
	if !ok {
		return false
	}
	
	devicePayload := payload.(*DeviceConfigPayload)
	
	// 检查站点匹配
	return devicePayload.StationID == client.StationID
}

// checkPriceUpdate 检查价格更新消息
func (sf *StationFilter) checkPriceUpdate(client *Client, message *Message) bool {
	payload, ok := sf.parsePayload(message.Payload, &PriceUpdatePayload{})
	if !ok {
		return false
	}
	
	pricePayload := payload.(*PriceUpdatePayload)
	
	// 检查站点匹配
	return pricePayload.StationID == client.StationID
}

// parsePayload 解析消息载荷
func (sf *StationFilter) parsePayload(payload interface{}, target interface{}) (interface{}, bool) {
	data, err := json.Marshal(payload)
	if err != nil {
		log.Printf("序列化载荷失败: %v", err)
		return nil, false
	}
	
	if err := json.Unmarshal(data, target); err != nil {
		log.Printf("反序列化载荷失败: %v", err)
		return nil, false
	}
	
	return target, true
}

// RoleFilter 角色过滤器
type RoleFilter struct {
	rolePermissions map[string][]MessageType
}

// NewRoleFilter 创建角色过滤器
func NewRoleFilter() *RoleFilter {
	return &RoleFilter{
		rolePermissions: map[string][]MessageType{
			"admin": {
				MessageTypeNozzleStatus,
				MessageTypeTransactionUpdate,
				MessageTypeShiftStatus,
				MessageTypeDashboardUpdate,
				MessageTypeDeviceConfig,
				MessageTypeDeviceStatus,
				MessageTypePriceUpdate,
				MessageTypePromotionUpdate,
				MessageTypeOrderUpdate,
			},
			"manager": {
				MessageTypeNozzleStatus,
				MessageTypeTransactionUpdate,
				MessageTypeShiftStatus,
				MessageTypeDashboardUpdate,
				MessageTypeDeviceStatus,
				MessageTypeOrderUpdate,
			},
			"operator": {
				MessageTypeNozzleStatus,
				MessageTypeTransactionUpdate,
				MessageTypeShiftStatus,
				MessageTypeOrderUpdate,
			},
			"viewer": {
				MessageTypeNozzleStatus,
				MessageTypeDashboardUpdate,
			},
		},
	}
}

// ShouldSend 检查角色权限
func (rf *RoleFilter) ShouldSend(client *Client, message *Message) bool {
	// 系统消息总是发送
	if message.Type == MessageTypeHeartbeat || message.Type == MessageTypeError {
		return true
	}
	
	// 获取用户角色（这里需要从认证信息中获取）
	// 暂时假设所有用户都是operator角色
	userRole := "operator"
	
	permissions, exists := rf.rolePermissions[userRole]
	if !exists {
		return false
	}
	
	for _, allowedType := range permissions {
		if allowedType == message.Type {
			return true
		}
	}
	
	return false
}

// TopicFilter 主题过滤器
type TopicFilter struct{}

// NewTopicFilter 创建主题过滤器
func NewTopicFilter() *TopicFilter {
	return &TopicFilter{}
}

// ShouldSend 检查主题订阅
func (tf *TopicFilter) ShouldSend(client *Client, message *Message) bool {
	// 如果客户端没有订阅任何主题，发送所有消息
	if len(client.Subscriptions) == 0 {
		return true
	}
	
	// 检查是否订阅了该消息类型
	subscribed, exists := client.Subscriptions[message.Type]
	return exists && subscribed
}

// RateLimitFilter 限流过滤器
type RateLimitFilter struct {
	clientLimits map[string]*ClientRateLimit
}

// ClientRateLimit 客户端限流信息
type ClientRateLimit struct {
	MessageCount int
	LastReset    int64
	Limit        int
	Window       int64 // 时间窗口（秒）
}

// NewRateLimitFilter 创建限流过滤器
func NewRateLimitFilter() *RateLimitFilter {
	return &RateLimitFilter{
		clientLimits: make(map[string]*ClientRateLimit),
	}
}

// ShouldSend 检查限流
func (rlf *RateLimitFilter) ShouldSend(client *Client, message *Message) bool {
	// 系统消息不限流
	if message.Type == MessageTypeHeartbeat || message.Type == MessageTypeError {
		return true
	}
	
	now := message.Timestamp.Unix()
	
	limit, exists := rlf.clientLimits[client.ID]
	if !exists {
		limit = &ClientRateLimit{
			MessageCount: 0,
			LastReset:    now,
			Limit:        100, // 每分钟100条消息
			Window:       60,  // 60秒窗口
		}
		rlf.clientLimits[client.ID] = limit
	}
	
	// 检查是否需要重置计数器
	if now-limit.LastReset >= limit.Window {
		limit.MessageCount = 0
		limit.LastReset = now
	}
	
	// 检查是否超过限制
	if limit.MessageCount >= limit.Limit {
		log.Printf("客户端 %s 消息限流", client.ID)
		return false
	}
	
	limit.MessageCount++
	return true
}

// CompositeFilter 组合过滤器
type CompositeFilter struct {
	filters []MessageFilter
}

// NewCompositeFilter 创建组合过滤器
func NewCompositeFilter(filters ...MessageFilter) *CompositeFilter {
	return &CompositeFilter{
		filters: filters,
	}
}

// ShouldSend 检查所有过滤器
func (cf *CompositeFilter) ShouldSend(client *Client, message *Message) bool {
	for _, filter := range cf.filters {
		if !filter.ShouldSend(client, message) {
			return false
		}
	}
	return true
}

// AddFilter 添加过滤器
func (cf *CompositeFilter) AddFilter(filter MessageFilter) {
	cf.filters = append(cf.filters, filter)
}

// DefaultMessageFilters 创建默认消息过滤器
func DefaultMessageFilters() MessageFilter {
	return NewCompositeFilter(
		NewStationFilter(),
		NewTopicFilter(),
		NewRateLimitFilter(),
	)
}
