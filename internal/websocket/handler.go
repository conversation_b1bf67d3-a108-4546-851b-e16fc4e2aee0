package websocket

import (
	"context"
	"encoding/json"
	"log"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
)

// Handler WebSocket处理器
type Handler struct {
	hub          *HubImpl
	upgrader     websocket.Upgrader
	auth         AuthService
	errorHandler *ErrorHandler
}

// AuthService 认证服务接口
type AuthService interface {
	ValidateToken(token string) (*UserInfo, error)
}

// UserInfo 用户信息
type UserInfo struct {
	UserID    string `json:"user_id"`
	Username  string `json:"username"`
	StationID int64  `json:"station_id"`
	Role      string `json:"role"`
}

// NewHandler 创建新的WebSocket处理器
func NewHandler(hub *HubImpl, auth AuthService) *Handler {
	return &Handler{
		hub:  hub,
		auth: auth,
		upgrader: websocket.Upgrader{
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			CheckOrigin: func(r *http.Request) bool {
				// TODO: 实现更严格的跨域检查
				return true
			},
		},
		errorHandler: NewErrorHandler(true), // 启用详细日志
	}
}

// HandleWebSocket 处理WebSocket连接
func (h *Handler) HandleWebSocket(c echo.Context) error {
	log.Printf("🔗 [DEBUG] 收到WebSocket连接请求: %s", c.Request().RemoteAddr)

	// 升级HTTP连接为WebSocket
	conn, err := h.upgrader.Upgrade(c.Response(), c.Request(), nil)
	if err != nil {
		log.Printf("❌ [ERROR] WebSocket升级失败: %v", err)
		wsErr := WrapError(ErrCodeConnectionFailed, "WebSocket升级失败", err)
		h.errorHandler.HandleError(wsErr, "HandleWebSocket")
		return err
	}

	// 创建客户端
	client := &Client{
		ID:            uuid.New().String(),
		Conn:          conn,
		Send:          make(chan []byte, 256),
		LastPing:      time.Now(),
		CreatedAt:     time.Now(),
		Subscriptions: make(map[MessageType]bool),
		IsActive:      true,
	}

	log.Printf("✅ [DEBUG] WebSocket连接成功，客户端ID: %s, 远程地址: %s", client.ID, c.Request().RemoteAddr)

	// 设置客户端上下文
	client.Context, client.Cancel = context.WithCancel(context.Background())

	// 启动客户端处理协程
	go h.handleClient(client)

	return nil
}

// handleClient 处理客户端连接
func (h *Handler) handleClient(client *Client) {
	defer func() {
		// 正确的清理顺序：先关闭连接，再取消上下文，最后从Hub移除
		client.Conn.Close()
		client.Cancel()
		h.hub.RemoveClient(client.ID)
	}()

	// 启动写入协程
	go h.writePump(client)

	// 启动读取协程（阻塞直到连接关闭）
	h.readPump(client)
}

// readPump 读取客户端消息
func (h *Handler) readPump(client *Client) {
	// 设置读取超时
	client.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	client.Conn.SetPongHandler(func(string) error {
		client.LastPing = time.Now()
		client.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		select {
		case <-client.Context.Done():
			return
		default:
			_, messageData, err := client.Conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Printf("WebSocket读取错误: %v", err)
				}
				return
			}

			// 处理接收到的消息
			h.handleMessage(client, messageData)
		}
	}
}

// writePump 向客户端写入消息
func (h *Handler) writePump(client *Client) {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		client.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-client.Send:
			client.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				client.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := client.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 批量发送队列中的其他消息
			n := len(client.Send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-client.Send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			client.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := client.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}

		case <-client.Context.Done():
			return
		}
	}
}

// handleMessage 处理客户端消息
func (h *Handler) handleMessage(client *Client, messageData []byte) {
	log.Printf("🔍 [DEBUG] 收到客户端消息: %s, 数据: %s", client.ID, string(messageData))

	var message Message
	if err := json.Unmarshal(messageData, &message); err != nil {
		log.Printf("❌ [ERROR] 消息解析失败: %v, 原始数据: %s", err, string(messageData))
		wsErr := WrapError(ErrCodeInvalidMessage, "消息格式无效", err)
		h.errorHandler.HandleError(wsErr, "HandleMessage")
		h.sendError(client, string(ErrCodeInvalidMessage), "消息格式无效", err.Error())
		return
	}

	log.Printf("✅ [DEBUG] 消息解析成功: 类型=%s, 客户端=%s", message.Type, client.ID)
	log.Printf("🔍 [DEBUG] 消息类型详细信息: 原始值='%s', 长度=%d, MessageTypeAuth='%s'",
		string(message.Type), len(string(message.Type)), string(MessageTypeAuth))

	// 移除有问题的统计更新，避免死锁
	// TODO: 使用原子操作或者异步更新统计
	log.Printf("🔍 [DEBUG] 跳过统计更新以避免死锁")

	// 根据消息类型处理
	log.Printf("🔍 [DEBUG] 开始处理消息类型: '%s'", message.Type)
	switch message.Type {
	case MessageTypeAuth:
		log.Printf("🔐 [DEBUG] 匹配到认证消息: 客户端=%s", client.ID)
		h.handleAuth(client, &message)
	case MessageTypeSubscribe:
		log.Printf("📋 [DEBUG] 匹配到订阅消息: 客户端=%s", client.ID)
		h.handleSubscribe(client, &message)
	case MessageTypeUnsubscribe:
		log.Printf("📋 [DEBUG] 匹配到取消订阅消息: 客户端=%s", client.ID)
		h.handleUnsubscribe(client, &message)
	case MessageTypeHeartbeat:
		log.Printf("💓 [DEBUG] 匹配到心跳消息: 客户端=%s", client.ID)
		h.handleHeartbeat(client, &message)
	default:
		log.Printf("❌ [ERROR] 进入default分支，不支持的消息类型: '%s', 客户端=%s", message.Type, client.ID)
		log.Printf("🔍 [DEBUG] 比较结果: message.Type == MessageTypeAuth: %v", message.Type == MessageTypeAuth)
		log.Printf("🔍 [DEBUG] 比较结果: string(message.Type) == 'auth': %v", string(message.Type) == "auth")
		log.Printf("🔍 [DEBUG] MessageTypeAuth常量值: '%s'", MessageTypeAuth)
		wsErr := WrapError(ErrCodeUnsupportedMessageType, "不支持的消息类型", nil)
		h.errorHandler.HandleError(wsErr, "HandleMessage")
		h.sendError(client, string(ErrCodeUnsupportedMessageType), "不支持的消息类型", string(message.Type))
	}
}

// handleAuth 处理认证消息
func (h *Handler) handleAuth(client *Client, message *Message) {
	log.Printf("🔐 [DEBUG] 开始处理认证: 客户端=%s", client.ID)

	var authMsg AuthMessage
	if err := h.parsePayload(message.Payload, &authMsg); err != nil {
		log.Printf("❌ [ERROR] 认证载荷解析失败: %v, 客户端=%s", err, client.ID)
		h.sendError(client, "INVALID_AUTH_PAYLOAD", "认证载荷无效", err.Error())
		return
	}

	log.Printf("🔐 [DEBUG] 认证载荷解析成功: token=%s, 客户端=%s", authMsg.Token, client.ID)

	// 验证token
	userInfo, err := h.auth.ValidateToken(authMsg.Token)
	if err != nil {
		log.Printf("❌ [ERROR] Token验证失败: %v, token=%s, 客户端=%s", err, authMsg.Token, client.ID)
		h.sendError(client, "AUTH_FAILED", "认证失败", err.Error())
		return
	}

	log.Printf("✅ [DEBUG] Token验证成功: 用户=%s, 站点=%d, 客户端=%s", userInfo.UserID, userInfo.StationID, client.ID)

	// 更新客户端信息
	client.UserID = userInfo.UserID
	client.StationID = userInfo.StationID

	// 注册客户端到Hub
	if err := h.hub.AddClient(client); err != nil {
		log.Printf("❌ [ERROR] 客户端注册失败: %v, 客户端=%s", err, client.ID)
		h.sendError(client, "REGISTRATION_FAILED", "注册失败", err.Error())
		return
	}

	log.Printf("✅ [DEBUG] 客户端注册成功: %s", client.ID)

	// 发送认证成功响应
	response := &Message{
		Type: MessageTypeAuth,
		Payload: map[string]interface{}{
			"success":    true,
			"client_id":  client.ID,
			"user_id":    client.UserID,
			"station_id": client.StationID,
		},
		Timestamp: time.Now(),
		Source:    "bos",
	}

	log.Printf("📤 [DEBUG] 发送认证成功响应: 客户端=%s", client.ID)
	h.sendMessage(client, response)

	// 发送初始数据快照
	log.Printf("📊 [DEBUG] 发送初始数据快照: 客户端=%s", client.ID)
	h.sendInitialSnapshot(client)

	log.Printf("🎉 [SUCCESS] 客户端认证成功: %s (用户: %s, 站点: %d)", client.ID, client.UserID, client.StationID)
}

// handleSubscribe 处理订阅消息
func (h *Handler) handleSubscribe(client *Client, message *Message) {
	var subMsg SubscribeMessage
	if err := h.parsePayload(message.Payload, &subMsg); err != nil {
		h.sendError(client, "INVALID_SUBSCRIBE_PAYLOAD", "订阅载荷无效", err.Error())
		return
	}

	// 更新客户端过滤器
	if subMsg.Filters != nil {
		client.Filters = subMsg.Filters
	}

	// 更新订阅
	if subMsg.Filters != nil && subMsg.Filters.Topics != nil {
		for _, topic := range subMsg.Filters.Topics {
			client.Subscriptions[topic] = true
		}
	}

	// 发送订阅成功响应
	response := &Message{
		Type: MessageTypeSubscribe,
		Payload: map[string]interface{}{
			"success":       true,
			"subscriptions": client.Subscriptions,
		},
		Timestamp: time.Now(),
		Source:    "bos",
	}

	h.sendMessage(client, response)
	log.Printf("客户端订阅更新: %s", client.ID)
}

// handleUnsubscribe 处理取消订阅消息
func (h *Handler) handleUnsubscribe(client *Client, message *Message) {
	var topics []MessageType
	if err := h.parsePayload(message.Payload, &topics); err != nil {
		h.sendError(client, "INVALID_UNSUBSCRIBE_PAYLOAD", "取消订阅载荷无效", err.Error())
		return
	}

	// 移除订阅
	for _, topic := range topics {
		delete(client.Subscriptions, topic)
	}

	// 发送取消订阅成功响应
	response := &Message{
		Type: MessageTypeUnsubscribe,
		Payload: map[string]interface{}{
			"success":       true,
			"subscriptions": client.Subscriptions,
		},
		Timestamp: time.Now(),
		Source:    "bos",
	}

	h.sendMessage(client, response)
	log.Printf("客户端取消订阅: %s", client.ID)
}

// handleHeartbeat 处理心跳消息
func (h *Handler) handleHeartbeat(client *Client, message *Message) {
	client.LastPing = time.Now()

	// 发送心跳响应
	response := &Message{
		Type: MessageTypeHeartbeat,
		Payload: &HeartbeatMessage{
			Timestamp: time.Now(),
			ClientID:  client.ID,
		},
		Timestamp: time.Now(),
		Source:    "bos",
	}

	h.sendMessage(client, response)
}

// sendMessage 发送消息给客户端
func (h *Handler) sendMessage(client *Client, message *Message) {
	data, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化消息失败: %v", err)
		return
	}

	select {
	case client.Send <- data:
	default:
		log.Printf("客户端发送缓冲区已满: %s", client.ID)
		h.hub.RemoveClient(client.ID)
	}
}

// sendError 发送错误消息
func (h *Handler) sendError(client *Client, code, message, detail string) {
	errorMsg := &Message{
		Type: MessageTypeError,
		Payload: &ErrorMessage{
			Code:    code,
			Message: message,
			Detail:  detail,
		},
		Timestamp: time.Now(),
		Source:    "bos",
	}

	h.sendMessage(client, errorMsg)
}

// sendInitialSnapshot 发送初始数据快照
func (h *Handler) sendInitialSnapshot(client *Client) {
	// 构造初始快照数据
	snapshot := &SnapshotPayload{
		Devices:            []interface{}{}, // TODO: 从设备服务获取
		Nozzles:            []interface{}{}, // TODO: 从FCC获取喷嘴状态
		ActiveTransactions: []interface{}{}, // TODO: 从交易服务获取
		CurrentShift:       nil,             // TODO: 从班次服务获取
		DashboardSummary:   nil,             // TODO: 从仪表板服务获取
		Timestamp:          time.Now(),
	}

	// 发送快照消息
	snapshotMsg := &Message{
		Type:      MessageTypeSnapshot,
		Payload:   snapshot,
		Timestamp: time.Now(),
		Source:    "bos",
	}

	h.sendMessage(client, snapshotMsg)
	log.Printf("已发送初始数据快照给客户端: %s", client.ID)
}

// parsePayload 解析消息载荷
func (h *Handler) parsePayload(payload interface{}, target interface{}) error {
	data, err := json.Marshal(payload)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, target)
}

// GetStats 获取处理器统计信息
func (h *Handler) GetStats() *HubStats {
	return h.hub.GetStats()
}
