package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"
)

// HubImpl WebSocket连接中心实现
type HubImpl struct {
	// 使用单一锁保护所有共享数据，避免死锁
	mu sync.RWMutex

	// 客户端管理
	clients map[string]*Client

	// 房间管理
	rooms map[string]*Room

	// 移除不必要的 channel，改为直接方法调用

	// 移除统计信息，避免死锁和不必要的复杂性

	// 上下文控制
	ctx    context.Context
	cancel context.CancelFunc

	// 消息过滤器
	filters []MessageFilter

	// 配置
	config *HubConfig

	// 错误处理器
	errorHandler *ErrorHandler
}

// HubConfig Hub配置
type HubConfig struct {
	MaxClients        int           `json:"max_clients"`
	HeartbeatInterval time.Duration `json:"heartbeat_interval"`
	ClientTimeout     time.Duration `json:"client_timeout"`
	BufferSize        int           `json:"buffer_size"`
	EnableStats       bool          `json:"enable_stats"`
}

// DefaultHubConfig 默认Hub配置
func DefaultHubConfig() *HubConfig {
	return &HubConfig{
		MaxClients:        1000,
		HeartbeatInterval: 30 * time.Second,
		ClientTimeout:     60 * time.Second,
		BufferSize:        256,
		EnableStats:       true,
	}
}

// NewHub 创建新的Hub实例
func NewHub(config *HubConfig) *HubImpl {
	if config == nil {
		config = DefaultHubConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &HubImpl{
		clients:      make(map[string]*Client),
		rooms:        make(map[string]*Room),
		ctx:          ctx,
		cancel:       cancel,
		filters:      make([]MessageFilter, 0),
		config:       config,
		errorHandler: NewErrorHandler(true), // 启用详细日志
	}
}

// Start 启动Hub
func (h *HubImpl) Start() {
	go h.heartbeatLoop()
	go h.cleanupLoop()
	log.Println("WebSocket Hub started")
}

// Stop 停止Hub
func (h *HubImpl) Stop() {
	h.cancel()
	log.Println("WebSocket Hub stopped")
}

// cleanupLoop 定期清理循环
func (h *HubImpl) cleanupLoop() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			h.cleanupInactiveClients()

		case <-h.ctx.Done():
			h.cleanup()
			return
		}
	}
}

// handleRegister 处理客户端注册
func (h *HubImpl) handleRegister(client *Client) {
	h.mu.Lock()
	defer h.mu.Unlock()

	// 检查最大连接数限制
	if len(h.clients) >= h.config.MaxClients {
		err := ErrMaxConnectionsExceeded
		h.errorHandler.HandleError(err, "HubRegister")
		close(client.Send)
		return
	}

	h.clients[client.ID] = client

	// 添加到相应房间 (在锁内调用，避免死锁)
	h.addClientToRoomsLocked(client)

	log.Printf("✅ [DEBUG] 客户端已注册到Hub: %s (用户: %s, 站点: %d), 总客户端数: %d",
		client.ID, client.UserID, client.StationID, len(h.clients))

	// 发送初始快照
	go h.sendSnapshot(client)
}

// handleUnregister 处理客户端注销
func (h *HubImpl) handleUnregister(client *Client) {
	h.mu.Lock()
	defer h.mu.Unlock()

	if _, exists := h.clients[client.ID]; exists {
		delete(h.clients, client.ID)
		close(client.Send)

		// 从房间中移除
		h.removeClientFromRooms(client)

		h.errorHandler.LogInfo("HubUnregister", "客户端已注销: %s", client.ID)
	}
}

// handleBroadcast 处理广播消息
func (h *HubImpl) handleBroadcast(message *Message) {
	h.mu.RLock()
	defer h.mu.RUnlock()

	sentCount := 0
	skippedCount := 0

	h.errorHandler.LogInfo("HubBroadcast", "开始广播消息: 类型=%s, 来源=%s, 总客户端数=%d",
		message.Type, message.Source, len(h.clients))

	for clientID, client := range h.clients {
		if h.shouldSendToClient(client, message) {
			messageData := h.marshalMessage(message)
			select {
			case client.Send <- messageData:
				sentCount++
				h.errorHandler.LogInfo("HubBroadcast", "消息已发送给客户端: ID=%s, UserID=%s, StationID=%d, 消息类型=%s",
					clientID, client.UserID, client.StationID, message.Type)
			default:
				// 客户端发送缓冲区已满，标记为不活跃并关闭连接
				err := WrapError(ErrCodeMessageSendFailed, "客户端发送缓冲区已满", nil)
				h.errorHandler.HandleError(err, "HubBroadcast")
				client.IsActive = false
				client.Cancel() // 触发连接关闭
			}
		} else {
			skippedCount++
			h.errorHandler.LogInfo("HubBroadcast", "跳过客户端: ID=%s, UserID=%s, StationID=%d, 原因=过滤器不匹配",
				clientID, client.UserID, client.StationID)
		}
	}

	h.errorHandler.LogInfo("HubBroadcast", "广播消息完成: 类型=%s, 发送=%d, 跳过=%d, 总计=%d",
		message.Type, sentCount, skippedCount, len(h.clients))
}

// handleRoomBroadcast 处理房间广播
func (h *HubImpl) handleRoomBroadcast(roomMessage *RoomMessage) {
	h.mu.RLock()
	room, exists := h.rooms[roomMessage.RoomID]
	if !exists {
		h.mu.RUnlock()
		log.Printf("房间不存在: %s", roomMessage.RoomID)
		return
	}

	// 创建房间客户端的副本，避免在迭代时持有锁
	roomClients := make(map[string]*Client)
	for id, client := range room.Clients {
		roomClients[id] = client
	}
	h.mu.RUnlock()

	sentCount := 0
	skippedCount := 0

	log.Printf("开始房间广播: 房间=%s, 消息类型=%s, 来源=%s, 房间客户端数=%d",
		roomMessage.RoomID, roomMessage.Message.Type, roomMessage.Message.Source, len(roomClients))

	for clientID, client := range roomClients {
		if h.shouldSendToClient(client, roomMessage.Message) {
			messageData := h.marshalMessage(roomMessage.Message)
			select {
			case client.Send <- messageData:
				sentCount++
				log.Printf("房间消息已发送给客户端: 房间=%s, 客户端ID=%s, UserID=%s, StationID=%d, 消息类型=%s",
					roomMessage.RoomID, clientID, client.UserID, client.StationID, roomMessage.Message.Type)
			default:
				log.Printf("房间客户端发送缓冲区已满，关闭连接: 房间=%s, 客户端ID=%s", roomMessage.RoomID, clientID)
				client.IsActive = false
				client.Cancel() // 触发连接关闭
			}
		} else {
			skippedCount++
			log.Printf("跳过房间客户端: 房间=%s, 客户端ID=%s, UserID=%s, StationID=%d, 原因=过滤器不匹配",
				roomMessage.RoomID, clientID, client.UserID, client.StationID)
		}
	}

	log.Printf("房间广播完成: 房间=%s, 消息类型=%s, 发送=%d, 跳过=%d, 总计=%d",
		roomMessage.RoomID, roomMessage.Message.Type, sentCount, skippedCount, len(roomClients))
}

// AddClient 添加客户端
func (h *HubImpl) AddClient(client *Client) error {
	select {
	case <-h.ctx.Done():
		return fmt.Errorf("hub已停止")
	default:
		h.handleRegister(client)
		return nil
	}
}

// RemoveClient 移除客户端
func (h *HubImpl) RemoveClient(clientID string) error {
	h.mu.RLock()
	client, exists := h.clients[clientID]
	h.mu.RUnlock()

	if !exists {
		return fmt.Errorf("客户端不存在: %s", clientID)
	}

	select {
	case <-h.ctx.Done():
		return fmt.Errorf("hub已停止")
	default:
		h.handleUnregister(client)
		return nil
	}
}

// BroadcastMessage 广播消息
func (h *HubImpl) BroadcastMessage(message *Message) error {
	message.Timestamp = time.Now()

	select {
	case <-h.ctx.Done():
		return fmt.Errorf("hub已停止")
	default:
		h.handleBroadcast(message)
		return nil
	}
}

// BroadcastToRoom 向房间广播消息
func (h *HubImpl) BroadcastToRoom(roomID string, message *Message) error {
	message.Timestamp = time.Now()

	roomMessage := &RoomMessage{
		RoomID:  roomID,
		Message: message,
	}

	select {
	case <-h.ctx.Done():
		return fmt.Errorf("hub已停止")
	default:
		h.handleRoomBroadcast(roomMessage)
		return nil
	}
}

// GetClient 获取客户端
func (h *HubImpl) GetClient(clientID string) (*Client, bool) {
	h.mu.RLock()
	defer h.mu.RUnlock()

	client, exists := h.clients[clientID]
	return client, exists
}

// GetAllClients 获取所有客户端
func (h *HubImpl) GetAllClients() map[string]*Client {
	h.mu.RLock()
	defer h.mu.RUnlock()

	// 创建副本避免并发修改
	clients := make(map[string]*Client)
	for id, client := range h.clients {
		clients[id] = client
	}
	return clients
}

// GetStats 获取统计信息 - 简化版本
func (h *HubImpl) GetStats() *HubStats {
	h.mu.RLock()
	defer h.mu.RUnlock()

	// 返回简单的统计信息
	return &HubStats{
		ActiveConnections: len(h.clients),
		LastUpdated:       time.Now(),
	}
}

// AddMessageFilter 添加消息过滤器
func (h *HubImpl) AddMessageFilter(filter MessageFilter) {
	h.filters = append(h.filters, filter)
}

// 私有方法

// shouldSendToClient 检查是否应该向客户端发送消息
func (h *HubImpl) shouldSendToClient(client *Client, message *Message) bool {
	// 检查客户端是否活跃
	if !client.IsActive {
		return false
	}

	// 检查订阅
	if client.Subscriptions != nil {
		if subscribed, exists := client.Subscriptions[message.Type]; !exists || !subscribed {
			return false
		}
	}

	// 应用过滤器
	for _, filter := range h.filters {
		if !filter.ShouldSend(client, message) {
			return false
		}
	}

	return true
}

// marshalMessage 序列化消息
func (h *HubImpl) marshalMessage(message *Message) []byte {
	data, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化消息失败: %v", err)
		return nil
	}
	return data
}

// sendSnapshot 发送初始快照
func (h *HubImpl) sendSnapshot(client *Client) {
	// TODO: 实现快照数据收集
	snapshot := &SnapshotPayload{
		Devices:            []interface{}{},
		Nozzles:            []interface{}{},
		ActiveTransactions: []interface{}{},
		CurrentShift:       nil,
		DashboardSummary:   nil,
		Timestamp:          time.Now(),
	}

	message := &Message{
		Type:      MessageTypeSnapshot,
		Payload:   snapshot,
		Timestamp: time.Now(),
		Source:    "bos",
	}

	select {
	case client.Send <- h.marshalMessage(message):
		log.Printf("快照已发送给客户端: %s", client.ID)
	default:
		log.Printf("发送快照失败，客户端缓冲区已满: %s", client.ID)
	}
}

// addClientToRooms 将客户端添加到相应房间
func (h *HubImpl) addClientToRooms(client *Client) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.addClientToRoomsLocked(client)
}

// addClientToRoomsLocked 将客户端添加到相应房间 (假设已持有锁)
func (h *HubImpl) addClientToRoomsLocked(client *Client) {
	// 根据站点ID创建房间
	if client.StationID > 0 {
		roomID := fmt.Sprintf("station_%d", client.StationID)
		h.ensureRoomLocked(roomID, &RoomFilters{StationID: &client.StationID})
		h.addClientToRoomLocked(client, roomID)
		log.Printf("✅ [DEBUG] 客户端已添加到房间: %s -> %s", client.ID, roomID)
	}
}

// removeClientFromRooms 从房间中移除客户端
// 注意：此方法假设调用者已经持有锁
func (h *HubImpl) removeClientFromRooms(client *Client) {
	for _, room := range h.rooms {
		delete(room.Clients, client.ID) // delete 是安全的，即使 key 不存在
	}
}

// ensureRoom 确保房间存在
func (h *HubImpl) ensureRoom(roomID string, filters *RoomFilters) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.ensureRoomLocked(roomID, filters)
}

// ensureRoomLocked 确保房间存在 (假设已持有锁)
func (h *HubImpl) ensureRoomLocked(roomID string, filters *RoomFilters) {
	if _, exists := h.rooms[roomID]; !exists {
		h.rooms[roomID] = &Room{
			ID:           roomID,
			Name:         roomID,
			Clients:      make(map[string]*Client),
			Filters:      filters,
			CreatedAt:    time.Now(),
			LastActivity: time.Now(),
		}
		log.Printf("✅ [DEBUG] 房间已创建: %s", roomID)
	}
}

// addClientToRoom 将客户端添加到房间
func (h *HubImpl) addClientToRoom(client *Client, roomID string) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.addClientToRoomLocked(client, roomID)
}

// addClientToRoomLocked 将客户端添加到房间 (假设已持有锁)
func (h *HubImpl) addClientToRoomLocked(client *Client, roomID string) {
	if room, exists := h.rooms[roomID]; exists {
		room.Clients[client.ID] = client
		room.LastActivity = time.Now()
		log.Printf("✅ [DEBUG] 客户端已添加到房间: %s -> %s, 房间客户端数: %d",
			client.ID, roomID, len(room.Clients))
	} else {
		log.Printf("❌ [ERROR] 房间不存在，无法添加客户端: %s -> %s", client.ID, roomID)
	}
}

// heartbeatLoop 心跳循环
func (h *HubImpl) heartbeatLoop() {
	ticker := time.NewTicker(h.config.HeartbeatInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			h.sendHeartbeat()
		case <-h.ctx.Done():
			return
		}
	}
}

// sendHeartbeat 发送心跳
func (h *HubImpl) sendHeartbeat() {
	message := &Message{
		Type: MessageTypeHeartbeat,
		Payload: &HeartbeatMessage{
			Timestamp: time.Now(),
		},
		Timestamp: time.Now(),
		Source:    "system",
	}

	h.BroadcastMessage(message)
}

// cleanupInactiveClients 清理不活跃的客户端
func (h *HubImpl) cleanupInactiveClients() {
	h.mu.RLock()
	inactiveClients := make([]*Client, 0)

	for _, client := range h.clients {
		if time.Since(client.LastPing) > h.config.ClientTimeout {
			inactiveClients = append(inactiveClients, client)
		}
	}
	h.mu.RUnlock()

	for _, client := range inactiveClients {
		log.Printf("清理不活跃客户端: %s", client.ID)
		h.RemoveClient(client.ID)
	}
}

// 移除统计更新方法，避免死锁和不必要的复杂性

// cleanup 清理资源
func (h *HubImpl) cleanup() {
	h.mu.Lock()
	defer h.mu.Unlock()

	// 清理所有客户端
	for _, client := range h.clients {
		client.Cancel()     // 取消客户端上下文
		close(client.Send)  // 关闭发送通道
		client.Conn.Close() // 关闭WebSocket连接
	}
	h.clients = make(map[string]*Client)

	// 清理所有房间
	h.rooms = make(map[string]*Room)

	log.Println("Hub cleanup completed")
}
