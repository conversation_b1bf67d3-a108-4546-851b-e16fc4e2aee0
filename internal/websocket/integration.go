package websocket

import (
	"context"
	"log"
	"time"

	"gitlab4.weicheche.cn/indo-bp/bos/internal/service"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// EventTrigger 事件触发器接口
type EventTrigger interface {
	// 触发交易状态更新事件
	TriggerTransactionUpdate(ctx context.Context, transactionID, transactionNumber, nozzleID string, stationID int64, status string) error

	// 触发班次状态更新事件
	TriggerShiftStatusUpdate(ctx context.Context, shiftID, shiftNumber string, stationID int64, status string, startTime time.Time, endTime *time.Time) error

	// 触发仪表板数据更新事件
	TriggerDashboardUpdate(ctx context.Context, stationID int64) error

	// 触发价格更新事件
	TriggerPriceUpdate(ctx context.Context, stationID int64, deviceID string, pumpID int, prices map[string]float64) error
}

// WebSocketEventTrigger WebSocket事件触发器实现
type WebSocketEventTrigger struct {
	wsService        *Service
	dashboardService *service.DashboardService
}

// NewWebSocketEventTrigger 创建WebSocket事件触发器
func NewWebSocketEventTrigger(wsService *Service, dashboardService *service.DashboardService) *WebSocketEventTrigger {
	return &WebSocketEventTrigger{
		wsService:        wsService,
		dashboardService: dashboardService,
	}
}

// TriggerTransactionUpdate 触发交易状态更新事件
func (wet *WebSocketEventTrigger) TriggerTransactionUpdate(ctx context.Context, transactionID, transactionNumber, nozzleID string, stationID int64, status string) error {
	// 构造交易更新载荷
	payload := &TransactionUpdatePayload{
		TransactionID:     transactionID,
		TransactionNumber: transactionNumber,
		NozzleID:          nozzleID,
		StationID:         stationID,
		Status:            status,
		Amount:            0, // 这些字段需要从其他地方获取
		Volume:            0, // 或者扩展接口参数
		FuelType:          "",
		FuelGrade:         "",
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	// 发送WebSocket事件
	if err := wet.wsService.EmitTransactionUpdate(payload); err != nil {
		log.Printf("发送交易更新WebSocket事件失败: %v", err)
		return err
	}

	log.Printf("已发送交易更新事件: %s (状态: %s)", transactionNumber, status)
	return nil
}

// TriggerShiftStatusUpdate 触发班次状态更新事件
func (wet *WebSocketEventTrigger) TriggerShiftStatusUpdate(ctx context.Context, shiftID, shiftNumber string, stationID int64, status string, startTime time.Time, endTime *time.Time) error {
	// 构造班次状态更新载荷
	payload := &ShiftStatusPayload{
		ShiftID:       shiftID,
		StationID:     stationID,
		ShiftNumber:   shiftNumber,
		Status:        status,
		StartTime:     startTime,
		EndTime:       endTime,
		AttendantID:   "", // 需要从其他地方获取
		AttendantName: "", // 需要从其他地方获取
	}

	// 发送WebSocket事件
	if err := wet.wsService.EmitShiftStatusUpdate(payload); err != nil {
		log.Printf("发送班次状态更新WebSocket事件失败: %v", err)
		return err
	}

	log.Printf("已发送班次状态更新事件: %s (状态: %s)", shiftNumber, status)
	return nil
}

// TriggerDashboardUpdate 触发仪表板数据更新事件
func (wet *WebSocketEventTrigger) TriggerDashboardUpdate(ctx context.Context, stationID int64) error {
	// 获取最新的仪表板数据
	summary, err := wet.dashboardService.GetDashboardSummary(ctx, stationID, "")
	if err != nil {
		log.Printf("获取仪表板数据失败: %v", err)
		return err
	}

	// 构造仪表板更新载荷
	payload := &DashboardUpdatePayload{
		StationID:         stationID,
		TodayRevenue:      summary.TodayRevenue,
		TodayTransactions: summary.TodayTransactions,
		TodayVolume:       summary.TodayVolume,
		RevenueChange:     summary.RevenueChange,
		TransactionChange: summary.TransactionChange,
		VolumeChange:      summary.VolumeChange,
		LastUpdated:       time.Now(),
	}

	// 转换燃油销售组合数据
	if len(summary.FuelSalesMix) > 0 {
		payload.FuelSalesMix = make([]FuelSalesMixItem, len(summary.FuelSalesMix))
		for i, item := range summary.FuelSalesMix {
			payload.FuelSalesMix[i] = FuelSalesMixItem{
				FuelType:   "", // repository.FuelSalesMixItem没有FuelType字段
				FuelGrade:  item.FuelGrade,
				Volume:     item.Volume,
				Revenue:    item.Revenue,
				Percentage: item.Percentage,
			}
		}
	}

	// 发送WebSocket事件
	if err := wet.wsService.EmitDashboardUpdate(payload); err != nil {
		log.Printf("发送仪表板更新WebSocket事件失败: %v", err)
		return err
	}

	log.Printf("已发送仪表板更新事件: 站点 %d", stationID)
	return nil
}

// TriggerPriceUpdate 触发价格更新事件
func (wet *WebSocketEventTrigger) TriggerPriceUpdate(ctx context.Context, stationID int64, deviceID string, pumpID int, prices map[string]float64) error {
	// 构造价格更新载荷
	payload := &PriceUpdatePayload{
		StationID:   stationID,
		DeviceID:    deviceID,
		PumpID:      pumpID,
		Prices:      prices,
		EffectiveAt: time.Now(),
		UpdatedBy:   "system", // TODO: 从上下文获取操作用户
	}

	// 发送WebSocket事件
	if err := wet.wsService.EmitPriceUpdate(payload); err != nil {
		log.Printf("发送价格更新WebSocket事件失败: %v", err)
		return err
	}

	log.Printf("已发送价格更新事件: 站点 %d, 设备 %s, 油泵 %d", stationID, deviceID, pumpID)
	return nil
}

// ServiceIntegrator 服务集成器
type ServiceIntegrator struct {
	eventTrigger EventTrigger
}

// NewServiceIntegrator 创建服务集成器
func NewServiceIntegrator(eventTrigger EventTrigger) *ServiceIntegrator {
	return &ServiceIntegrator{
		eventTrigger: eventTrigger,
	}
}

// IntegrateWithFuelTransactionService 集成燃油交易服务
func (si *ServiceIntegrator) IntegrateWithFuelTransactionService(fuelTransactionService interface{}) {
	// TODO: 这里需要修改燃油交易服务，在状态变更时调用事件触发器
	// 由于无法直接修改现有服务，我们将通过装饰器模式来实现
	log.Println("燃油交易服务WebSocket集成已配置")
}

// IntegrateWithShiftService 集成班次服务
func (si *ServiceIntegrator) IntegrateWithShiftService(shiftService interface{}) {
	// TODO: 这里需要修改班次服务，在状态变更时调用事件触发器
	log.Println("班次服务WebSocket集成已配置")
}

// IntegrateWithDashboardService 集成仪表板服务
func (si *ServiceIntegrator) IntegrateWithDashboardService(dashboardService interface{}) {
	// TODO: 这里需要修改仪表板服务，在数据变更时调用事件触发器
	log.Println("仪表板服务WebSocket集成已配置")
}

// WebSocketMiddleware WebSocket中间件
type WebSocketMiddleware struct {
	eventTrigger EventTrigger
}

// NewWebSocketMiddleware 创建WebSocket中间件
func NewWebSocketMiddleware(eventTrigger EventTrigger) *WebSocketMiddleware {
	return &WebSocketMiddleware{
		eventTrigger: eventTrigger,
	}
}

// TransactionUpdateMiddleware 交易更新中间件
func (wsm *WebSocketMiddleware) TransactionUpdateMiddleware(next func(ctx context.Context, transaction *repository.FuelTransaction) error) func(ctx context.Context, transaction *repository.FuelTransaction) error {
	return func(ctx context.Context, transaction *repository.FuelTransaction) error {
		// 执行原始操作
		err := next(ctx, transaction)
		if err != nil {
			return err
		}

		// 触发WebSocket事件
		transactionID := transaction.ID.String()
		stationID := int64(transaction.StationID)
		status := string(transaction.Status)
		if triggerErr := wsm.eventTrigger.TriggerTransactionUpdate(ctx, transactionID, transaction.TransactionNumber, transaction.NozzleID, stationID, status); triggerErr != nil {
			log.Printf("触发交易更新WebSocket事件失败: %v", triggerErr)
			// 不返回错误，避免影响主要业务流程
		}

		return nil
	}
}

// ShiftUpdateMiddleware 班次更新中间件
func (wsm *WebSocketMiddleware) ShiftUpdateMiddleware(next func(ctx context.Context, shift *repository.Shift) error) func(ctx context.Context, shift *repository.Shift) error {
	return func(ctx context.Context, shift *repository.Shift) error {
		// 执行原始操作
		err := next(ctx, shift)
		if err != nil {
			return err
		}

		// 触发WebSocket事件
		// 注意：这里需要根据实际的Shift结构来获取字段
		// 由于无法确定Shift的确切结构，这里使用占位符
		shiftID := "shift-id-placeholder" // shift.ID.String()
		stationID := int64(0)             // shift.StationID
		shiftNumber := "shift-number"     // shift.ShiftNumber
		status := "active"                // shift.Status
		startTime := time.Now()           // shift.StartTime
		var endTime *time.Time = nil      // shift.EndTime

		if triggerErr := wsm.eventTrigger.TriggerShiftStatusUpdate(ctx, shiftID, shiftNumber, stationID, status, startTime, endTime); triggerErr != nil {
			log.Printf("触发班次状态更新WebSocket事件失败: %v", triggerErr)
		}

		return nil
	}
}

// DashboardUpdateScheduler 仪表板更新调度器
type DashboardUpdateScheduler struct {
	eventTrigger EventTrigger
	interval     time.Duration
	stationIDs   []int64
	ctx          context.Context
	cancel       context.CancelFunc
}

// NewDashboardUpdateScheduler 创建仪表板更新调度器
func NewDashboardUpdateScheduler(eventTrigger EventTrigger, stationIDs []int64, interval time.Duration) *DashboardUpdateScheduler {
	ctx, cancel := context.WithCancel(context.Background())

	return &DashboardUpdateScheduler{
		eventTrigger: eventTrigger,
		interval:     interval,
		stationIDs:   stationIDs,
		ctx:          ctx,
		cancel:       cancel,
	}
}

// Start 启动仪表板更新调度器
func (dus *DashboardUpdateScheduler) Start() {
	go dus.run()
	log.Printf("仪表板更新调度器已启动，间隔: %v", dus.interval)
}

// Stop 停止仪表板更新调度器
func (dus *DashboardUpdateScheduler) Stop() {
	dus.cancel()
	log.Println("仪表板更新调度器已停止")
}

// run 调度器主循环
func (dus *DashboardUpdateScheduler) run() {
	ticker := time.NewTicker(dus.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			dus.updateDashboards()
		case <-dus.ctx.Done():
			return
		}
	}
}

// updateDashboards 更新所有站点的仪表板数据
func (dus *DashboardUpdateScheduler) updateDashboards() {
	for _, stationID := range dus.stationIDs {
		if err := dus.eventTrigger.TriggerDashboardUpdate(dus.ctx, stationID); err != nil {
			log.Printf("更新站点 %d 仪表板数据失败: %v", stationID, err)
		}
	}
}

// WebSocketRouter WebSocket路由器
type WebSocketRouter struct {
	service *Service
}

// NewWebSocketRouter 创建WebSocket路由器
func NewWebSocketRouter(service *Service) *WebSocketRouter {
	return &WebSocketRouter{
		service: service,
	}
}

// RegisterRoutes 注册WebSocket路由
func (wsr *WebSocketRouter) RegisterRoutes(e interface{}) {
	// TODO: 这里需要根据实际的路由框架来实现
	// 假设使用Echo框架
	log.Println("WebSocket路由已注册: /ws")
}

// GetWebSocketHandler 获取WebSocket处理器
func (wsr *WebSocketRouter) GetWebSocketHandler() *Handler {
	return wsr.service.GetHandler()
}

// GetWebSocketStats 获取WebSocket统计信息
func (wsr *WebSocketRouter) GetWebSocketStats() *ServiceStats {
	return wsr.service.GetStats()
}
