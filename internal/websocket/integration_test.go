package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestWebSocketIntegration_FullFlow 测试完整的WebSocket流程
func TestWebSocketIntegration_FullFlow(t *testing.T) {
	// 创建WebSocket服务
	service := CreateDefaultWebSocketService()
	handler := service.GetHandler()

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 模拟Echo Context
		c := &mockEchoContext{
			request:  r,
			response: w,
		}
		handler.HandleWebSocket(c)
	}))
	defer server.Close()

	// 启动WebSocket服务
	err := service.Start()
	require.NoError(t, err, "启动WebSocket服务失败")
	defer service.Stop()

	// 等待服务启动
	time.Sleep(200 * time.Millisecond)

	// 连接WebSocket
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http")
	conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	require.NoError(t, err, "WebSocket连接失败")
	defer conn.Close()

	// 1. 测试认证
	authMsg := map[string]interface{}{
		"type": "auth",
		"payload": map[string]interface{}{
			"token": "test-token-123",
		},
	}

	err = conn.WriteJSON(authMsg)
	require.NoError(t, err, "发送认证消息失败")

	// 接收认证响应
	var authResponse map[string]interface{}
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	err = conn.ReadJSON(&authResponse)
	require.NoError(t, err, "读取认证响应失败")

	assert.Equal(t, "auth", authResponse["type"], "认证响应类型不匹配")
	payload, ok := authResponse["payload"].(map[string]interface{})
	require.True(t, ok, "认证响应载荷类型错误")
	assert.True(t, payload["success"].(bool), "认证应该成功")

	// 2. 测试订阅
	subscribeMsg := map[string]interface{}{
		"type": "subscribe",
		"payload": map[string]interface{}{
			"token": "test-token-123",
			"filters": map[string]interface{}{
				"station_id": float64(1), // JSON数字默认是float64
				"topics": []interface{}{
					"nozzle_status_update",
					"nozzle_status_batch_update",
					"transaction_update",
					"dashboard_update",
				},
			},
		},
	}

	err = conn.WriteJSON(subscribeMsg)
	require.NoError(t, err, "发送订阅消息失败")

	// 接收订阅响应
	var subscribeResponse map[string]interface{}
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	err = conn.ReadJSON(&subscribeResponse)
	require.NoError(t, err, "读取订阅响应失败")

	assert.Equal(t, "subscribe", subscribeResponse["type"], "订阅响应类型不匹配")

	// 3. 测试消息推送
	go func() {
		time.Sleep(500 * time.Millisecond) // 等待订阅完成

		// 发送交易更新
		transactionPayload := &TransactionUpdatePayload{
			TransactionID:     "integration-tx-001",
			TransactionNumber: "INT001",
			NozzleID:          "integration-nozzle-1",
			StationID:         1,
			Status:            "completed",
			Amount:            150000.0,
			Volume:            15.0,
			FuelType:          "gasoline",
			FuelGrade:         "RON92",
			CreatedAt:         time.Now(),
			UpdatedAt:         time.Now(),
		}

		service.EmitTransactionUpdate(transactionPayload)

		time.Sleep(100 * time.Millisecond)

		// 发送仪表板更新
		dashboardPayload := &DashboardUpdatePayload{
			StationID:         1,
			TodayRevenue:      2000000.0,
			TodayTransactions: 200,
			TodayVolume:       1000.0,
			RevenueChange:     8.0,
			TransactionChange: 5.0,
			VolumeChange:      3.0,
			LastUpdated:       time.Now(),
			FuelSalesMix:      []FuelSalesMixItem{},
		}

		service.EmitDashboardUpdate(dashboardPayload)
	}()

	// 接收消息
	messagesReceived := 0
	expectedMessages := 2

	for messagesReceived < expectedMessages {
		conn.SetReadDeadline(time.Now().Add(10 * time.Second))

		var message map[string]interface{}
		err = conn.ReadJSON(&message)
		if err != nil {
			t.Fatalf("读取消息失败: %v", err)
		}

		messageType, ok := message["type"].(string)
		require.True(t, ok, "消息类型应该是字符串")

		switch messageType {
		case "transaction_update":
			t.Log("✅ 收到交易更新消息")
			messagesReceived++
		case "dashboard_update":
			t.Log("✅ 收到仪表板更新消息")
			messagesReceived++
		default:
			t.Logf("收到其他消息类型: %s", messageType)
		}
	}

	assert.Equal(t, expectedMessages, messagesReceived, "应该收到所有预期消息")

	t.Log("✅ WebSocket完整流程集成测试通过")
}

// TestWebSocketIntegration_MultipleClients 测试多客户端场景
func TestWebSocketIntegration_MultipleClients(t *testing.T) {
	// 创建WebSocket服务
	service := CreateDefaultWebSocketService()
	handler := service.GetHandler()

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		c := &mockEchoContext{
			request:  r,
			response: w,
		}
		handler.HandleWebSocket(c)
	}))
	defer server.Close()

	// 启动WebSocket服务
	err := service.Start()
	require.NoError(t, err, "启动WebSocket服务失败")
	defer service.Stop()

	time.Sleep(200 * time.Millisecond)

	const numClients = 5
	connections := make([]*websocket.Conn, numClients)
	var wg sync.WaitGroup

	wsURL := "ws" + strings.TrimPrefix(server.URL, "http")

	// 并发连接多个客户端
	for i := 0; i < numClients; i++ {
		wg.Add(1)
		go func(clientID int) {
			defer wg.Done()

			conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
			require.NoError(t, err, "客户端 %d WebSocket连接失败", clientID)
			connections[clientID] = conn

			// 认证
			authMsg := map[string]interface{}{
				"type": "auth",
				"payload": map[string]interface{}{
					"token": fmt.Sprintf("multi-test-token-%d", clientID),
				},
			}

			err = conn.WriteJSON(authMsg)
			require.NoError(t, err, "客户端 %d 发送认证消息失败", clientID)

			var authResponse map[string]interface{}
			conn.SetReadDeadline(time.Now().Add(5 * time.Second))
			err = conn.ReadJSON(&authResponse)
			require.NoError(t, err, "客户端 %d 读取认证响应失败", clientID)

			// 订阅
			subscribeMsg := map[string]interface{}{
				"type": "subscribe",
				"payload": map[string]interface{}{
					"token": fmt.Sprintf("multi-test-token-%d", clientID),
					"filters": map[string]interface{}{
						"station_id": float64(1),
						"topics":     []interface{}{"dashboard_update"},
					},
				},
			}

			err = conn.WriteJSON(subscribeMsg)
			require.NoError(t, err, "客户端 %d 发送订阅消息失败", clientID)

			var subscribeResponse map[string]interface{}
			conn.SetReadDeadline(time.Now().Add(5 * time.Second))
			err = conn.ReadJSON(&subscribeResponse)
			require.NoError(t, err, "客户端 %d 读取订阅响应失败", clientID)
		}(i)
	}

	wg.Wait()

	// 等待所有客户端连接完成
	time.Sleep(500 * time.Millisecond)

	// 广播消息给所有客户端
	dashboardPayload := &DashboardUpdatePayload{
		StationID:         1,
		TodayRevenue:      5000000.0,
		TodayTransactions: 500,
		TodayVolume:       2500.0,
		RevenueChange:     15.0,
		TransactionChange: 10.0,
		VolumeChange:      8.0,
		LastUpdated:       time.Now(),
		FuelSalesMix:      []FuelSalesMixItem{},
	}

	err = service.EmitDashboardUpdate(dashboardPayload)
	require.NoError(t, err, "发送仪表板更新失败")

	// 验证所有客户端都收到消息
	for i, conn := range connections {
		if conn == nil {
			continue
		}

		conn.SetReadDeadline(time.Now().Add(5 * time.Second))

		var message map[string]interface{}
		err = conn.ReadJSON(&message)
		require.NoError(t, err, "客户端 %d 读取消息失败", i)

		messageType, ok := message["type"].(string)
		require.True(t, ok, "客户端 %d 消息类型应该是字符串", i)
		assert.Equal(t, "dashboard_update", messageType, "客户端 %d 应该收到仪表板更新消息", i)

		conn.Close()
	}

	t.Logf("✅ 多客户端集成测试通过，成功测试 %d 个客户端", numClients)
}

// TestWebSocketIntegration_FCCScenario 测试FCC场景集成
func TestWebSocketIntegration_FCCScenario(t *testing.T) {
	// 创建模拟FCC服务器
	fccOnline := true
	fccServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if !fccOnline {
			w.WriteHeader(http.StatusServiceUnavailable)
			return
		}

		switch r.URL.Path {
		case "/api/nozzles/status":
			nozzles := []map[string]interface{}{
				{
					"nozzle_id":      "fcc-integration-nozzle-1",
					"dispenser_id":   "fcc-integration-dispenser-1",
					"station_id":     1,
					"status":         "idle",
					"current_volume": 0.0,
					"current_amount": 0.0,
					"fuel_grade":     "RON92",
				},
				{
					"nozzle_id":      "fcc-integration-nozzle-2",
					"dispenser_id":   "fcc-integration-dispenser-1",
					"station_id":     1,
					"status":         "filling",
					"current_volume": 8.5,
					"current_amount": 120000.0,
					"fuel_grade":     "RON95",
				},
			}
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(nozzles)
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
	defer fccServer.Close()

	// 测试FCC在线场景
	client := NewHTTPFCCClient()
	ctx := context.Background()

	nozzles, err := client.GetNozzleStatus(ctx, fccServer.URL+"/api")
	require.NoError(t, err, "FCC在线时获取喷嘴状态应该成功")
	assert.Len(t, nozzles, 2, "应该返回2个喷嘴")

	// 验证在线数据
	nozzle1, ok := nozzles[0].(map[string]interface{})
	require.True(t, ok, "喷嘴数据应该是map类型")
	assert.Equal(t, "fcc-integration-nozzle-1", nozzle1["nozzle_id"], "喷嘴ID应该匹配")
	assert.Equal(t, "idle", nozzle1["status"], "喷嘴状态应该是idle")

	// 模拟FCC离线
	fccOnline = false

	offlineNozzles, err := client.GetNozzleStatus(ctx, fccServer.URL+"/api")
	require.NoError(t, err, "FCC离线时不应该返回错误")
	assert.NotEmpty(t, offlineNozzles, "应该返回离线喷嘴数据")

	// 验证离线数据
	offlineNozzle1, ok := offlineNozzles[0].(map[string]interface{})
	require.True(t, ok, "离线喷嘴数据应该是map类型")
	assert.Equal(t, "offline", offlineNozzle1["status"], "喷嘴状态应该是offline")
	assert.Equal(t, "FCC_DISCONNECTED", offlineNozzle1["offline_reason"], "离线原因应该正确")

	// 模拟FCC恢复在线
	fccOnline = true

	recoveredNozzles, err := client.GetNozzleStatus(ctx, fccServer.URL+"/api")
	require.NoError(t, err, "FCC恢复在线时获取喷嘴状态应该成功")
	assert.Len(t, recoveredNozzles, 2, "恢复后应该返回2个喷嘴")

	t.Log("✅ FCC场景集成测试通过")
}

// mockEchoContext 模拟Echo Context
type mockEchoContext struct {
	request  *http.Request
	response http.ResponseWriter
}

func (m *mockEchoContext) Request() *http.Request {
	return m.request
}

func (m *mockEchoContext) Response() *mockResponseWriter {
	return &mockResponseWriter{writer: m.response}
}

type mockResponseWriter struct {
	writer http.ResponseWriter
}

func (m *mockResponseWriter) Writer() http.ResponseWriter {
	return m.writer
}
