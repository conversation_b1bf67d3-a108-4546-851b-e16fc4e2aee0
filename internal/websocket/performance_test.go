package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"runtime"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// BenchmarkFCCClient_GetNozzleStatus FCC客户端性能基准测试
func BenchmarkFCCClient_GetNozzleStatus(b *testing.B) {
	// 创建模拟FCC服务器
	fccServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		nozzles := []map[string]interface{}{
			{
				"nozzle_id":      "bench-nozzle-1",
				"dispenser_id":   "bench-dispenser-1",
				"station_id":     1,
				"status":         "idle",
				"current_volume": 0.0,
				"current_amount": 0.0,
				"fuel_grade":     "RON92",
			},
			{
				"nozzle_id":      "bench-nozzle-2",
				"dispenser_id":   "bench-dispenser-1",
				"station_id":     1,
				"status":         "filling",
				"current_volume": 5.5,
				"current_amount": 75000.0,
				"fuel_grade":     "RON95",
			},
		}
		w.<PERSON>().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(nozzles)
	}))
	defer fccServer.Close()

	client := NewHTTPFCCClient()
	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, err := client.GetNozzleStatus(ctx, fccServer.URL+"/api")
			if err != nil {
				b.Fatalf("FCC客户端调用失败: %v", err)
			}
		}
	})
}

// BenchmarkWebSocketService_EmitMessage WebSocket服务消息发送性能基准测试
func BenchmarkWebSocketService_EmitMessage(b *testing.B) {
	service := CreateDefaultWebSocketService()
	err := service.Start()
	require.NoError(b, err, "启动WebSocket服务失败")
	defer service.Stop()

	// 等待服务启动
	time.Sleep(100 * time.Millisecond)

	payload := &DashboardUpdatePayload{
		StationID:         1,
		TodayRevenue:      1000000.0,
		TodayTransactions: 100,
		TodayVolume:       500.0,
		RevenueChange:     5.0,
		TransactionChange: 3.0,
		VolumeChange:      2.0,
		LastUpdated:       time.Now(),
		FuelSalesMix:      []FuelSalesMixItem{},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := service.EmitDashboardUpdate(payload)
		if err != nil {
			b.Fatalf("发送消息失败: %v", err)
		}
	}
}

// TestWebSocketService_ConcurrentLoad 测试并发负载
func TestWebSocketService_ConcurrentLoad(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过并发负载测试（短测试模式）")
	}

	service := CreateDefaultWebSocketService()
	err := service.Start()
	require.NoError(t, err, "启动WebSocket服务失败")
	defer service.Stop()

	time.Sleep(200 * time.Millisecond)

	const (
		numGoroutines = 50
		messagesPerGoroutine = 100
	)

	var wg sync.WaitGroup
	var successCount int64
	var errorCount int64

	// 启动多个goroutine并发发送消息
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < messagesPerGoroutine; j++ {
				payload := &TransactionUpdatePayload{
					TransactionID:     fmt.Sprintf("load-test-tx-%d-%d", goroutineID, j),
					TransactionNumber: fmt.Sprintf("LOAD%d%d", goroutineID, j),
					NozzleID:          fmt.Sprintf("load-nozzle-%d", goroutineID%5),
					StationID:         1,
					Status:            "completed",
					Amount:            float64(100000 + j*1000),
					Volume:            float64(10 + j),
					FuelType:          "gasoline",
					FuelGrade:         "RON92",
					CreatedAt:         time.Now(),
					UpdatedAt:         time.Now(),
				}

				err := service.EmitTransactionUpdate(payload)
				if err != nil {
					atomic.AddInt64(&errorCount, 1)
				} else {
					atomic.AddInt64(&successCount, 1)
				}

				// 随机延迟，模拟真实场景
				time.Sleep(time.Duration(j%10) * time.Millisecond)
			}
		}(i)
	}

	wg.Wait()

	totalMessages := int64(numGoroutines * messagesPerGoroutine)
	finalSuccessCount := atomic.LoadInt64(&successCount)
	finalErrorCount := atomic.LoadInt64(&errorCount)

	t.Logf("并发负载测试结果:")
	t.Logf("  Goroutines数: %d", numGoroutines)
	t.Logf("  每个Goroutine消息数: %d", messagesPerGoroutine)
	t.Logf("  总消息数: %d", totalMessages)
	t.Logf("  成功消息数: %d", finalSuccessCount)
	t.Logf("  失败消息数: %d", finalErrorCount)
	t.Logf("  成功率: %.2f%%", float64(finalSuccessCount)/float64(totalMessages)*100)

	assert.Equal(t, totalMessages, finalSuccessCount+finalErrorCount, "成功+失败应该等于总数")
	assert.Greater(t, finalSuccessCount, int64(totalMessages*0.95), "成功率应该大于95%")

	t.Log("✅ 并发负载测试通过")
}

// TestWebSocketService_MemoryUsage 测试内存使用情况
func TestWebSocketService_MemoryUsage(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过内存使用测试（短测试模式）")
	}

	// 记录初始内存使用
	runtime.GC()
	var m1 runtime.MemStats
	runtime.ReadMemStats(&m1)

	service := CreateDefaultWebSocketService()
	err := service.Start()
	require.NoError(t, err, "启动WebSocket服务失败")

	time.Sleep(200 * time.Millisecond)

	// 发送大量消息
	const numMessages = 1000
	for i := 0; i < numMessages; i++ {
		payload := &DashboardUpdatePayload{
			StationID:         1,
			TodayRevenue:      float64(1000000 + i*1000),
			TodayTransactions: int64(100 + i),
			TodayVolume:       float64(500 + i),
			RevenueChange:     5.0,
			TransactionChange: 3.0,
			VolumeChange:      2.0,
			LastUpdated:       time.Now(),
			FuelSalesMix:      []FuelSalesMixItem{},
		}

		err := service.EmitDashboardUpdate(payload)
		require.NoError(t, err, "发送消息失败")

		if i%100 == 0 {
			time.Sleep(10 * time.Millisecond) // 给系统一些处理时间
		}
	}

	// 等待消息处理完成
	time.Sleep(1 * time.Second)

	// 记录峰值内存使用
	runtime.GC()
	var m2 runtime.MemStats
	runtime.ReadMemStats(&m2)

	// 停止服务
	err = service.Stop()
	require.NoError(t, err, "停止服务失败")

	// 等待清理
	time.Sleep(2 * time.Second)

	// 记录清理后内存使用
	runtime.GC()
	var m3 runtime.MemStats
	runtime.ReadMemStats(&m3)

	t.Logf("内存使用情况:")
	t.Logf("  初始内存: %d KB", m1.Alloc/1024)
	t.Logf("  峰值内存: %d KB", m2.Alloc/1024)
	t.Logf("  清理后内存: %d KB", m3.Alloc/1024)
	t.Logf("  内存增长: %d KB", (m2.Alloc-m1.Alloc)/1024)
	t.Logf("  内存回收: %d KB", (m2.Alloc-m3.Alloc)/1024)

	// 验证内存没有严重泄漏
	memoryGrowth := m3.Alloc - m1.Alloc
	maxAcceptableGrowth := uint64(50 * 1024 * 1024) // 50MB

	assert.Less(t, memoryGrowth, maxAcceptableGrowth,
		"内存增长过多，可能存在内存泄漏: %d KB", memoryGrowth/1024)

	t.Log("✅ 内存使用测试通过")
}

// TestWebSocketService_Throughput 测试消息吞吐量
func TestWebSocketService_Throughput(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过吞吐量测试（短测试模式）")
	}

	service := CreateDefaultWebSocketService()
	err := service.Start()
	require.NoError(t, err, "启动WebSocket服务失败")
	defer service.Stop()

	time.Sleep(200 * time.Millisecond)

	const (
		testDuration = 5 * time.Second
		batchSize    = 100
	)

	var messageCount int64
	start := time.Now()
	end := start.Add(testDuration)

	for time.Now().Before(end) {
		// 批量发送消息
		for i := 0; i < batchSize; i++ {
			payload := &TransactionUpdatePayload{
				TransactionID:     fmt.Sprintf("throughput-tx-%d", atomic.LoadInt64(&messageCount)),
				TransactionNumber: fmt.Sprintf("THR%d", atomic.LoadInt64(&messageCount)),
				NozzleID:          "throughput-nozzle",
				StationID:         1,
				Status:            "completed",
				Amount:            100000.0,
				Volume:            10.0,
				FuelType:          "gasoline",
				FuelGrade:         "RON92",
				CreatedAt:         time.Now(),
				UpdatedAt:         time.Now(),
			}

			err := service.EmitTransactionUpdate(payload)
			if err == nil {
				atomic.AddInt64(&messageCount, 1)
			}
		}

		// 短暂休息，避免过度占用CPU
		time.Sleep(10 * time.Millisecond)
	}

	actualDuration := time.Since(start)
	finalMessageCount := atomic.LoadInt64(&messageCount)
	throughput := float64(finalMessageCount) / actualDuration.Seconds()

	t.Logf("消息吞吐量测试结果:")
	t.Logf("  测试时长: %v", actualDuration)
	t.Logf("  发送消息数: %d", finalMessageCount)
	t.Logf("  吞吐量: %.2f 消息/秒", throughput)

	// 验证吞吐量达到合理水平
	minThroughput := 100.0 // 每秒至少100条消息
	assert.Greater(t, throughput, minThroughput,
		"吞吐量应该大于 %.2f 消息/秒", minThroughput)

	t.Log("✅ 消息吞吐量测试通过")
}

// TestWebSocketService_StressTest 压力测试
func TestWebSocketService_StressTest(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过压力测试（短测试模式）")
	}

	service := CreateDefaultWebSocketService()
	err := service.Start()
	require.NoError(t, err, "启动WebSocket服务失败")
	defer service.Stop()

	time.Sleep(200 * time.Millisecond)

	const (
		numWorkers = 20
		duration   = 10 * time.Second
	)

	var wg sync.WaitGroup
	var totalMessages int64
	var totalErrors int64

	ctx, cancel := context.WithTimeout(context.Background(), duration)
	defer cancel()

	// 启动多个工作者
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			messageCount := 0
			for ctx.Err() == nil {
				payload := &DashboardUpdatePayload{
					StationID:         1,
					TodayRevenue:      float64(1000000 + messageCount*1000),
					TodayTransactions: int64(100 + messageCount),
					TodayVolume:       float64(500 + messageCount),
					RevenueChange:     5.0,
					TransactionChange: 3.0,
					VolumeChange:      2.0,
					LastUpdated:       time.Now(),
					FuelSalesMix:      []FuelSalesMixItem{},
				}

				err := service.EmitDashboardUpdate(payload)
				if err != nil {
					atomic.AddInt64(&totalErrors, 1)
				} else {
					atomic.AddInt64(&totalMessages, 1)
				}

				messageCount++

				// 随机延迟，模拟真实负载
				time.Sleep(time.Duration(messageCount%50) * time.Millisecond)
			}
		}(i)
	}

	wg.Wait()

	finalMessages := atomic.LoadInt64(&totalMessages)
	finalErrors := atomic.LoadInt64(&totalErrors)
	errorRate := float64(finalErrors) / float64(finalMessages+finalErrors) * 100

	t.Logf("压力测试结果:")
	t.Logf("  工作者数: %d", numWorkers)
	t.Logf("  测试时长: %v", duration)
	t.Logf("  成功消息数: %d", finalMessages)
	t.Logf("  错误消息数: %d", finalErrors)
	t.Logf("  错误率: %.2f%%", errorRate)
	t.Logf("  平均吞吐量: %.2f 消息/秒", float64(finalMessages)/duration.Seconds())

	// 验证系统在压力下的表现
	assert.Less(t, errorRate, 5.0, "错误率应该小于5%")
	assert.Greater(t, finalMessages, int64(100), "应该处理至少100条消息")

	t.Log("✅ 压力测试通过")
}
