package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"sync"
	"time"
)

// Service WebSocket服务
type Service struct {
	hub          *HubImpl
	handler      *Handler
	eventManager *EventManager
	auth         AuthService

	// 服务状态
	isRunning bool
	mu        sync.RWMutex
}

// ServiceConfig WebSocket服务配置
type ServiceConfig struct {
	Hub          *HubConfig
	EventManager *EventManagerConfig
}

// DefaultServiceConfig 默认服务配置
func DefaultServiceConfig() *ServiceConfig {
	return &ServiceConfig{
		Hub:          DefaultHubConfig(),
		EventManager: DefaultEventManagerConfig(),
	}
}

// NewService 创建WebSocket服务
func NewService(auth AuthService, config *ServiceConfig) *Service {
	if config == nil {
		config = DefaultServiceConfig()
	}

	// 创建Hub
	hub := NewHub(config.Hub)

	// 添加默认过滤器
	hub.AddMessageFilter(DefaultMessageFilters())

	// 创建处理器
	handler := NewHandler(hub, auth)

	// 创建事件管理器
	eventManager := NewEventManager(hub, config.EventManager)

	return &Service{
		hub:          hub,
		handler:      handler,
		eventManager: eventManager,
		auth:         auth,
		isRunning:    false,
	}
}

// Start 启动WebSocket服务
func (s *Service) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		return fmt.Errorf("WebSocket服务已在运行")
	}

	// 启动Hub
	s.hub.Start()

	// 启动事件管理器
	if err := s.eventManager.Start(); err != nil {
		s.hub.Stop()
		return fmt.Errorf("启动事件管理器失败: %w", err)
	}

	s.isRunning = true
	log.Println("WebSocket服务已启动")

	return nil
}

// Stop 停止WebSocket服务
func (s *Service) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return fmt.Errorf("WebSocket服务未运行")
	}

	// 停止事件管理器
	s.eventManager.Stop()

	// 停止Hub
	s.hub.Stop()

	s.isRunning = false
	log.Println("WebSocket服务已停止")

	return nil
}

// GetHandler 获取WebSocket处理器
func (s *Service) GetHandler() *Handler {
	return s.handler
}

// GetHub 获取Hub
func (s *Service) GetHub() *HubImpl {
	return s.hub
}

// GetEventManager 获取事件管理器
func (s *Service) GetEventManager() *EventManager {
	return s.eventManager
}

// BroadcastMessage 广播消息
func (s *Service) BroadcastMessage(message *Message) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if !s.isRunning {
		return fmt.Errorf("WebSocket服务未运行")
	}

	return s.hub.BroadcastMessage(message)
}

// BroadcastToStation 向指定站点广播消息
func (s *Service) BroadcastToStation(stationID int64, message *Message) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if !s.isRunning {
		return fmt.Errorf("WebSocket服务未运行")
	}

	roomID := fmt.Sprintf("station_%d", stationID)
	return s.hub.BroadcastToRoom(roomID, message)
}

// EmitNozzleStatusUpdate 发送喷嘴状态更新
func (s *Service) EmitNozzleStatusUpdate(payload *NozzleStatusPayload) error {
	message := &Message{
		Type:    MessageTypeNozzleStatus,
		Payload: payload,
		Source:  "fcc",
	}

	return s.BroadcastToStation(payload.StationID, message)
}

// EmitTransactionUpdate 发送交易更新
func (s *Service) EmitTransactionUpdate(payload *TransactionUpdatePayload) error {
	message := &Message{
		Type:    MessageTypeTransactionUpdate,
		Payload: payload,
		Source:  "bos",
	}

	log.Printf("发送交易更新WebSocket消息: 交易ID=%s, 交易号=%s, 喷嘴=%s, 站点=%d, 状态=%s",
		payload.TransactionID, payload.TransactionNumber, payload.NozzleID, payload.StationID, payload.Status)

	return s.BroadcastToStation(payload.StationID, message)
}

// EmitShiftStatusUpdate 发送班次状态更新
func (s *Service) EmitShiftStatusUpdate(payload *ShiftStatusPayload) error {
	message := &Message{
		Type:    MessageTypeShiftStatus,
		Payload: payload,
		Source:  "bos",
	}

	return s.BroadcastToStation(payload.StationID, message)
}

// EmitDashboardUpdate 发送仪表板更新
func (s *Service) EmitDashboardUpdate(payload *DashboardUpdatePayload) error {
	message := &Message{
		Type:    MessageTypeDashboardUpdate,
		Payload: payload,
		Source:  "bos",
	}

	log.Printf("发送仪表板更新WebSocket消息: 站点=%d, 收入=%.2f, 交易数=%d, 销量=%.2f",
		payload.StationID, payload.TodayRevenue, payload.TodayTransactions, payload.TodayVolume)

	return s.BroadcastToStation(payload.StationID, message)
}

// EmitPriceUpdate 发送价格更新
func (s *Service) EmitPriceUpdate(payload *PriceUpdatePayload) error {
	message := &Message{
		Type:    MessageTypePriceUpdate,
		Payload: payload,
		Source:  "bos",
	}

	return s.BroadcastToStation(payload.StationID, message)
}

// GetStats 获取服务统计信息
func (s *Service) GetStats() *ServiceStats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	hubStats := s.hub.GetStats()
	eventSources := s.eventManager.GetEventSources()

	return &ServiceStats{
		IsRunning:    s.isRunning,
		HubStats:     hubStats,
		EventSources: eventSources,
	}
}

// ServiceStats 服务统计信息
type ServiceStats struct {
	IsRunning    bool      `json:"is_running"`
	HubStats     *HubStats `json:"hub_stats"`
	EventSources []string  `json:"event_sources"`
}

// AddEventSource 添加事件源
func (s *Service) AddEventSource(source EventSource) error {
	return s.eventManager.AddEventSource(source)
}

// RemoveEventSource 移除事件源
func (s *Service) RemoveEventSource(name string) error {
	return s.eventManager.RemoveEventSource(name)
}

// MockAuthService 模拟认证服务（用于测试）
type MockAuthService struct {
	validTokens map[string]*UserInfo
}

// NewMockAuthService 创建模拟认证服务
func NewMockAuthService() *MockAuthService {
	return &MockAuthService{
		validTokens: map[string]*UserInfo{
			"test-token-1": {
				UserID:    "user1",
				Username:  "testuser1",
				StationID: 1,
				Role:      "operator",
			},
			"test-token-2": {
				UserID:    "user2",
				Username:  "testuser2",
				StationID: 2,
				Role:      "manager",
			},
			"admin-token": {
				UserID:    "admin",
				Username:  "admin",
				StationID: 0, // 管理员可以访问所有站点
				Role:      "admin",
			},
		},
	}
}

// ValidateToken 验证token
func (mas *MockAuthService) ValidateToken(token string) (*UserInfo, error) {
	if userInfo, exists := mas.validTokens[token]; exists {
		return userInfo, nil
	}
	return nil, fmt.Errorf("无效的token")
}

// AddToken 添加测试token
func (mas *MockAuthService) AddToken(token string, userInfo *UserInfo) {
	mas.validTokens[token] = userInfo
}

// HTTPFCCClient HTTP FCC客户端实现
type HTTPFCCClient struct {
	client  *http.Client
	timeout time.Duration
	// 记录FCC连接状态
	fccStatus map[string]bool // fccID -> isOnline
	mu        sync.RWMutex
}

// NewHTTPFCCClient 创建HTTP FCC客户端
func NewHTTPFCCClient() *HTTPFCCClient {
	return &HTTPFCCClient{
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
		timeout:   10 * time.Second,
		fccStatus: make(map[string]bool),
	}
}

// GetDeviceStatus 获取设备状态
func (hfc *HTTPFCCClient) GetDeviceStatus(ctx context.Context, url string) ([]interface{}, error) {
	// 构造完整的API URL
	fullURL := url + "/devices/status"

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		log.Printf("创建FCC设备状态请求失败: %v", err)
		return hfc.getFallbackDeviceStatus(), nil // 返回备用数据
	}

	// 发送请求
	resp, err := hfc.client.Do(req)
	if err != nil {
		log.Printf("FCC设备状态API调用失败: %v", err)
		return hfc.getFallbackDeviceStatus(), nil // 返回备用数据
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		log.Printf("FCC设备状态API返回错误状态: %d", resp.StatusCode)
		return hfc.getFallbackDeviceStatus(), nil // 返回备用数据
	}

	// 解析响应
	var devices []interface{}
	if err := json.NewDecoder(resp.Body).Decode(&devices); err != nil {
		log.Printf("解析FCC设备状态响应失败: %v", err)
		return hfc.getFallbackDeviceStatus(), nil // 返回备用数据
	}

	log.Printf("成功获取FCC设备状态: %d个设备", len(devices))
	return devices, nil
}

// getFallbackDeviceStatus 获取备用设备状态数据
func (hfc *HTTPFCCClient) getFallbackDeviceStatus() []interface{} {
	return []interface{}{
		map[string]interface{}{
			"device_id":   "device1",
			"station_id":  1,
			"device_name": "Dispenser 1",
			"is_enabled":  true,
			"status":      "offline", // 标记为离线，表示无法获取真实状态
		},
	}
}

// GetNozzleStatus 获取喷嘴状态 (支持FCC掉线检测和批量状态推送)
func (hfc *HTTPFCCClient) GetNozzleStatus(ctx context.Context, url string) ([]interface{}, error) {
	// 从URL中提取FCC ID (假设URL格式为 http://host:port/api)
	fccID := hfc.extractFCCID(url)

	// 构造完整的API URL
	fullURL := url + "/nozzles/status"

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		log.Printf("❌ 创建FCC喷嘴状态请求失败 [%s]: %v", fccID, err)
		return hfc.handleFCCOffline(fccID), nil
	}

	// 发送请求
	resp, err := hfc.client.Do(req)
	if err != nil {
		log.Printf("❌ FCC喷嘴状态API调用失败 [%s]: %v", fccID, err)
		return hfc.handleFCCOffline(fccID), nil
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		log.Printf("❌ FCC喷嘴状态API返回错误状态 [%s]: %d", fccID, resp.StatusCode)
		return hfc.handleFCCOffline(fccID), nil
	}

	// 解析响应
	var nozzles []interface{}
	if err := json.NewDecoder(resp.Body).Decode(&nozzles); err != nil {
		log.Printf("❌ 解析FCC喷嘴状态响应失败 [%s]: %v", fccID, err)
		return hfc.handleFCCOffline(fccID), nil
	}

	// FCC在线，更新状态
	hfc.setFCCOnline(fccID)
	log.Printf("✅ 成功获取FCC喷嘴状态 [%s]: %d个喷嘴", fccID, len(nozzles))

	return nozzles, nil
}

// getFallbackNozzleStatus 获取备用喷嘴状态数据
func (hfc *HTTPFCCClient) getFallbackNozzleStatus() []interface{} {
	return []interface{}{
		map[string]interface{}{
			"nozzle_id":      "offline_nozzle",
			"dispenser_id":   "offline_dispenser",
			"station_id":     1,
			"status":         "offline", // 标记为离线，表示无法获取真实状态
			"current_volume": 0.0,
			"current_amount": 0.0,
			"fuel_grade":     "UNKNOWN",
		},
	}
}

// extractFCCID 从URL中提取FCC ID
func (hfc *HTTPFCCClient) extractFCCID(url string) string {
	// 简单实现：从URL中提取主机和端口作为FCC ID
	// 例如: http://localhost:8081/api -> localhost:8081
	if len(url) > 7 { // 去掉 "http://"
		urlPart := url[7:] // 去掉协议部分
		if idx := strings.Index(urlPart, "/"); idx > 0 {
			return urlPart[:idx] // 返回主机:端口部分
		}
		return urlPart
	}
	return "unknown"
}

// setFCCOnline 设置FCC为在线状态
func (hfc *HTTPFCCClient) setFCCOnline(fccID string) {
	hfc.mu.Lock()
	defer hfc.mu.Unlock()

	wasOffline := !hfc.fccStatus[fccID]
	hfc.fccStatus[fccID] = true

	if wasOffline {
		log.Printf("🟢 FCC恢复在线 [%s]", fccID)
	}
}

// handleFCCOffline 处理FCC离线情况
func (hfc *HTTPFCCClient) handleFCCOffline(fccID string) []interface{} {
	hfc.mu.Lock()
	defer hfc.mu.Unlock()

	wasOnline := hfc.fccStatus[fccID]
	hfc.fccStatus[fccID] = false

	if wasOnline {
		log.Printf("🔴 FCC掉线检测 [%s] - 将相关喷嘴设为离线状态", fccID)
	}

	// 返回该FCC关联的所有喷嘴的离线状态
	return hfc.getOfflineNozzlesForFCC(fccID)
}

// getOfflineNozzlesForFCC 获取指定FCC的所有喷嘴的离线状态
func (hfc *HTTPFCCClient) getOfflineNozzlesForFCC(fccID string) []interface{} {
	// TODO: 这里应该从配置或数据库中获取该FCC关联的真实喷嘴列表
	// 现在先返回模拟的离线喷嘴状态
	offlineNozzles := []interface{}{}

	// 根据FCC ID生成对应的离线喷嘴
	nozzleCount := 8 // 假设每个FCC管理8个喷嘴
	for i := 1; i <= nozzleCount; i++ {
		nozzleID := fmt.Sprintf("%s_nozzle_%d", fccID, i)
		dispenserID := fmt.Sprintf("%s_dispenser_%d", fccID, (i+1)/2) // 2个喷嘴共用1个dispenser

		offlineNozzles = append(offlineNozzles, map[string]interface{}{
			"nozzle_id":      nozzleID,
			"dispenser_id":   dispenserID,
			"station_id":     1,
			"status":         "offline", // FCC掉线，所有喷嘴离线
			"current_volume": 0.0,
			"current_amount": 0.0,
			"fuel_grade":     "RON92",
			"fcc_id":         fccID,
			"offline_reason": "FCC_DISCONNECTED",
		})
	}

	log.Printf("📤 生成FCC离线状态: [%s] %d个喷嘴", fccID, len(offlineNozzles))
	return offlineNozzles
}

// CreateDefaultWebSocketService 创建默认WebSocket服务
func CreateDefaultWebSocketService() *Service {
	// 创建模拟认证服务
	auth := NewMockAuthService()

	// 创建WebSocket服务
	service := NewService(auth, nil)

	// 添加FCC事件源
	fccClient := NewHTTPFCCClient()
	fccConfigs := []FCCConfig{
		{
			ID:         "fcc1",
			PrimaryURL: "http://localhost:8081/api",
			HealthURL:  "http://localhost:8081/health",
			DeviceIDs:  []string{"device1", "device2"},
		},
	}

	fccSource := NewFCCEventSource("fcc-main", fccConfigs, fccClient)
	service.AddEventSource(fccSource)

	// 添加BOS事件源
	bosSource := NewBOSEventSource("bos-main")
	service.AddEventSource(bosSource)

	return service
}
