package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockWebSocketConn 模拟WebSocket连接
type MockWebSocketConn struct {
	mock.Mock
	messages chan []byte
	closed   bool
	mu       sync.Mutex
}

func NewMockWebSocketConn() *MockWebSocketConn {
	return &MockWebSocketConn{
		messages: make(chan []byte, 100),
	}
}

func (m *MockWebSocketConn) ReadMessage() (int, []byte, error) {
	args := m.Called()
	return args.Int(0), args.Get(1).([]byte), args.Error(2)
}

func (m *MockWebSocketConn) WriteMessage(messageType int, data []byte) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.closed {
		select {
		case m.messages <- data:
		default:
			return fmt.Errorf("message buffer full")
		}
	}

	args := m.Called(messageType, data)
	return args.Error(0)
}

func (m *MockWebSocketConn) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.closed = true
	close(m.messages)

	args := m.Called()
	return args.Error(0)
}

func (m *MockWebSocketConn) GetMessages() [][]byte {
	var messages [][]byte
	for msg := range m.messages {
		messages = append(messages, msg)
	}
	return messages
}

// MockFCCClient 模拟FCC客户端
type MockFCCClient struct {
	mock.Mock
	deviceStatus []interface{}
	nozzleStatus []interface{}
	shouldFail   bool
}

func NewMockFCCClient() *MockFCCClient {
	return &MockFCCClient{
		deviceStatus: []interface{}{
			map[string]interface{}{
				"device_id":   "device1",
				"station_id":  1,
				"device_name": "Test Dispenser 1",
				"is_enabled":  true,
				"status":      "online",
			},
		},
		nozzleStatus: []interface{}{
			map[string]interface{}{
				"nozzle_id":      "nozzle1",
				"dispenser_id":   "dispenser1",
				"station_id":     1,
				"status":         "idle",
				"current_volume": 0.0,
				"current_amount": 0.0,
				"fuel_grade":     "RON92",
			},
			map[string]interface{}{
				"nozzle_id":      "nozzle2",
				"dispenser_id":   "dispenser1",
				"station_id":     1,
				"status":         "idle",
				"current_volume": 0.0,
				"current_amount": 0.0,
				"fuel_grade":     "RON95",
			},
		},
	}
}

func (m *MockFCCClient) GetDeviceStatus(ctx context.Context, url string) ([]interface{}, error) {
	m.Called(ctx, url)
	if m.shouldFail {
		return nil, fmt.Errorf("mock FCC error")
	}
	return m.deviceStatus, nil
}

func (m *MockFCCClient) GetNozzleStatus(ctx context.Context, url string) ([]interface{}, error) {
	m.Called(ctx, url)
	if m.shouldFail {
		return m.getOfflineNozzles(), nil
	}
	return m.nozzleStatus, nil
}

func (m *MockFCCClient) SetShouldFail(fail bool) {
	m.shouldFail = fail
}

func (m *MockFCCClient) getOfflineNozzles() []interface{} {
	return []interface{}{
		map[string]interface{}{
			"nozzle_id":      "offline_nozzle_1",
			"dispenser_id":   "offline_dispenser_1",
			"station_id":     1,
			"status":         "offline",
			"current_volume": 0.0,
			"current_amount": 0.0,
			"fuel_grade":     "RON92",
			"offline_reason": "FCC_DISCONNECTED",
		},
		map[string]interface{}{
			"nozzle_id":      "offline_nozzle_2",
			"dispenser_id":   "offline_dispenser_1",
			"station_id":     1,
			"status":         "offline",
			"current_volume": 0.0,
			"current_amount": 0.0,
			"fuel_grade":     "RON95",
			"offline_reason": "FCC_DISCONNECTED",
		},
	}
}

// MockDashboardService 模拟仪表板服务
type MockDashboardService struct {
	mock.Mock
}

func NewMockDashboardService() *MockDashboardService {
	return &MockDashboardService{}
}

func (m *MockDashboardService) GetDashboardData(stationID int64) (*DashboardUpdatePayload, error) {
	args := m.Called(stationID)
	return &DashboardUpdatePayload{
		StationID:         stationID,
		TodayRevenue:      5000000.0,
		TodayTransactions: 150,
		TodayVolume:       800.5,
		RevenueChange:     5.2,
		TransactionChange: 3.1,
		VolumeChange:      2.8,
		LastUpdated:       time.Now(),
		FuelSalesMix:      []FuelSalesMixItem{},
	}, args.Error(1)
}

// TestWebSocketServer 测试WebSocket服务器
type TestWebSocketServer struct {
	server   *httptest.Server
	upgrader websocket.Upgrader
	handler  func(*websocket.Conn)
}

func NewTestWebSocketServer(handler func(*websocket.Conn)) *TestWebSocketServer {
	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	tws := &TestWebSocketServer{
		upgrader: upgrader,
		handler:  handler,
	}

	tws.server = httptest.NewServer(http.HandlerFunc(tws.handleWebSocket))
	return tws
}

func (tws *TestWebSocketServer) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := tws.upgrader.Upgrade(w, r, nil)
	if err != nil {
		return
	}
	defer conn.Close()

	if tws.handler != nil {
		tws.handler(conn)
	}
}

func (tws *TestWebSocketServer) URL() string {
	return "ws" + strings.TrimPrefix(tws.server.URL, "http")
}

func (tws *TestWebSocketServer) Close() {
	tws.server.Close()
}

// TestClient 测试客户端
type TestClient struct {
	ID        string
	UserID    string
	StationID int64
	Send      chan []byte
	conn      *MockWebSocketConn
}

func NewTestClient(id, userID string, stationID int64) *TestClient {
	return &TestClient{
		ID:        id,
		UserID:    userID,
		StationID: stationID,
		Send:      make(chan []byte, 256),
		conn:      NewMockWebSocketConn(),
	}
}

func (tc *TestClient) GetMessages() [][]byte {
	var messages [][]byte
	for {
		select {
		case msg := <-tc.Send:
			messages = append(messages, msg)
		default:
			return messages
		}
	}
}

// AssertMessageReceived 断言收到了指定类型的消息
func AssertMessageReceived(t *testing.T, client *TestClient, messageType MessageType, timeout time.Duration) *Message {
	select {
	case data := <-client.Send:
		var message Message
		err := json.Unmarshal(data, &message)
		assert.NoError(t, err, "消息解析失败")
		assert.Equal(t, messageType, message.Type, "消息类型不匹配")
		return &message
	case <-time.After(timeout):
		t.Fatalf("超时等待消息类型: %s", messageType)
		return nil
	}
}

// AssertNoMessage 断言在指定时间内没有收到消息
func AssertNoMessage(t *testing.T, client *TestClient, timeout time.Duration) {
	select {
	case msg := <-client.Send:
		t.Fatalf("意外收到消息: %s", string(msg))
	case <-time.After(timeout):
		// 正常，没有收到消息
	}
}

// CreateTestMessage 创建测试消息
func CreateTestMessage(messageType MessageType, payload interface{}) *Message {
	return &Message{
		Type:      messageType,
		Payload:   payload,
		Timestamp: time.Now(),
		Source:    "test",
	}
}

// CreateTestAuthMessage 创建认证消息
func CreateTestAuthMessage(token string) *Message {
	return CreateTestMessage(MessageTypeAuth, map[string]interface{}{
		"token": token,
	})
}

// CreateTestSubscribeMessage 创建订阅消息
func CreateTestSubscribeMessage(stationID int64, topics []string) *Message {
	return CreateTestMessage(MessageTypeSubscribe, map[string]interface{}{
		"token": "test-token",
		"filters": map[string]interface{}{
			"station_id": stationID,
			"topics":     topics,
		},
	})
}

// WaitForCondition 等待条件满足
func WaitForCondition(t *testing.T, condition func() bool, timeout time.Duration, message string) {
	ticker := time.NewTicker(10 * time.Millisecond)
	defer ticker.Stop()

	timeoutCh := time.After(timeout)

	for {
		select {
		case <-ticker.C:
			if condition() {
				return
			}
		case <-timeoutCh:
			t.Fatalf("等待条件超时: %s", message)
		}
	}
}
