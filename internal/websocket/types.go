package websocket

import (
	"context"
	"time"

	"github.com/gorilla/websocket"
)

// MessageType 定义WebSocket消息类型
type MessageType string

const (
	// 系统消息
	MessageTypeAuth        MessageType = "auth"
	MessageTypeSubscribe   MessageType = "subscribe"
	MessageTypeUnsubscribe MessageType = "unsubscribe"
	MessageTypeHeartbeat   MessageType = "heartbeat"
	MessageTypeSnapshot    MessageType = "snapshot"

	// 业务消息 - FCC相关
	MessageTypeNozzleStatus      MessageType = "nozzle_status_update"
	MessageTypeNozzleStatusBatch MessageType = "nozzle_status_batch_update" // 批量喷嘴状态更新
	MessageTypeDeviceConfig      MessageType = "device_config_update"
	MessageTypeDeviceStatus      MessageType = "device_status_update"

	// 业务消息 - BOS相关
	MessageTypeTransactionUpdate MessageType = "transaction_update"
	MessageTypeShiftStatus       MessageType = "shift_status_update"
	MessageTypeDashboardUpdate   MessageType = "dashboard_update"
	MessageTypeOrderUpdate       MessageType = "order_update"

	// 业务消息 - 系统相关
	MessageTypePriceUpdate     MessageType = "price_update"
	MessageTypePromotionUpdate MessageType = "promotion_update"

	// 错误消息
	MessageTypeError MessageType = "error"
)

// Client WebSocket客户端连接
type Client struct {
	ID        string          `json:"id"`
	Conn      *websocket.Conn `json:"-"`
	UserID    string          `json:"user_id"`
	StationID int64           `json:"station_id"`
	Send      chan []byte     `json:"-"`
	LastPing  time.Time       `json:"last_ping"`
	CreatedAt time.Time       `json:"created_at"`

	// 订阅信息
	Subscriptions map[MessageType]bool `json:"subscriptions"`
	Filters       *ClientFilters       `json:"filters"`

	// 连接状态
	IsActive bool               `json:"is_active"`
	Context  context.Context    `json:"-"`
	Cancel   context.CancelFunc `json:"-"`
}

// ClientFilters 客户端过滤条件
type ClientFilters struct {
	StationID    *int64        `json:"station_id,omitempty"`
	IslandID     *string       `json:"island_id,omitempty"`
	DispenserIDs []string      `json:"dispenser_ids,omitempty"`
	NozzleIDs    []string      `json:"nozzle_ids,omitempty"`
	Topics       []MessageType `json:"topics,omitempty"`
}

// Message WebSocket消息结构
type Message struct {
	Type      MessageType `json:"type"`
	Payload   interface{} `json:"payload,omitempty"`
	Seq       int64       `json:"seq,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
	ClientID  string      `json:"client_id,omitempty"`
	Source    string      `json:"source,omitempty"` // 消息来源：bos, fcc, system
}

// AuthMessage 认证消息
type AuthMessage struct {
	Token string `json:"token"`
}

// SubscribeMessage 订阅消息
type SubscribeMessage struct {
	Token   string         `json:"token"`
	Filters *ClientFilters `json:"filters"`
}

// ErrorMessage 错误消息
type ErrorMessage struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Detail  string `json:"detail,omitempty"`
}

// HeartbeatMessage 心跳消息
type HeartbeatMessage struct {
	Timestamp time.Time `json:"timestamp"`
	ClientID  string    `json:"client_id"`
}

// NozzleStatusPayload 喷嘴状态更新载荷
type NozzleStatusPayload struct {
	NozzleID      string    `json:"nozzle_id"`
	DispenserID   string    `json:"dispenser_id"`
	StationID     int64     `json:"station_id"`
	Status        string    `json:"status"`
	CurrentVolume float64   `json:"current_volume"`
	CurrentAmount float64   `json:"current_amount"`
	CurrentPrice  float64   `json:"current_price"`
	FuelGrade     string    `json:"fuel_grade"`
	ErrorMessage  string    `json:"error_message,omitempty"`
	LastUpdated   time.Time `json:"last_updated"`

	// 扩展字段
	IsAuthorized  bool    `json:"is_authorized"`
	PreAuthAmount float64 `json:"pre_auth_amount,omitempty"`
	TransactionID string  `json:"transaction_id,omitempty"`
}

// TransactionUpdatePayload 交易更新载荷
type TransactionUpdatePayload struct {
	TransactionID     string    `json:"transaction_id"`
	TransactionNumber string    `json:"transaction_number"`
	NozzleID          string    `json:"nozzle_id"`
	StationID         int64     `json:"station_id"`
	Status            string    `json:"status"`
	Amount            float64   `json:"amount"`
	Volume            float64   `json:"volume"`
	FuelType          string    `json:"fuel_type"`
	FuelGrade         string    `json:"fuel_grade"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`

	// 扩展字段
	CustomerID    *string `json:"customer_id,omitempty"`
	PaymentMethod string  `json:"payment_method,omitempty"`
	PromotionID   *string `json:"promotion_id,omitempty"`
}

// ShiftStatusPayload 班次状态更新载荷
type ShiftStatusPayload struct {
	ShiftID       string     `json:"shift_id"`
	StationID     int64      `json:"station_id"`
	ShiftNumber   string     `json:"shift_number"`
	Status        string     `json:"status"`
	StartTime     time.Time  `json:"start_time"`
	EndTime       *time.Time `json:"end_time,omitempty"`
	AttendantID   string     `json:"attendant_id"`
	AttendantName string     `json:"attendant_name"`

	// 统计信息
	TotalTransactions int     `json:"total_transactions"`
	TotalRevenue      float64 `json:"total_revenue"`
	TotalVolume       float64 `json:"total_volume"`
}

// FuelSalesMixItem 燃油销售组合项
type FuelSalesMixItem struct {
	FuelType   string  `json:"fuel_type"`
	FuelGrade  string  `json:"fuel_grade"`
	Volume     float64 `json:"volume"`
	Revenue    float64 `json:"revenue"`
	Percentage float64 `json:"percentage"`
}

// DashboardUpdatePayload 仪表板更新载荷
type DashboardUpdatePayload struct {
	StationID         int64     `json:"station_id"`
	TodayRevenue      float64   `json:"today_revenue"`
	TodayTransactions int       `json:"today_transactions"`
	TodayVolume       float64   `json:"today_volume"`
	RevenueChange     float64   `json:"revenue_change"`
	TransactionChange float64   `json:"transaction_change"`
	VolumeChange      float64   `json:"volume_change"`
	LastUpdated       time.Time `json:"last_updated"`

	// 燃油销售组合
	FuelSalesMix []FuelSalesMixItem `json:"fuel_sales_mix"`
}

// SnapshotPayload 初始快照载荷
type SnapshotPayload struct {
	Devices            []interface{} `json:"devices"`
	Nozzles            []interface{} `json:"nozzles"`
	ActiveTransactions []interface{} `json:"active_transactions"`
	CurrentShift       interface{}   `json:"current_shift"`
	DashboardSummary   interface{}   `json:"dashboard_summary"`
	Timestamp          time.Time     `json:"timestamp"`
}

// DeviceConfigPayload 设备配置更新载荷
type DeviceConfigPayload struct {
	DeviceID     string    `json:"device_id"`
	StationID    int64     `json:"station_id"`
	DeviceName   string    `json:"device_name"`
	DeviceType   string    `json:"device_type"`
	IsEnabled    bool      `json:"is_enabled"`
	MaxNozzles   int       `json:"max_nozzles"`
	Capabilities []string  `json:"capabilities"`
	LastUpdated  time.Time `json:"last_updated"`
}

// PriceUpdatePayload 价格更新载荷
type PriceUpdatePayload struct {
	StationID   int64              `json:"station_id"`
	DeviceID    string             `json:"device_id"`
	PumpID      int                `json:"pump_id"`
	Prices      map[string]float64 `json:"prices"` // fuel_grade -> price
	EffectiveAt time.Time          `json:"effective_at"`
	UpdatedBy   string             `json:"updated_by"`
}

// Room 房间管理
type Room struct {
	ID      string             `json:"id"`
	Name    string             `json:"name"`
	Clients map[string]*Client `json:"clients"`
	Filters *RoomFilters       `json:"filters"`

	// 房间统计
	CreatedAt    time.Time `json:"created_at"`
	LastActivity time.Time `json:"last_activity"`
	MessageCount int64     `json:"message_count"`
}

// RoomFilters 房间过滤条件
type RoomFilters struct {
	StationID *int64  `json:"station_id,omitempty"`
	IslandID  *string `json:"island_id,omitempty"`
	UserRole  *string `json:"user_role,omitempty"`
}

// Hub WebSocket连接中心
type Hub struct {
	// 注册的客户端
	Clients map[string]*Client

	// 房间管理
	Rooms map[string]*Room

	// 通道
	Register      chan *Client
	Unregister    chan *Client
	Broadcast     chan *Message
	RoomBroadcast chan *RoomMessage

	// 统计信息
	Stats *HubStats

	// 上下文控制
	Context context.Context
	Cancel  context.CancelFunc
}

// RoomMessage 房间消息
type RoomMessage struct {
	RoomID  string   `json:"room_id"`
	Message *Message `json:"message"`
}

// ConnectionStats 连接统计
type ConnectionStats struct {
	TotalConnections     int            `json:"total_connections"`
	ActiveConnections    int            `json:"active_connections"`
	ConnectionsByRoom    map[string]int `json:"connections_by_room"`
	ConnectionsByStation map[int64]int  `json:"connections_by_station"`
	LastUpdated          time.Time      `json:"last_updated"`
}

// HubStats Hub统计信息
type HubStats struct {
	TotalConnections     int            `json:"total_connections"`
	ActiveConnections    int            `json:"active_connections"`
	ConnectionsByRoom    map[string]int `json:"connections_by_room"`
	ConnectionsByStation map[int64]int  `json:"connections_by_station"`
	MessagesSent         int64          `json:"messages_sent"`
	MessagesReceived     int64          `json:"messages_received"`
	LastUpdated          time.Time      `json:"last_updated"`
}

// MessageFilter 消息过滤器接口
type MessageFilter interface {
	// 检查消息是否应该发送给客户端
	ShouldSend(client *Client, message *Message) bool
}

// EventSource 事件源接口
type EventSource interface {
	// 启动事件源
	Start(ctx context.Context) error

	// 停止事件源
	Stop() error

	// 获取事件通道
	Events() <-chan *Message

	// 获取事件源名称
	Name() string
}

// ConnectionManager 连接管理器接口
type ConnectionManager interface {
	// 添加客户端
	AddClient(client *Client) error

	// 移除客户端
	RemoveClient(clientID string) error

	// 获取客户端
	GetClient(clientID string) (*Client, bool)

	// 获取所有客户端
	GetAllClients() map[string]*Client

	// 根据条件获取客户端
	GetClientsByFilter(filter *ClientFilters) []*Client
}
