package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"gitlab4.weicheche.cn/indo-bp/bos/config"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/server"
)

// @BasePath    /api/v1
func main() {
	// 加载配置
	cfg, err := config.LoadConfig("./config/config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 创建服务器实例
	srv, err := server.New(cfg)
	if err != nil {
		log.Fatalf("创建服务器失败: %v", err)
	}

	// 启动服务器
	serverPort := strconv.Itoa(cfg.Server.Port)
	log.Printf("启动服务器，监听端口: %s", serverPort)

	go func() {
		if err := srv.Start(":" + serverPort); err != nil {
			log.Printf("服务器关闭: %v", err)
		}
	}()

	// 等待中断信号优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// 设置超时时间为10秒，进行优雅关闭
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err := srv.Shutdown(shutdownCtx); err != nil {
		log.Fatal("服务器强制关闭:", err)
	}
}
