# BOS (Back Office System) Systemd Service Configuration
# 复制此文件到 /etc/systemd/system/bos-service.service

[Unit]
# 服务描述
Description=BOS (Back Office System) Service
# 文档链接
Documentation=https://gitlab4.weicheche.cn/indo-bp/bos
# 在网络启动后启动
After=network.target
# 需要网络服务
Requires=network.target

[Service]
# 服务类型：simple表示主进程不会fork
Type=simple
# 运行用户（可选，默认root）
User=bos
Group=bos
# 工作目录
WorkingDirectory=/opt/bos
# 启动命令
ExecStart=/opt/bos/bos
# 重启策略：总是重启
Restart=always
# 重启间隔：5秒
RestartSec=5
# 日志输出到systemd journal
StandardOutput=journal
StandardError=journal
# 日志标识符
SyslogIdentifier=bos-service

# 基本环境变量
Environment=BOS_ENVIRONMENT=production
Environment=BOS_SERVER_PORT=8080
Environment=BOS_LOG_LEVEL=info

# 从文件加载敏感环境变量（数据库密码、JWT密钥等）
# 文件路径前的 - 表示文件不存在时不报错
EnvironmentFile=-/etc/bos/environment

# 基本安全配置
# 禁止获取新权限
NoNewPrivileges=true
# 使用私有临时目录
PrivateTmp=true

# 基本资源限制
# 最大文件描述符数量
LimitNOFILE=65536
# 最大进程数量
LimitNPROC=4096

[Install]
# 在多用户模式下启动
WantedBy=multi-user.target
# 服务别名
Alias=bos.service
