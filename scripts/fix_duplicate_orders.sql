-- 修复重复订单问题的SQL脚本
-- 该脚本将清理超过燃油交易金额的重复分配

-- 1. 查看当前重复分配的燃油交易
WITH fuel_allocation_summary AS (
    SELECT 
        ftol.fuel_transaction_id,
        ft.amount as total_amount,
        COUNT(ftol.id) as link_count,
        SUM(ftol.allocated_amount) as total_allocated,
        SUM(ftol.allocated_amount) - ft.amount as over_allocated
    FROM fuel_transaction_order_links ftol
    JOIN fuel_transactions ft ON ftol.fuel_transaction_id = ft.id
    WHERE ftol.status = 'active'
    GROUP BY ftol.fuel_transaction_id, ft.amount
    HAVING SUM(ftol.allocated_amount) > ft.amount
)
SELECT 
    fuel_transaction_id,
    total_amount,
    link_count,
    total_allocated,
    over_allocated
FROM fuel_allocation_summary
ORDER BY over_allocated DESC;

-- 2. 查看具体的重复关联详情
WITH duplicate_allocations AS (
    SELECT 
        ftol.fuel_transaction_id,
        ft.amount as total_amount,
        SUM(ftol.allocated_amount) as total_allocated,
        SUM(ftol.allocated_amount) - ft.amount as over_allocated
    FROM fuel_transaction_order_links ftol
    JOIN fuel_transactions ft ON ftol.fuel_transaction_id = ft.id
    WHERE ftol.status = 'active'
    GROUP BY ftol.fuel_transaction_id, ft.amount
    HAVING SUM(ftol.allocated_amount) > ft.amount
)
SELECT 
    ftol.id as link_id,
    ftol.fuel_transaction_id,
    ftol.order_id,
    ftol.allocated_amount,
    ftol.created_at,
    o.order_number,
    o.status as order_status,
    da.total_amount,
    da.total_allocated,
    da.over_allocated
FROM fuel_transaction_order_links ftol
JOIN orders o ON ftol.order_id = o.id
JOIN duplicate_allocations da ON ftol.fuel_transaction_id = da.fuel_transaction_id
WHERE ftol.status = 'active'
ORDER BY ftol.fuel_transaction_id, ftol.created_at;

-- 3. 修复策略：保留最早创建的订单，取消后续的重复订单
-- 注意：运行前请先备份数据库！

-- 开始事务
BEGIN;

-- 标记需要取消的重复订单（保留每个燃油交易最早的订单）
WITH duplicate_allocations AS (
    SELECT 
        ftol.fuel_transaction_id,
        ft.amount as total_amount,
        SUM(ftol.allocated_amount) as total_allocated
    FROM fuel_transaction_order_links ftol
    JOIN fuel_transactions ft ON ftol.fuel_transaction_id = ft.id
    WHERE ftol.status = 'active'
    GROUP BY ftol.fuel_transaction_id, ft.amount
    HAVING SUM(ftol.allocated_amount) > ft.amount
),
orders_to_keep AS (
    SELECT DISTINCT ON (ftol.fuel_transaction_id)
        ftol.fuel_transaction_id,
        ftol.order_id as keep_order_id,
        ftol.id as keep_link_id
    FROM fuel_transaction_order_links ftol
    JOIN duplicate_allocations da ON ftol.fuel_transaction_id = da.fuel_transaction_id
    WHERE ftol.status = 'active'
    ORDER BY ftol.fuel_transaction_id, ftol.created_at ASC
),
orders_to_cancel AS (
    SELECT 
        ftol.id as link_id,
        ftol.order_id,
        ftol.fuel_transaction_id
    FROM fuel_transaction_order_links ftol
    JOIN duplicate_allocations da ON ftol.fuel_transaction_id = da.fuel_transaction_id
    LEFT JOIN orders_to_keep otk ON ftol.fuel_transaction_id = otk.fuel_transaction_id 
        AND ftol.order_id = otk.keep_order_id
    WHERE ftol.status = 'active' 
        AND otk.keep_order_id IS NULL
)
-- 首先将重复的订单状态设置为已取消
UPDATE orders 
SET 
    status = 'cancelled',
    updated_at = NOW()
WHERE id IN (
    SELECT order_id FROM orders_to_cancel
);

-- 将对应的链接设置为非活跃
WITH duplicate_allocations AS (
    SELECT 
        ftol.fuel_transaction_id,
        ft.amount as total_amount,
        SUM(ftol.allocated_amount) as total_allocated
    FROM fuel_transaction_order_links ftol
    JOIN fuel_transactions ft ON ftol.fuel_transaction_id = ft.id
    WHERE ftol.status = 'active'
    GROUP BY ftol.fuel_transaction_id, ft.amount
    HAVING SUM(ftol.allocated_amount) > ft.amount
),
orders_to_keep AS (
    SELECT DISTINCT ON (ftol.fuel_transaction_id)
        ftol.fuel_transaction_id,
        ftol.order_id as keep_order_id,
        ftol.id as keep_link_id
    FROM fuel_transaction_order_links ftol
    JOIN duplicate_allocations da ON ftol.fuel_transaction_id = da.fuel_transaction_id
    WHERE ftol.status = 'active'
    ORDER BY ftol.fuel_transaction_id, ftol.created_at ASC
),
orders_to_cancel AS (
    SELECT 
        ftol.id as link_id,
        ftol.order_id,
        ftol.fuel_transaction_id
    FROM fuel_transaction_order_links ftol
    JOIN duplicate_allocations da ON ftol.fuel_transaction_id = da.fuel_transaction_id
    LEFT JOIN orders_to_keep otk ON ftol.fuel_transaction_id = otk.fuel_transaction_id 
        AND ftol.order_id = otk.keep_order_id
    WHERE ftol.status = 'active' 
        AND otk.keep_order_id IS NULL
)
UPDATE fuel_transaction_order_links 
SET 
    status = 'inactive',
    deactivated_at = NOW(),
    updated_at = NOW()
WHERE id IN (
    SELECT link_id FROM orders_to_cancel
);

-- 验证修复结果
WITH verification AS (
    SELECT 
        ftol.fuel_transaction_id,
        ft.amount as total_amount,
        COUNT(ftol.id) as active_link_count,
        SUM(ftol.allocated_amount) as total_allocated,
        SUM(ftol.allocated_amount) - ft.amount as remaining_over_allocation
    FROM fuel_transaction_order_links ftol
    JOIN fuel_transactions ft ON ftol.fuel_transaction_id = ft.id
    WHERE ftol.status = 'active'
    GROUP BY ftol.fuel_transaction_id, ft.amount
)
SELECT 
    fuel_transaction_id,
    total_amount,
    active_link_count,
    total_allocated,
    remaining_over_allocation,
    CASE 
        WHEN remaining_over_allocation > 0.01 THEN '仍有超分配'
        ELSE '已修复'
    END as fix_status
FROM verification
ORDER BY remaining_over_allocation DESC;

-- 如果验证通过，提交事务；否则回滚
-- COMMIT;
-- ROLLBACK; 