-- Phase 2 索引优化脚本
-- 执行前请确保：
-- 1. 在测试环境先验证
-- 2. 在业务低峰期执行
-- 3. 监控系统资源使用情况

-- ============================================================================
-- P0 优先级索引（立即实施）
-- ============================================================================

-- 1. fuel_transactions 主要查询索引
-- 支持: WHERE station_id = ? AND status = 'processed' AND created_at BETWEEN ? AND ?
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fuel_transactions_station_status_created
ON fuel_transactions (station_id, status, created_at)
WHERE status = 'processed';

-- 2. order_payments 订单支付关联索引（支持 LATERAL 子查询优化）
-- 支持: WHERE order_id = ? AND status = 'completed' ORDER BY completed_at DESC
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_payments_order_status_completed_desc
ON order_payments (order_id, status, completed_at DESC)
WHERE status = 'completed';

-- ============================================================================
-- P1 优先级索引（1周内实施）
-- ============================================================================

-- 3. fuel_transactions 班次查询索引
-- 支持: WHERE shift_id = ? AND status = 'processed'
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fuel_transactions_shift_status
ON fuel_transactions (shift_id, status)
WHERE status = 'processed';

-- 4. orders 站点时间查询索引
-- 支持: WHERE station_id = ? AND created_at BETWEEN ? AND ?
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_station_created_status
ON orders (station_id, created_at, status);

-- 5. fuel_transaction_order_links 交易链接索引
-- 支持: WHERE fuel_transaction_id = ? AND status = 'active'
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ftol_transaction_status
ON fuel_transaction_order_links (fuel_transaction_id, status)
WHERE status = 'active';

-- 6. fuel_transaction_order_links 订单链接索引
-- 支持: WHERE order_id = ? AND status = 'active'
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ftol_order_status
ON fuel_transaction_order_links (order_id, status)
WHERE status = 'active';

-- ============================================================================
-- P2 优先级索引（2周内实施）
-- ============================================================================

-- 7. fuel_transactions 员工统计索引
-- 支持: WHERE employee_id = ? AND status = 'processed'
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fuel_transactions_employee_created
ON fuel_transactions (employee_id, created_at)
WHERE status = 'processed' AND employee_id IS NOT NULL;

-- 8. fuel_transactions 员工卡统计索引
-- 支持: WHERE staff_card_id = ? AND status = 'processed'
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fuel_transactions_staff_card_created
ON fuel_transactions (staff_card_id, created_at)
WHERE status = 'processed' AND staff_card_id IS NOT NULL;

-- 9. order_payments 完成时间范围查询索引
-- 支持: WHERE completed_at BETWEEN ? AND ? AND status = 'completed'
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_payments_completed_status
ON order_payments (completed_at, status)
WHERE status = 'completed';

-- 10. order_payments 支付方式统计索引
-- 支持: WHERE payment_method = ? AND status = 'completed'
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_payments_method_completed
ON order_payments (payment_method, completed_at)
WHERE status = 'completed';

-- 11. staff_cards 用户关联索引
-- 支持: WHERE user_id = ? AND deleted_at IS NULL
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_staff_cards_user_status
ON staff_cards (user_id, status)
WHERE deleted_at IS NULL;

-- 12. staff_cards 卡号查询索引
-- 支持: WHERE card_number = ? AND deleted_at IS NULL
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_staff_cards_card_number
ON staff_cards (card_number)
WHERE deleted_at IS NULL;

-- ============================================================================
-- 可选索引（根据实际需求决定是否创建）
-- ============================================================================

-- 13. orders 状态时间索引（用于特定查询优化）
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_status_created
-- ON orders (status, created_at)
-- WHERE status = 'completed';

-- 14. users 全名搜索索引（如果需要按姓名搜索）
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_full_name
-- ON users (full_name)
-- WHERE deleted_at IS NULL;

-- 15. fuel_transactions 加油机统计索引（如果需要按加油机统计）
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fuel_transactions_pump_created
-- ON fuel_transactions (pump_id, created_at)
-- WHERE status = 'processed' AND pump_id IS NOT NULL;

-- ============================================================================
-- 执行完成后的验证
-- ============================================================================

-- 查看索引创建状态
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE indexname LIKE 'idx_fuel_transactions_%' 
   OR indexname LIKE 'idx_order_payments_%'
   OR indexname LIKE 'idx_orders_%'
   OR indexname LIKE 'idx_ftol_%'
   OR indexname LIKE 'idx_staff_cards_%'
ORDER BY tablename, indexname;

-- 查看索引大小
SELECT 
    t.tablename,
    i.indexname,
    pg_size_pretty(pg_relation_size(i.indexname::regclass)) as index_size,
    pg_size_pretty(pg_relation_size(t.tablename::regclass)) as table_size
FROM pg_tables t
JOIN pg_indexes i ON t.tablename = i.tablename
WHERE i.indexname LIKE 'idx_%'
  AND t.schemaname = 'public'
ORDER BY pg_relation_size(i.indexname::regclass) DESC;
