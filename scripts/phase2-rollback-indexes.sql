-- Phase 2 索引回滚脚本
-- 用于删除 Phase 2 创建的索引（如果需要回滚）
-- 警告：删除索引前请确认不会影响查询性能

-- ============================================================================
-- 回滚前检查
-- ============================================================================

-- 1. 查看当前索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as scans,
    idx_tup_read as tuples_read,
    pg_size_pretty(pg_relation_size(indexname::regclass)) as size
FROM pg_stat_user_indexes
WHERE indexname LIKE 'idx_fuel_transactions_%' 
   OR indexname LIKE 'idx_order_payments_%'
   OR indexname LIKE 'idx_orders_%'
   OR indexname LIKE 'idx_ftol_%'
   OR indexname LIKE 'idx_staff_cards_%'
ORDER BY idx_scan DESC;

-- 2. 确认要删除的索引列表
SELECT 
    'DROP INDEX CONCURRENTLY IF EXISTS ' || indexname || ';' as drop_statement
FROM pg_indexes 
WHERE indexname LIKE 'idx_fuel_transactions_%' 
   OR indexname LIKE 'idx_order_payments_%'
   OR indexname LIKE 'idx_orders_%'
   OR indexname LIKE 'idx_ftol_%'
   OR indexname LIKE 'idx_staff_cards_%'
ORDER BY indexname;

-- ============================================================================
-- 分阶段回滚（按优先级逆序）
-- ============================================================================

-- 阶段 1: 删除 P2 优先级索引（可选索引，影响最小）
-- ============================================================================

-- 删除员工统计相关索引
DROP INDEX CONCURRENTLY IF EXISTS idx_fuel_transactions_employee_created;
DROP INDEX CONCURRENTLY IF EXISTS idx_fuel_transactions_staff_card_created;

-- 删除支付方式统计索引
DROP INDEX CONCURRENTLY IF EXISTS idx_order_payments_method_completed;

-- 删除员工卡相关索引
DROP INDEX CONCURRENTLY IF EXISTS idx_staff_cards_user_status;
DROP INDEX CONCURRENTLY IF EXISTS idx_staff_cards_card_number;

-- 删除完成时间范围查询索引
DROP INDEX CONCURRENTLY IF EXISTS idx_order_payments_completed_status;

-- 阶段 2: 删除 P1 优先级索引（中等影响）
-- ============================================================================

-- 删除班次查询索引
DROP INDEX CONCURRENTLY IF EXISTS idx_fuel_transactions_shift_status;

-- 删除订单站点时间索引
DROP INDEX CONCURRENTLY IF EXISTS idx_orders_station_created_status;

-- 删除交易链接索引
DROP INDEX CONCURRENTLY IF EXISTS idx_ftol_transaction_status;
DROP INDEX CONCURRENTLY IF EXISTS idx_ftol_order_status;

-- 阶段 3: 删除 P0 优先级索引（最高影响，谨慎删除）
-- ============================================================================

-- 警告：删除这些索引会显著影响查询性能，请确认必要性

-- 删除订单支付关联索引（影响 LATERAL 子查询性能）
-- DROP INDEX CONCURRENTLY IF EXISTS idx_order_payments_order_status_completed_desc;

-- 删除燃油交易主查询索引（影响 End-of-Day 和 Shifts Attendants 性能）
-- DROP INDEX CONCURRENTLY IF EXISTS idx_fuel_transactions_station_status_created;

-- ============================================================================
-- 回滚后验证
-- ============================================================================

-- 1. 确认索引已删除
SELECT 
    indexname,
    tablename
FROM pg_indexes 
WHERE indexname LIKE 'idx_fuel_transactions_%' 
   OR indexname LIKE 'idx_order_payments_%'
   OR indexname LIKE 'idx_orders_%'
   OR indexname LIKE 'idx_ftol_%'
   OR indexname LIKE 'idx_staff_cards_%'
ORDER BY tablename, indexname;

-- 2. 查看表大小变化
SELECT 
    tablename,
    pg_size_pretty(pg_relation_size(tablename::regclass)) as table_size,
    pg_size_pretty(pg_indexes_size(tablename::regclass)) as indexes_size,
    pg_size_pretty(pg_total_relation_size(tablename::regclass)) as total_size
FROM pg_tables
WHERE tablename IN ('fuel_transactions', 'orders', 'order_payments', 
                    'fuel_transaction_order_links', 'staff_cards', 'users')
  AND schemaname = 'public'
ORDER BY pg_total_relation_size(tablename::regclass) DESC;

-- 3. 测试关键查询性能（回滚后可能变慢）
\timing on

-- 测试燃油交易查询性能
EXPLAIN (ANALYZE, BUFFERS)
SELECT COUNT(*)
FROM fuel_transactions ft
WHERE ft.station_id = 1 
  AND ft.status = 'processed' 
  AND ft.created_at >= CURRENT_DATE - INTERVAL '1 day'
  AND ft.created_at < CURRENT_DATE;

-- 测试订单支付查询性能
EXPLAIN (ANALYZE, BUFFERS)
SELECT COUNT(*)
FROM orders o
JOIN order_payments op ON o.id = op.order_id
WHERE o.station_id = 1 
  AND op.status = 'completed'
  AND o.created_at >= CURRENT_DATE - INTERVAL '1 day';

\timing off

-- ============================================================================
-- 部分回滚选项
-- ============================================================================

-- 如果只需要删除特定索引，可以使用以下模板：

-- 删除未使用的索引（扫描次数为 0）
/*
SELECT 
    'DROP INDEX CONCURRENTLY IF EXISTS ' || indexname || '; -- Unused index' as statement
FROM pg_stat_user_indexes
WHERE (indexname LIKE 'idx_fuel_transactions_%' 
    OR indexname LIKE 'idx_order_payments_%'
    OR indexname LIKE 'idx_orders_%'
    OR indexname LIKE 'idx_ftol_%'
    OR indexname LIKE 'idx_staff_cards_%')
  AND idx_scan = 0;
*/

-- 删除低使用率索引（扫描次数 < 100）
/*
SELECT 
    'DROP INDEX CONCURRENTLY IF EXISTS ' || indexname || '; -- Low usage: ' || idx_scan || ' scans' as statement
FROM pg_stat_user_indexes
WHERE (indexname LIKE 'idx_fuel_transactions_%' 
    OR indexname LIKE 'idx_order_payments_%'
    OR indexname LIKE 'idx_orders_%'
    OR indexname LIKE 'idx_ftol_%'
    OR indexname LIKE 'idx_staff_cards_%')
  AND idx_scan > 0 AND idx_scan < 100;
*/

-- ============================================================================
-- 回滚完成确认
-- ============================================================================

-- 最终确认：显示剩余的相关索引
SELECT 
    'Remaining indexes after rollback:' as status,
    COUNT(*) as count
FROM pg_indexes 
WHERE indexname LIKE 'idx_fuel_transactions_%' 
   OR indexname LIKE 'idx_order_payments_%'
   OR indexname LIKE 'idx_orders_%'
   OR indexname LIKE 'idx_ftol_%'
   OR indexname LIKE 'idx_staff_cards_%';

-- 显示数据库总大小变化
SELECT 
    'Database size after rollback:' as status,
    pg_size_pretty(pg_database_size(current_database())) as size;
