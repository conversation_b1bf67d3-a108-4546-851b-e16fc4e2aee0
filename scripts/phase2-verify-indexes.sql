-- Phase 2 索引验证脚本
-- 用于验证索引是否正确创建并被查询使用

-- ============================================================================
-- 1. 检查索引创建状态
-- ============================================================================

-- 查看所有新创建的索引
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef,
    CASE 
        WHEN indexdef LIKE '%CONCURRENTLY%' THEN 'CONCURRENT'
        ELSE 'NORMAL'
    END as creation_method
FROM pg_indexes 
WHERE indexname LIKE 'idx_fuel_transactions_%' 
   OR indexname LIKE 'idx_order_payments_%'
   OR indexname LIKE 'idx_orders_%'
   OR indexname LIKE 'idx_ftol_%'
   OR indexname LIKE 'idx_staff_cards_%'
ORDER BY tablename, indexname;

-- ============================================================================
-- 2. 验证关键查询的执行计划
-- ============================================================================

-- 验证 fuel_transactions 主查询索引使用情况
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT)
SELECT ft.id, ft.transaction_number, ft.amount, ft.created_at
FROM fuel_transactions ft
WHERE ft.station_id = 1 
  AND ft.status = 'processed' 
  AND ft.created_at >= '2024-01-01'::timestamp 
  AND ft.created_at < '2024-01-02'::timestamp;

-- 验证 order_payments LATERAL 子查询索引使用情况
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT)
SELECT o.id, op_latest.payment_method, op_latest.amount
FROM orders o
LEFT JOIN LATERAL (
    SELECT op.id, op.payment_method, op.amount, op.status, op.completed_at
    FROM order_payments op
    WHERE op.order_id = o.id AND op.status = 'completed'
    ORDER BY op.completed_at DESC
    LIMIT 1
) AS op_latest ON TRUE
WHERE o.station_id = 1 
  AND o.created_at >= '2024-01-01'::timestamp 
  AND o.created_at < '2024-01-02'::timestamp;

-- 验证班次查询索引使用情况
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT)
SELECT ft.id, ft.amount, ft.employee_id
FROM fuel_transactions ft
WHERE ft.shift_id = '01234567-89ab-cdef-0123-456789abcdef'::uuid
  AND ft.status = 'processed';

-- 验证交易链接查询索引使用情况
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT)
SELECT ftol.order_id, ftol.fuel_transaction_id
FROM fuel_transaction_order_links ftol
WHERE ftol.fuel_transaction_id = '01234567-89ab-cdef-0123-456789abcdef'::uuid
  AND ftol.status = 'active';

-- ============================================================================
-- 3. 索引使用统计
-- ============================================================================

-- 查看索引使用统计（需要运行一段时间后才有数据）
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched,
    CASE 
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_scan < 100 THEN 'LOW_USAGE'
        WHEN idx_scan < 1000 THEN 'MEDIUM_USAGE'
        ELSE 'HIGH_USAGE'
    END as usage_level
FROM pg_stat_user_indexes
WHERE indexname LIKE 'idx_fuel_transactions_%' 
   OR indexname LIKE 'idx_order_payments_%'
   OR indexname LIKE 'idx_orders_%'
   OR indexname LIKE 'idx_ftol_%'
   OR indexname LIKE 'idx_staff_cards_%'
ORDER BY idx_scan DESC;

-- ============================================================================
-- 4. 索引大小和表大小对比
-- ============================================================================

SELECT 
    t.schemaname,
    t.tablename,
    pg_size_pretty(pg_total_relation_size(t.tablename::regclass)) as total_size,
    pg_size_pretty(pg_relation_size(t.tablename::regclass)) as table_size,
    pg_size_pretty(pg_indexes_size(t.tablename::regclass)) as indexes_size,
    ROUND(
        (pg_indexes_size(t.tablename::regclass)::numeric / 
         NULLIF(pg_relation_size(t.tablename::regclass), 0)) * 100, 2
    ) as index_ratio_percent
FROM pg_tables t
WHERE t.tablename IN ('fuel_transactions', 'orders', 'order_payments', 
                      'fuel_transaction_order_links', 'staff_cards', 'users')
  AND t.schemaname = 'public'
ORDER BY pg_total_relation_size(t.tablename::regclass) DESC;

-- ============================================================================
-- 5. 查询性能对比测试
-- ============================================================================

-- 测试 1: End-of-Day 主查询性能
\timing on

-- 燃油交易查询（应该使用 idx_fuel_transactions_station_status_created）
SELECT COUNT(*), SUM(amount), AVG(amount)
FROM fuel_transactions ft
WHERE ft.station_id = 1 
  AND ft.status = 'processed' 
  AND ft.created_at >= CURRENT_DATE - INTERVAL '7 days'
  AND ft.created_at < CURRENT_DATE;

-- 订单支付查询（应该使用 idx_order_payments_order_status_completed_desc）
SELECT COUNT(*), SUM(op.amount)
FROM orders o
JOIN order_payments op ON o.id = op.order_id
WHERE o.station_id = 1 
  AND op.status = 'completed'
  AND o.created_at >= CURRENT_DATE - INTERVAL '7 days'
  AND o.created_at < CURRENT_DATE;

-- 测试 2: Shifts Attendants 查询性能
-- 班次交易查询（应该使用 idx_fuel_transactions_shift_status）
SELECT COUNT(*), SUM(amount)
FROM fuel_transactions ft
WHERE ft.shift_id IN (
    SELECT id FROM shifts 
    WHERE station_id = 1 
      AND start_time >= CURRENT_DATE - INTERVAL '1 day'
)
AND ft.status = 'processed';

\timing off

-- ============================================================================
-- 6. 系统资源使用情况
-- ============================================================================

-- 查看当前数据库大小
SELECT 
    pg_database.datname,
    pg_size_pretty(pg_database_size(pg_database.datname)) AS size
FROM pg_database
WHERE pg_database.datname = current_database();

-- 查看表和索引的磁盘使用情况
SELECT 
    'Tables' as type,
    pg_size_pretty(SUM(pg_relation_size(tablename::regclass))) as size
FROM pg_tables 
WHERE schemaname = 'public'
UNION ALL
SELECT 
    'Indexes' as type,
    pg_size_pretty(SUM(pg_relation_size(indexname::regclass))) as size
FROM pg_indexes 
WHERE schemaname = 'public';

-- ============================================================================
-- 7. 索引健康检查
-- ============================================================================

-- 检查是否有重复或冗余的索引
WITH index_columns AS (
    SELECT 
        i.indexname,
        i.tablename,
        array_agg(a.attname ORDER BY a.attnum) as columns
    FROM pg_indexes i
    JOIN pg_class c ON c.relname = i.indexname
    JOIN pg_index idx ON idx.indexrelid = c.oid
    JOIN pg_attribute a ON a.attrelid = idx.indrelid 
        AND a.attnum = ANY(idx.indkey)
    WHERE i.schemaname = 'public'
    GROUP BY i.indexname, i.tablename
)
SELECT 
    ic1.tablename,
    ic1.indexname as index1,
    ic2.indexname as index2,
    ic1.columns
FROM index_columns ic1
JOIN index_columns ic2 ON ic1.tablename = ic2.tablename 
    AND ic1.columns = ic2.columns
    AND ic1.indexname < ic2.indexname
ORDER BY ic1.tablename, ic1.indexname;
