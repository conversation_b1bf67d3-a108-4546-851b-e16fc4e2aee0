2025-08-17T17:06:10+07:00 INFO server.go:168  验证order数据库连接schema设置...
2025-08-17T17:06:10+07:00 INFO server.go:176  order数据库当前search_path: order_schema, pg_catalog
2025-08-17T17:06:10+07:00 INFO server.go:185  staff_cards表访问成功，共有 20 条记录
2025-08-17T17:06:10+07:00 INFO server.go:229  燃油交易服务初始化成功
2025-08-17T17:06:10+07:00 INFO server.go:257  初始化Staff Card服务...
数据库连接成功，当前search_path: promotion_schema,pg_catalog
2025-08-17T17:06:10+07:00 INFO server.go:366  配置promotion-service连接池回调以确保search_path正确性...
2025-08-17T17:06:10+07:00 INFO server.go:415  promotion-service search_path设置成功: promotion_schema, pg_catalog
2025-08-17T17:06:10+07:00 INFO server.go:424  promotion表访问成功，共有 14 条记录
2025-08-17T17:06:10+07:00 INFO server.go:425  promotion-service连接池配置完成，包含自动search_path设置机制
2025-08-17T17:06:10+07:00 INFO server.go:438  初始化油品服务...
数据库连接成功，当前search_path: oil_schema,pg_catalog
2025-08-17T17:06:10+07:00 INFO server.go:488  初始化PTS2设备相关服务...
2025-08-17T17:06:10+07:00 INFO server.go:503  PTS2设备数据库连接成功
2025-08-17T17:06:10+07:00 INFO server.go:522  初始化客户服务处理器...
2025-08-17T17:06:10+07:00 INFO server.go:537  初始化库存服务处理器...
2025-08-17T17:06:10+07:00 INFO server.go:552  初始化支付服务...
{"level":"info","msg":"使用传递的数据库配置","time":"2025-08-17 17:06:10"}
{"level":"info","msg":"网关管理器创建成功","time":"2025-08-17 17:06:10"}
{"level":"info","msg":"event publisher started","time":"2025-08-17 17:06:10"}
{"level":"info","msg":"支付服务SDK初始化成功","time":"2025-08-17 17:06:10"}
2025-08-17T17:06:10+07:00 INFO server.go:588  初始化WebSocket服务...
2025-08-17T17:06:10+07:00 INFO server.go:602  添加FCC事件源...
2025/08/17 17:06:10 FCC事件源已启动: fcc-main
2025/08/17 17:06:10 事件源已添加: fcc-main
2025-08-17T17:06:10+07:00 INFO server.go:617  FCC事件源添加成功
2025-08-17T17:06:10+07:00 INFO server.go:620  添加BOS事件源...
2025/08/17 17:06:10 BOS事件源已启动: bos-main
2025/08/17 17:06:10 事件源已添加: bos-main
2025-08-17T17:06:10+07:00 INFO server.go:626  BOS事件源添加成功
2025/08/17 17:06:10 WebSocket Hub started
2025/08/17 17:06:10 Event Manager started
2025/08/17 17:06:10 WebSocket服务已启动
2025-08-17T17:06:10+07:00 INFO server.go:632  WebSocket服务启动成功
2025/08/17 17:06:10 仪表板更新调度器已启动，间隔: 30s
2025-08-17T17:06:10+07:00 INFO server.go:711  注册API路由...
2025-08-17T17:06:10+07:00 INFO server.go:715  注册WebSocket路由...
2025-08-17T17:06:10+07:00 INFO server.go:729  初始化认证模块...
2025-08-17T17:06:10+07:00 INFO server.go:738  认证模块数据库连接成功
2025-08-17T17:06:10+07:00 INFO server.go:763  认证模块集成完成
{"level":"info","ts":1755425170.8113337,"caller":"server/server.go:925","msg":"认证中间件设置完成 - BOS Core路由已受保护"}
2025/08/17 17:06:10 启动服务器，监听端口: 8090
2025-08-17T17:06:10+07:00 INFO server.go:894  启动服务器，监听地址: :8090

   ____    __
  / __/___/ /  ___
 / _// __/ _ \/ _ \
/___/\__/_//_/\___/ v4.13.3
High performance, minimalist Go web framework
https://echo.labstack.com
____________________________________O/_______
                                    O\
2025/08/17 17:06:10 服务器关闭: listen tcp :8090: bind: address already in use
2025/08/17 17:06:12 INFO: [HubBroadcast] 开始广播消息: 类型=device_status_update, 来源=fcc, 总客户端数=0
执行单站点Dashboard汇总查询，站点ID: 1，日期: 2025-08-17
2025/08/17 17:06:40 发送仪表板更新WebSocket消息: 站点=1, 收入=0.00, 交易数=0, 销量=0.00
2025/08/17 17:11:11 仪表板更新调度器已停止
