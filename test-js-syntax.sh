#!/bin/bash

echo "=== 检查JavaScript文件语法 ==="

echo "检查 websocket-client.js..."
if node -c web/websocket-client.js; then
    echo "✓ websocket-client.js 语法正确"
else
    echo "✗ websocket-client.js 语法错误"
    exit 1
fi

echo "检查 websocket-integration.js..."
if node -c web/websocket-integration.js; then
    echo "✓ websocket-integration.js 语法正确"
else
    echo "✗ websocket-integration.js 语法错误"
    exit 1
fi

echo "=== 所有JavaScript文件语法检查通过 ==="

echo "=== 检查文件大小 ==="
ls -la web/*.js

echo "=== 检查文件内容 ==="
echo "websocket-client.js 前10行:"
head -10 web/websocket-client.js

echo ""
echo "websocket-integration.js 前10行:"
head -10 web/websocket-integration.js

echo ""
echo "=== 检查类定义 ==="
echo "BOSWebSocketClient 类定义:"
grep -n "class BOSWebSocketClient" web/websocket-client.js

echo "BOSRealtimeManager 类定义:"
grep -n "class BOSRealtimeManager" web/websocket-integration.js

echo "=== 检查完成 ==="
