<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 15px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }
        .log-sent { color: #007bff; }
        .log-received { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #6c757d; }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            font-family: monospace;
        }
        .stats {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .stats div {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 WebSocket调试工具</h1>
    
    <div class="container">
        <div class="panel">
            <h3>🔌 连接控制</h3>
            <div id="connection-status" class="status disconnected">未连接</div>
            
            <div>
                <label>WebSocket URL:</label>
                <input type="text" id="ws-url" value="">
            </div>
            
            <div>
                <button class="btn-primary" onclick="connect()">连接</button>
                <button class="btn-secondary" onclick="disconnect()">断开</button>
                <button class="btn-danger" onclick="clearLog()">清空日志</button>
            </div>
            
            <div class="stats">
                <h4>📊 连接统计</h4>
                <div>发送消息: <span id="sent-count">0</span></div>
                <div>接收消息: <span id="received-count">0</span></div>
                <div>连接时间: <span id="connection-time">-</span></div>
                <div>最后活动: <span id="last-activity">-</span></div>
            </div>
            
            <h4>🚀 快速测试</h4>
            <button class="btn-success" onclick="sendAuth()">认证</button>
            <button class="btn-success" onclick="sendSubscribe()">订阅</button>
            <button class="btn-success" onclick="sendHeartbeat()">心跳</button>
            <button class="btn-success" onclick="sendPing()">Ping</button>
        </div>
        
        <div class="panel">
            <h3>📝 自定义消息</h3>
            <div>
                <label>消息类型:</label>
                <input type="text" id="message-type" value="test" placeholder="auth, subscribe, test等">
            </div>
            
            <div>
                <label>消息载荷 (JSON):</label>
                <textarea id="message-payload" placeholder='{"key": "value"}'></textarea>
            </div>
            
            <button class="btn-primary" onclick="sendCustomMessage()">发送自定义消息</button>
            
            <h4>📋 预设消息</h4>
            <button class="btn-secondary" onclick="loadAuthMessage()">认证消息</button>
            <button class="btn-secondary" onclick="loadSubscribeMessage()">订阅消息</button>
            <button class="btn-secondary" onclick="loadTestMessage()">测试消息</button>
            
            <h4>🧪 服务器测试</h4>
            <button class="btn-success" onclick="testBroadcast()">测试广播</button>
            <button class="btn-success" onclick="getStats()">获取统计</button>
            <button class="btn-success" onclick="getClients()">获取客户端</button>
        </div>
    </div>
    
    <div class="panel" style="margin-top: 20px;">
        <h3>📋 消息日志</h3>
        <div id="message-log" class="log"></div>
    </div>
    
    <script>
        let ws = null;
        let sentCount = 0;
        let receivedCount = 0;
        let connectionTime = null;
        
        function log(message, type = 'info') {
            const logContainer = document.getElementById('message-log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            
            // 格式化消息
            let formattedMessage = message;
            if (typeof message === 'object') {
                formattedMessage = JSON.stringify(message, null, 2);
            }
            
            entry.innerHTML = `<strong>[${timestamp}]</strong> ${formattedMessage}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 更新最后活动时间
            document.getElementById('last-activity').textContent = timestamp;
        }
        
        function updateStatus(connected) {
            const statusElement = document.getElementById('connection-status');
            if (connected) {
                statusElement.textContent = '✅ 已连接';
                statusElement.className = 'status connected';
                connectionTime = new Date();
                document.getElementById('connection-time').textContent = connectionTime.toLocaleTimeString();
            } else {
                statusElement.textContent = '❌ 未连接';
                statusElement.className = 'status disconnected';
                connectionTime = null;
                document.getElementById('connection-time').textContent = '-';
            }
        }
        
        function updateStats() {
            document.getElementById('sent-count').textContent = sentCount;
            document.getElementById('received-count').textContent = receivedCount;
        }
        
        function connect() {
            const url = document.getElementById('ws-url').value;
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('⚠️ WebSocket已连接', 'info');
                return;
            }
            
            log(`🔌 正在连接到: ${url}`, 'info');
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function(event) {
                    log('✅ WebSocket连接成功', 'info');
                    updateStatus(true);
                };
                
                ws.onmessage = function(event) {
                    receivedCount++;
                    updateStats();
                    
                    log(`📥 收到消息 #${receivedCount}: ${event.data}`, 'received');
                    
                    try {
                        const message = JSON.parse(event.data);
                        log(`📋 解析成功: 类型=${message.type}, 来源=${message.source || 'unknown'}`, 'received');
                        
                        // 特殊处理某些消息类型
                        if (message.type === 'auth' && message.payload && message.payload.success) {
                            log('🔐 认证成功！', 'info');
                        }
                        if (message.type === 'error') {
                            log(`❌ 服务器错误: ${message.payload.message}`, 'error');
                        }
                    } catch (e) {
                        log(`❌ 消息解析失败: ${e.message}`, 'error');
                    }
                };
                
                ws.onclose = function(event) {
                    log(`🔌 WebSocket连接关闭: 代码=${event.code}, 原因=${event.reason || '无'}`, 'info');
                    updateStatus(false);
                    ws = null;
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket错误: ${error}`, 'error');
                    updateStatus(false);
                };
                
            } catch (error) {
                log(`❌ 连接失败: ${error.message}`, 'error');
            }
        }
        
        function disconnect() {
            if (ws) {
                ws.close(1000, '用户主动断开');
                ws = null;
                log('🔌 主动断开连接', 'info');
                updateStatus(false);
            }
        }
        
        function sendMessage(message) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket未连接，无法发送消息', 'error');
                return false;
            }
            
            const messageStr = JSON.stringify(message);
            ws.send(messageStr);
            sentCount++;
            updateStats();
            
            log(`📤 发送消息 #${sentCount}: ${messageStr}`, 'sent');
            return true;
        }
        
        function sendAuth() {
            const message = {
                type: 'auth',
                payload: {
                    token: 'debug-token-' + Date.now()
                },
                timestamp: new Date().toISOString()
            };
            sendMessage(message);
        }
        
        function sendSubscribe() {
            const message = {
                type: 'subscribe',
                payload: {
                    token: 'debug-token',
                    filters: {
                        station_id: 1,
                        topics: [
                            'nozzle_status_update',
                            'transaction_update',
                            'shift_status_update',
                            'dashboard_update'
                        ]
                    }
                },
                timestamp: new Date().toISOString()
            };
            sendMessage(message);
        }
        
        function sendHeartbeat() {
            const message = {
                type: 'heartbeat',
                payload: {
                    timestamp: new Date().toISOString(),
                    client_id: 'debug-client'
                },
                timestamp: new Date().toISOString()
            };
            sendMessage(message);
        }
        
        function sendPing() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.ping();
                log('📡 发送Ping帧', 'sent');
            }
        }
        
        function sendCustomMessage() {
            const type = document.getElementById('message-type').value;
            const payloadStr = document.getElementById('message-payload').value;
            
            if (!type) {
                log('❌ 请输入消息类型', 'error');
                return;
            }
            
            let payload = {};
            if (payloadStr.trim()) {
                try {
                    payload = JSON.parse(payloadStr);
                } catch (e) {
                    log(`❌ 载荷JSON解析失败: ${e.message}`, 'error');
                    return;
                }
            }
            
            const message = {
                type: type,
                payload: payload,
                timestamp: new Date().toISOString()
            };
            
            sendMessage(message);
        }
        
        // 预设消息加载函数
        function loadAuthMessage() {
            document.getElementById('message-type').value = 'auth';
            document.getElementById('message-payload').value = JSON.stringify({
                token: 'debug-token-' + Date.now()
            }, null, 2);
        }
        
        function loadSubscribeMessage() {
            document.getElementById('message-type').value = 'subscribe';
            document.getElementById('message-payload').value = JSON.stringify({
                token: 'debug-token',
                filters: {
                    station_id: 1,
                    topics: ['nozzle_status_update', 'transaction_update', 'dashboard_update']
                }
            }, null, 2);
        }
        
        function loadTestMessage() {
            document.getElementById('message-type').value = 'test';
            document.getElementById('message-payload').value = JSON.stringify({
                message: 'Hello from debug client!',
                timestamp: new Date().toISOString(),
                random: Math.random()
            }, null, 2);
        }
        
        // 服务器测试函数
        async function testBroadcast() {
            try {
                const response = await fetch('/api/v1/ws/broadcast', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        type: 'test_broadcast',
                        payload: {
                            message: '这是一条测试广播消息',
                            timestamp: new Date().toISOString()
                        }
                    })
                });
                const data = await response.json();
                log(`📡 广播测试响应: ${JSON.stringify(data)}`, 'info');
            } catch (error) {
                log(`❌ 广播测试失败: ${error.message}`, 'error');
            }
        }
        
        async function getStats() {
            try {
                const response = await fetch('/api/v1/ws/stats');
                const data = await response.json();
                log(`📊 服务器统计: ${JSON.stringify(data, null, 2)}`, 'info');
            } catch (error) {
                log(`❌ 获取统计失败: ${error.message}`, 'error');
            }
        }
        
        async function getClients() {
            try {
                const response = await fetch('/api/v1/ws/clients');
                const data = await response.json();
                log(`👥 客户端列表: ${JSON.stringify(data, null, 2)}`, 'info');
            } catch (error) {
                log(`❌ 获取客户端列表失败: ${error.message}`, 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('message-log').innerHTML = '';
            sentCount = 0;
            receivedCount = 0;
            updateStats();
            log('🧹 日志已清空', 'info');
        }
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认WebSocket URL
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            const defaultUrl = `${protocol}//${host}/ws`;
            document.getElementById('ws-url').value = defaultUrl;
            
            log('🚀 WebSocket调试工具已加载', 'info');
            log(`🔗 默认WebSocket URL: ${defaultUrl}`, 'info');
            
            updateStats();
        });
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (ws) {
                ws.close();
            }
        });
    </script>
</body>
</html>
