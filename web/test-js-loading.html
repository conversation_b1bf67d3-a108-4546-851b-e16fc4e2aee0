<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>JavaScript文件加载测试</h1>
    
    <div id="loading-status">
        <div class="status info">正在检查JavaScript文件加载状态...</div>
    </div>
    
    <h2>加载状态</h2>
    <div id="status-container"></div>
    
    <h2>错误信息</h2>
    <div id="error-container"></div>
    
    <h2>测试结果</h2>
    <div id="test-results"></div>
    
    <script>
        // 检查加载状态的函数
        function checkLoadingStatus() {
            const statusContainer = document.getElementById('status-container');
            const errorContainer = document.getElementById('error-container');
            const testResults = document.getElementById('test-results');
            
            let status = '';
            let errors = '';
            let results = '';
            
            // 检查BOSWebSocketClient
            if (typeof BOSWebSocketClient !== 'undefined') {
                status += '<div class="status success">✓ BOSWebSocketClient 已加载</div>';
                results += '<p><strong>BOSWebSocketClient:</strong> 可用</p>';
            } else {
                status += '<div class="status error">✗ BOSWebSocketClient 未加载</div>';
                errors += '<p>websocket-client.js 文件加载失败</p>';
            }
            
            // 检查BOSRealtimeManager
            if (typeof BOSRealtimeManager !== 'undefined') {
                status += '<div class="status success">✓ BOSRealtimeManager 已加载</div>';
                results += '<p><strong>BOSRealtimeManager:</strong> 可用</p>';
            } else {
                status += '<div class="status error">✗ BOSRealtimeManager 未加载</div>';
                errors += '<p>websocket-integration.js 文件加载失败</p>';
            }
            
            statusContainer.innerHTML = status;
            errorContainer.innerHTML = errors || '<p class="status success">无错误</p>';
            testResults.innerHTML = results || '<p class="status error">所有测试失败</p>';
        }
        
        // 页面加载完成后检查
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟检查，确保所有脚本都已加载
            setTimeout(checkLoadingStatus, 100);
        });
        
        // 监听脚本加载错误
        window.addEventListener('error', function(e) {
            const errorContainer = document.getElementById('error-container');
            const errorMsg = `<div class="status error">脚本加载错误: ${e.filename} - ${e.message}</div>`;
            errorContainer.innerHTML += errorMsg;
        });
    </script>
    
    <!-- 尝试加载JavaScript文件 -->
    <script src="websocket-client.js" onerror="console.error('websocket-client.js 加载失败')"></script>
    <script src="websocket-integration.js" onerror="console.error('websocket-integration.js 加载失败')"></script>
    
    <script>
        // 最终检查
        setTimeout(() => {
            checkLoadingStatus();
            
            // 显示详细的调试信息
            const debugInfo = document.createElement('div');
            debugInfo.innerHTML = `
                <h2>调试信息</h2>
                <pre>
当前URL: ${window.location.href}
WebSocket URL: ${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws
静态文件基础URL: ${window.location.origin}/static/

尝试的文件路径:
- ${window.location.origin}/static/websocket-client.js
- ${window.location.origin}/static/websocket-integration.js

浏览器信息:
- User Agent: ${navigator.userAgent}
- 支持WebSocket: ${typeof WebSocket !== 'undefined' ? '是' : '否'}
                </pre>
            `;
            document.body.appendChild(debugInfo);
        }, 500);
    </script>
</body>
</html>
