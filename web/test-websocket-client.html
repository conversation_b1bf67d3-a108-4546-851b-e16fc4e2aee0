<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 测试客户端</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-success { background-color: #28a745; color: white; }
    </style>
</head>
<body>
    <h1>WebSocket 测试客户端</h1>

    <div style="background-color: #e9ecef; padding: 10px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px;">
        <strong>服务器信息：</strong><br>
        当前页面：<span id="current-url"></span><br>
        WebSocket 地址：<span id="websocket-url"></span><br>
        局域网访问：将 localhost 替换为服务器 IP 地址
    </div>

    <div id="status" class="status disconnected">未连接</div>
    
    <div>
        <button class="btn-primary" onclick="connect()">连接</button>
        <button class="btn-danger" onclick="disconnect()">断开</button>
        <button class="btn-success" onclick="sendAuth()">发送认证</button>
        <button class="btn-success" onclick="sendSubscribe()">订阅消息</button>
        <button onclick="clearLog()">清空日志</button>
        <button onclick="testConnection()">测试连接</button>
        <label><input type="checkbox" id="autoReconnect" checked> 自动重连</label>
    </div>
    
    <div style="display: flex; gap: 20px;">
        <div style="flex: 1;">
            <h3>消息统计</h3>
            <div id="stats" style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 14px;">
                等待连接...
            </div>
        </div>
        <div style="flex: 1;">
            <h3>实时状态</h3>
            <div id="realtime" style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 14px;">
                <div>连接状态: <span id="conn-status">未连接</span></div>
                <div>运行时间: <span id="uptime">0s</span></div>
                <div>消息频率: <span id="msg-rate">0 msg/s</span></div>
                <div>最后消息: <span id="last-msg">无</span></div>
            </div>
        </div>
    </div>

    <h3>消息日志</h3>
    <div id="log" class="log"></div>
    
    <script>
        let ws = null;
        let messageCount = 0;
        let startTime = null;
        let messageStats = {
            heartbeat: 0,
            device_status_update: 0,
            nozzle_status_update: 0,
            dashboard_update: 0,
            transaction_update: 0,
            other: 0
        };

        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(connected) {
            const statusDiv = document.getElementById('status');
            if (connected) {
                statusDiv.className = 'status connected';
                statusDiv.textContent = '已连接';
            } else {
                statusDiv.className = 'status disconnected';
                statusDiv.textContent = '未连接';
            }
        }

        function updateStats() {
            const total = Object.values(messageStats).reduce((a, b) => a + b, 0);
            const statsHtml = `
                <strong>消息统计 (总计: ${total})</strong><br>
                💓 心跳: ${messageStats.heartbeat}<br>
                🔧 设备状态: ${messageStats.device_status_update}<br>
                ⛽ 喷嘴状态: ${messageStats.nozzle_status_update}<br>
                📊 仪表板: ${messageStats.dashboard_update}<br>
                💰 交易更新: ${messageStats.transaction_update}<br>
                ❓ 其他: ${messageStats.other}
            `;
            document.getElementById('stats').innerHTML = statsHtml;
        }

        function connect() {
            if (ws) {
                log('WebSocket 已连接');
                return;
            }

            // 自动检测服务器地址，支持局域网访问
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host || 'localhost:8090';
            const wsUrl = `${protocol}//${host}/ws`;
            log(`🚀 正在连接到: ${wsUrl}`);
            log(`📍 检测到的服务器地址: ${host}`);
            log(`🔗 使用协议: ${protocol}`);

            ws = new WebSocket(wsUrl);
            startTime = Date.now();
            messageCount = 0;
            messageStats = {
                heartbeat: 0,
                device_status_update: 0,
                nozzle_status_update: 0,
                dashboard_update: 0,
                transaction_update: 0,
                other: 0
            };

            ws.onopen = function(event) {
                log('✅ WebSocket 连接成功');
                updateStatus(true);
            };

            ws.onmessage = function(event) {
                messageCount++;
                const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);

                try {
                    const data = JSON.parse(event.data);
                    const msgType = data.type || 'unknown';

                    // 更新统计
                    if (messageStats.hasOwnProperty(msgType)) {
                        messageStats[msgType]++;
                    } else {
                        messageStats.other++;
                    }
                    updateStats();

                    // 更新最后消息时间
                    document.getElementById('last-msg').textContent = new Date().toLocaleTimeString();

                    // 根据消息类型显示不同的信息
                    if (msgType === 'heartbeat') {
                        log(`💓 心跳消息 #${messageCount} (${elapsed}s)`);
                    } else if (msgType === 'device_status_update') {
                        log(`🔧 设备状态更新 #${messageCount} (${elapsed}s):`);
                        log(`   设备ID: ${data.payload?.device_id || 'N/A'}`);
                        log(`   状态: ${data.payload?.status || 'N/A'}`);
                        log(`   来源: ${data.source || 'N/A'}`);
                    } else if (msgType === 'nozzle_status_update') {
                        log(`⛽ 喷嘴状态更新 #${messageCount} (${elapsed}s):`);
                        log(`   喷嘴ID: ${data.payload?.nozzle_id || 'N/A'}`);
                        log(`   状态: ${data.payload?.status || 'N/A'}`);
                        log(`   当前油量: ${data.payload?.current_volume || 0}L`);
                        log(`   当前金额: ${data.payload?.current_amount || 0}元`);
                    } else if (msgType === 'dashboard_update') {
                        log(`📊 仪表板更新 #${messageCount} (${elapsed}s):`);
                        log(`   站点ID: ${data.payload?.station_id || 'N/A'}`);
                        log(`   今日收入: ${data.payload?.today_revenue || 0}元`);
                        log(`   今日交易数: ${data.payload?.today_transactions || 0}`);
                        log(`   今日销量: ${data.payload?.today_volume || 0}L`);
                    } else if (msgType === 'transaction_update') {
                        log(`💰 交易更新 #${messageCount} (${elapsed}s):`);
                        log(`   交易ID: ${data.payload?.transaction_id || 'N/A'}`);
                        log(`   状态: ${data.payload?.status || 'N/A'}`);
                        log(`   金额: ${data.payload?.amount || 0}元`);
                    } else {
                        log(`📨 收到消息 #${messageCount} (${elapsed}s) [${msgType}]:`);
                        log(`   ${JSON.stringify(data, null, 2)}`);
                    }
                } catch (e) {
                    log(`❌ 解析消息失败 #${messageCount}: ${event.data}`);
                }
            };

            ws.onclose = function(event) {
                log(`🔌 WebSocket 连接关闭: code=${event.code}, reason=${event.reason}`);
                updateStatus(false);
                ws = null;

                // 如果不是正常关闭，尝试自动重连
                if (event.code !== 1000) {
                    attemptReconnect();
                }
            };

            ws.onerror = function(error) {
                log(`❌ WebSocket 错误: ${error}`);
                updateStatus(false);
            };
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
                log('🔌 主动断开连接');
                updateStatus(false);
            }
        }
        
        function sendAuth() {
            if (!ws) {
                log('❌ 请先连接 WebSocket');
                return;
            }
            
            const authMessage = {
                type: 'auth',
                payload: {
                    token: 'test-token-123',
                    user_id: 'test-user',
                    station_id: 1001
                }
            };
            
            ws.send(JSON.stringify(authMessage));
            log(`📤 发送认证消息: ${JSON.stringify(authMessage)}`);
        }
        
        function sendSubscribe() {
            if (!ws) {
                log('❌ 请先连接 WebSocket');
                return;
            }
            
            const subscribeMessage = {
                type: 'subscribe',
                payload: {
                    topics: ['nozzle_status', 'transaction_update', 'dashboard_update'],
                    station_id: 1001
                }
            };
            
            ws.send(JSON.stringify(subscribeMessage));
            log(`📤 发送订阅消息: ${JSON.stringify(subscribeMessage)}`);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            messageCount = 0;
        }

        function testConnection() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host || 'localhost:8090';
            const wsUrl = `${protocol}//${host}/ws`;

            log(`🧪 测试连接到: ${wsUrl}`);

            const testWs = new WebSocket(wsUrl);

            testWs.onopen = function() {
                log(`✅ 测试连接成功！服务器响应正常`);
                testWs.close();
            };

            testWs.onerror = function(error) {
                log(`❌ 测试连接失败: ${error}`);
            };

            testWs.onclose = function(event) {
                log(`🔌 测试连接关闭: code=${event.code}`);
            };
        }

        function attemptReconnect() {
            if (document.getElementById('autoReconnect').checked && !ws) {
                log(`🔄 5秒后尝试自动重连...`);
                setTimeout(() => {
                    if (!ws) {
                        log(`🔄 开始自动重连...`);
                        connect();
                    }
                }, 5000);
            }
        }
        
        function updateRealTimeStatus() {
            if (startTime) {
                const uptime = Math.floor((Date.now() - startTime) / 1000);
                const rate = messageCount > 0 ? (messageCount / uptime).toFixed(2) : '0.00';

                document.getElementById('conn-status').textContent = ws ? '已连接' : '未连接';
                document.getElementById('uptime').textContent = `${uptime}s`;
                document.getElementById('msg-rate').textContent = `${rate} msg/s`;
            }
        }

        function updateServerInfo() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host || 'localhost:8090';
            const wsUrl = `${protocol}//${host}/ws`;

            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('websocket-url').textContent = wsUrl;
        }

        // 页面加载时自动连接
        window.onload = function() {
            log('🚀 页面加载完成，准备测试 WebSocket');

            // 更新服务器信息显示
            updateServerInfo();

            // 每秒更新实时状态
            setInterval(updateRealTimeStatus, 1000);
        };
    </script>
</body>
</html>
