<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 测试页面访问指南</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 25px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .url-box {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
            word-break: break-all;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            color: #0c5460;
            background-color: #d1ecf1;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .link-button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        .link-button:hover {
            background-color: #0056b3;
            color: white;
            text-decoration: none;
        }
        .debug-button {
            background-color: #28a745;
        }
        .debug-button:hover {
            background-color: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 WebSocket 测试页面访问指南</h1>
        
        <div class="section">
            <h3>📍 服务器信息</h3>
            <div class="info">
                <strong>服务器 IP 地址：</strong> <span class="highlight">*************</span><br>
                <strong>服务端口：</strong> <span class="highlight">8090</span><br>
                <strong>WebSocket 端点：</strong> <span class="highlight">/ws</span>
            </div>
        </div>

        <div class="section">
            <h3>🌐 本机访问（服务器本地）</h3>
            <p>在服务器本机上访问：</p>
            
            <h4>🎨 生产级演示页面</h4>
            <div class="url-box">http://localhost:8090/static/websocket-demo.html</div>
            <a href="http://localhost:8090/static/websocket-demo.html" class="link-button" target="_blank">打开演示页面</a>
            
            <h4>🛠️ 调试测试页面</h4>
            <div class="url-box">http://localhost:8090/static/test-websocket-client.html</div>
            <a href="http://localhost:8090/static/test-websocket-client.html" class="link-button debug-button" target="_blank">打开调试页面</a>
        </div>

        <div class="section">
            <h3>📱 局域网访问（其他设备）</h3>
            <p>在同一局域网的其他设备（手机、平板、其他电脑）上访问：</p>
            
            <h4>🎨 生产级演示页面</h4>
            <div class="url-box">http://*************:8090/static/websocket-demo.html</div>
            
            <h4>🛠️ 调试测试页面</h4>
            <div class="url-box">http://*************:8090/static/test-websocket-client.html</div>
            
            <div class="success">
                ✅ 页面会自动检测服务器地址，无需手动配置 WebSocket 连接！
            </div>
        </div>

        <div class="section">
            <h3>🔧 页面功能对比</h3>
            
            <h4>🎨 websocket-demo.html（演示页面）</h4>
            <ul>
                <li>专业的用户界面设计</li>
                <li>实时设备状态卡片</li>
                <li>喷嘴状态监控</li>
                <li>交易数据展示</li>
                <li>仪表板数据</li>
                <li>适合演示和生产环境</li>
            </ul>
            
            <h4>🛠️ test-websocket-client.html（调试页面）</h4>
            <ul>
                <li>详细的消息日志</li>
                <li>原始数据查看</li>
                <li>消息统计计数</li>
                <li>连接状态监控</li>
                <li>手动测试功能</li>
                <li>适合开发和调试</li>
            </ul>
        </div>

        <div class="section">
            <h3>📊 预期数据类型</h3>
            <ul>
                <li>💓 <strong>心跳消息</strong>：每 30 秒</li>
                <li>🔧 <strong>设备状态更新</strong>：每 2 秒</li>
                <li>⛽ <strong>喷嘴状态更新</strong>：每 2 秒</li>
                <li>📊 <strong>仪表板更新</strong>：每 30 秒</li>
                <li>💰 <strong>交易更新</strong>：实时触发</li>
            </ul>
        </div>

        <div class="section">
            <h3>🚨 故障排除</h3>
            <ul>
                <li><strong>无法连接：</strong>检查服务器是否运行在 8090 端口</li>
                <li><strong>局域网无法访问：</strong>确认防火墙设置允许 8090 端口</li>
                <li><strong>WebSocket 连接失败：</strong>检查浏览器控制台错误信息</li>
                <li><strong>没有数据：</strong>确认事件源已正确启动</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <p style="color: #6c757d; font-size: 14px;">
                🎯 选择适合你需求的页面开始测试 WebSocket 实时数据推送！
            </p>
        </div>
    </div>
</body>
</html>
