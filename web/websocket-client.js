/**
 * BOS WebSocket客户端
 * 提供实时数据推送功能，替代传统的轮询方式
 */

class BOSWebSocketClient {
    constructor(options = {}) {
        this.url = options.url || this.getWebSocketURL();
        this.token = options.token || this.getAuthToken();
        this.reconnectInterval = options.reconnectInterval || 5000;
        this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
        this.heartbeatInterval = options.heartbeatInterval || 30000;
        
        // 连接状态
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.reconnectTimer = null;
        this.heartbeatTimer = null;
        this.lastHeartbeat = null;
        
        // 事件监听器
        this.eventListeners = new Map();
        this.subscriptions = new Set();
        this.filters = null;
        
        // 消息队列
        this.messageQueue = [];
        this.sequenceNumber = 0;
        
        // 调试模式
        this.debug = options.debug || false;
        
        this.log('WebSocket客户端初始化完成');
    }
    
    /**
     * 连接WebSocket服务器
     */
    connect() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.log('WebSocket已连接');
            return Promise.resolve();
        }
        
        return new Promise((resolve, reject) => {
            try {
                this.log(`连接WebSocket服务器: ${this.url}`);
                this.ws = new WebSocket(this.url);
                
                this.ws.onopen = (event) => {
                    this.log('WebSocket连接已建立');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    
                    // 发送认证消息
                    this.authenticate().then(() => {
                        // 恢复订阅
                        this.restoreSubscriptions();
                        
                        // 启动心跳
                        this.startHeartbeat();
                        
                        // 处理消息队列
                        this.processMessageQueue();
                        
                        this.emit('connected', event);
                        resolve();
                    }).catch(reject);
                };
                
                this.ws.onmessage = (event) => {
                    this.handleMessage(event);
                };
                
                this.ws.onclose = (event) => {
                    this.log(`WebSocket连接关闭: ${event.code} - ${event.reason}`);
                    this.isConnected = false;
                    this.stopHeartbeat();
                    this.emit('disconnected', event);
                    
                    // 自动重连
                    if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
                        this.scheduleReconnect();
                    }
                };
                
                this.ws.onerror = (event) => {
                    this.log('WebSocket连接错误', event);
                    this.emit('error', event);
                    reject(new Error('WebSocket连接失败'));
                };
                
            } catch (error) {
                this.log('创建WebSocket连接失败', error);
                reject(error);
            }
        });
    }
    
    /**
     * 断开WebSocket连接
     */
    disconnect() {
        this.log('主动断开WebSocket连接');
        
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        this.stopHeartbeat();
        
        if (this.ws) {
            this.ws.close(1000, '客户端主动断开');
            this.ws = null;
        }
        
        this.isConnected = false;
        this.reconnectAttempts = this.maxReconnectAttempts; // 阻止自动重连
    }
    
    /**
     * 发送认证消息
     */
    authenticate() {
        return new Promise((resolve, reject) => {
            const authMessage = {
                type: 'auth',
                payload: {
                    token: this.token
                },
                seq: ++this.sequenceNumber,
                timestamp: new Date().toISOString()
            };
            
            // 监听认证响应
            const authHandler = (data) => {
                if (data.type === 'auth') {
                    this.off('message', authHandler);
                    if (data.payload && data.payload.success) {
                        this.log('WebSocket认证成功');
                        resolve(data.payload);
                    } else {
                        this.log('WebSocket认证失败', data.payload);
                        reject(new Error('认证失败'));
                    }
                }
            };
            
            this.on('message', authHandler);
            this.sendMessage(authMessage);
            
            // 认证超时
            setTimeout(() => {
                this.off('message', authHandler);
                reject(new Error('认证超时'));
            }, 10000);
        });
    }
    
    /**
     * 订阅消息类型
     */
    subscribe(topics, filters = null) {
        if (typeof topics === 'string') {
            topics = [topics];
        }
        
        topics.forEach(topic => this.subscriptions.add(topic));
        this.filters = filters;
        
        if (this.isConnected) {
            const subscribeMessage = {
                type: 'subscribe',
                payload: {
                    token: this.token,
                    filters: {
                        topics: Array.from(this.subscriptions),
                        ...filters
                    }
                },
                seq: ++this.sequenceNumber,
                timestamp: new Date().toISOString()
            };
            
            this.sendMessage(subscribeMessage);
        }
        
        this.log(`订阅消息类型: ${topics.join(', ')}`);
    }
    
    /**
     * 取消订阅
     */
    unsubscribe(topics) {
        if (typeof topics === 'string') {
            topics = [topics];
        }
        
        topics.forEach(topic => this.subscriptions.delete(topic));
        
        if (this.isConnected) {
            const unsubscribeMessage = {
                type: 'unsubscribe',
                payload: topics,
                seq: ++this.sequenceNumber,
                timestamp: new Date().toISOString()
            };
            
            this.sendMessage(unsubscribeMessage);
        }
        
        this.log(`取消订阅消息类型: ${topics.join(', ')}`);
    }
    
    /**
     * 发送消息
     */
    sendMessage(message) {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            const messageStr = JSON.stringify(message);
            this.ws.send(messageStr);
            this.log('发送消息', message);
        } else {
            // 连接未建立时，将消息加入队列
            this.messageQueue.push(message);
            this.log('消息已加入队列', message);
        }
    }
    
    /**
     * 处理接收到的消息
     */
    handleMessage(event) {
        try {
            const messages = event.data.split('\n').filter(line => line.trim());
            
            messages.forEach(messageStr => {
                const message = JSON.parse(messageStr);
                this.log('接收消息', message);
                
                // 更新心跳时间
                if (message.type === 'heartbeat') {
                    this.lastHeartbeat = new Date();
                    return;
                }
                
                // 触发消息事件
                this.emit('message', message);
                this.emit(message.type, message.payload, message);
            });
            
        } catch (error) {
            this.log('解析消息失败', error, event.data);
        }
    }
    
    /**
     * 启动心跳
     */
    startHeartbeat() {
        this.stopHeartbeat();
        this.lastHeartbeat = new Date();
        
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected) {
                const heartbeatMessage = {
                    type: 'heartbeat',
                    payload: {
                        timestamp: new Date().toISOString(),
                        client_id: this.clientId
                    },
                    seq: ++this.sequenceNumber,
                    timestamp: new Date().toISOString()
                };
                
                this.sendMessage(heartbeatMessage);
                
                // 检查心跳超时
                if (this.lastHeartbeat && (new Date() - this.lastHeartbeat) > this.heartbeatInterval * 2) {
                    this.log('心跳超时，重新连接');
                    this.reconnect();
                }
            }
        }, this.heartbeatInterval);
    }
    
    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }
    
    /**
     * 安排重连
     */
    scheduleReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }
        
        this.reconnectAttempts++;
        const delay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1), 30000);
        
        this.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连`);
        
        this.reconnectTimer = setTimeout(() => {
            this.connect().catch(error => {
                this.log('重连失败', error);
            });
        }, delay);
    }
    
    /**
     * 立即重连
     */
    reconnect() {
        this.disconnect();
        this.reconnectAttempts = 0;
        return this.connect();
    }
    
    /**
     * 恢复订阅
     */
    restoreSubscriptions() {
        if (this.subscriptions.size > 0) {
            this.subscribe(Array.from(this.subscriptions), this.filters);
        }
    }
    
    /**
     * 处理消息队列
     */
    processMessageQueue() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.sendMessage(message);
        }
    }
    
    /**
     * 事件监听
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }
    
    /**
     * 移除事件监听
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }
    
    /**
     * 触发事件
     */
    emit(event, ...args) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(...args);
                } catch (error) {
                    this.log(`事件处理器错误 [${event}]`, error);
                }
            });
        }
    }
    
    /**
     * 获取WebSocket URL
     */
    getWebSocketURL() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        return `${protocol}//${host}/ws`;
    }
    
    /**
     * 获取认证Token
     */
    getAuthToken() {
        // 从localStorage或其他地方获取token
        return localStorage.getItem('auth_token') || 'default-token';
    }
    
    /**
     * 日志输出
     */
    log(message, ...args) {
        if (this.debug) {
            console.log(`[BOSWebSocket] ${message}`, ...args);
        }
    }
    
    /**
     * 获取连接状态
     */
    getStatus() {
        return {
            connected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            subscriptions: Array.from(this.subscriptions),
            lastHeartbeat: this.lastHeartbeat,
            messageQueueLength: this.messageQueue.length
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BOSWebSocketClient;
} else {
    window.BOSWebSocketClient = BOSWebSocketClient;
}
