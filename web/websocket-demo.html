<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BOS WebSocket 实时数据演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #dc3545;
        }
        
        .status-indicator.connected {
            background-color: #28a745;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #495057;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            font-weight: 600;
            font-size: 16px;
        }
        
        .trend {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .trend-up {
            background-color: #d4edda;
            color: #155724;
        }
        
        .trend-down {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .trend-neutral {
            background-color: #e2e3e5;
            color: #383d41;
        }
        
        .nozzle-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .nozzle-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .nozzle-item.status-idle {
            border-color: #6c757d;
        }
        
        .nozzle-item.status-dispensing {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        
        .nozzle-item.status-error {
            border-color: #dc3545;
            background-color: #fff5f5;
        }
        
        .nozzle-id {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        .nozzle-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .transaction-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .transaction-item {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
            gap: 10px;
            padding: 10px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        
        .transaction-item:hover {
            background-color: #f8f9fa;
        }
        
        .transaction-header {
            font-weight: 600;
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }
        
        .fuel-mix-item {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        
        .fuel-mix-item:last-child {
            border-bottom: none;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .price-updated {
            animation: priceUpdate 2s ease-in-out;
        }
        
        @keyframes priceUpdate {
            0% { background-color: #fff3cd; }
            50% { background-color: #ffeaa7; }
            100% { background-color: transparent; }
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-timestamp {
            color: #a0aec0;
        }
        
        .log-level-info {
            color: #63b3ed;
        }
        
        .log-level-warn {
            color: #f6e05e;
        }
        
        .log-level-error {
            color: #fc8181;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>BOS WebSocket 实时数据演示</h1>
            <p>实时监控加油站数据，替代传统轮询方式</p>
        </div>
        
        <div class="status-bar">
            <div class="connection-status">
                <div class="status-indicator" id="connection-indicator"></div>
                <span id="connection-status">未连接</span>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="connectWebSocket()">连接</button>
                <button class="btn btn-secondary" onclick="disconnectWebSocket()">断开</button>
                <button class="btn btn-danger" onclick="clearLogs()">清空日志</button>
                <button class="btn btn-primary" onclick="triggerDashboardUpdate()">触发仪表板更新</button>
                <button class="btn btn-primary" onclick="triggerTransactionUpdate()">触发交易更新</button>
            </div>
        </div>
        
        <div class="dashboard">
            <!-- 仪表板数据 -->
            <div class="card">
                <div class="card-title">今日数据</div>
                <div class="metric">
                    <span>总收入</span>
                    <span class="metric-value" id="today-revenue">Rp 0</span>
                    <span class="trend" id="revenue-trend">0%</span>
                </div>
                <div class="metric">
                    <span>交易笔数</span>
                    <span class="metric-value" id="today-transactions">0</span>
                    <span class="trend" id="transaction-trend">0%</span>
                </div>
                <div class="metric">
                    <span>销售量</span>
                    <span class="metric-value" id="today-volume">0 L</span>
                    <span class="trend" id="volume-trend">0%</span>
                </div>
            </div>
            
            <!-- 当前班次 -->
            <div class="card">
                <div class="card-title">当前班次</div>
                <div id="current-shift">
                    <div class="metric">
                        <span>班次编号</span>
                        <span class="metric-value shift-number">-</span>
                    </div>
                    <div class="metric">
                        <span>状态</span>
                        <span class="metric-value shift-status">-</span>
                    </div>
                    <div class="metric">
                        <span>操作员</span>
                        <span class="metric-value attendant-name">-</span>
                    </div>
                    <div class="metric">
                        <span>开始时间</span>
                        <span class="metric-value start-time">-</span>
                    </div>
                </div>
            </div>
            
            <!-- 燃油销售组合 -->
            <div class="card">
                <div class="card-title">燃油销售组合</div>
                <div id="fuel-sales-mix">
                    <div class="fuel-mix-item">
                        <span>油品</span>
                        <span>销量</span>
                        <span>收入</span>
                        <span>占比</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 喷嘴状态 -->
        <div class="card">
            <div class="card-title">喷嘴状态</div>
            <div class="nozzle-grid" id="nozzle-grid">
                <!-- 动态生成喷嘴状态 -->
            </div>
        </div>
        
        <!-- 交易列表 -->
        <div class="card">
            <div class="card-title">最新交易</div>
            <div class="transaction-list">
                <div class="transaction-item transaction-header">
                    <span>交易号</span>
                    <span>喷嘴</span>
                    <span>油品</span>
                    <span>销量</span>
                    <span>金额</span>
                    <span>状态</span>
                    <span>时间</span>
                </div>
                <div id="transaction-list">
                    <!-- 动态生成交易记录 -->
                </div>
            </div>
        </div>
        
        <!-- 实时日志 -->
        <div class="card">
            <div class="card-title">实时日志</div>
            <div class="log-container" id="log-container">
                <!-- 动态生成日志 -->
            </div>
        </div>
    </div>
    
    <script src="/static/websocket-client.js" onload="console.log('websocket-client.js 加载成功')" onerror="console.error('websocket-client.js 加载失败')"></script>
    <script src="/static/websocket-integration-v2.js" onload="console.log('websocket-integration-v2.js 加载成功')" onerror="console.error('websocket-integration-v2.js 加载失败')"></script>
    <script>
        // 等待所有脚本加载完成后检查依赖
        window.addEventListener('load', function() {
            setTimeout(function() {
                // 检查依赖是否加载
                if (typeof BOSWebSocketClient === 'undefined') {
                    console.error('BOSWebSocketClient未定义，请检查websocket-client.js是否正确加载');
                    addLog('error', 'WebSocket客户端库加载失败');
                    return;
                }

                if (typeof BOSRealtimeManager === 'undefined') {
                    console.error('BOSRealtimeManager未定义，请检查websocket-integration.js是否正确加载');
                    addLog('error', 'WebSocket集成库加载失败');
                    return;
                }

                console.log('所有WebSocket库加载成功');
                addLog('info', '所有WebSocket库加载成功');
            }, 100);
        });
    </script>
    <script>
        let realtimeManager = null;
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });
        
        function initializePage() {
            // 生成模拟喷嘴
            generateMockNozzles();
            
            // 添加日志
            addLog('info', '页面初始化完成');
        }
        
        function connectWebSocket() {
            // 检查依赖是否加载
            if (typeof BOSRealtimeManager === 'undefined') {
                addLog('error', 'BOSRealtimeManager未定义，请刷新页面重试');
                alert('WebSocket库未正确加载，请刷新页面重试');
                return;
            }

            if (realtimeManager) {
                addLog('warn', 'WebSocket已连接');
                return;
            }

            addLog('info', '正在连接WebSocket...');

            try {
                realtimeManager = new BOSRealtimeManager({
                stationId: 1,
                debug: true,
                onConnectionChange: (connected) => {
                    updateConnectionStatus(connected);
                    addLog(connected ? 'info' : 'warn', `连接状态: ${connected ? '已连接' : '已断开'}`);
                },
                onNozzleStatusUpdate: (payload) => {
                    addLog('info', `喷嘴 ${payload.nozzle_id} 状态更新: ${payload.status}`);
                },
                onTransactionUpdate: (payload) => {
                    addLog('info', `交易 ${payload.transaction_number} 更新: ${payload.status}`);
                },
                onShiftStatusUpdate: (payload) => {
                    addLog('info', `班次 ${payload.shift_number} 状态更新: ${payload.status}`);
                },
                onDashboardUpdate: (payload) => {
                    addLog('info', `仪表板数据更新: 收入 ${formatCurrency(payload.today_revenue)}`);
                }
            });
            
            realtimeManager.initialize().then(() => {
                addLog('info', 'WebSocket连接成功');
                
                // 模拟数据推送
                setTimeout(() => {
                    simulateDataUpdates();
                }, 2000);
                
            }).catch(error => {
                addLog('error', `WebSocket连接失败: ${error.message}`);
            });

            } catch (error) {
                addLog('error', `创建WebSocket管理器失败: ${error.message}`);
            }
        }
        
        function disconnectWebSocket() {
            if (realtimeManager) {
                realtimeManager.destroy();
                realtimeManager = null;
                updateConnectionStatus(false);
                addLog('info', 'WebSocket已断开');
            }
        }
        
        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('connection-indicator');
            const status = document.getElementById('connection-status');
            
            if (connected) {
                indicator.classList.add('connected');
                status.textContent = '已连接';
            } else {
                indicator.classList.remove('connected');
                status.textContent = '未连接';
            }
        }
        
        function generateMockNozzles() {
            const nozzleGrid = document.getElementById('nozzle-grid');
            
            for (let i = 1; i <= 8; i++) {
                const nozzleElement = document.createElement('div');
                nozzleElement.className = 'nozzle-item status-idle';
                nozzleElement.setAttribute('data-nozzle-id', `nozzle${i}`);
                
                nozzleElement.innerHTML = `
                    <div class="nozzle-id">喷嘴 ${i}</div>
                    <div class="nozzle-status status">空闲</div>
                    <div class="metric">
                        <span>销量</span>
                        <span class="volume">0.00 L</span>
                    </div>
                    <div class="metric">
                        <span>金额</span>
                        <span class="amount">Rp 0</span>
                    </div>
                    <div class="error" style="color: red; font-size: 12px; margin-top: 5px;"></div>
                `;
                
                nozzleGrid.appendChild(nozzleElement);
            }
        }
        
        function simulateDataUpdates() {
            if (!realtimeManager) return;
            
            // 模拟喷嘴状态更新
            const nozzleStatuses = ['idle', 'dispensing', 'error'];
            const randomNozzle = Math.floor(Math.random() * 8) + 1;
            const randomStatus = nozzleStatuses[Math.floor(Math.random() * nozzleStatuses.length)];
            
            realtimeManager.handleNozzleStatusUpdate({
                nozzle_id: `nozzle${randomNozzle}`,
                dispenser_id: `dispenser${Math.ceil(randomNozzle / 2)}`,
                station_id: 1,
                status: randomStatus,
                current_volume: Math.random() * 50,
                current_amount: Math.random() * 500000,
                current_price: 10000 + Math.random() * 2000,
                fuel_grade: 'RON92',
                last_updated: new Date().toISOString()
            });
            
            // 模拟交易更新
            if (Math.random() > 0.7) {
                const transactionId = 'tx_' + Date.now();
                realtimeManager.handleTransactionUpdate({
                    transaction_id: transactionId,
                    transaction_number: `TXN${Date.now().toString().slice(-6)}`,
                    nozzle_id: `nozzle${randomNozzle}`,
                    station_id: 1,
                    status: 'completed',
                    amount: 50000 + Math.random() * 200000,
                    volume: 5 + Math.random() * 20,
                    fuel_type: 'gasoline',
                    fuel_grade: 'RON92',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                });
            }
            
            // 模拟仪表板更新
            if (Math.random() > 0.8) {
                realtimeManager.handleDashboardUpdate({
                    station_id: 1,
                    today_revenue: 5000000 + Math.random() * 2000000,
                    today_transactions: 50 + Math.floor(Math.random() * 100),
                    today_volume: 500 + Math.random() * 300,
                    revenue_change: (Math.random() - 0.5) * 20,
                    transaction_change: (Math.random() - 0.5) * 15,
                    volume_change: (Math.random() - 0.5) * 10,
                    last_updated: new Date().toISOString(),
                    fuel_sales_mix: [
                        { fuel_grade: 'RON92', volume: 300, revenue: 3000000, percentage: 60 },
                        { fuel_grade: 'RON95', volume: 150, revenue: 1800000, percentage: 30 },
                        { fuel_grade: 'Diesel', volume: 100, revenue: 1200000, percentage: 10 }
                    ]
                });
            }
            
            // 继续模拟
            setTimeout(() => {
                simulateDataUpdates();
            }, 2000 + Math.random() * 3000);
        }
        
        function addLog(level, message) {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-level-${level}">[${level.toUpperCase()}]</span>
                ${message}
            `;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 限制日志条数
            while (logContainer.children.length > 100) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }
        
        function clearLogs() {
            document.getElementById('log-container').innerHTML = '';
        }

        // 手动触发仪表板更新
        async function triggerDashboardUpdate() {
            try {
                const response = await fetch('/api/v1/ws/broadcast', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: 'dashboard_update',
                        payload: {
                            station_id: 1,
                            today_revenue: 5000000 + Math.random() * 2000000,
                            today_transactions: 50 + Math.floor(Math.random() * 100),
                            today_volume: 500 + Math.random() * 300,
                            revenue_change: (Math.random() - 0.5) * 20,
                            transaction_change: (Math.random() - 0.5) * 15,
                            volume_change: (Math.random() - 0.5) * 10,
                            last_updated: new Date().toISOString(),
                            fuel_sales_mix: [
                                { fuel_grade: 'RON92', volume: 300, revenue: 3000000, percentage: 60 },
                                { fuel_grade: 'RON95', volume: 150, revenue: 1800000, percentage: 30 },
                                { fuel_grade: 'Diesel', volume: 100, revenue: 1200000, percentage: 10 }
                            ]
                        }
                    })
                });
                const data = await response.json();
                addLog('info', `手动触发仪表板更新: ${JSON.stringify(data)}`);
            } catch (error) {
                addLog('error', `触发仪表板更新失败: ${error.message}`);
            }
        }

        // 手动触发交易更新
        async function triggerTransactionUpdate() {
            try {
                const response = await fetch('/api/v1/ws/broadcast', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: 'transaction_update',
                        payload: {
                            transaction_id: 'tx_' + Date.now(),
                            transaction_number: 'TXN' + Date.now().toString().slice(-6),
                            nozzle_id: 'nozzle' + (Math.floor(Math.random() * 8) + 1),
                            station_id: 1,
                            status: 'completed',
                            amount: 50000 + Math.random() * 200000,
                            volume: 5 + Math.random() * 20,
                            fuel_type: 'gasoline',
                            fuel_grade: 'RON92',
                            created_at: new Date().toISOString(),
                            updated_at: new Date().toISOString()
                        }
                    })
                });
                const data = await response.json();
                addLog('info', `手动触发交易更新: ${JSON.stringify(data)}`);
            } catch (error) {
                addLog('error', `触发交易更新失败: ${error.message}`);
            }
        }
        
        function formatCurrency(amount) {
            return new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0
            }).format(amount);
        }
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (realtimeManager) {
                realtimeManager.destroy();
            }
        });
    </script>
</body>
</html>
