/**
 * BOS前端WebSocket集成示例 - 简化版本
 * 展示如何在现有前端应用中集成WebSocket实时数据推送
 */

class BOSRealtimeManager {
    constructor(options = {}) {
        this.stationId = options.stationId || this.getCurrentStationId();
        this.wsClient = null;
        this.isInitialized = false;
        
        // 数据缓存
        this.cache = {
            nozzleStatus: new Map(),
            transactions: new Map(),
            shiftStatus: null,
            dashboardData: null
        };
        
        // 回调函数
        this.callbacks = {
            onNozzleStatusUpdate: options.onNozzleStatusUpdate || this.defaultNozzleStatusHandler.bind(this),
            onTransactionUpdate: options.onTransactionUpdate || this.defaultTransactionHandler.bind(this),
            onShiftStatusUpdate: options.onShiftStatusUpdate || this.defaultShiftStatusHandler.bind(this),
            onDashboardUpdate: options.onDashboardUpdate || this.defaultDashboardHandler.bind(this),
            onConnectionChange: options.onConnectionChange || this.defaultConnectionHandler.bind(this)
        };
        
        this.debug = options.debug || false;
    }
    
    /**
     * 初始化WebSocket连接
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        
        try {
            // 创建WebSocket客户端
            this.wsClient = new BOSWebSocketClient({
                debug: this.debug,
                reconnectInterval: 3000,
                maxReconnectAttempts: 10
            });
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 连接WebSocket
            await this.wsClient.connect();
            
            // 认证
            await this.authenticate();
            
            // 订阅消息
            await this.subscribe();
            
            this.isInitialized = true;
            this.log('WebSocket实时管理器初始化完成');
            
        } catch (error) {
            this.log('初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 连接状态变化
        this.wsClient.on('connected', () => {
            this.callbacks.onConnectionChange(true);
        });
        
        this.wsClient.on('disconnected', () => {
            this.callbacks.onConnectionChange(false);
        });
        
        // 业务消息处理
        this.wsClient.on('nozzle_status_update', (payload) => {
            this.log('收到喷嘴状态更新:', payload);
            this.handleNozzleStatusUpdate(payload);
        });

        // 批量喷嘴状态更新
        this.wsClient.on('nozzle_status_batch_update', (payload) => {
            this.log('收到批量喷嘴状态更新:', payload);
            this.handleNozzleStatusBatchUpdate(payload);
        });

        this.wsClient.on('transaction_update', (payload) => {
            this.log('收到交易更新:', payload);
            this.handleTransactionUpdate(payload);
        });

        this.wsClient.on('shift_status_update', (payload) => {
            this.log('收到班次状态更新:', payload);
            this.handleShiftStatusUpdate(payload);
        });

        this.wsClient.on('dashboard_update', (payload) => {
            this.log('收到仪表板更新:', payload);
            this.handleDashboardUpdate(payload);
        });

        // 添加通用消息监听器用于调试
        this.wsClient.on('message', (message) => {
            this.log('收到WebSocket消息:', message);
        });
    }
    
    /**
     * 认证
     */
    async authenticate() {
        const token = this.getAuthToken();
        await this.wsClient.send({
            type: 'auth',
            payload: { token: token }
        });
    }
    
    /**
     * 订阅消息
     */
    async subscribe() {
        const token = this.getAuthToken();
        await this.wsClient.send({
            type: 'subscribe',
            payload: {
                token: token,
                filters: {
                    station_id: this.stationId,
                    topics: [
                        'nozzle_status_update',
                        'transaction_update',
                        'shift_status_update',
                        'dashboard_update'
                    ]
                }
            }
        });
    }
    
    /**
     * 处理喷嘴状态更新
     */
    handleNozzleStatusUpdate(payload) {
        this.cache.nozzleStatus.set(payload.nozzle_id, payload);
        this.callbacks.onNozzleStatusUpdate(payload);
        this.updateNozzleStatusUI(payload);
    }

    /**
     * 处理批量喷嘴状态更新
     */
    handleNozzleStatusBatchUpdate(payload) {
        this.log('处理批量喷嘴状态更新:', payload);

        if (payload.nozzles && Array.isArray(payload.nozzles)) {
            // 批量更新本地缓存
            payload.nozzles.forEach(nozzle => {
                if (nozzle.nozzle_id) {
                    this.cache.nozzleStatus.set(nozzle.nozzle_id, nozzle);
                    this.updateNozzleStatusUI(nozzle);
                }
            });

            // 触发批量回调
            if (this.callbacks.onNozzleStatusBatchUpdate) {
                this.callbacks.onNozzleStatusBatchUpdate(payload);
            } else if (this.callbacks.onNozzleStatusUpdate) {
                // 如果没有批量回调，逐个触发单个回调
                payload.nozzles.forEach(nozzle => {
                    this.callbacks.onNozzleStatusUpdate(nozzle);
                });
            }

            this.log(`✅ 批量更新完成: ${payload.nozzles.length}个喷嘴, 时间: ${payload.batch_time}`);
        }
    }
    
    /**
     * 处理交易更新
     */
    handleTransactionUpdate(payload) {
        this.cache.transactions.set(payload.transaction_id, payload);
        this.callbacks.onTransactionUpdate(payload);
        this.updateTransactionUI(payload);
    }
    
    /**
     * 处理班次状态更新
     */
    handleShiftStatusUpdate(payload) {
        this.cache.shiftStatus = payload;
        this.callbacks.onShiftStatusUpdate(payload);
        this.updateShiftStatusUI(payload);
    }
    
    /**
     * 处理仪表板更新
     */
    handleDashboardUpdate(payload) {
        this.cache.dashboardData = payload;
        this.callbacks.onDashboardUpdate(payload);
        this.updateDashboardUI(payload);
    }
    
    /**
     * 更新喷嘴状态UI
     */
    updateNozzleStatusUI(payload) {
        const nozzleElement = document.querySelector('[data-nozzle-id="' + payload.nozzle_id + '"]');
        if (nozzleElement) {
            // 更新状态显示
            const statusElement = nozzleElement.querySelector('.status');
            if (statusElement) {
                statusElement.textContent = payload.status;
            }
            
            const volumeElement = nozzleElement.querySelector('.volume');
            if (volumeElement) {
                volumeElement.textContent = payload.current_volume.toFixed(2);
            }
            
            const amountElement = nozzleElement.querySelector('.amount');
            if (amountElement) {
                amountElement.textContent = payload.current_amount.toFixed(0);
            }
            
            // 更新状态样式
            nozzleElement.className = 'nozzle-item status-' + payload.status;
            
            // 显示错误信息
            if (payload.error_message) {
                const errorElement = nozzleElement.querySelector('.error');
                if (errorElement) {
                    errorElement.textContent = payload.error_message;
                }
            }
        }
    }
    
    /**
     * 更新交易UI
     */
    updateTransactionUI(payload) {
        const transactionList = document.getElementById('transaction-list');
        if (transactionList) {
            const transactionElement = document.createElement('div');
            transactionElement.className = 'transaction-item';
            transactionElement.innerHTML = 
                '<span>' + payload.transaction_number + '</span>' +
                '<span>' + payload.nozzle_id + '</span>' +
                '<span>' + payload.fuel_grade + '</span>' +
                '<span>' + payload.volume.toFixed(2) + ' L</span>' +
                '<span>Rp ' + payload.amount.toFixed(0) + '</span>' +
                '<span>' + payload.status + '</span>' +
                '<span>' + new Date(payload.updated_at).toLocaleTimeString() + '</span>';
            
            transactionList.insertBefore(transactionElement, transactionList.firstChild);
            
            // 限制显示数量
            while (transactionList.children.length > 20) {
                transactionList.removeChild(transactionList.lastChild);
            }
        }
    }
    
    /**
     * 更新班次状态UI
     */
    updateShiftStatusUI(payload) {
        const shiftElement = document.querySelector('#current-shift');
        if (shiftElement) {
            const shiftNumberElement = shiftElement.querySelector('.shift-number');
            if (shiftNumberElement) {
                shiftNumberElement.textContent = payload.shift_number;
            }
            
            const shiftStatusElement = shiftElement.querySelector('.shift-status');
            if (shiftStatusElement) {
                shiftStatusElement.textContent = payload.status;
            }
            
            const attendantNameElement = shiftElement.querySelector('.attendant-name');
            if (attendantNameElement) {
                attendantNameElement.textContent = payload.attendant_name;
            }
            
            // 更新时间显示
            const startTime = new Date(payload.start_time).toLocaleString();
            const startTimeElement = shiftElement.querySelector('.start-time');
            if (startTimeElement) {
                startTimeElement.textContent = startTime;
            }
            
            if (payload.end_time) {
                const endTime = new Date(payload.end_time).toLocaleString();
                const endTimeElement = shiftElement.querySelector('.end-time');
                if (endTimeElement) {
                    endTimeElement.textContent = endTime;
                }
            }
        }
    }
    
    /**
     * 更新仪表板UI
     */
    updateDashboardUI(payload) {
        // 更新今日数据
        const revenueElement = document.getElementById('today-revenue');
        if (revenueElement) {
            revenueElement.textContent = 'Rp ' + payload.today_revenue.toLocaleString();
        }
        
        const transactionsElement = document.getElementById('today-transactions');
        if (transactionsElement) {
            transactionsElement.textContent = payload.today_transactions.toString();
        }
        
        const volumeElement = document.getElementById('today-volume');
        if (volumeElement) {
            volumeElement.textContent = payload.today_volume.toFixed(2) + ' L';
        }
        
        // 更新趋势
        this.updateTrend('revenue-trend', payload.revenue_change);
        this.updateTrend('transaction-trend', payload.transaction_change);
        this.updateTrend('volume-trend', payload.volume_change);
        
        // 更新燃油销售组合
        this.updateFuelSalesMix(payload.fuel_sales_mix);
    }
    
    /**
     * 更新趋势显示
     */
    updateTrend(elementId, change) {
        const element = document.getElementById(elementId);
        if (element) {
            const changeText = (change >= 0 ? '+' : '') + change.toFixed(1) + '%';
            element.textContent = changeText;
            
            element.className = 'trend';
            if (change > 0) {
                element.className += ' trend-up';
            } else if (change < 0) {
                element.className += ' trend-down';
            } else {
                element.className += ' trend-neutral';
            }
        }
    }
    
    /**
     * 更新燃油销售组合
     */
    updateFuelSalesMix(fuelSalesMix) {
        const container = document.getElementById('fuel-sales-mix');
        if (container && fuelSalesMix) {
            // 清空现有内容，保留表头
            const header = container.querySelector('.fuel-mix-item');
            container.innerHTML = '';
            if (header) {
                container.appendChild(header);
            }
            
            fuelSalesMix.forEach(item => {
                const mixElement = document.createElement('div');
                mixElement.className = 'fuel-mix-item';
                mixElement.innerHTML = 
                    '<span>' + item.fuel_grade + '</span>' +
                    '<span>' + item.volume.toFixed(2) + ' L</span>' +
                    '<span>Rp ' + item.revenue.toLocaleString() + '</span>' +
                    '<span>' + item.percentage.toFixed(1) + '%</span>';
                container.appendChild(mixElement);
            });
        }
    }
    
    // 默认处理器
    defaultNozzleStatusHandler(payload) {
        this.log('喷嘴状态更新:', payload);
    }
    
    defaultTransactionHandler(payload) {
        this.log('交易更新:', payload);
    }
    
    defaultShiftStatusHandler(payload) {
        this.log('班次状态更新:', payload);
    }
    
    defaultDashboardHandler(payload) {
        this.log('仪表板更新:', payload);
    }
    
    defaultConnectionHandler(connected) {
        this.log('连接状态变化:', connected);
    }
    
    // 工具方法
    getCurrentStationId() {
        return 1; // 默认站点ID
    }
    
    getAuthToken() {
        return 'demo-token'; // 演示用token
    }
    
    log(...args) {
        if (this.debug) {
            console.log('[BOSRealtimeManager]', ...args);
        }
    }
    
    /**
     * 销毁实例
     */
    destroy() {
        if (this.wsClient) {
            this.wsClient.disconnect();
            this.wsClient = null;
        }
        this.isInitialized = false;
        this.log('WebSocket实时管理器已销毁');
    }
}
