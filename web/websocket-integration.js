/**
 * BOS前端WebSocket集成示例
 * 展示如何在现有前端应用中集成WebSocket实时数据推送
 */

class BOSRealtimeManager {
    constructor(options = {}) {
        this.stationId = options.stationId || this.getCurrentStationId();
        this.wsClient = null;
        this.isInitialized = false;
        
        // 数据缓存
        this.cache = {
            nozzleStatus: new Map(),
            transactions: new Map(),
            shiftStatus: null,
            dashboardData: null
        };
        
        // 回调函数
        this.callbacks = {
            onNozzleStatusUpdate: options.onNozzleStatusUpdate || this.defaultNozzleStatusHandler.bind(this),
            onTransactionUpdate: options.onTransactionUpdate || this.defaultTransactionHandler.bind(this),
            onShiftStatusUpdate: options.onShiftStatusUpdate || this.defaultShiftStatusHandler.bind(this),
            onDashboardUpdate: options.onDashboardUpdate || this.defaultDashboardHandler.bind(this),
            onConnectionChange: options.onConnectionChange || this.defaultConnectionHandler.bind(this)
        };
        
        this.debug = options.debug || false;
    }
    
    /**
     * 初始化WebSocket连接
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        
        try {
            // 创建WebSocket客户端
            this.wsClient = new BOSWebSocketClient({
                debug: this.debug,
                reconnectInterval: 3000,
                maxReconnectAttempts: 10
            });
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 连接WebSocket
            await this.wsClient.connect();
            
            // 订阅相关消息
            this.subscribeToMessages();
            
            this.isInitialized = true;
            this.log('实时数据管理器初始化完成');
            
        } catch (error) {
            this.log('初始化失败', error);
            throw error;
        }
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 连接状态变化
        this.wsClient.on('connected', () => {
            this.log('WebSocket已连接');
            this.callbacks.onConnectionChange(true);
        });
        
        this.wsClient.on('disconnected', () => {
            this.log('WebSocket已断开');
            this.callbacks.onConnectionChange(false);
        });
        
        this.wsClient.on('error', (error) => {
            this.log('WebSocket错误', error);
        });
        
        // 业务消息监听
        this.wsClient.on('nozzle_status_update', (payload) => {
            this.handleNozzleStatusUpdate(payload);
        });
        
        this.wsClient.on('transaction_update', (payload) => {
            this.handleTransactionUpdate(payload);
        });
        
        this.wsClient.on('shift_status_update', (payload) => {
            this.handleShiftStatusUpdate(payload);
        });
        
        this.wsClient.on('dashboard_update', (payload) => {
            this.handleDashboardUpdate(payload);
        });
        
        this.wsClient.on('price_update', (payload) => {
            this.handlePriceUpdate(payload);
        });
    }
    
    /**
     * 订阅消息
     */
    subscribeToMessages() {
        const topics = [
            'nozzle_status_update',
            'transaction_update',
            'shift_status_update',
            'dashboard_update',
            'price_update'
        ];
        
        const filters = {
            station_id: this.stationId
        };
        
        this.wsClient.subscribe(topics, filters);
        this.log(`已订阅站点 ${this.stationId} 的实时消息`);
    }
    
    /**
     * 处理喷嘴状态更新
     */
    handleNozzleStatusUpdate(payload) {
        this.log('喷嘴状态更新', payload);
        
        // 更新缓存
        this.cache.nozzleStatus.set(payload.nozzle_id, payload);
        
        // 调用回调函数
        this.callbacks.onNozzleStatusUpdate(payload);
        
        // 更新UI
        this.updateNozzleStatusUI(payload);
    }
    
    /**
     * 处理交易更新
     */
    handleTransactionUpdate(payload) {
        this.log('交易更新', payload);
        
        // 更新缓存
        this.cache.transactions.set(payload.transaction_id, payload);
        
        // 调用回调函数
        this.callbacks.onTransactionUpdate(payload);
        
        // 更新UI
        this.updateTransactionUI(payload);
    }
    
    /**
     * 处理班次状态更新
     */
    handleShiftStatusUpdate(payload) {
        this.log('班次状态更新', payload);
        
        // 更新缓存
        this.cache.shiftStatus = payload;
        
        // 调用回调函数
        this.callbacks.onShiftStatusUpdate(payload);
        
        // 更新UI
        this.updateShiftStatusUI(payload);
    }
    
    /**
     * 处理仪表板更新
     */
    handleDashboardUpdate(payload) {
        this.log('仪表板更新', payload);
        
        // 更新缓存
        this.cache.dashboardData = payload;
        
        // 调用回调函数
        this.callbacks.onDashboardUpdate(payload);
        
        // 更新UI
        this.updateDashboardUI(payload);
    }
    
    /**
     * 处理价格更新
     */
    handlePriceUpdate(payload) {
        this.log('价格更新', payload);
        
        // 更新UI
        this.updatePriceUI(payload);
    }
    
    /**
     * 更新喷嘴状态UI
     */
    updateNozzleStatusUI(payload) {
        const nozzleElement = document.querySelector(`[data-nozzle-id="${payload.nozzle_id}"]`);
        if (nozzleElement) {
            // 更新状态显示
            const statusElement = nozzleElement.querySelector('.status');
            if (statusElement) statusElement.textContent = payload.status;

            const volumeElement = nozzleElement.querySelector('.volume');
            if (volumeElement) volumeElement.textContent = payload.current_volume.toFixed(2);

            const amountElement = nozzleElement.querySelector('.amount');
            if (amountElement) amountElement.textContent = payload.current_amount.toFixed(0);

            // 更新状态样式
            nozzleElement.className = `nozzle-item status-${payload.status}`;

            // 显示错误信息
            if (payload.error_message) {
                const errorElement = nozzleElement.querySelector('.error');
                if (errorElement) errorElement.textContent = payload.error_message;
            }
        }
    }
    
    /**
     * 更新交易UI
     */
    updateTransactionUI(payload) {
        // 更新交易列表
        const transactionList = document.querySelector('#transaction-list');
        if (transactionList) {
            let transactionElement = document.querySelector(`[data-transaction-id="${payload.transaction_id}"]`);
            
            if (!transactionElement) {
                // 创建新的交易元素
                transactionElement = this.createTransactionElement(payload);
                transactionList.insertBefore(transactionElement, transactionList.firstChild);
            } else {
                // 更新现有交易元素
                this.updateTransactionElement(transactionElement, payload);
            }
        }
        
        // 更新交易统计
        this.updateTransactionStats();
    }
    
    /**
     * 更新班次状态UI
     */
    updateShiftStatusUI(payload) {
        const shiftElement = document.querySelector('#current-shift');
        if (shiftElement) {
            const shiftNumberElement = shiftElement.querySelector('.shift-number');
            if (shiftNumberElement) shiftNumberElement.textContent = payload.shift_number;

            const shiftStatusElement = shiftElement.querySelector('.shift-status');
            if (shiftStatusElement) shiftStatusElement.textContent = payload.status;

            const attendantNameElement = shiftElement.querySelector('.attendant-name');
            if (attendantNameElement) attendantNameElement.textContent = payload.attendant_name;

            // 更新时间显示
            const startTime = new Date(payload.start_time).toLocaleString();
            const startTimeElement = shiftElement.querySelector('.start-time');
            if (startTimeElement) startTimeElement.textContent = startTime;

            if (payload.end_time) {
                const endTime = new Date(payload.end_time).toLocaleString();
                const endTimeElement = shiftElement.querySelector('.end-time');
                if (endTimeElement) endTimeElement.textContent = endTime;
            }
        }
    }
    
    /**
     * 更新仪表板UI
     */
    updateDashboardUI(payload) {
        // 更新收入数据
        const revenueElement = document.querySelector('#today-revenue');
        if (revenueElement) {
            revenueElement.textContent = this.formatCurrency(payload.today_revenue);
        }
        
        // 更新交易数量
        const transactionCountElement = document.querySelector('#today-transactions');
        if (transactionCountElement) {
            transactionCountElement.textContent = payload.today_transactions;
        }
        
        // 更新销量
        const volumeElement = document.querySelector('#today-volume');
        if (volumeElement) {
            volumeElement.textContent = payload.today_volume.toFixed(2) + ' L';
        }
        
        // 更新燃油销售组合
        this.updateFuelSalesMix(payload.fuel_sales_mix);
        
        // 更新变化趋势
        this.updateTrendIndicators(payload);
    }
    
    /**
     * 更新价格UI
     */
    updatePriceUI(payload) {
        Object.entries(payload.prices).forEach(([fuelGrade, price]) => {
            const priceElement = document.querySelector(`[data-fuel-grade="${fuelGrade}"] .price`);
            if (priceElement) {
                priceElement.textContent = this.formatCurrency(price);
                
                // 添加价格变化动画
                priceElement.classList.add('price-updated');
                setTimeout(() => {
                    priceElement.classList.remove('price-updated');
                }, 2000);
            }
        });
    }
    
    /**
     * 创建交易元素
     */
    createTransactionElement(payload) {
        const element = document.createElement('div');
        element.className = `transaction-item status-${payload.status}`;
        element.setAttribute('data-transaction-id', payload.transaction_id);
        
        element.innerHTML = `
            <div class="transaction-number">${payload.transaction_number}</div>
            <div class="nozzle-id">${payload.nozzle_id}</div>
            <div class="fuel-type">${payload.fuel_type}</div>
            <div class="volume">${payload.volume.toFixed(2)} L</div>
            <div class="amount">${this.formatCurrency(payload.amount)}</div>
            <div class="status">${payload.status}</div>
            <div class="time">${new Date(payload.created_at).toLocaleString()}</div>
        `;
        
        return element;
    }
    
    /**
     * 更新交易元素
     */
    updateTransactionElement(element, payload) {
        element.className = `transaction-item status-${payload.status}`;
        element.querySelector('.status').textContent = payload.status;
        element.querySelector('.volume').textContent = payload.volume.toFixed(2) + ' L';
        element.querySelector('.amount').textContent = this.formatCurrency(payload.amount);
    }
    
    /**
     * 默认事件处理器
     */
    defaultNozzleStatusHandler(payload) {
        this.log('默认喷嘴状态处理器', payload);
    }
    
    defaultTransactionHandler(payload) {
        this.log('默认交易处理器', payload);
    }
    
    defaultShiftStatusHandler(payload) {
        this.log('默认班次状态处理器', payload);
    }
    
    defaultDashboardHandler(payload) {
        this.log('默认仪表板处理器', payload);
    }
    
    defaultConnectionHandler(connected) {
        this.log('连接状态变化', connected);
        
        // 显示连接状态
        const statusElement = document.querySelector('#connection-status');
        if (statusElement) {
            statusElement.textContent = connected ? '已连接' : '已断开';
            statusElement.className = connected ? 'connected' : 'disconnected';
        }
    }
    
    /**
     * 工具方法
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
        }).format(amount);
    }
    
    getCurrentStationId() {
        // 从URL参数或其他地方获取站点ID
        const urlParams = new URLSearchParams(window.location.search);
        return parseInt(urlParams.get('station_id')) || 1;
    }
    
    updateTransactionStats() {
        const transactions = Array.from(this.cache.transactions.values());
        const todayTransactions = transactions.filter(t => 
            new Date(t.created_at).toDateString() === new Date().toDateString()
        );
        
        const totalAmount = todayTransactions.reduce((sum, t) => sum + t.amount, 0);
        const totalVolume = todayTransactions.reduce((sum, t) => sum + t.volume, 0);
        
        // 更新统计显示
        const statsElement = document.querySelector('#transaction-stats');
        if (statsElement) {
            statsElement.innerHTML = `
                <div>今日交易: ${todayTransactions.length}</div>
                <div>总金额: ${this.formatCurrency(totalAmount)}</div>
                <div>总销量: ${totalVolume.toFixed(2)} L</div>
            `;
        }
    }
    
    updateFuelSalesMix(fuelSalesMix) {
        const mixElement = document.querySelector('#fuel-sales-mix');
        if (mixElement && fuelSalesMix) {
            mixElement.innerHTML = fuelSalesMix.map(item => `
                <div class="fuel-mix-item">
                    <span class="fuel-grade">${item.fuel_grade}</span>
                    <span class="volume">${item.volume.toFixed(2)} L</span>
                    <span class="revenue">${this.formatCurrency(item.revenue)}</span>
                    <span class="percentage">${item.percentage.toFixed(1)}%</span>
                </div>
            `).join('');
        }
    }
    
    updateTrendIndicators(payload) {
        const indicators = [
            { key: 'revenue_change', element: '#revenue-trend' },
            { key: 'transaction_change', element: '#transaction-trend' },
            { key: 'volume_change', element: '#volume-trend' }
        ];
        
        indicators.forEach(({ key, element }) => {
            const el = document.querySelector(element);
            if (el) {
                const change = payload[key];
                el.textContent = `${change > 0 ? '+' : ''}${change.toFixed(1)}%`;
                el.className = change > 0 ? 'trend-up' : change < 0 ? 'trend-down' : 'trend-neutral';
            }
        });
    }
    
    log(message, ...args) {
        if (this.debug) {
            console.log(`[BOSRealtime] ${message}`, ...args);
        }
    }
    
    /**
     * 销毁实例
     */
    destroy() {
        if (this.wsClient) {
            this.wsClient.disconnect();
            this.wsClient = null;
        }
        this.isInitialized = false;
        this.cache = {
            nozzleStatus: new Map(),
            transactions: new Map(),
            shiftStatus: null,
            dashboardData: null
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BOSRealtimeManager;
} else {
    window.BOSRealtimeManager = BOSRealtimeManager;
}

/**
 * 使用示例
 */
/*
// 1. 基本使用
const realtimeManager = new BOSRealtimeManager({
    stationId: 1,
    debug: true,
    onNozzleStatusUpdate: (payload) => {
        console.log('喷嘴状态更新:', payload);
        // 自定义处理逻辑
    },
    onTransactionUpdate: (payload) => {
        console.log('交易更新:', payload);
        // 更新交易列表
        updateTransactionList(payload);
    }
});

// 初始化连接
realtimeManager.initialize().then(() => {
    console.log('实时数据管理器已启动');
}).catch(error => {
    console.error('启动失败:', error);
});

// 2. 页面卸载时清理
window.addEventListener('beforeunload', () => {
    realtimeManager.destroy();
});

// 3. 手动订阅特定消息
realtimeManager.wsClient.subscribe(['price_update'], {
    station_id: 1,
    device_ids: ['device1', 'device2']
});
*/
