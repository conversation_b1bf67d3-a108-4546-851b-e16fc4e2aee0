# 前端轮询接口分析与 WebSocket 重构方案

## 文档目的

基于真实代码分析，梳理前端当前所有轮询接口的：
- URL、请求参数、返回字段（前端实际使用的）
- 业务目的与数据用途
- WebSocket 重构的可行方案：事件推送、迁移策略

## 当前轮询总览

### 服务架构
- **FCC 服务**（设备/喷嘴状态）：`http://172.18.7.81:8081`
- **BOS/Base 服务**（交易与班次）：`http://172.18.7.81:8080`

### 轮询频率（基于真实代码）
- **FCC 配置轮询**：5 分钟（`Duration(minutes: 5)`）
- **FCC 喷嘴状态**：1-2 秒（默认 2 秒，实际使用中设为 1 秒）
- **BOS 交易轮询**：5 秒（`Duration(seconds: 5)`）
- **班次状态轮询**：15 秒（`Duration(seconds: 15)`）

### 实现位置
```
lib/services/fcc_status_polling_service.dart     # FCC 轮询调度
lib/services/bos_transaction_polling_service.dart # BOS 交易轮询
lib/services/global_polling_service.dart         # 全局班次轮询
lib/controllers/fcc_status_controller.dart       # FCC 控制器
```

## 详细轮询分析

### 1. FCC 设备/喷嘴状态轮询

#### 低频设备配置轮询（5 分钟）
**API 调用**：
```
GET /api/v2/devices
```

**查询参数**：
- `station_id`, `island_id`, `type`, `status`, `limit`, `offset`

**响应字段（前端使用）**：
```json
{
  "devices": [
    {
      "id": "string",
      "name": "string", 
      "type": "pump|dart_pump|...",
      "model": "string?",
      "vendor": "string?",
      "serial_number": "string?",
      "controller_id": "string",
      "controller": { /* 控制器对象 */ },
      "device_address": "number",
      "station_id": "string",
      "island_id": "string?",
      "position": "string?",
      "dispenser_number": "number?",
      "status": "online|offline|error|maintenance",
      "health": "healthy|good|warning|critical|error",
      "last_seen": "ISO8601?",
      "capabilities": { /* 能力对象 */ },
      "nozzles": [ /* 喷嘴数组，可能为空 */ ],
      "max_nozzles": "number",
      "supported_grades": ["string"],
      "metadata": "object?",
      "created_at": "ISO8601",
      "updated_at": "ISO8601",
      "version": "number"
    }
  ]
}
```

**补充调用**（当设备 nozzles 为空时）：
```
GET /api/v2/devices/{deviceId}/nozzles
```

#### 高频喷嘴状态轮询（1-2 秒）
**API 调用**：
```
GET /api/v2/devices/{deviceId}/nozzles
```

**响应字段（前端使用）**：
```json
{
  "device_name": "string",
  "nozzles": [
    {
      "id": "string",
      "nozzle_number": "number",
      "name": "string",
      "fuel_grade_name": "string",
      "current_price": "number",
      "status": "idle|selected|authorized|out|filling|completed|suspended|error|maintenance",
      "is_enabled": "boolean",
      "is_selected": "boolean", 
      "current_volume": "number",
      "current_amount": "number",
      "last_update": "ISO8601",
      "metadata": {
        "dispenser_id": "string?",
        "pump_group_id": "string?"
      },
      "state_data": "object?",
      "preauth_type": "string?",
      "preauth_number": "string?", 
      "preauth_created_at": "ISO8601?",
      "preauth_expires_at": "ISO8601?",
      "error_message": "string?"
    }
  ]
}
```

### 2. BOS 交易轮询（5 秒）

**API 调用**：
```
GET /api/v1/fuel-transactions
```

**查询参数（实际使用）**：
```json
{
  "status": "pending",
  "date_from": "YYYY-MM-DD",
  "date_to": "YYYY-MM-DD", 
  "page": 1,
  "limit": 20,
  "sort_by": "created_at",
  "sort_dir": "desc"
}
```

**响应字段（前端使用）**：
```json
{
  "items": [
    {
      "id": "string",
      "transaction_number": "string",
      "station_id": "number",
      "pump_id": "string",
      "nozzle_id": "string",
      "fuel_type": "string",
      "fuel_grade": "string", 
      "unit_price": "number",
      "volume": "number",
      "amount": "number",
      "status": "pending|processed|cancelled",
      "member_card_id": "string?",
      "member_id": "number?",
      "user_id": "number?",
      "fcc_transaction_id": "string?",
      "pos_terminal_id": "string?",
      "metadata": "object",
      "created_at": "ISO8601",
      "updated_at": "ISO8601",
      "processed_at": "ISO8601?",
      "cancelled_at": "ISO8601?"
    }
  ],
  "total": "number",
  "page": "number", 
  "page_size": "number",
  "total_page": "number"
}
```

### 3. 班次状态轮询（15 秒）

**API 调用**：
```
GET /api/v1/shifts/current/{station_id}
```

**响应字段（前端使用）**：
```json
{
  "has_active_shift": "boolean",
  "shift": {
    "id": "string",
    "shift_number": "string",
    "station_id": "number",
    "start_time": "ISO8601",
    "end_time": "ISO8601?",
    "status": "string?",
    "created_at": "ISO8601",
    "updated_at": "ISO8601", 
    "deleted_at": "ISO8601?",
    "metadata": "object?"
  },
  "message": "string"
}
```

## 业务目的与数据用途

### FCC 配置轮询（低频）
**业务目的**：
- 设备发现与 UI 映射
- 获取设备能力与限制（max_nozzles、supported_grades）
- 建立设备基础属性（dispenser_number、位置、价格）

**数据用途**：
- 构造 UI 中的 dispenser/nozzle 列表结构
- 建立 device/nozzle 到 UI 控件的映射关系
- 设置权限与可操作性（is_enabled、capabilities）
- 为高频状态更新提供上下文（device_name、fuel_grade 等）

### FCC 喷嘴状态轮询（高频）
**业务目的**：
- 实时展示喷嘴状态变化
- 驱动前端交易状态机（idle → authorized → filling → completed）
- 同步预授权状态与错误提示
- 显示实时交易进度（volume、amount）

**数据用途**：
- 更新 nozzle 的 status/current_volume/current_amount/current_price
- 预授权信息影响"可支付/可结算"的 UI 状态
- metadata.dispenser_id/pump_group_id 用于 UI 分组与用户筛选
- error_message 用于故障提示与断线检测

### BOS 交易轮询（5 秒）
**业务目的**：
- 从业务系统拉取待处理交易
- 驱动 EDC 前端将 nozzle 锁定到 complete 状态（Tap to Pay 流程）
- 跟踪 pending 交易的"消失"以解锁 nozzle

**数据用途**：
- 将有 pending 交易的 nozzle 强制维持在 complete 状态
- 交易消失时触发解锁，清除完成态并刷新 FCC 状态
- 交易金额/油量用于结算界面显示与小票打印
- transaction_number 与 nozzle_id 建立绑定关系

### 班次状态轮询（15 秒）
**业务目的**：
- 控制交易查询的时间窗口
- 提供班结入口与操作提示
- 控制权限与可操作性（未开班时的限制）

**数据用途**：
- 决定 date_from/date_to 的选择（活跃班次→班次时段；否则回退最近 6 小时）
- 班次变化事件触发 UI 更新
- 控制开班/结班操作的可用性
- 状态栏显示与统计信息

## WebSocket 重构方案

### 事件主题与载荷设计

#### 1. device_config_update（低频或按需）
```json
{
  "topic": "device_config_update",
  "payload": {
    "devices": [
      {
        "id": "string",
        "name": "string",
        "type": "string", 
        "status": "string",
        "health": "string",
        "last_seen": "ISO8601",
        "station_id": "string",
        "island_id": "string?",
        "dispenser_number": "number?",
        "capabilities": "object",
        "max_nozzles": "number",
        "supported_grades": ["string"],
        "metadata": "object?"
      }
    ]
  },
  "seq": "number",
  "occurred_at": "ISO8601"
}
```

#### 2. nozzle_status_update（高频）
```json
{
  "topic": "nozzle_status_update", 
  "payload": {
    "device_id": "string",
    "device_name": "string",
    "nozzles": [
      {
        "id": "string",
        "nozzle_number": "number",
        "name": "string",
        "fuel_grade_name": "string",
        "current_price": "number",
        "status": "string",
        "is_enabled": "boolean",
        "is_selected": "boolean",
        "current_volume": "number", 
        "current_amount": "number",
        "last_update": "ISO8601",
        "metadata": {
          "dispenser_id": "string?",
          "pump_group_id": "string?"
        },
        "state_data": "object?",
        "preauth_type": "string?",
        "preauth_number": "string?",
        "preauth_created_at": "ISO8601?",
        "preauth_expires_at": "ISO8601?",
        "error_message": "string?"
      }
    ]
  },
  "seq": "number",
  "occurred_at": "ISO8601"
}
```

#### 3. transaction_update（交易变更）
```json
{
  "topic": "transaction_update",
  "payload": {
    "transaction_number": "string",
    "nozzle_id": "string", 
    "status": "pending|processed|cancelled",
    "unit_price": "number",
    "volume": "number",
    "amount": "number",
    "created_at": "ISO8601",
    "updated_at": "ISO8601",
    "station_id": "number?",
    "pump_id": "string?"
  },
  "seq": "number",
  "occurred_at": "ISO8601"
}
```

#### 4. transaction_disappear（交易消失）
```json
{
  "topic": "transaction_disappear",
  "payload": {
    "transaction_numbers": ["string"]
  },
  "seq": "number", 
  "occurred_at": "ISO8601"
}
```

#### 5. shift_status_update（班次状态）
```json
{
  "topic": "shift_status_update",
  "payload": {
    "has_active_shift": "boolean",
    "shift": {
      "id": "string",
      "shift_number": "string", 
      "station_id": "number",
      "start_time": "ISO8601",
      "end_time": "ISO8601?",
      "status": "string?",
      "metadata": "object?"
    },
    "message": "string"
  },
  "seq": "number",
  "occurred_at": "ISO8601"
}
```

### WebSocket 连接与订阅

#### 连接建立
```
# FCC WebSocket (已有 URL 生成函数)
ws://172.18.7.82:8081/ws

# BOS WebSocket (需新增)  
ws://172.18.7.81:8080/ws
```

#### 认证与订阅
```json
{
  "action": "subscribe",
  "token": "Bearer <jwt_token>",
  "filters": {
    "station_id": "required_string",
    "island_id": "optional_string",
    "dispenser_ids": ["optional_array"],
    "nozzle_ids": ["optional_array"],
    "topics": ["device_config_update", "nozzle_status_update", "transaction_update", "shift_status_update"]
  }
}
```

#### 初始快照响应
```json
{
  "type": "snapshot",
  "payload": {
    "devices": [ /* 当前设备状态 */ ],
    "nozzles": [ /* 当前喷嘴状态 */ ],
    "active_transactions": [ /* 当前 pending 交易 */ ],
    "current_shift": { /* 当前班次 */ }
  },
  "seq": "number",
  "occurred_at": "ISO8601"
}
```

### 推送策略

#### 频率控制
- **设备配置**：按需推送（配置变更时）
- **喷嘴状态**：200-500ms 批量合并推送
- **交易状态**：实时推送（状态变更时）
- **班次状态**：变更时推送

#### 去重与合并
- 同一帧内的多个喷嘴状态变更合并为单条消息
- 相同 nozzle_id 的连续状态更新去重（保留最新）

#### 顺序保证
- 每条消息包含递增的 seq 序号
- 客户端按 seq 有序应用，乱序时缓存等待
- 提供幂等处理机制

### 断线重连机制

#### 重连请求
```json
{
  "action": "reconnect",
  "token": "Bearer <jwt_token>",
  "last_seq": "number",
  "filters": { /* 同订阅 */ }
}
```

#### 服务端响应
- 支持从 `last_seq` 之后补发增量事件
- 若增量补发不可用，返回完整快照
- 超过补发窗口时，强制快照重建

### 渐进式迁移策略

#### 阶段 1：双轨运行（2-4 周）
- 接入 WebSocket，保持现有轮询作为兜底
- 降低轮询频率（FCC 状态：2s→10s，交易：5s→30s）
- 对比 WebSocket 与轮询数据的一致性
- 监控 WebSocket 连接稳定性与延迟

#### 阶段 2：逐步切换（1-2 周）
- 高频数据优先切换到 WebSocket（喷嘴状态）
- 保留低频轮询作为校验（设备配置、班次状态）
- 完善错误处理与重连逻辑
- 压力测试与性能优化

#### 阶段 3：完全切换（1 周）
- 移除所有定时轮询
- 保留手动刷新按钮作为人工兜底
- 监控生产环境稳定性
- 建立 WebSocket 监控与告警

### 风险控制

#### 技术风险
- **消息顺序**：喷嘴状态与交易事件的时序一致性
- **重复消息**：客户端幂等处理机制
- **断线补偿**：支持增量补发或快照重建
- **负载控制**：高频更新的批量合并与限流

#### 业务风险  
- **权限隔离**：按站点/设备权限精确下发，避免数据泄漏
- **时钟同步**：以服务端时间为准，包含 occurred_at 用于排序
- **兜底机制**：保留手动刷新，关键操作前强制同步

## 实施建议

### 前端改造点
1. **适配层**：在现有 PollingService 基础上增加 WebSocketAdapter
2. **状态管理**：保持现有 Controller 与 Model 不变，仅替换数据源
3. **错误处理**：完善断线重连、消息去重、顺序处理逻辑
4. **监控面板**：增加 WebSocket 连接状态、消息延迟、重连次数等指标

### 服务端要求
1. **消息设计**：按本文档的事件主题与载荷格式实现
2. **权限控制**：基于 JWT token 的站点级数据过滤
3. **性能优化**：支持批量推送、消息合并、增量补发
4. **监控告警**：WebSocket 连接数、消息吞吐量、错误率监控

---

**注**：本文档基于真实代码分析，所有 API 路径、参数、响应字段均来自实际实现。WebSocket 重构方案考虑了现有业务逻辑与技术架构的兼容性。
